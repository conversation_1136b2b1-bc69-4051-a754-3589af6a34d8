# build stage
FROM node:16-alpine as build-stage

# make the 'app' folder the current working directory
WORKDIR /app

# copy both 'package.json' and 'package-lock.json' (if available)
COPY package*.json ./

# install project dependencies
RUN npm install

# copy project files and folders to the current working directory (i.e. 'app' folder)
COPY . .

# build app for production with minification
RUN npm run build

# production stage
FROM nginx:stable-alpine as production-stage
COPY --from=build-stage /app/dist /usr/share/nginx/html
EXPOSE 80

# Set a build argument for the image tag and deploy_places with default value
ARG IMAGE_TAG=latest
ARG PLACES=latest

# Set environment variables for image_tag and places
ENV IMAGE_TAG=$IMAGE_TAG
ENV PLACES=$PLACES

# Copy the script into the container
COPY auto_update_versioning.sh /auto_update_versioning.sh

# Use sed to remove ^M characters
RUN sed -i 's/\r//' auto_update_versioning.sh

# Set execute permissions on the script
RUN chmod +x /auto_update_versioning.sh

CMD sh /auto_update_versioning.sh & \
    nginx -g 'daemon off;'
