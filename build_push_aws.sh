#!/bin/bash
## Interactive Docker Build and Push Script for TC Dashboard
## Usage: bash build_push_aws.sh [optional_tag]

# Define available tags with descriptions
declare -A tag_descriptions=(
    ["latest"]="Subang - Production deployment"
    ["penta-abc"]="Penta ABC - Production deployment"
    ["penta-d"]="Penta D - Production deployment"
    ["vitrox"]="Vitrox - Production deployment"
    ["mockv"]="Mock Vitrox - Testing environment"
    ["maintenance-staging"]="Maintenance Dashboard - Staging environment"
    ["vitrox-mock-sm-2"]="SM-WMS Integration - Vitrox dual-winch skycar staging"
    ["vitrox-mock-sm-model-c"]="SM-WMS Integration - Vitrox model C skycar staging"
    ["wirago"]="Wirago - Production deployment"
    ["stable"]="Vitrox 2.0 - Production deployment"
    ["kha"]="KHA - Production deployment"
)

# Function to get tag description
get_tag_description() {
    case "$1" in
        "latest") echo "Subang - Production deployment" ;;
        "maintenance-staging") echo "Maintenance Dashboard - Staging environment" ;;
        "mockv") echo "Mock Vitrox - Testing environment" ;;
        "penta-abc") echo "Penta ABC - Production deployment" ;;
        "penta-d") echo "Penta D - Production deployment" ;;
        "vitrox") echo "Vitrox - Production deployment" ;;
        "vitrox-mock-sm-2") echo "SM-WMS Integration - Vitrox dual-winch skycar staging" ;;
        "vitrox-mock-sm-model-c") echo "SM-WMS Integration - Vitrox model C skycar staging" ;;
        "wirago") echo "Wirago - Production deployment" ;;
        "stable") echo "Vitrox 2.0 - Production deployment" ;;
        "kha") echo "KHA - Production deployment" ;;
        *) echo "Custom deployment" ;;
    esac
}

# Function to display tag selection menu
show_tag_menu() {
    echo "=============================================="
    echo "🚀 TC Dashboard - Docker Build & Push Tool"
    echo "=============================================="
    echo ""
    echo "Available deployment tags:"
    echo ""
    
    # Define tags with descriptions directly
    printf "%2d) %-30s - %s\n" 1 "latest" "Subang - Production deployment"
    printf "%2d) %-30s - %s\n" 2 "maintenance-staging" "Maintenance Dashboard - Staging environment"
    printf "%2d) %-30s - %s\n" 3 "mockv" "Mock Vitrox - Testing environment"
    printf "%2d) %-30s - %s\n" 4 "penta-abc" "Penta ABC - Production deployment"
    printf "%2d) %-30s - %s\n" 5 "penta-d" "Penta D - Production deployment"
    printf "%2d) %-30s - %s\n" 6 "vitrox" "Vitrox - Production deployment"
    printf "%2d) %-30s - %s\n" 7 "vitrox-mock-sm-2" "SM-WMS Integration - Vitrox dual-winch skycar staging"
    printf "%2d) %-30s - %s\n" 8 "vitrox-mock-sm-model-c" "SM-WMS Integration - Vitrox model C skycar staging"
    printf "%2d) %-30s - %s\n" 9 "wirago" "Wirago - Production deployment"
<<<<<<< HEAD
=======
    printf "%2d) %-30s - %s\n" 10 "kha" "KHA - Production deployment"
    printf "%2d) %-30s - %s\n" 11 "stable" "Vitrox 2.0 - Production deployment"
>>>>>>> 4b374782 (add new vitrox image tag)
    
    # Define tags array for selection logic
    local tags=(
        "latest"
        "maintenance-staging"
        "mockv"
        "penta-abc"
        "penta-d"
        "vitrox"
        "vitrox-mock-sm-2"
        "vitrox-mock-sm-model-c"
        "wirago"
<<<<<<< HEAD
=======
        "kha"
        "stable"
>>>>>>> 4b374782 (add new vitrox image tag)
    )
    
    echo ""
    echo " 0) Enter custom tag"
    echo ""
    
    # Get user selection
    while true; do
        read -p "Select deployment target (1-${#tags[@]} or 0 for custom): " choice
        
        if [[ "$choice" == "0" ]]; then
            read -p "Enter custom tag: " custom_tag
            if [[ -n "$custom_tag" ]]; then
                image_tag="$custom_tag"
                places="$custom_tag"
                echo "✅ Using custom tag: $image_tag"
                break
            else
                echo "❌ Custom tag cannot be empty!"
            fi
        elif [[ "$choice" =~ ^[0-9]+$ ]] && [ "$choice" -ge 1 ] && [ "$choice" -le "${#tags[@]}" ]; then
            selected_index=$((choice - 1))
            image_tag="${tags[$selected_index]}"
            places="$image_tag"
            echo ""
            echo "✅ Selected: $image_tag - $(get_tag_description "$image_tag")"
            break
        else
            echo "❌ Invalid selection. Please choose 1-${#tags[@]} or 0."
        fi
    done
}

# Check if tag provided as argument
if [ -n "$1" ]; then
    image_tag="$1"
    places="${2:-$1}"
    echo "🏷️  Using provided tag: $image_tag"
    description=$(get_tag_description "$image_tag")
    if [[ "$description" != "Custom deployment" ]]; then
        echo "📝 Description: $description"
    fi
else
    show_tag_menu
fi

echo ""
echo "=============================================="
echo "📋 Build Configuration Summary"
echo "=============================================="
echo "🏷️  Image Tag: $image_tag"
echo "📍 Places: $places"
echo "🖥️  Platform: $OSTYPE"
echo "🐳 Repository: 551979302293.dkr.ecr.ap-southeast-1.amazonaws.com/cube-ihub-tc-web"
echo ""

# Confirmation before proceeding
read -p "🤔 Proceed with build and push? (y/N): " confirm
if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
    echo "❌ Build cancelled by user."
    exit 1
fi

echo ""
echo "🔨 Starting Docker build process..."
echo "=============================================="

if [[ "$OSTYPE" == "darwin"* ]]; then 
    echo "🍎 Building for macOS (linux/amd64 platform)..."
    docker build --platform linux/amd64 \
        --build-arg IMAGE_TAG="$image_tag" \
        --build-arg PLACES="$places" \
        -t 551979302293.dkr.ecr.ap-southeast-1.amazonaws.com/cube-ihub-tc-web:$image_tag .
else 
    echo "🐧 Building for Linux platform..."
    docker build --build-arg IMAGE_TAG="$image_tag" \
        --build-arg PLACES="$places" \
        -t 551979302293.dkr.ecr.ap-southeast-1.amazonaws.com/cube-ihub-tc-web:$image_tag .
fi

# Check if build was successful
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Docker build completed successfully!"
    echo ""
    echo "🚀 Pushing image to AWS ECR..."
    echo "=============================================="
    
    # Login to ECR
    echo "🔐 Authenticating with AWS ECR..."
    aws ecr get-login-password --region ap-southeast-1 | docker login --username AWS --password-stdin 551979302293.dkr.ecr.ap-southeast-1.amazonaws.com
    
    if [ $? -eq 0 ]; then
        echo "📤 Pushing image..."
        docker push 551979302293.dkr.ecr.ap-southeast-1.amazonaws.com/cube-ihub-tc-web:$image_tag
        
        if [ $? -eq 0 ]; then
            echo ""
            echo "🎉 SUCCESS! Image pushed successfully!"
            echo "=============================================="
            echo "🏷️  Tag: $image_tag"
            echo "📍 Environment: $(get_tag_description "$image_tag")"
            echo "🐳 Image: 551979302293.dkr.ecr.ap-southeast-1.amazonaws.com/cube-ihub-tc-web:$image_tag"
            echo ""
        else
            echo "❌ Failed to push image to ECR!"
            exit 1
        fi
    else
        echo "❌ Failed to authenticate with AWS ECR!"
        exit 1
    fi
else
    echo "❌ Docker build failed!"
    exit 1
fi

echo "✨ Deployment process completed!"
read -p "Press Enter to exit..."
