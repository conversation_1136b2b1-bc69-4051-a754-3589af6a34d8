
// class Job {
//     constructor(id, type , status,skycar_id,created, updated) {
//       id  : Number= id;
//       type : String = type 
//       status : String = status
//       skycar_id : Number = skycar_id
//       created : Date = created
//       updated : Date = updated
//     }

//     singleJob():void{
//       console.log(this.id)
//     }
// }
// class JobEvent{
//     constructor(id, type , status,created, updated) {

//     }

// }

// enum JobType {
//     RequestPick = "RequestPick",
//     UpdatePick = "UpdatePick",
//     EndStage = "EndStage",
//     RequestDrop = "RequestDrop",
//     UpdateDrop = "UpdateDrop",
//   }
//   let c: JobType = JobType.EndStage;