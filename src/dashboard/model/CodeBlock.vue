<template>
  <pre :name="name"><slot></slot></pre>
</template>

<script>
export default {
  name: "CodeBlock",
  props: {
    name: String,
  },
};
</script>

<style scoped>
pre {
  overflow: auto;
  max-height: 50vh;
  line-height: 1.2em;
  background-color: #eeeeee;
  border-radius: 3px;
  padding: 1em;
}

pre::-webkit-scrollbar {
  height: 16px;
  height: 16px;
  background-color: transparent;
}

pre::-webkit-scrollbar-thumb {
  border-radius: 1.5em;
  background-color: rgba(150, 150, 150, 0.4);
  background-clip: padding-box;
  border: 4px solid transparent;
  transition: all 0.5s ease-in-out;
}

pre::-webkit-scrollbar-thumb:hover {
  background-color: rgba(150, 150, 150, 0.8);
}

pre::-webkit-scrollbar-corner,
pre::-webkit-scrollbar-track {
  background-color: transparent;
}
</style>
