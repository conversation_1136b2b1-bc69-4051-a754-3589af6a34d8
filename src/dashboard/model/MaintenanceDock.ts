// export class MaintenanceDock {
//     coordinate: string;
//     text: string;
//     skycar_id: number;

//     constructor(text: string , coordinate : string , id :number) {
//         this.coordinate = coordinate
//         this.text = text
//         this.skycar_id = id
//     }

//     singleJob(): number {
//         let incre = this.skycar_id + 1
//         console.log(incre)
//         return incre
//     }
// }