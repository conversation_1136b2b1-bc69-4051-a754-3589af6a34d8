export default {
  title: 'Test/Simple',
  parameters: {
    docs: {
      description: {
        component: 'A simple test component'
      }
    }
  }
};

export const SimpleTest = () => ({
  template: `
    <div style="padding: 20px; background: #333; color: white;">
      <h1>🎉 Storybook is working!</h1>
      <p>This is a simple test story to verify Storybook is functioning correctly.</p>
      <p>If you can see this, Storybook is working properly.</p>
    </div>
  `
});

export const ButtonTest = () => ({
  template: `
    <div style="padding: 20px; background: #333; color: white;">
      <h2>Button Test</h2>
      <v-btn color="primary" @click="handleClick">
        Click me!
      </v-btn>
      <p v-if="clicked" style="color: green; margin-top: 10px;">
        Button was clicked!
      </p>
    </div>
  `,
  data() {
    return {
      clicked: false
    };
  },
  methods: {
    handleClick() {
      this.clicked = true;
      console.log('Button clicked!');
    }
  }
}); 