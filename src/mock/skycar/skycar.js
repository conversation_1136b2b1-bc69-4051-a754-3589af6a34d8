export function mock_skycar(){
return [
    {
        "skycar_id": 7,
        "status": "MAINTENANCE",
        "connect": true,
        "pair": false,
        "direction": "Y",
        "coordinate": "10,1,0",
        "storage_no": null,
        "bin_on_hold": false,
        "battery": 60,
        "mode": 0
    },
    {
        "skycar_id": 0,
        "status": "MAINTENANCE",
        "connect": false,
        "pair": false,
        "direction": "X",
        "coordinate": "6,5,0",
        "storage_no": null,
        "bin_on_hold": false,
        "battery": 0,
        "mode": 0
    },
    {
        "skycar_id": 1,
        "status": "AVAILABLE",
        "connect": true,
        "pair": true,
        "direction": "Y",
        "coordinate": "7,2,0",
        "storage_no": null,
        "bin_on_hold": false,
        "battery": 12,
        "mode": 0
    },
    {
        "skycar_id": 2,
        "status": "AVAILABLE",
        "connect": true,
        "pair": true,
        "direction": "X",
        "coordinate": "0,0,0",
        "storage_no": null,
        "bin_on_hold": false,
        "battery": 11,
        "mode": 0
    },
    {
        "skycar_id": 4,
        "status": "AVAILABLE",
        "connect": true,
        "pair": true,
        "direction": "Y",
        "coordinate": "11,2,0",
        "storage_no": null,
        "bin_on_hold": false,
        "battery": 12,
        "mode": 0
    },
    {
        "skycar_id": 8,
        "status": "AVAILABLE",
        "connect": true,
        "pair": true,
        "direction": "Y",
        "coordinate": "15,2,0",
        "storage_no": null,
        "bin_on_hold": false,
        "battery": 7,
        "mode": 0
    },
    {
        "skycar_id": 9,
        "status": "AVAILABLE",
        "connect": true,
        "pair": true,
        "direction": "Y",
        "coordinate": "16,2,0",
        "storage_no": null,
        "bin_on_hold": false,
        "battery": 9,
        "mode": 0
    },
    {
        "skycar_id": 3,
        "status": "AVAILABLE",
        "connect": true,
        "pair": true,
        "direction": "X",
        "coordinate": "10,2,0",
        "storage_no": null,
        "bin_on_hold": false,
        "battery": 79,
        "mode": 0
    },
    {
        "skycar_id": 6,
        "status": "AVAILABLE",
        "connect": true,
        "pair": true,
        "direction": "Y",
        "coordinate": "13,2,0",
        "storage_no": null,
        "bin_on_hold": false,
        "battery": 74,
        "mode": 0
    },
    {
        "skycar_id": 5,
        "status": "AVAILABLE",
        "connect": true,
        "pair": true,
        "direction": "Y",
        "coordinate": "12,2,0",
        "storage_no": null,
        "bin_on_hold": false,
        "battery": 43,
        "mode": 0
    },
    {
        "skycar_id": 10,
        "status": "AVAILABLE",
        "connect": true,
        "pair": true,
        "direction": "Y",
        "coordinate": "18,2,0",
        "storage_no": null,
        "bin_on_hold": false,
        "battery": 33,
        "mode": 0
    }
]}