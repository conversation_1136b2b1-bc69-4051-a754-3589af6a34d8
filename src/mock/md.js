export function mock_md_station() {
  return [
    {
      maintenanceDockId: 2,
      nodeId: 7108,
      entranceId: 7109,
      type: "OUT",
      is_paired: true,
      obstacle_two_d: "10,2",
      is_obstacle: true,
    },
    {
      maintenanceDockId: 1,
      nodeId: 7089,
      entranceId: 7090,
      type: "IN",
      is_paired: true,
      obstacle_two_d: null,
      is_obstacle: false,
    },
    {
      maintenanceDockId: 3,
      nodeId: 7108,
      entranceId: 7109,
      type: "OUT",
      is_paired: true,
      obstacle_two_d: "10,3",
      is_obstacle: true,
    },
    {
      maintenanceDockId: 4,
      nodeId: 7089,
      entranceId: 7090,
      type: "MIX",
      is_paired: true,
      obstacle_two_d: "10,4",
      is_obstacle: true,
    },
    {
      maintenanceDockId: 5,
      nodeId: 7108,
      entranceId: 7109,
      type: "OUT",
      is_paired: true,
      obstacle_two_d: "10,5",
      is_obstacle: true,
    },
    {
      maintenanceDockId: 6,
      nodeId: 7089,
      entranceId: 7090,
      type: "IN",
      is_paired: true,
      obstacle_two_d: null,
      is_obstacle: false,
    },
  ];
}
export function mock_md_job() {
  return [
    {
      job_id: 1,
      skycar_id: 1,
      status: "PROCESSING",
      type: "IN",
      md_station: 1,
      three_d_id: "17,4,0",
      state: "APPROVAL",
      request_by: "MD",
      created_at: "Wed Sep 22 2021 15:19:57 GMT+0800 (Malaysia Time)",
      stepper: [
        ["REQUEST", true],
        ["REQUEST ACCEPTED", true],
        ["CHARGED IN", false],
      ],
    },
    {
      job_id: 2,
      skycar_id: 2,
      status: "PROCESSING",
      type: "OUT",
      md_station: 2,
      three_d_id: "17,19,0",
      state: "REQUEST",
      request_by: "ERR",
      created_at: "Wed Sep 22 2021 15:19:57 GMT+0800 (Malaysia Time)",
      stepper: [
        ["REQUEST", true],
        ["REQUEST ACCEPTED", true],
        ["TRANSIT", true],
        ["ARRIVED", false],
        ["CHARGED OUT", false],
      ],
    },
  ];
}
