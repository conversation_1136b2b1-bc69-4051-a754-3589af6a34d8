export function mock_charging_job() {
  return [{
    "id": 1,
    "tc_order_id": 42,
    "station": 4,
    "skycar": 19,
    "status": "AVAILABLE",
    "type": "Swap"
  },
  {
    "id": 2,
    "tc_order_id": 50,
    "station": 6,
    "skycar": 3,
    "status": "COMPLETE",
    "type": "Charge"
  }]

}


export function mock_error_log() {
  return [{
    "job_id": 451,
    "skycar_id": 8,
    "station_id": 1,
    "error_code": 61,
    "error_name": "TOP_CHARGER_FORK_MOTOR_AXIS_ERROR",
    "error_desc": "Top charger fork motor axis error",
    "error_note": "Charging Station 1 encountered error: TOP_CHARGER_FORK_MOTOR_AXIS_ERROR at 08:55:51",
    "datetime": "2023-02-20T08:55:51.074906+00:00"
  },
  {
    "job_id": 441,
    "skycar_id": 10,
    "station_id": 2,
    "error_code": 95,
    "error_name": "BTM_CHARGER_FORK_MOTOR_RETRIEVE_BATTERY_TIMEOUT",
    "error_desc": "Bottom charger fork motor retrieve battery time-out",
    "error_note": "Charging Station 2 encountered error: BTM_CHARGER_FORK_MOTOR_RETRIEVE_BATTERY_TIMEOUT at 03:33:33",
    "datetime": "2023-02-18T03:33:34.014344+00:00"
  },
  {
    "job_id": 299,
    "skycar_id": 9,
    "station_id": 2,
    "error_code": 999,
    "error_name": "MANUAL_ERROR",
    "error_desc": "Manually triggered Error, Dashboard",
    "error_note": "Charging Job 299 for SC9 and CS2 manually error-triggered by dashboard at 01:12:52",
    "datetime": "2023-01-27T01:12:52.830738+00:00"
  },
  {
    "job_id": 296,
    "skycar_id": 9,
    "station_id": 3,
    "error_code": 1,
    "error_name": "E_STOP",
    "error_desc": "E-Stop button has been pressed",
    "error_note": "Charging Station 3 encountered error: E_STOP at 06:39:42",
    "datetime": "2023-01-26T06:39:42.775962+00:00"
  },
  {
    "job_id": 269,
    "skycar_id": 6,
    "station_id": 2,
    "error_code": 61,
    "error_name": "TOP_CHARGER_FORK_MOTOR_AXIS_ERROR",
    "error_desc": "Top charger fork motor axis error",
    "error_note": "Charging Station 2 encountered error: TOP_CHARGER_FORK_MOTOR_AXIS_ERROR at 00:54:08",
    "datetime": "2023-01-20T00:54:08.067666+00:00"
  },
  {
    "job_id": 256,
    "skycar_id": 4,
    "station_id": 2,
    "error_code": 999,
    "error_name": "MANUAL_ERROR",
    "error_desc": "Manually triggered Error, Dashboard",
    "error_note": "Charging Job 256 for SC4 and CS2 manually error-triggered by dashboard at 04:10:51",
    "datetime": "2023-01-19T04:10:51.589958+00:00"
  },
  {
    "job_id": 257,
    "skycar_id": 9,
    "station_id": 1,
    "error_code": 97,
    "error_name": "BTM_CHARGER_FORK_MOTOR_OVER_TEMPERATURE",
    "error_desc": "Bottom charger fork motor over-temperature",
    "error_note": "Charging Station 1 encountered error: BTM_CHARGER_FORK_MOTOR_OVER_TEMPERATURE at 04:02:27",
    "datetime": "2023-01-19T04:02:27.957998+00:00"
  },
  {
    "job_id": 132,
    "skycar_id": 10,
    "station_id": 2,
    "error_code": 999,
    "error_name": "MANUAL_ERROR",
    "error_desc": "Manually triggered Error, Dashboard",
    "error_note": "Charging Job 132 for SC10 and CS2 manually error-triggered by dashboard at 02:01:13",
    "datetime": "2022-12-22T02:01:13.203295+00:00"
  },
  {
    "job_id": 132,
    "skycar_id": 10,
    "station_id": 2,
    "error_code": 999,
    "error_name": "MANUAL_ERROR",
    "error_desc": "Manually triggered Error, Dashboard",
    "error_note": "Charging Job 132 for SC10 and CS2 manually error-triggered by dashboard at 01:58:20",
    "datetime": "2022-12-22T01:58:20.472756+00:00"
  },
  {
    "job_id": 132,
    "skycar_id": 10,
    "station_id": 2,
    "error_code": 95,
    "error_name": "BTM_CHARGER_FORK_MOTOR_RETRIEVE_BATTERY_TIMEOUT",
    "error_desc": "Bottom charger fork motor retrieve battery time-out",
    "error_note": "Charging Station 2 encountered error: BTM_CHARGER_FORK_MOTOR_RETRIEVE_BATTERY_TIMEOUT at 01:53:08",
    "datetime": "2022-12-22T01:53:08.635083+00:00"
  },
  {
    "job_id": 132,
    "skycar_id": 10,
    "station_id": 2,
    "error_code": 95,
    "error_name": "BTM_CHARGER_FORK_MOTOR_RETRIEVE_BATTERY_TIMEOUT",
    "error_desc": "Bottom charger fork motor retrieve battery time-out",
    "error_note": "Charging Station 2 encountered error: BTM_CHARGER_FORK_MOTOR_RETRIEVE_BATTERY_TIMEOUT at 01:48:25",
    "datetime": "2022-12-22T01:48:25.469079+00:00"
  },
  {
    "job_id": 130,
    "skycar_id": 6,
    "station_id": 2,
    "error_code": 95,
    "error_name": "BTM_CHARGER_FORK_MOTOR_RETRIEVE_BATTERY_TIMEOUT",
    "error_desc": "Bottom charger fork motor retrieve battery time-out",
    "error_note": "Charging Station 2 encountered error: BTM_CHARGER_FORK_MOTOR_RETRIEVE_BATTERY_TIMEOUT at 10:16:51",
    "datetime": "2022-12-21T10:16:51.491385+00:00"
  },
  {
    "job_id": 130,
    "skycar_id": 6,
    "station_id": 2,
    "error_code": 95,
    "error_name": "BTM_CHARGER_FORK_MOTOR_RETRIEVE_BATTERY_TIMEOUT",
    "error_desc": "Bottom charger fork motor retrieve battery time-out",
    "error_note": "Charging Station 2 encountered error: BTM_CHARGER_FORK_MOTOR_RETRIEVE_BATTERY_TIMEOUT at 10:09:12",
    "datetime": "2022-12-21T10:09:12.857122+00:00"
  },
  {
    "job_id": 130,
    "skycar_id": 6,
    "station_id": 2,
    "error_code": 999,
    "error_name": "MANUAL_ERROR",
    "error_desc": "Manually triggered Error, Dashboard",
    "error_note": "Charging Job 130 for SC6 and CS2 manually error-triggered by dashboard at 10:06:38",
    "datetime": "2022-12-21T10:06:38.845992+00:00"
  },
  {
    "job_id": null,
    "skycar_id": null,
    "station_id": 2,
    "error_code": 185,
    "error_name": "BTM_ROTARY_MOTOR_LOCK_POSITION_TIMEOUT",
    "error_desc": "Bottom rotary motor lock position time-out",
    "error_note": "Charging Station 2 encountered error: BTM_ROTARY_MOTOR_LOCK_POSITION_TIMEOUT at 07:44:44",
    "datetime": "2022-12-14T07:44:44.765333+00:00"
  },
  {
    "job_id": null,
    "skycar_id": null,
    "station_id": 2,
    "error_code": 1,
    "error_name": "E_STOP",
    "error_desc": "E-Stop button has been pressed",
    "error_note": "Charging Station 2 encountered error: E_STOP at 07:09:49",
    "datetime": "2022-12-14T07:09:49.502699+00:00"
  },
  {
    "job_id": null,
    "skycar_id": null,
    "station_id": 2,
    "error_code": 1,
    "error_name": "E_STOP",
    "error_desc": "E-Stop button has been pressed",
    "error_note": "Charging Station 2 encountered error: E_STOP at 02:52:04",
    "datetime": "2022-12-14T02:52:04.229779+00:00"
  },
  {
    "job_id": 105,
    "skycar_id": 8,
    "station_id": 1,
    "error_code": 1,
    "error_name": "E_STOP",
    "error_desc": "E-Stop button has been pressed",
    "error_note": "Charging Station 1 encountered error: E_STOP at 07:42:27",
    "datetime": "2022-12-13T07:42:27.819909+00:00"
  },
  {
    "job_id": 105,
    "skycar_id": 8,
    "station_id": 1,
    "error_code": 1,
    "error_name": "E_STOP",
    "error_desc": "E-Stop button has been pressed",
    "error_note": "Charging Station 1 encountered error: E_STOP at 07:38:34",
    "datetime": "2022-12-13T07:38:34.301646+00:00"
  },
  {
    "job_id": 105,
    "skycar_id": 8,
    "station_id": 1,
    "error_code": 3,
    "error_name": "UNKNOWN",
    "error_desc": "Unknown Error",
    "error_note": "Charging Station 1 encountered error: UNKNOWN at 07:33:48",
    "datetime": "2022-12-13T07:33:48.636208+00:00"
  },
  {
    "job_id": 105,
    "skycar_id": 8,
    "station_id": 1,
    "error_code": 3,
    "error_name": "UNKNOWN",
    "error_desc": "Unknown Error",
    "error_note": "Charging Station 1 encountered error: UNKNOWN at 07:22:45",
    "datetime": "2022-12-13T07:22:45.574621+00:00"
  },
  {
    "job_id": 105,
    "skycar_id": 8,
    "station_id": 1,
    "error_code": 1,
    "error_name": "E_STOP",
    "error_desc": "E-Stop button has been pressed",
    "error_note": "Charging Station 1 encountered error: E_STOP at 07:09:57",
    "datetime": "2022-12-13T07:09:57.370020+00:00"
  },
  {
    "job_id": null,
    "skycar_id": null,
    "station_id": 1,
    "error_code": 185,
    "error_name": "BTM_ROTARY_MOTOR_LOCK_POSITION_TIMEOUT",
    "error_desc": "Bottom rotary motor lock position time-out",
    "error_note": "Charging Station 1 encountered error: BTM_ROTARY_MOTOR_LOCK_POSITION_TIMEOUT at 04:44:40",
    "datetime": "2022-12-13T04:44:40.756056+00:00"
  },
  {
    "job_id": null,
    "skycar_id": null,
    "station_id": 1,
    "error_code": 1,
    "error_name": "E_STOP",
    "error_desc": "E-Stop button has been pressed",
    "error_note": "Charging Station 1 encountered error: E_STOP at 03:49:02",
    "datetime": "2022-12-13T03:49:02.648935+00:00"
  },
  {
    "job_id": null,
    "skycar_id": null,
    "station_id": 1,
    "error_code": 3,
    "error_name": "UNKNOWN",
    "error_desc": "Unknown Error",
    "error_note": "Charging Station 1 encountered error: UNKNOWN at 03:32:21",
    "datetime": "2022-12-13T03:32:21.564997+00:00"
  },
  {
    "job_id": 103,
    "skycar_id": 10,
    "station_id": 3,
    "error_code": 1,
    "error_name": "E_STOP",
    "error_desc": "E-Stop button has been pressed",
    "error_note": "Charging Station 3 encountered error: E_STOP at 10:16:13",
    "datetime": "2022-12-12T10:16:13.327845+00:00"
  },
  {
    "job_id": null,
    "skycar_id": null,
    "station_id": 1,
    "error_code": 3,
    "error_name": "UNKNOWN",
    "error_desc": "Unknown Error",
    "error_note": "Charging Station 1 encountered error: UNKNOWN at 07:55:29",
    "datetime": "2022-12-12T07:55:29.747680+00:00"
  },
  {
    "job_id": 90,
    "skycar_id": 2,
    "station_id": 2,
    "error_code": 185,
    "error_name": "BTM_ROTARY_MOTOR_LOCK_POSITION_TIMEOUT",
    "error_desc": "Bottom rotary motor lock position time-out",
    "error_note": "Charging Station 2 encountered error: BTM_ROTARY_MOTOR_LOCK_POSITION_TIMEOUT at 01:51:46",
    "datetime": "2022-12-08T01:51:46.601896+00:00"
  },
  {
    "job_id": 89,
    "skycar_id": 9,
    "station_id": 1,
    "error_code": 155,
    "error_name": "TOP_ROTARY_MOTOR_LOCK_POSITION_TIMEOUT",
    "error_desc": "Top rotary motor lock position time-out",
    "error_note": "Charging Station 1 encountered error: TOP_ROTARY_MOTOR_LOCK_POSITION_TIMEOUT at 01:37:50",
    "datetime": "2022-12-08T01:37:50.142948+00:00"
  },
  {
    "job_id": 89,
    "skycar_id": 9,
    "station_id": 1,
    "error_code": 155,
    "error_name": "TOP_ROTARY_MOTOR_LOCK_POSITION_TIMEOUT",
    "error_desc": "Top rotary motor lock position time-out",
    "error_note": "Charging Station 1 encountered error: TOP_ROTARY_MOTOR_LOCK_POSITION_TIMEOUT at 01:09:52",
    "datetime": "2022-12-08T01:09:52.686868+00:00"
  },
  {
    "job_id": 75,
    "skycar_id": 2,
    "station_id": 1,
    "error_code": 91,
    "error_name": "BTM_CHARGER_FORK_MOTOR_AXIS_ERROR",
    "error_desc": "Bottom charger fork motor axis error",
    "error_note": "Charging Station 1 encountered error: BTM_CHARGER_FORK_MOTOR_AXIS_ERROR at 04:29:58",
    "datetime": "2022-12-05T04:29:58.820437+00:00"
  },
  {
    "job_id": 61,
    "skycar_id": 10,
    "station_id": 3,
    "error_code": 155,
    "error_name": "TOP_ROTARY_MOTOR_LOCK_POSITION_TIMEOUT",
    "error_desc": "Top rotary motor lock position time-out",
    "error_note": "Charging Station 3 encountered error: TOP_ROTARY_MOTOR_LOCK_POSITION_TIMEOUT at 09:07:59",
    "datetime": "2022-12-01T09:07:59.770149+00:00"
  },
  {
    "job_id": 53,
    "skycar_id": 7,
    "station_id": 3,
    "error_code": 1,
    "error_name": "E_STOP",
    "error_desc": "E-Stop button has been pressed",
    "error_note": "Charging Station 3 encountered error: E_STOP at 10:16:27",
    "datetime": "2022-11-29T10:16:27.844580+00:00"
  },
  {
    "job_id": 53,
    "skycar_id": 7,
    "station_id": 3,
    "error_code": 97,
    "error_name": "BTM_CHARGER_FORK_MOTOR_OVER_TEMPERATURE",
    "error_desc": "Bottom charger fork motor over-temperature",
    "error_note": "Charging Station 3 encountered error: BTM_CHARGER_FORK_MOTOR_OVER_TEMPERATURE at 09:38:33",
    "datetime": "2022-11-29T09:38:33.647071+00:00"
  },
  {
    "job_id": null,
    "skycar_id": null,
    "station_id": 2,
    "error_code": 1,
    "error_name": "E_STOP",
    "error_desc": "E-Stop button has been pressed",
    "error_note": "Charging Station 2 encountered error: E_STOP at 07:44:19",
    "datetime": "2022-11-25T07:44:19.201808+00:00"
  },
  {
    "job_id": 45,
    "skycar_id": 5,
    "station_id": 1,
    "error_code": 1,
    "error_name": "E_STOP",
    "error_desc": "E-Stop button has been pressed",
    "error_note": "Charging Station 1 encountered error: E_STOP at 04:48:04",
    "datetime": "2022-11-25T04:48:04.382530+00:00"
  },
  {
    "job_id": 45,
    "skycar_id": 5,
    "station_id": 1,
    "error_code": 1,
    "error_name": "E_STOP",
    "error_desc": "E-Stop button has been pressed, Dashboard",
    "error_note": "Charging Job 45 for SC5 and CS1 manually error-triggered by dashboard at 04:48:04",
    "datetime": "2022-11-25T04:48:04.356672+00:00"
  },
  {
    "job_id": 40,
    "skycar_id": 9,
    "station_id": 1,
    "error_code": 67,
    "error_name": "TOP_CHARGER_FORK_MOTOR_OVER_TEMPERATURE",
    "error_desc": "Top charger fork motor over-temperature",
    "error_note": "Charging Station 1 encountered error: TOP_CHARGER_FORK_MOTOR_OVER_TEMPERATURE at 08:01:58",
    "datetime": "2022-11-24T08:01:58.889085+00:00"
  },
  {
    "job_id": 40,
    "skycar_id": 9,
    "station_id": 1,
    "error_code": 67,
    "error_name": "TOP_CHARGER_FORK_MOTOR_OVER_TEMPERATURE",
    "error_desc": "Top charger fork motor over-temperature, Dashboard",
    "error_note": "Charging Job 40 for SC9 and CS1 manually error-triggered by dashboard at 08:01:58",
    "datetime": "2022-11-24T08:01:58.872493+00:00"
  },
  {
    "job_id": 39,
    "skycar_id": 9,
    "station_id": 1,
    "error_code": 1,
    "error_name": "E_STOP",
    "error_desc": "E-Stop button has been pressed",
    "error_note": "Charging Station 1 encountered error: E_STOP at 07:52:25",
    "datetime": "2022-11-24T07:52:25.546401+00:00"
  },
  {
    "job_id": 39,
    "skycar_id": 9,
    "station_id": 1,
    "error_code": 1,
    "error_name": "E_STOP",
    "error_desc": "E-Stop button has been pressed, Dashboard",
    "error_note": "Charging Job 39 for SC9 and CS1 manually error-triggered by dashboard at 07:52:25",
    "datetime": "2022-11-24T07:52:25.529085+00:00"
  },
  {
    "job_id": 29,
    "skycar_id": 4,
    "station_id": 3,
    "error_code": 999,
    "error_name": "MANUAL_ERROR",
    "error_desc": "Manually triggered Error, Dashboard",
    "error_note": "Charging Job 29 for SC4 and CS3 manually error-triggered by dashboard at 08:33:18",
    "datetime": "2022-11-23T08:33:18.575897+00:00"
  },
  {
    "job_id": null,
    "skycar_id": null,
    "station_id": 1,
    "error_code": 186,
    "error_name": "BTM_ROTARY_MOTOR_UNLOCK_POSITION_TIMEOUT",
    "error_desc": "Bottom rotary motor unlock position time-out",
    "error_note": "Charging Station 1 encountered error: BTM_ROTARY_MOTOR_UNLOCK_POSITION_TIMEOUT at 10:25:57",
    "datetime": "2022-11-22T10:25:57.966908+00:00"
  },
  {
    "job_id": null,
    "skycar_id": null,
    "station_id": 1,
    "error_code": 1,
    "error_name": "E_STOP",
    "error_desc": "E-Stop button has been pressed",
    "error_note": "Charging Station 1 encountered error: E_STOP at 09:53:48",
    "datetime": "2022-11-22T09:53:48.459769+00:00"
  },
  {
    "job_id": 26,
    "skycar_id": 1,
    "station_id": 2,
    "error_code": 2,
    "error_name": "UNKNOWN",
    "error_desc": "Unknown Error",
    "error_note": "Skycar 1 encountered error: UNKNOWN at 01:29:23",
    "datetime": "2022-11-22T01:29:23.071767+00:00"
  },
  {
    "job_id": 26,
    "skycar_id": 1,
    "station_id": 2,
    "error_code": 2,
    "error_name": "UNKNOWN",
    "error_desc": "Unknown Error, Dashboard",
    "error_note": "Charging Job 26 for SC1 and CS2 manually error-triggered by dashboard at 01:29:23",
    "datetime": "2022-11-22T01:29:23.057743+00:00"
  },
  {
    "job_id": 6,
    "skycar_id": 6,
    "station_id": 1,
    "error_code": 186,
    "error_name": "BTM_ROTARY_MOTOR_UNLOCK_POSITION_TIMEOUT",
    "error_desc": "Bottom rotary motor unlock position time-out",
    "error_note": "Charging Station 1 encountered error: BTM_ROTARY_MOTOR_UNLOCK_POSITION_TIMEOUT at 03:01:05",
    "datetime": "2022-11-16T03:01:05.253973+00:00"
  },
  {
    "job_id": 6,
    "skycar_id": 6,
    "station_id": 1,
    "error_code": 186,
    "error_name": "BTM_ROTARY_MOTOR_UNLOCK_POSITION_TIMEOUT",
    "error_desc": "Bottom rotary motor unlock position time-out, Dashboard",
    "error_note": "Charging Job 6 for SC6 and CS1 manually error-triggered by dashboard at 03:01:05",
    "datetime": "2022-11-16T03:01:05.241603+00:00"
  },
  {
    "job_id": 5,
    "skycar_id": 10,
    "station_id": 3,
    "error_code": 91,
    "error_name": "BTM_CHARGER_FORK_MOTOR_AXIS_ERROR",
    "error_desc": "Bottom charger fork motor axis error",
    "error_note": "Charging Station 3 encountered error: BTM_CHARGER_FORK_MOTOR_AXIS_ERROR at 02:44:45",
    "datetime": "2022-11-16T02:44:45.779862+00:00"
  },
  {
    "job_id": 5,
    "skycar_id": 10,
    "station_id": 3,
    "error_code": 91,
    "error_name": "BTM_CHARGER_FORK_MOTOR_AXIS_ERROR",
    "error_desc": "Bottom charger fork motor axis error, Dashboard",
    "error_note": "Charging Job 5 for SC10 and CS3 manually error-triggered by dashboard at 02:44:45",
    "datetime": "2022-11-16T02:44:45.765959+00:00"
  }]

}