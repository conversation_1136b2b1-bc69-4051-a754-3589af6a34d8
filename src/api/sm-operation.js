import { axiosInstance } from "./base";

export class SmOperationAPI {
  static domain = "/operations";

  static callBin(dto, headers) {
    return axiosInstance.post(`${this.domain}/call`, dto, { headers });
  }

  static storeBin(dto) {
    return axiosInstance.post(`${this.domain}/store`, dto);
  }

  static transferBin(dto) {
    return axiosInstance.post(`${this.domain}/transfer`, dto)
  }

  static disenrollBin(dto) {
    return axiosInstance.post(`${this.domain}/disenroll`, dto)
  }

  static getStackStats(zoneGroupName) {
    return axiosInstance.get(`${this.domain}/stack-stats/${zoneGroupName}`)
  }

  static getNonExec() {
    return axiosInstance.get(`${this.domain}/non-exec-orders`)
  }

  static triggerDispatcher(zoneGroupName) {
    return axiosInstance.post(`${this.domain}/dispatch`, {
      zoneGroupName,
    })
  }

  static triggerStorageOptimizer(zg) {
    return axiosInstance.post(`${this.domain}/optimize`, {
      zoneGroup: zg
    })
  }

  static getStorageMovements(code) {
    return axiosInstance.get(`${this.domain}/storage-movements`, { params: { code } })
  }
}
