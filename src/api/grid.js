const httpRequest = require("../helper/http_request.js");
import { getHost, getCurrentDateTime } from "../helper/common";
import { routeMatrix, RouteSkycar, SkycarStatus } from "../helper/enums";

// export async function getGrid(url, route) {
//   const response = await httpRequest.axiosRequest("GET", url, route);
//   if (response != null) {
//     return response.data.data;
//   }
// }

// export async function getSkycar(url, route) {
//   const response = await httpRequest.axiosRequest("GET", url, route);
//   if (response != null) {
//     return this.transformSkycarData(response.data.data);
//   }
// }

// export async function getObstacle(url, route) {
//   const response = await httpRequest.axiosRequest("GET", url, route);
//   if (response != null) {
//     return response.data.data;
//   }
// }
export async function initializeGrid(currentZone) {
  const url = getHost(currentZone);
  let gridQRResponse = { data: { model: { coordinate_with_qr: [] } } };

  try {
    const response = await httpRequest.axiosRequest(
      "GET",
      url,
      routeMatrix.gridQr
    );
    if (response.status < 400) {
      gridQRResponse = response;
    } else {
      console.warn(
        `Grid QR API returned an error with status ${response.status}`
      );
    }
  } catch (error) {
    console.warn("Grid QR API request failed. Using default empty array.");
  }

  const [gridResponse, skycarResponse, obstacleResponse] = await Promise.all([
    httpRequest.axiosRequest("GET", url, routeMatrix.grid),
    httpRequest.axiosRequest("GET", url, RouteSkycar.GET_SKYCAR),
    httpRequest.axiosRequest("GET", url, routeMatrix.gridObstacle),
  ]);

  const responseMap = new Map([
    [gridResponse, "Grid API"],
    [skycarResponse, "Skycar API"],
    [obstacleResponse, "Obstacle API"],
    [gridQRResponse, "Grid QR API"],
  ]);

  if (gridResponse && skycarResponse && obstacleResponse) {
    for (const [response, apiName] of responseMap.entries()) {
      if (response.status >= 400) {
        console.error(
          `${apiName} returned a client error. ${response.data.message}`
        );
        return null;
      }
      if (response.status >= 500) {
        console.error(`${apiName} returned a server error.`);
        return null;
      }
    }
    const gridObject = gridResponse.data.data;
    const gridObstacle = obstacleResponse.data.data;
    const gridSkycar = skycarDataResponseNormalizeForGridComponent(
      skycarResponse.data.data,
      gridObstacle
    );
    const gridQr = gridQRResponse.data.model["coordinate_with_qr"];
    const lastUpdated = getCurrentDateTime();
    return {
      gridObject,
      gridSkycar,
      gridObstacle,
      gridQr,
      lastUpdated,
    };
  }
}

function skycarDataResponseNormalizeForGridComponent(skycarData, obstacleData) {
  return skycarData.reduce((result, item) => {
    const [x, y] = item.coordinate.split(",").slice(0, 2);
    if (item.status !== SkycarStatus.MAINTENANCE) {
      result[`${x},${y}`] = {
        sid: item.skycar_id,
        status: item.status,
        assignedStorage: item.assigned_storage_code,
      };
    } else {
      const obstacleObject = obstacleData[`${x},${y}`];
      if (obstacleObject && obstacleObject.sid) {
        if (obstacleObject.sid.includes(item.skycar_id)) {
          result[`${x},${y}`] = {
            sid: item.skycar_id,
            status: item.status,
            assignedStorage: item.assigned_storage_code,
          };
        }
      }
    }
    return result;
  }, {});
}
