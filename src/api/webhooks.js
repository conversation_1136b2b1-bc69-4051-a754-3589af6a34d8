import { webhookAxios } from "./base";

export class WebhooksAPI {

  static getMessages(params, token) {
    if(token) {
       return webhookAxios.get("/messages", { params, headers: { Authorization: `<PERSON><PERSON> ${token}` } });
    }else {
        return webhookAxios.get("/messages", { params });
    } 
  }

  static replaySpecificMessage(messageId, token) {
    if(token) {
        return webhookAxios.post(
            "/messages/replay", 
            { id: messageId }, 
            { headers: { Authorization: `Bearer ${token}` } }
        );
    } else {
        return webhookAxios.post("/messages/replay", { id: messageId })
    }
  }
}
