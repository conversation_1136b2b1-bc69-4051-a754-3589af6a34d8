import { gatewayAxios } from "./base";

export class GatewayAPI {
    static domain = "/operations";
    
    static disenrollBinThroughApiGateway(dto, token) {
        if(token) {
          return gatewayAxios.post(`${this.domain}/disenroll`, dto,
          { headers: { Authorization: `Bearer ${token}`, "Content-Type": "application/json" }});
        } else {
          return gatewayAxios.post(`${this.domain}/disenroll`, dto)
        }
      }
}
