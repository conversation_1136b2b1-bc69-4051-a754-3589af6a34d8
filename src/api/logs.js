import { axiosInstance } from "./base";

export class SmLogsAPI {
  static domain = "/logs";

  static getSmOrderLogs(params) {
    return axiosInstance.get(`${this.domain}/sm-orders`, { params });
  }

  static getStorageLogs(params) {
    return axiosInstance.get(`${this.domain}/storage`, { params })
  }

  static exportSmOrderLogsExcel(dto) {
    return axiosInstance.get(`${this.domain}/sm-orders/excel`, { params: dto, responseType: "blob" })
  }

  static exportStorageLogsExcel(dto) {
    return axiosInstance.get(`${this.domain}/storage/excel`, { params: dto, responseType: "blob" })
  }
}
