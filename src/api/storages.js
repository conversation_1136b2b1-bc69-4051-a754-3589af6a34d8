import { axiosInstance } from "./base";

export class SmStorageAPI {
  static domain = "/storages";

  static getStorage(id) {
    return axiosInstance.get(`${this.domain}/${id}`)
  }

  static getStorages(params) {
    return axiosInstance.get(this.domain, { params });
  }

  static createStorage(dto) {
    return axiosInstance.post(this.domain, dto);
  }

  static updateStorage(id, dto) {
    return axiosInstance.patch(`${this.domain}/${id}`, dto)
  }
}
