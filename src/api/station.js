import { axiosInstance } from "./base.js";

export class StationAPI {
  static domain = "/stations";

  static getAll() {
    return axiosInstance.get(this.domain, {
      params: {
        perPage: -1
      }
    })
      .then((response) => {
        return response.data.data
      })
  }

  static updateStation(stationId, dto) {
    return axiosInstance.patch(`${this.domain}/${stationId}`, dto)
  }
}