import axios from "axios"

export const getUrl = () => {
  return process.env.VUE_APP_SM_API_URL ?? "$VUE_APP_SM_API_URL"
}

const mainUrl = getUrl()
export const axiosInstance = axios.create({
  baseURL: `${mainUrl}/v3`,
  timeout: 6000000,
})


const webhookUrl = process.env.VUE_APP_WEBHOOK_API_URL ?? "http://localhost:3026"
export const webhookAxios = axios.create({
  baseURL: `${webhookUrl}/v1`,
  timeout: 6000000,
})

const gatewayUrl = process.env.VUE_APP_API_GATEWAY_URL ?? "http://localhost:3024"
export const gatewayAxios = axios.create({
  baseURL: `${gatewayUrl}/v1`,
  timeout: 6000000,
})