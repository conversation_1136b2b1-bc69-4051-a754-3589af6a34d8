import { axiosInstance } from "./base.js";

export class SettingAPI {
  static domain = "/settings"

  static getBy<PERSON>ey(key) {
    return axiosInstance.get(`${this.domain}/${key}`).then((response) => {
      return response.data.data.value;
    });
  }

  static updateAutoStore(action, stations, delay) {
    return axiosInstance.put(`${this.domain}/auto-store`, {
      action,
      stations,
      delay: parseInt(delay) ?? 0,
    });
  }

  static updatePickDropRules(setting) {
    return axiosInstance.put(`${this.domain}/pick-drop-rules`, setting);
  }

  static updateOrderDispatcher(setting) {
    return axiosInstance.put(`${this.domain}/order-dispatcher`, setting);
  }

  static updateStorageOptimizer(setting) {
    return axiosInstance.put(`${this.domain}/storage-optimizer`, setting);
  }

  static updateFindStoragesFactors(factors) {
    return axiosInstance.put(`${this.domain}/find-storages-factors`, { factors });
  }

  static updateFindDestinationFactors(setting) {
    return axiosInstance.put(`${this.domain}/find-destination-factors`, setting);
  }

  static updateOrderPrioritisation(setting) {
    return axiosInstance.put(`${this.domain}/order-prioritisation`, setting);
  }
}
