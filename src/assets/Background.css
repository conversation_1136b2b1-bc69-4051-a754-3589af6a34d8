.dark-cloud {
    /* Set rules to fill background */
    min-height: 100%;
    min-width: 1024px;
  
    /* Set up proportionate scaling */
    width: 100%;
    height: auto;
  
    /* Set up positioning */
    position: fixed;
    top: 0;
    left: 0;
  
    color: white;
    background-image: url('cloud.jpg');
    background-attachment: fixed;
    background-position: center;
    background-size: cover;
  }
  .transparent {
    background-color: transparent;
    border-color: transparent;
  }
  