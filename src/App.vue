<template>
  <v-app>
    <AppBar />
    <!-- <v-card height="5%" width="256"> -->
      
    <NavBar />      
    <!-- </v-card> -->
    <!-- <router-link to="/foo">Go to Foo</router-link>
                <router-link to="/bar">Go to Bar</router-link>
                <router-link to="/app">Go to Hello</router-link> -->
    <v-main>   
      <v-container fluid>     
        <!-- <transition-group name="fade"> -->
        <router-view>templete render here</router-view>
        <!-- </transition-group> -->
        <SnackbarNotification ref="snackbarNotification" />
      </v-container>
    </v-main>
    <!-- <Dashboard/> -->
  </v-app>
</template>

<script>
// import Dashboard from "./components/Dashboard";

import  AppBar  from "./components/shared/AppBar.vue"
import  NavBar  from "./components/shared/NavBar.vue"
import SnackbarNotification from "./components/shared/SnackbarNotification.vue"
import io from "socket.io-client"
import AWN from "awesome-notifications"
import { AwesomeEvent, Websocket, cookieKey, RouteVersioning, AwesomeMode, Role } from "./helper/enums"
import { convertStringToLocal, getDefaultHost } from "./helper/common"
import { getCookie } from "./main"
import { authorizeRole } from "./helper/authorize.js"
import "../src/assets/styles/common-animation-effect.css"

const httpRequest = require("./helper/http_request.js");

export var socket = io(process.env.VUE_APP_TC_BT, {
  withCredentials: false,
  extraHeaders: {
    "my-custom-header": "tc-dashboard"
  }
})

export function emitAuth(userId) {
  socket.emit(Websocket.AUTH, userId)
}

export default {
  name: "App",

  components: {
    NavBar,
    AppBar,
    SnackbarNotification
  },
  // When a Vue instance is created, it adds all the properties found in its data object to Vue’s reactivity system. 
  data: () => ({
    containerVersionSocket: null,
    versionCheckingInterval: null,
    refreshNotificationShowing: false,
  }),
  // computed properties are cached based on their reactive dependencies. A computed property will only re-evaluate when some of its reactive dependencies have changed. 
  computed:{
    catchContainerVersionMessage() {
      return this.containerVersionSocket
    },
  },

  //watch properties - When you have some data that needs to change based on some other data, it is tempting to overuse watch - especially if you are coming from an AngularJS background. However, it is often a better idea to use a computed property rather than an imperative watch callback.
  watch:{
    catchContainerVersionMessage(newValue){
      if (newValue.container_type !== "TC_DASHBOARD_VERSION"){
        return
      }

      const result = this.compareVersions(newValue.new_version, this.$store.state.versions.TC_DASHBOARD_VERSION);
      if (result === 1 && !this.refreshNotificationShowing){
        clearInterval(this.versionCheckingInterval);
        const msg = `A new version of the dashboard has been detected. 
        Please refresh the page to access the latest updates.`
        this.$refs.snackbarNotification.showNotification(null, msg, true)
        this.refreshNotificationShowing = true
      }
    }
  },

  created: function() {
    this.getMessage(socket)
    this.checkDashboardVersion()
    // this.getCMEvents(socket_cm)
  },

  methods: {
    compareVersions(newVersion, oldVersion) {
      const newV = newVersion.split(".").map(Number);
      const oldV = oldVersion.split(".").map(Number);

      for (let i = 0; i < Math.max(newV.length, oldV.length); i++) {
        const num1 = i < newV.length ? newV[i] : 0;
        const num2 = i < oldV.length ? oldV[i] : 0;

        if (num1 < num2) {
          return -1;
        } else if (num1 > num2) {
          return 1;
        }
      }
      return 0;
    },
    checkDashboardVersion(){
      this.versionCheckingInterval = setInterval(() => {
        if (this.$store.state.versions.update){
          this.getDashboardVersion();
        }
      }, 10 * 60 * 1000);
    },

    getDashboardVersion(){
      httpRequest.axiosRequest(
        "post", 
        getDefaultHost(), 
        RouteVersioning.VERSIONING, 
        { "TC_DASHBOARD_VERSION": this.$store.state.versions.TC_DASHBOARD_VERSION }
      )
    },

    // getCMEvents(socket) {
    //   let notifier = new AWN()

    //   socket.on('connect', function () {
    //     notifier.info("Connected to CM WebSocket")
    //     console.info("Connected to CM WebSocket")
    //   })
    // },

    getMessage(socket) {
      let notifier = new AWN()
      socket.on(Websocket.GENERAL, function(body) {
        let createdAt = convertStringToLocal(body.created_at)
        let msg = `${createdAt} ${body.item.message}`
        let config = {
          durations: { global: 5000 }
        }
        if (
          body.item.mode == AwesomeMode.MODE_0 
          && authorizeRole(Role.MAINT)
        ) {
          config.durations.global = 0
        }
        switch (body.item.event) {
          case AwesomeEvent.INFO:
            notifier.info(msg, config)
            break
          case AwesomeEvent.ALERT:
            notifier.alert(msg, config)
            break
          case AwesomeEvent.SUCCESS:
            config.durations.global = config.durations.global || 10000
            notifier.success(msg, config)
            break
          case AwesomeEvent.WARNING:
            notifier.warning(msg, config)
            break
        }
      })

      socket.on(Websocket.CONNECT, function() {
        notifier.success("Websocket is connected.")
      })

      socket.on(Websocket.DISCONNECT, function() {
        notifier.alert("Websocket is disconnected.")
      })

      socket.on(Websocket.AUTH, function() {
        let id = getCookie(cookieKey.USERID)
        if (id) {
          emitAuth(id)
        }
      })

      var here = this
      socket.on(Websocket.CONTAINER_SERVICES_VERSION, function(item) {
        here.containerVersionSocket = item.item
      })
    }
  }
};

</script>


<style lang="css">
@import '~vue-awesome-notifications/dist/styles/style.css';
</style>