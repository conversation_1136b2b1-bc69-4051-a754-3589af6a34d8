// Vue Infra / Common
import axios from "axios";
import Vue from "vue";
import VueApexCharts from "vue-apexcharts";
import VueAWN from "vue-awesome-notifications";
import JsonCSV from "vue-json-csv";
import JsonExcel from "vue-json-excel";
import VueRouter from "vue-router";
import Vuex from "vuex";
import App from "./App.vue";
import vuetify from "./plugins/vuetify";

// Vue View
// TC
import ChargingService from "./components/ChargingService.vue";
import MatrixInsight from "./components/MatrixInsight.vue";
import Obstacle from "./components/Obstacle.vue";
import Order from "./components/Order.vue";
import Skycar from "./components/Skycar.vue";
import SkycarLog from "./components/SkycarLog.vue";
// import SkycarOld from "./components/Skycar_OLD.vue";
import Station from "./components/Station.vue";
import ServiceDoor from "./components/ServiceDoor.vue";
import Visualisation from "./components/Visualisation.vue";
import Analysis from "./components/analysis/Analysis.vue";
import Operation from "./components/operation/Operation";
import PlanningParameters from "./components/planningSetting/PlanningParameters.vue";
import PMDashboard from "./components/pm/PreventiveMaintenanceDashboard";
import PMManage from "./components/pm/PreventiveMaintenanceManage";
import SkycarOTA from "./components/skycarOTA/SkycarOTA.vue";

// SM
import SmCallStoreBin from "./components/SmCallStoreBin.vue";
import SmOrder from "./components/SmOrder.vue";
import SmSettings from "./components/SmSettings.vue";
import SmStorageUpdate from "./components/SmStorageUpdate.vue";

import SmOrderLog from "./components/SmOrderLog.vue";
import SmStorage from "./components/SmStoragesView.vue";
import StackOverview from "./components/StackOverview.vue";
// import StationNode from "./components/StationNode.vue";
import StorageLog from "./components/StorageLog.vue";
import Webhook from "./components/Webhook.vue";

// Auth
import { emitAuth } from "./App.vue";
import ForgotPassword from "./components/Users/<USER>";
import Login from "./components/Users/<USER>/Login.vue";
import Register from "./components/Users/<USER>";
import ResetPassword from "./components/Users/<USER>";

// Sim
import Simulation from "./components/Simulation.vue";
import SimStationDetail from "./components/sim/SimulationStationDetail.vue";

// Module Functions
import { resetStateUser, setStateUser } from "./helper/authorize";
import {
  getAuthLogin,
  getAuthServerUrl,
  getClientID,
  getDefaultHost,
  getRequestHeader,
  useRefreshToken,
} from "./helper/common";
import {
  Route,
  RouteAuthServer,
  RouteIsolation,
  RouteVersioning,
  cookieKey,
} from "./helper/enums";

let httpRequest = require("./helper/http_request");

import { StationAPI } from "./api/station";
import { ZoneGroupAPI } from "./api/zone-group";

// import colors from 'vuetify/lib/util/colors'
Vue.config.productionTip = false;
Vue.use(vuetify);
Vue.use(VueRouter);
Vue.use(Vuex);
Vue.use(VueAWN);
Vue.use(VueApexCharts);
Vue.component("downloadCsv", JsonCSV);
Vue.component("downloadExcel", JsonExcel);
Vue.component("apexchart", VueApexCharts);

export const store = new Vuex.Store({
  state: {
    drawer: false,
    login: getAuthLogin(),
    versions: {
      TC_DASHBOARD_VERSION: "0.0.0",
      update: false,
    },
    user: {
      id: null,
      authValidated: false,
      userLog: false,
      username: "",
      accessToken: "a",
      refreshToken: "",
      role: null,
    }, // Initial value for login user
    cubeConfig: {
      mode: false,
      dual: false,
      update: false,
    },
    // login_user: null,
    zoneGroups: [],
    smStations: [], // sm stations entity
  },
  mutations: {
    updateDrawerState(state) {
      // Update negated value of current state.drawer
      return (state.drawer = !state.drawer);
    },
    updateZoneGroup(state, zoneGroups) {
      state.zoneGroups = zoneGroups;
    },
    updateSmStation(state, smStations) {
      state.smStations = smStations;
    },
  },
  actions: {
    getZoneGroups: async ({ commit, state }) => {
      if (state.zoneGroups.length > 0) {
        return state.zoneGroups;
      } else {
        return await ZoneGroupAPI.getAll()
          .then((data) => {
            commit("updateZoneGroup", data);
            return data;
          })
          .catch(() => {
            Vue.prototype.$awn.alert("Fail to get zone groups");
            return [];
          });
      }
    },
    getSmStations: async ({ commit, state }) => {
      if (state.smStations.length > 0) {
        return state.smStations;
      } else {
        return await StationAPI.getAll()
          .then((data) => {
            commit("updateSmStation", data);
            return data;
          })
          .catch(() => {
            Vue.prototype.$awn.alert("Fail to get sm stations");
            return [];
          });
      }
    },
  },
});

const dotenv = require("dotenv");
dotenv.config({ path: "./" });

const routes = [
  {
    path: "/charging",
    component: ChargingService,
    meta: { title: "Charging Service" },
  },
  { path: "/order", component: Order, meta: { title: "Order" } },
  { path: "/obstacle", component: Obstacle, meta: { title: "Obstacle" } },
  {
    path: "/planningparameters",
    component: PlanningParameters,
    meta: { title: "Planning Parameters" },
  },
  {
    path: "/preventive",
    component: PMDashboard,
    meta: { title: "Preventive Maintenance" },
  },
  {
    path: "/preventive/manage",
    component: PMManage,
    meta: { title: "PM Manage" },
  },
  {
    path: "/preventive/dashboard",
    component: PMDashboard,
    meta: { title: "PM Dashboard" },
  },
  { path: "/insight", component: MatrixInsight, meta: { title: "Insight" } },
  { path: "/skycarLog", component: SkycarLog, meta: { title: "Skycar Log" } },
  {
    path: "/operation",
    component: Operation,
    meta: { title: "Operation" },
  },

  { path: "/register", component: Register, meta: { title: "Register" } },
  { path: "/login", component: Login, meta: { title: "Login" } },
  {
    path: "/resetpassword",
    component: ResetPassword,
    meta: { title: "Password Reset" },
  },
  {
    path: "/forgotpassword",
    component: ForgotPassword,
    meta: { title: "Forgot Password" },
  },
  {
    path: "/servicedoor",
    component: ServiceDoor,
    meta: { title: "Service Door" },
  },
  {
    path: "/station",
    component: Station,
    meta: { title: "Station" },
  },
  { path: "/skycarOTA", component: SkycarOTA, meta: { title: "Skycar OTA" } },

  { path: "/skycar", component: Skycar, meta: { title: "Skycar" } },
  {
    path: "/stackoverview",
    component: StackOverview,
    meta: { title: "Stack Overview" },
  },
  {
    path: "/storage",
    component: SmStorage,
    meta: { title: "SM Storages" },
  },
  { path: "/storage" + "/:id", component: SmStorageUpdate },
  {
    path: "/settings",
    component: SmSettings,
    meta: { title: "SM Settings" },
  },
  {
    path: "/callStoreBin",
    component: SmCallStoreBin,
    meta: { title: "Call/Store Bins" },
  },
  {
    path: "/smorder",
    component: SmOrder,
    meta: { title: "SM Order" },
  },
  {
    path: Route.SmOrderLogs,
    component: SmOrderLog,
    meta: { title: "SM Order Log" },
  },
  {
    path: `${Route.SmOrderLogs}/:smOrderId`,
    component: SmOrderLog,
    meta: { title: "SM Order Log" },
  },
  {
    path: Route.StorageLogs,
    component: StorageLog,
    meta: { title: "Storage Log" },
  },
  {
    path: `${Route.StorageLogs}/:storageCode`,
    component: StorageLog,
    meta: { title: "Storage Log" },
  },
  {
    path: "/webhook",
    component: Webhook,
    meta: { title: "Webhooks" },
  },
  // {
  //   path: "/station-node",
  //   component: StationNode,
  //   meta: { title: "Station Node" },
  // },

  {
    path: "/simulation",
    component: Simulation,
    meta: { title: "Simulation" },
  },

  { path: "/simulation/:id", component: SimStationDetail },

  {
    path: "/visualisation",
    component: Visualisation,
    meta: { title: "Visualisation" },
  },
  {
    path: "/analysis",
    component: Analysis,
  },
];
// let options = {enabled: false}
const router = new VueRouter({
  routes,
});

router.beforeEach(async (to, from, next) => {
  if (to.meta.title) {
    document.title = `${process.env.VUE_APP_DOC_TITLE} ${to.meta.title}`;
  } else {
    document.title = process.env.VUE_APP_DOC_TITLE;
  }

  if (store.state.login) {
    await requestAuthorizeWithCookie(to, from, next);
  } else {
    console.log("Login feature disable or not defined env.");

    // Redirect user to default page
    if (to.path === "/") {
      next("/insight");
    }

    // Redirect user to default page it auth related page
    if (
      ["/login", "/register", "/forgotpassword", "/resetpassword"].includes(
        to.path
      )
    ) {
      next("/insight");
    }

    // Follow user page
    next();
  }

  // Fetch dashboard version
  if (!store.state.versions.update) {
    await fetchVersion();
  }

  //Fetch isolation
  if (store.state.user.userLog && !store.state.cubeConfig.update) {
    fetchIsolation();
  } else if (!store.state.login && !store.state.cubeConfig.update) {
    fetchIsolation();
    console.log("login feature disable");
  } else {
    console.log("skip isolation");
  }
}),
  new Vue({
    vuetify,
    router: router,
    store: store,
    render: (h) => h(App),
  }).$mount("#app");

async function getUserInfo(username) {
  return await httpRequest.axiosRequest(
    "post",
    getAuthServerUrl(),
    RouteAuthServer.userInfo,
    { username: username, client_id: getClientID() }
  );
}

async function requestAuthorizeWithCookie(to, from, next) {
  let username = getCookie(cookieKey.USERNAME);
  let defaultView = "/insight";
  let emptyView = "/";

  if (username && !store.state.user.authValidated) {
    try {
      console.log("Cookies exists and user is yet to validate");
      const response = await getUserInfo(username); //TODO use cached token in cookies for auth-server authentication.

      if (response.data !== null) {
        let userInfo = response.data.data;
        setStateUser(userInfo);

        /**
         * When user is logon
         * If user refresh at page other than "/" , follow user pages
         * If user login/refresh at "/" , redirect to default view */
        if (to.path === emptyView) {
          next(defaultView);
        }
        emitAuth(userInfo.id);
      }
    } catch (error) {
      console.log(error);
      resetStateUser();
      next("/login");
    }
  } else if (username && store.state.user.authValidated) {
    console.log("User cookies is validated, skip API request.");
  } else {
    /**
     * When user is not login
     * Only auth related pages allow to visit else redirect to login straight, to prevent user F5 at /order or other pages
     */
    if (
      !["/login", "/register", "/forgotpassword", "/resetpassword"].includes(
        to.path
      )
    ) {
      next("/login");
    }
  }

  /**
   * When user is login
   * When user try to navigate to /login via url manual typing , redirect it to default view.
   */
  if (store.state.user.userLog && to.path === "/login") {
    console.log("Redirect to default view from login");
    next(defaultView);
  }

  /**
   * When user is login
   * When user at base view "/" redirect it to default view.
   */
  if (store.state.user.userLog && to.path === emptyView) {
    console.log("Redirect to default view from empty");
    next(defaultView);
  }

  next();
}

export function getCookie(key) {
  const cookieName = `${process.env.VUE_APP_DOC_TITLE}-${key}`;
  const cookies = document.cookie.split("; ");
  for (let i = 0; i < cookies.length; i++) {
    const cookie = cookies[i].split("=");
    if (cookie[0] === cookieName) {
      // alert(cookie[1]);
      return cookie[1];
    }
  }
  return null; // Return null if the cookie is not found
}

async function fetchIsolation() {
  const url = getDefaultHost() + RouteIsolation.ISOLATION;

  axios
    .get(url, { headers: getRequestHeader() })
    .then(function(response) {
      store.state.cubeConfig = response.data.data;
      store.state.cubeConfig.update = true;
      console.log(store.state.cubeConfig);
    })
    .catch(function(error) {
      store.state.cubeConfig = {
        mode: false,
        dual: false,
      };
      // console.log(JSON.stringify(error));
      Vue.prototype.$awn.alert(
        "Network error, unable to fetch" + JSON.stringify(error.config.url)
      );
      if (error.response && error.response.status == 401) {
        return useRefreshToken(this, fetchIsolation);
      }
    });
}

async function fetchVersion() {
  const url = getDefaultHost() + RouteVersioning.VERSIONING;
  axios
    .get(url, { headers: getRequestHeader() })
    .then(function(response) {
      store.state.versions = response.data.data;
      store.state.versions.update = true;
    })
    .catch(function(error) {
      (store.state.versions = {
        TC_DASHBOARD_VERSION: "0.0.0",
        update: false,
      }),
        Vue.prototype.$awn.alert(
          "Network error, unable to fetch" + JSON.stringify(error.config.url)
        );
      if (error.response.status == 401) {
        return useRefreshToken(this, fetchVersion);
      }
    });
}
