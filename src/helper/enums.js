var versioning = "v2";

export const Status = {
  AVAILABLE: "AVAILABLE",
  ERROR: "ERROR"
};

export const SkycarJob = {
  PICK: "o",
  DROP: "c",
  FORWARD: "f",
  BACKWARD: "b"
};

export const JobStatus = {
  ...Status,
  FAILED_CONDITION: "FAILED_CONDITION",
  FAILED_CONDITION_WHILE_PROCESSING: "FAILED_CONDITION_WHILE_PROCESSING",
  PREPROCESSING: "PREPROCESSING",
  PROCESSING: "PROCESSING",
  COMPLETED: "COMPLETED",
  DELETED: "DELETED"
};

export const GridStatus = {
  SKYCAR: "SKYCAR",
  SKYCAR_DOCKED: "SKYCAR_DOCKED",
  PILLAR: "PILLAR",
  INACTIVE: "INACTIVE",
  MAINTENANCE: "MAINTENANCE",
  SERVICE_RAILWAY: "SERVICE_RAILWAY",
  SERVICE_DOCK: "SERVICE_DOCK",
  PARKING: "PARKING"
};

export const AdgStatus = {
  STAGED: "Staged",
  ENQUEUED: "Enqueued",
  COMPLETED: "Completed",
};

export const CycleStop = {
  PREENABLED: "PreEnabled",
  DISABLED: "Disabled",
  ENABLED: "Enabled"
};

export const AwesomeEvent = {
  ALERT: "alert",
  INFO: "info",
  SUCCESS: "success",
  WARNING: "warning"
};

export const Websocket = {
  GENERAL: "GENERAL",
  ERROR: "ERROR",
  MAINTENANCE: "MAINTENANCE",
  SKYCAR: "SKYCAR",
  CONNECT: "connect",
  DISCONNECT: "disconnect",
  SKYCAR_LOG: "SKYCAR_LOG",
  ENTER_SKYCAR_LOG: "ENTER_SKYCAR_LOG",
  LEAVE_SKYCAR_LOG: "LEAVE_SKYCAR_LOG",
  OPERATION: "OPERATION",
  STATION: "STATION",
  SKYCAR_OTA: "SKYCAR_OTA",
  SKYCAR_OTA_PROGRESS: "SKYCAR_OTA_PROGRESS",
  AUTH: "AUTH",
  SKYCAR_ADG: "SKYCAR_ADG",
  OBSTACLES: "OBSTACLES",
  VISUALISE_SKYCAR_STATUS: "VISUALISE_SKYCAR_STATUS",
  CONTAINER_SERVICES_VERSION: "CONTAINER_SERVICES_VERSION",
  SERVICE_DOOR: "SERVICE_DOOR",
  UPDATE_SKYCAR: "UPDATE_SKYCAR",
  UPDATE_MATRIX: "UPDATE_MATRIX",
  OTA_UPDATE: "OTA_UPDATE"
};

export const CMWebSocketEvents = {
  Pairing: "Pairing",
  Error: "Error",

  HealthCheckUpdate: "HealthCheckUpdate",
  JobUpdate: "JobUpdate",
  RequestUpdate: "RequestUpdate",
  StationsUpdate: "StationsUpdate",
  SkycarsUpdate: "SkycarsUpdate",
  LogTCP: "LogTCP",
  LogEvent: "LogEvent"
};

export const ActionLabel = {
  UNINSTALL: "UNINSTALL",
  INSTALL: "INSTALL",
  FAULTY: "FAULTY"
};

export const RoutePM = {
  CONTROLLER_BINDING_HARDWARE: "/parts/binding/hardware",
  CONTROLLER_BINDING_HARDWARE_HISTORY: "/parts/binding/hardware/history",
  CONTROLLER_BINDING: "/parts/binding",
  CONTROLLER_PARTS: "/parts"
};

export const HardwareType = {
  // Use by preventive maintenance module to differentiate the domain of parts installation.
  // Must synchronize with backend tc-bt's enum in parts table
  SKYCAR: "SKYCAR",
  WORKSTATION: "WORKSTATION",
  CHARGING_STATION: "CHARGING_STATION",
  INFRASTRUCTURE: "INFRASTRUCTURE"
};

export const PreventiveMaintenanceActionType = {
  UNINSTALL: "UNINSTALL",
  FAULTY: "FAULTY",
  OTHER: "OTHER"
};

export const MDJobState = {
  Approved: "Approved"
};

export const RouteSkycar = {
  SWAP_BIN: "/api/v2/skycar/swap-bin",
  SKYCAR: "/api/v2/skycar/enroll",
  GET_SKYCAR: "/api/v2/skycar",
  DISENROLL: "/api/v2/skycar/disenroll",
  PARKING: "/api/v2/skycar/parking",
  MANUAL_MODE: "/api/v2/skycar/manual-mode",
  ERROR_SKYCAR: "/api/v2/skycar/error",
  WINCH: "/api/v2/skycar/winch",
  GET_ADG: "/api/v2/skycar/adg-message",
  FLAG_PICKED: "/api/v2/skycar/flag-picked",
  FLAG_DROPPED: "/api/v2/skycar/flag-completed"
};

export const RouteAnalysis = {
  CUBE_DATA: "/analysis/cube_data",
  DUAL_WINCH_PERCENTAGE: "/analysis/dual_winch_percentage",
  JOB_RATE: "/job-rate",
  JOB_RATE_STATISTICS: "/job-rate/statistics",
  JOB_RATE_STATISTICS_SUMMARY: "/job-rate/statistics-summary",
  JOB_RATIO: "/job-duration/job-ratio",
  UPH: "/job-rate/uph",
  JOB_DURATION_STATISTICS: "/job-duration/statistics",
  JOB_DURATION_STATISTICS_SUMMARY: "/job-duration/statistics-summary"
};

export const RouteError = {
  ERROR_MSG: "/api/v2/error/error-message",
  MOCK_ERROR: "/api/v2/error/mock-error",
  QR_CODE: "/api/v2/error/qr-code",
  ERROR_CODE: "/api/v2/error/error-code",
  ERROR_CODE_UPLOAD: "/api/v2/error/error-code/upload"
};

export const RouteMD = {
  MD: "/wcs/maintenanceDock"
};

export const RouteIsolation = {
  ISOLATION: `/api/${versioning}/isolation`,
  CONFIG_JOB: `/api/${versioning}/isolation/job/mock`,
  CONFIG_STATION: `/api/${versioning}/isolation/config/station`,
  CONFIG: `/api/${versioning}/isolation/config`
};

export const RouteJob = {
  // UPDATE_JOB: `/api/${versioning}/isolation`
  UPDATE_JOB: "/job/runtime-job",
  DELETE_JOB: "/job/runtime-job",
  DASHBOARD_JOB: "/dashboard/skycar/job"
};

export const RouteRunTime = {
  UPDATE_STORAGE_CODE: "/runtime/job/storage"
};

export const RouteStorage = {
  CHECK_BIN: "/check_bin"
};

export const SkycarLog = {
  GET_LOG: "/skycar_log"
};

export const SkycarMessage = {
  GET_MESSAGE: "/skycar_message"
};

export const SkycarShellCommand = {
  MCU_COMMAND: "/skycar/mcu_command",
  SAVE_COMMAND: "/skycar/save_command"
};

export const RouteOperation = {
  CUBE: "/operation/cube",
  BY_PASS_CUBE_EMO: "/operation/cube/emo",
  BATTERY: "/operation/battery",
  HALT_CUBE: "/operation/halt-cube",
  SKYCAR_ACTION: "/operation/skycar-action",
  SKYCAR_ACTION_WITHOUT_SHELL: "/operation/skycar-action-without-shell",
  PARKING_PREFER_COORD: "/operation/parking-prefer-coord",
  HEALTH_CHECK: "/operation/healthcheck",
  TRAVEL: "/dashboard/travel",
  CYCLESTOP: "/operation/cyclestop",
  PKS_DATA: "/operation/pks",
  OTA: "/operation/ota"
};

export const SkycarError = {
  recoverError: "/api/v2/error/recover-error",
  addRemoveObs: "/wcs/obstacle"
};

export const SkycarRecovery = {
  REVIVE: "REVIVE",
  PARTIAL_REVIVE: "PARTIAL_REVIVE",
  MANUAL_CHARGE_OUT: "MANUAL_CHARGE_OUT",
  AUTO_CHARGE_OUT: "AUTO_CHARGE_OUT",
  INSPECT: "INSPECT",
  TRAVEL: "TRAVEL",
  CHARGING: "CHARGING"
};

export const SkycarStatus = {
  AVAILABLE: "AVAILABLE",
  DOCKED: "DOCKED",
  ERROR: "ERROR",
  MAINTENANCE: "MAINTENANCE"
};

export const StoragePosition = {
  BOTH: "BOTH",
  LEFT: "LEFT",
  RIGHT: "RIGHT"
};

export const StorageSuffix = {
  B: StoragePosition.BOTH,
  L: StoragePosition.LEFT,
  R: StoragePosition.RIGHT
};

export const SmSettingsKey = {
  FindStoragesFactors: "FindStoragesFactors",
  FindDestinationFactors: "FindDestinationFactors",
  OrderPrioritisation: "OrderPrioritisation",
  TcOrderThreshold: "TcOrderThreshold",
  PickDropRules: "PickDropRules",
  OrderExecutor: "OrderExecutor",
  OrderDispatcher: "OrderDispatcher",
  AutoStore: "AutoStore",
  StorageOptimizer: "StorageOptimizer",
  DualStationMap: "DualStationMap"
};

export const RouteAuthServer = {
  login: "/user/login",
  userInfo: "/user/info",
  forgetPassword: "/user/forgetpassword",
  register: "/user/create",
  resetPassword: "/user/resetpassword",
  resendVerification: "/user/verification_resend",
  authorizeToken: "/oauth2/token",
  staySignedIn: "/user/update/signedin"
}

export const RouteSkycarOTA = {
  File: "/skycar_ota/file",
  Flash: "/skycar_ota/flash",
  Update: "/ota"
};

export const RouteSkycarLog = {
  skycarLog: "/skycar_log"
}

export const BatteryOrientation = {
  SOUTH: "South",
  NORTH: "North"
};

export const Route = {
  Configuration: "/configuration",
  General: "/general",
  Layout: "/layout",
  Login: "/login",
  Misc: "/misc",
  Order: "/order",
  Overview: "/overview",
  Mocks: "/mocks",
  Storage: "/storage",
  Info: "/info",
  Initialization: "/initialization",
  SmOrderLogs: "/sm-order-log",
  StorageLogs: "/storage-log",
  StationNode: "/station-node"
};

export const SmNodeStatus = {
  Available: "AVAILABLE",
  Reserved: "RESERVED",
  Maintenance: "MAINTENANCE"
};

export const SmNodeType = {
  Storage: "STORAGE",
  Transit: "TRANSIT",
  Void: "VOID",
  GatewayOut: "GATEWAY_OUT",
  GatewayIn: "GATEWAY_IN",
  Gateway: "GATEWAY"
};

export const SmStorageLastMovement = {
  InCub: "IN_CUBE",
  InTransi: "IN_TRANSIT",
  AtGatewayOu: "AT_GATEWAY_OUT", // drop point
  AtBufferI: "AT_BUFFER_IN",
  AtStationWor: "AT_STATION_WORK",
  AtBufferOu: "AT_BUFFER_OUT",
  AtGatewayI: "AT_GATEWAY_IN", // pick point
  OutOfCube: "OUT_OF_CUBE"
};

export const StorageStatus = {
  Available: "AVAILABLE",
  Reserved: "RESERVED",
  Maintenance: "MAINTENANCE"
};

export const StorageLastMovement = {
  InCube: "IN_CUBE",
  InTransit: "IN_TRANSIT",
  AtGatewayOut: "AT_GATEWAY_OUT", // drop point
  AtBufferIn: "AT_BUFFER_IN",
  AtStationWork: "AT_STATION_WORK",
  AtBufferOut: "AT_BUFFER_OUT",
  AtGatewayIn: "AT_GATEWAY_IN", // pick point
  OutOfCube: "OUT_OF_CUBE"
};

export const SmOrderStatus = {
  Available: "AVAILABLE",
  Dispatched: "DISPATCHED",
  Picking: "PICKING",
  Picked: "PICKED",
  Completed: "COMPLETED",
  Error: "ERROR",
  Skipped: "SKIPPED"
};

export const SmOrderType = {
  Retrieving: "RETRIEVING",
  Putaway: "PUTAWAY",
  Internal: "INTERNAL",
};

export const SmOrderPriorityTier = {
  ErrorHandling: 1,
  HumanIntervention: 2,
  Normal: 3,
  Transfer: 4,
}

export const AutoStoreUpdateActions = {
  Enable: "ENABLE",
  Disable: "DISABLE"
};




// Auth 
export const Role = {
  ADMIN: "admin",
  SM: "sm",
  MCU: "mcu",
  PLC: "plc",
  MAINT: "maint",

};

export const routeCM = {
  CHECK_CHARGING_STATION: "/check/charging-stations",
  CHARGE_SKYCAR: "/charge/skycar",
  CREATE_REQUEST: "/create/charging-request",
  CHECK_CHARGING_SKYCARS: "/check/charging-skycars",
  UNDOCK_ALL_SKYCARS: "/undock/all/skycar",
  UNDOCK_SKYCAR: "/undock/skycar"
}


export const routeMatrix = {
  grid: "/matrix",
  gridObstacle: "/matrix/obstacle",
  gridQr: "/matrix/initialization/qr",
  ADGNODES: "/dashboard/adgnodes"
}

export const RouteVersioning = {
  VERSIONING: "/dashboard/versioning"
}


export const cookieKey = {
  USERNAME: "username",
  USERID: "userid"
}


export const RouteEventLog = {
  eventLog: "/event-log"
}


export const Module = {
  CUBE: "CUBE",
  SC: "SC",
  CS: "CS",
  WS: "WS",
  OBSTACLE: "OBSTACLE",
  STORAGE: "STORAGE",
  COORD: "COORD"
}

export const Event = {
  GENERAL: "GENERAL",
  STORAGE: "STORAGE",
  BATTERY: "BATTERY",
  OBSTACLE: "OBSTACLE"
}

export const Palette = [
  "#008FFB", "#00E396", "#FEB019", "#FF4560", "#775DD0",
  "#3F51B5", "#03A9F4", "#4CAF50", "#F9CE1D", "#FF9800",
  "#33B2DF", "#546E7A", "#D4526E", "#13D8AA", "#A5978B",
  "#4ECDC4", "#C7F464", "#81D4FA", "#546E7A", "#FD6A6A",
  "#2B908F", "#F9A3A4", "#90EE7E", "#FA4443", "#69D2E7",
  "#449DD1", "#F86624", "#EA3546", "#662E9B", "#C5D86D",
  "#D7263D", "#1B998B", "#2E294E", "#F46036", "#E2C044",
  "#662E9B", "#F86624", "#F9C80E", "#EA3546", "#43BCCD",
  "#5C4742", "#A5978B", "#8D5B4C", "#5A2A27", "#C4BBAF",
  "#A300D6", "#7D02EB", "#5653FE", "#2983FF", "#00B1F2",
]

export const PositionMapping = {
  "LEFT (A)": StoragePosition.LEFT,
  "RIGHT (B)": StoragePosition.RIGHT,
  "BOTH": StoragePosition.BOTH
}

export const AwesomeMode = {
  MODE_0: "0" // If role is AE, then duration=0
}

export const RouteStatus = {
  ALL: "/api/v2/connectivity-status/connectivity-status/all"
};

export const OTA_STATUS = {
  FAILED: "FAILED",
  COMPLETED: "COMPLETED",
  WARNING: "WARNING",
  UPDATING: "UPDATING",
  NO_DATA: "NO DATA"
}

export const OTA_TYPE = {
  MAIN: "MAIN",
  WINCH: "WINCH"
}