import { getCurrentDateTimeWithMilliseconds } from "../helper/common";

class EventBus {
  constructor() {
    this.events = {};
  }

  subscribe(eventName, callback) {
    if (!this.events[eventName]) {
      this.events[eventName] = [];
    }
    this.events[eventName].push(callback);
  }

  publish(eventName, data) {
    if (this.events[eventName]) {
      const currentDateTime = getCurrentDateTimeWithMilliseconds();
      console.log(`Event publish ${eventName} at ${currentDateTime}`);
      this.events[eventName].forEach((callback) => {
        callback(data);
      });
    }
  }
}

export function waitForEvent(eventName) {
  return new Promise((resolve ,reject) => {
    const currentDateTime = getCurrentDateTimeWithMilliseconds();
    console.log(`Event subscribe ${eventName} at ${currentDateTime}`);

    const timeout = setTimeout(() => {
      reject(new Error(`Timeout occurred for promise ${eventName}`)); 
    }, 5000);

    eventBus.subscribe(eventName, (eventData) => {
      clearTimeout(timeout);
      resolve(eventData);
    });
  });
}

export const eventType = {
  authValidated: "authValidated",
};

const eventBus = new EventBus();
export default eventBus;
