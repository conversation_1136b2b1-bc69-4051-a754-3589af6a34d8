module.exports = {
  getEnv,
  getStatus,
  getHost,
  getCMHost,
  getCube,
  getMapping,
  getGridStatus,
  getJobStatus,
  getHccUrl,
  getAuthServerUrl,
  getAuthLogin,
  getClientID,
  getClientSecret,
  getTimezoneOffset,
  convertStringToLocal,
  convertLocalToIsoFormat,
  convertTimestampToLocal,
  convertLocalToTimestamp,
  getCurrentDateTime,
  getBTUrl,
  getStorageNo,
  getDefaultHost,
  getNoOfStorages,
  isTCSubang,
  getAuthToken,
  getRefreshTokenBody,
  getRequestHeaderWithoutJson,
  getRequestHeader,
  useRefreshToken,
  getNHourAgo,
  getCurrentDateTimeWithMilliseconds,
};

var moment = require("moment-timezone");

const { v4: uuidv4 } = require("uuid");
const store = require("../main");
let httpRequest = require("./http_request");
const { RouteAuthServer } = require("./enums");
const { resetStateUser, setTokens } = require("./authorize");

function getStatus(bol) {
  if (bol == true) {
    return ["green", "✓"];
  } else {
    return ["red", "✗"];
  }
}

function getGridStatus(status) {
  switch (status) {
    case "SKYCAR":
      return {
        color: "#ff0000",
        desc: "Obstacle created by skycar error.",
        text: "Skycar Obstacle",
      };
    case "SKYCAR_DOCKED":
      return {
        color: "#8fe3f7",
        desc: "Obstacle created when a skycar is in the docking process.",
        text: "Skycar Docking",
      };
    case "PILLAR":
      return {
        color: "#08040a",
        desc:
          "Obstacle created by building structures, making coordinates inaccessible.",
        text: "Pillar",
      };
    case "INACTIVE":
      return {
        color: "#545352",
        desc: "Coordinates without grid structures, making them inaccessible",
        text: "Inactive",
      };
    case "MAINTENANCE":
      return {
        color: "#e5f257",
        desc:
          "Obstacle created during maintenance or operational activities.",
        text: "Operation",
      };
    case "SERVICE_RAILWAY":
      return {
        color: "#bdbabf",
        desc:
          "Skycar maintenance and service railway used for robotic maintenance and transport.",
        text: "Railway",
      };
    case "SERVICE_DOCK":
      return {
        color: "#cbbed1",
        desc: "Skycar maintenance and service dock entrance coordinate.",
        text: "Service Dock",
      };
    case "PARKING":
      return {
        color: "#00FF00",
        desc: "Skycar parking coordinate.",
        text: "Parking",
      };
    default:
      return {
        desc: "UNKNOWN",
        color: "#3D3635",
        text: "Something go wrong, contact TC team.",
      };
  }
}

function getJobStatus(status) {
  // Tips : Install vscode extension color highlights for visualize color

  switch (status) {
    case "AVAILABLE":
      return {
        desc: "AVAILABLE",
        color: "#92C7C7",
        alias: status[0],
        state: "Fresh job pending assignment to Skycar.",
        style: "background: linear-gradient(135deg, #92C7C7 , #92C7C7)",
      };

    case "FAILED_CONDITION":
      return {
        desc: "FAILED_CONDITION",
        color: "#F67280",
        alias: "F",
        state:
          "Job is available, but the required condition has not been satisfied.",
      };

    case "FAILED_CONDITION_WHILE_PROCESSING":
      return {
        desc: "FAILED_CONDITION_WHILE_PROCESSING",
        color: "##F67280",
        alias: "F",
        state:
          "Job is processing, but the required condition has changed from satisfied to not satisfied",
        style: "background: linear-gradient(135deg, #FFA500 , #F67280)",
      };

    case "PREPROCESSING":
      return {
        desc: "PREPROCESSING",
        color: "#F2BB66",
        alias: status[0],
        state: `Job has entered planning but has not yet been enqueued action pick to skycar, 
                        planning allow to reassign skycar to other job under this state.`,
      };

    case "PROCESSING":
      return {
        desc: "PROCESSING",
        color: "#FFA500",
        alias: status[0],
        state: `Job has been enqueued action pick to skycar, planning not allow 
                      reassign skycar to other job, except skycar provide error.`,
      };

    case "ERROR":
      return {
        desc: "ERROR",
        color: "#FD1C03",
        alias: status[0],
        state: `Job encountered an error due to hardware failure 
                      and requires handling, replanning, and deletion.`,
      };

    case "DELETED":
      return {
        desc: "DELETED",
        color: "#2E1A47",
        alias: status[0],
        state: "Job has been deleted and is obsolete.",
      };

    case "COMPLETED":
      return {
        desc: "COMPLETED",
        color: "#12AD2B",
        alias: status[0],
        state: "Job has been completed successfully.",
      };

    default:
      return {
        desc: "UNKNOWN",
        color: "#3D3635",
        alias: status[0],
        state: "Something go wrong, contact TC team.",
      };
  }
}

let cubes = JSON.parse(process.env.VUE_APP_CUBE);
let mapping;
if (process.env.VUE_APP_CUBE_MAPPING) {
  mapping = JSON.parse(process.env.VUE_APP_CUBE_MAPPING);
} else {
  mapping = {};
}

function getCube() {
  return Object.keys(cubes);
}

function getMapping(cube) {
  return mapping[cube] || cube;
}

function getHost(zone) {
  return cubes[zone];
}

function getDefaultHost() {
  return Object.values(cubes)[0];
}

function getCMHost() {
  return process.env.VUE_APP_CM;
}

function getHccUrl() {
  return process.env.VUE_APP_HWX;
}

function getBTUrl() {
  return process.env.VUE_APP_TC_BT;
}

function getEnv() {
  return process.env.NODE_ENV;
}

function getAuthServerUrl() {
  return process.env.VUE_APP_AUTH_SERVER;
}

function getClientID() {
  return process.env.VUE_APP_CLIENT_ID;
}

function getClientSecret() {
  return process.env.VUE_APP_CLIENT_SECRET;
}

function getAuthLogin() {
  const envValue = process.env.VUE_APP_LOGIN;
  if (envValue === undefined) {
    console.log("The environment variable VUE_APP_LOGIN is not defined");
    return false;
  }
  return envValue === "true" ? true : false;
}

function isTCSubang() {
  /**
   * To indicate is current deployment production is Subang
   *
   * @returns {Boolean} Return true if Subang based on process.env
   * @see {@link ../../.env.subang.subang} - Link to another module or resource.
   * @since 2.0.0
   * @deprecated After deploy SM v3 to subang.
   */
  return process.env.VUE_APP_DOC_TITLE === "TC Subang";
}

function getTimezoneOffset() {
  return moment.tz(process.env.VUE_APP_TIME_ZONE).utcOffset()
}

function convertStringToLocal(isoformat, withDate = false, withMilliseconds = false) {
  let local = new Date(isoformat);
  let argv = {
    timeZone: process.env.VUE_APP_TIME_ZONE,
    hour12: false,
  };
  if (withDate) {
    if (withMilliseconds) {
      return local.toLocaleString("en-Us", argv) + `.${local.getMilliseconds().toString().padStart(3, "0")}`
    } else {
      return local.toLocaleString("en-US", argv);
    }
  }
  {
    return local.toLocaleTimeString("en-US", argv);
  }
}

function convertLocalToIsoFormat(str, format) {
  let datetime = moment.tz(str, format, process.env.VUE_APP_TIME_ZONE);
  return datetime.utc().toISOString(true);
}

function convertTimestampToLocal(timestamp, withDate=false, withMs=false) {
  let local = new Date(timestamp * 1000);
  let argv = {
    timeZone: process.env.VUE_APP_TIME_ZONE,
    hourCycle: "h23",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  }
  if (withDate) {
    argv = {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      ...argv
    }
  }
  if (withMs) {
    argv = {
      fractionalSecondDigits: 3,
      ...argv
    }
  }
  return local.toLocaleString("en-US", argv);
}

function convertLocalToTimestamp(str, format) {
  let datetime = moment.tz(str, format, process.env.VUE_APP_TIME_ZONE);
  return datetime.utc().unix();
}

function getCurrentDateTime() {
  return new Date().toLocaleString("en-US", {
    timeZone: process.env.VUE_APP_TIME_ZONE,
    hourCycle: "h23",
  });
}

function getNHourAgo(hour) {
  let now = new Date();
  return new Date(now.getTime() - hour * 60 * 60 * 1000).toLocaleString(
    "en-US",
    {
      timeZone: process.env.VUE_APP_TIME_ZONE,
      hourCycle: "h23",
    }
  );
}

function getCurrentDateTimeWithMilliseconds() {
  /**
   * Retrieves the current date and time with milliseconds in the specified time zone.
   *
   * This function is utilized to indicate if the current deployment is in the Subang production environment.
   *
   * @returns {string} Returns the current date and time with milliseconds in the provided time zone.
   */
  const dt = new Date();
  const options = {
    timeZone: process.env.VUE_APP_TIME_ZONE,
    hour12: false,
  };
  const str = `${dt.toLocaleString("en-US", options)}.${dt.getMilliseconds()}`;
  return str;
}

function getStorageNo(winch) {
  return Object.entries(winch)
    .filter((item) => item[1].storage_no)
    .map(([position, winch]) => [`${winch.storage_no} (${position[0]})`]);
}

function getNoOfStorages(winch) {
  return Object.values(winch).filter((winch) => winch.storage_no).length;
}

function getAuthToken() {
  return "Token " + store.store.state.user.accessToken;
}

function getRefreshTokenBody() {
  const formData = new FormData();
  formData.append("grant_type", "refresh_token");
  formData.append("refresh_token", store.store.state.user.refreshToken);
  return formData;
}

function getRequestHeaderWithoutJson() {
  var headers = {
    Authorization: getAuthToken(),
    "X-Correlation-ID": uuidv4(),
    "X-Request-ID": uuidv4(),
    "X-Idempotency-ID": uuidv4()
  };

  if (store.store.state.versions.update) {
    headers["X-Client-Version"] = store.store.state.versions.TC_DASHBOARD_VERSION;
  }

  return headers;
}

function getRequestHeader() {
  return {
    "Content-Type": "application/json",
    ...getRequestHeaderWithoutJson(),
  };
}

async function useRefreshToken(vm, currentFunction, ...params) {
  try {
    const res = await httpRequest.axiosRequest(
      "post",
      getAuthServerUrl(),
      RouteAuthServer.authorizeToken,
      getRefreshTokenBody(),
      true
    );

    // If new access token is obtained successfully, update those tokens then reload the page
    if (res.status === 200) {
      await setTokens(res.data);
      // Now call the function again with the new tokens
      const refreshedData = await currentFunction.call(vm, ...params);
      return refreshedData;
    }
    throw Error(`Unable to retrieve new token due to ${res.status}.`)
  } catch (error) {
    console.log(error);
    resetStateUser();
    vm.$router.push({ path: "/login" });
  }
}
