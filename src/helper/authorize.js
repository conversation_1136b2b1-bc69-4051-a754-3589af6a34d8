import eventBus from "../helper/eventBus";
import { store } from "../main";
import { cookieKey } from "./enums";

async function setRole(userMetaData) {
  if (userMetaData.role) {
    store.state.user.role = userMetaData.role;
  }
}

async function deleteCookie(user) {
  const datetime = new Date();
  datetime.setTime(datetime.getTime() + -1 * 60 * 1000);
  let expires = "expires=" + datetime.toUTCString();
  document.cookie = `${process.env.VUE_APP_DOC_TITLE}-${cookieKey.USERNAME}=${user.username}; ${expires};`;
  document.cookie = `${process.env.VUE_APP_DOC_TITLE}-${cookieKey.USERID}=${user.id}; ${expires};`;
}
async function resetStateUser() {
  deleteCookie(store.state.user);
  store.state.user.id = null;
  store.state.user.role = null;
  store.state.user.userLog = false;
  store.state.user.username = "";
  store.state.user.accessToken = "a";
  store.state.user.refreshToken = "";
  store.state.user.authValidated = false;
  store.state.drawer = false;
}

async function setStateUser(data) {
  // setRole(response.data.data.user_metadata);
  store.state.user.id = data.id;
  store.state.user.role = data.user_metadata.role;
  store.state.user.username = data.username;
  store.state.user.accessToken = data.access_token;
  store.state.user.refreshToken = data.refresh_token;
  store.state.user.userLog = true;
  store.state.user.authValidated = true;
  eventBus.publish("authValidated", true);
}

async function setTokens(data) {
  store.state.user.accessToken = data.access_token;
  store.state.user.refreshToken = data.refresh_token;
}

async function getAccessToken() {
  return store.state.user.accessToken;
}

function authorizeRole(targetRole) {
  return store.state.user.role === targetRole;
}

function isloginDisabled() {
  console.log("Login feature disable or not defined env");
  return !store.state.login;
}

// function waitForAuthValidation() {
//   /* Helper function to check if authValidated is resolve.
//   Why : Routing initialization at NavBar.vue depend on user role to decide logic for render
//   simulation vue, NavBar initialized before auth authorization is complete.
//   */
//   return new Promise((resolve) => {
//     const checkValidation = () => {
//       if (store.state.user.authValidated) {
//         resolve();
//       } else {
//         setTimeout(checkValidation, 100);
//       }
//     };
//     checkValidation();
//   });
// }

export {
  authorizeRole,
  deleteCookie,
  getAccessToken,
  isloginDisabled,
  resetStateUser,
  setRole,
  setStateUser,
  setTokens
};

