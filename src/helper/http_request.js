const {
  getCurrentDateTime,
  getRequestHeader,
  getClient<PERSON>,
  getClientSecret,
  useRefreshToken,
} = require("./common");
const axios = require("axios");

module.exports = {
  postRequest,
  deleteRequest,
  AxiosHttpWithAwesomeAlert,
  testFunction2,
  axiosRequest,
  fetchRequest,
  mypost,
};

async function deleteRequest(arg, route, tcUrl) {
  var tcHost = new URL(tcUrl + route + "/" + arg);

  var requestOptions = {
    method: "DELETE",
    // body: JSON.stringify(body),
    headers: getRequestHeader(),
  };

  try {
    const response = await fetch(tcHost, requestOptions);

    const myJson = await response.json();
    if (myJson != null) {
      return myJson;
    }
    return null;
  } catch (error) {
    let msg = {
      reason:
        "Remote end point " +
        tcHost +
        " not accessible, please ensure TC backend is alive.",
      status: error + getCurrentDateTime(),
    };
    return JSON.stringify([msg, 400]);
  }
}

async function postRequest(body, route, tcUrl) {
  var tcHost = new URL(tcUrl + route);

  var requestOptions = {
    method: "POST",
    body: JSON.stringify(body),
    headers: getRequestHeader(),
  };

  try {
    const response = await fetch(tcHost, requestOptions);
    const myJson = await response.json();
    if (myJson != null) {
      return myJson;
    }
    return null;
  } catch (error) {
    let msg = {
      reason:
        "Remote end point " +
        tcHost +
        " not accessible, please ensure TC backend is alive.",
      status: error + getCurrentDateTime(),
    };
    return JSON.stringify([msg, 200]);
  }
}

async function AxiosHttpWithAwesomeAlert(awn, promise, displayModels = true) {
  /**
  Calls an HTTP request using Axios with an Awesome Alert notification.

  Supposed to support all HTTP Methods, in use methods GET , POST , DELETE 

  @param {Object} awn - An Awesome Alert notification object.
  @param {Promise} promise - A Promise that wraps the Axios HTTP request.
  @param {boolean} displayModels - Boolean to decide whether display the response data models in the success message, 
  normally GET request will not display the returned models in the notification as it will too long for a awesome alert message.
 
  @returns {Promise} - A Promise that resolves or rejects based on the outcome of the HTTP request.                
  */

  return new Promise((resolve, reject) => {
    awn.async(
      promise,
      (resp) => {
        awn.success(
          `${resp.data.message}` +
            (displayModels ? "\n" + JSON.stringify(resp.data.data) : "")
        );
        resolve(resp.data);
      },
      (res) => {
        awn.alert(
          `${res.response.data.message}` +
            "\n" +
            JSON.stringify(res.response.data.data)
        );
        reject(res);
      }
    );
  });
}

async function axiosRequest(method, url, route, body = null, header = false, query=null) {
  var host = new URL(url + route);

  if (query){
    host = `${host}?${query}`
  }
    
  var authServerLoginAuthorization = {
    Authorization: `Basic ${Buffer.from(
      `${getClientID()}:${getClientSecret()}`
    ).toString("base64")}`,
  };

  const headers =
    header === false ? getRequestHeader() : authServerLoginAuthorization;

  try {
    const response = await axios({
      method: method,
      url: host,
      data: body,
      headers: headers,
    });

    if (response != null) {
      // 200 range
      return response;
    }
  } catch (error) {
    if (error.response) {
      // 400-500 range
      console.error("Client Error:", JSON.stringify(error.response));
      return error.response;
    }

    // Request made, but no response received , error response is undefined
    console.error("Network Error:", JSON.stringify(error.config.url));
    return error.response;
  }
}

async function fetchRequest(method, body, url, route) {
  var host = new URL(url + route);

  var requestOptions = {
    method: method,
    body: JSON.stringify(body),
    headers: getRequestHeader(),
  };

  try {
    const response = await fetch(host, requestOptions);

    if (response != null) {
      return response;
    }
    return null;
  } catch (error) {
    let msg = {
      status: "Time of Error Occured: " + getCurrentDateTime(),
      status_code: error.response.status,
    };
    return JSON.stringify([msg, 200]);
  }
}

function testFunction2() {
  console.log("test");
}

async function mypost(fullRoute, body, method = "POST") {
  var host = fullRoute;
  var requestOptions = {
    method: method,
    body: JSON.stringify(body),
    headers: getRequestHeader(),
  };
  try {
    const response = await fetch(host, requestOptions);
    let res = JSON.parse(await response.text());
    if (res.code == 401) {
      return useRefreshToken(this, mypost, fullRoute, body, (method = "POST"));
    }
    return res;
  } catch (error) {
    let msg = {
      status: "Rejected",
      reason:
        "Remote end point " +
        host +
        " not accessible, please ensure there is valid Input selected.",
    };
    return msg;
  }
}
