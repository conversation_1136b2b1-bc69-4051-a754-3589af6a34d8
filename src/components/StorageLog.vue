<template>
  <v-container>
    <div class="dark-cloud" />
    <div class="stars" />
    <div class="stars2" />
    <div class="stars3" />

    <v-tabs dark :background-color="'transparent'" class="pa-4" v-model="tab">
      <v-tab href="#storageLogs">Storage Logs</v-tab>
    </v-tabs>

    <v-divider></v-divider>
    <v-tabs-items class="transparent" v-model="tab">
      <v-tab-item value="storageLogs">
        <v-card flat class="pa-4 transparent">
          <v-btn
            large
            @click="getStorageInfo(true, false)"
            :loading="refreshLoading"
            dark
            style="position: fixed; top: 10%; right: 3%; z-index: 999"
            >Refresh</v-btn
          >
          <v-data-table
            :headers="headers"
            :items="Object.values(storageLogs)"
            :items-per-page="maxPageSize"
            :item-class="rowColor"
            hide-default-footer
            dark
            dense
            class="transparent"
          >
            <template v-slot:top>
              <v-row class="flex justify-space-between">
                <v-switch
                  v-model="autoRefresh"
                  :label="`Auto Refresh (${autoRefreshIntervalInSec}s)`"
                  @click="toggleAutoRefresh()"
                ></v-switch>

                <v-btn 
                  class="ma-1"
                  @click="downloadExcel"
                >
                  <v-icon>mdi-download</v-icon> DOWNLOAD CSV
                </v-btn>
              </v-row>
              <v-expansion-panels class="transparent">
                <v-expansion-panel class="transparent">
                  <v-expansion-panel-header class="transparent">
                    Advanced Filter
                  </v-expansion-panel-header>
                  <v-expansion-panel-content class="transparent">
                    <v-row dense>
                      <v-col>
                        <v-combobox
                          v-model="storageCodeFilter"
                          label="Storage Code"
                          multiple
                          small-chips
                          :deletable-chips="clearable"
                          :clearable="clearable"
                          outlined
                          :readonly="storageCodeReadOnly"
                        />
                      </v-col>
                      <v-col>
                        <v-text-field
                          label="Created At - Start Time"
                          type="datetime-local"
                          v-model="storageLogCreatedAtStartTimeFilter"
                          step="2"
                          clearable
                          outlined
                        />
                      </v-col>
                      <v-col>
                        <v-text-field
                          label="Created At - End Time"
                          type="datetime-local"
                          v-model="storageLogCreatedAtEndTimeFilter"
                          step="2"
                          clearable
                          outlined
                        />
                      </v-col>
                    </v-row>
                  </v-expansion-panel-content>
                </v-expansion-panel>
              </v-expansion-panels>
              <v-spacer></v-spacer>
              <v-row>
                <v-col cols="4" sm="3">
                  <v-select
                    v-model="perPage"
                    :items="pageSizes"
                    label="Items per Page"
                    @change="handlePageSizeChange"
                  ></v-select>
                </v-col>
                <v-col cols="12" sm="9">
                  <v-pagination
                    v-model="currentPage"
                    :length="totalPages"
                    total-visible="7"
                    next-icon="mdi-menu-right"
                    prev-icon="mdi-menu-left"
                    @input="handlePageChange"
                    dark
                  ></v-pagination>
                </v-col>
              </v-row>
            </template>
            <template v-slot:items="{ item }">
              {{ item }}
            </template>
            <template v-slot:[`item.tagNames`]="{ value }">
              <v-tooltip right>
                <template v-slot:activator="{ on, attrs }">
                  <td v-bind="attrs" v-on="on">
                    {{ validateStringLength(value.tags) }}
                  </td>
                </template>
                <pre
                  v-for="(val, key) in tags.find((x) => x.key === value.key)
                    .tagNames"
                  :key="key"
                  >{{ `${key} : ${val}` }}</pre
                >
              </v-tooltip>
            </template>
          </v-data-table>
        </v-card>
      </v-tab-item>
    </v-tabs-items>
  </v-container>
</template>

<script>
import * as moment from "moment";
import { SmLogsAPI } from "../api/logs";
import { convertStringToLocal } from "../helper/common";


export default {
  name: "StorageLog",

  data() {
    return {
      storageCodeReadOnly: false,
      clearable: true,

      oldQuery: null,
      autoRefresh: false,
      autoRefreshTimer: null,
      autoRefreshIntervalInSec: 10,

      tab: null,
      refreshLoading: false,

      storageCodeFilter: [],
      totalPages: 3,
      currentPage: 1,
      perPage: 15,
      pageSizes: [15, 30, 50, 100],
      maxPageSize: 100,

      storageLogCreatedAtStartTimeFilter: null,
      storageLogCreatedAtEndTimeFilter: null,

      storageLogs: [],
      tags: [],
      hasError: false,
      isSuccess: false,
      errorMessage: null,
      headers: [
        {
          text: "LOG CREATE AT",
          value: "createdAt",
          groupable: false,
          sortable: false,
        },
        { text: "ID", value: "id", groupable: false },
        { text: "STORAGE", value: "storageCode", groupable: false },
        {
          text: "LAST MOVEMENT",
          value: "snapshot.lastMovement",
          groupable: false,
        },
        {
          text: "LAST MOVED AT",
          value: "snapshot.lastMovedAt",
          groupable: false,
        },
        {
          text: "STATUS",
          value: "snapshot.status",
          groupable: false,
        },
        {
          text: "COORDINATE",
          value: "snapshot.node.threeDim",
          groupable: false,
        },
        {
          text: "ZONE GROUP",
          value: "snapshot.node.zoneGroup",
          groupable: false,
        },
        { text: "STATION", value: "snapshot.station", groupable: false },
        { text: "TAGS", value: "tagNames", groupable: false },
        { text: "CREATED AT", value: "snapshot.createdAt", groupable: false },
        { text: "UPDATED AT", value: "snapshot.updatedAt", groupable: false },
        { text: "DELETED AT", value: "snapshot.deletedAt", groupable: false },
      ],

      storageLogData: {
        exportFields: {
          ID: "id",
          "LOG CREATED AT": "createdAt",
          STORAGE: "storageCode",
          STATUS: "snapshot.status",
          "LAST MOVED AT": "snapshot.lastMovedAt",
          "LAST MOVEMENT": "snapshot.lastMovement",
          STATION: "snapshot.station",
          "CREATED AT": "snapshot.createdAt",
          "UPDATED AT": "snapshot.updatedAt",
          "DELETED AT": "snapshot.deletedAt",
        },
        result: [],
      },
    };
  },
  beforeMount() {
    this.reloadComponent();
  },
  beforeDestroy() {
    clearInterval(this.autoRefreshTimer);
  },

  methods: {
    async reloadComponent() {
      const params = this.$route.params

      if(params.storageCode) {
        this.storageCodeFilter.push(this.$route.params.storageCode)
        this.storageCodeReadOnly = true
        this.clearable = false
      } else {
        this.storageCodeFilter = []
        this.storageCodeReadOnly = false
        this.clearable = true
      }

      await this.getStorageInfo();
    },
    async downloadExcel() {
      await SmLogsAPI.exportStorageLogsExcel({
        storages: this.storageCodeFilter,
        createdAtStart:
          this.storageLogCreatedAtStartTimeFilter ?? undefined,
        createdAtEnd: this.storageLogCreatedAtEndTimeFilter ?? undefined,
      }).then(response => {
        // Create a temporary link element to trigger the download
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement("a");
        link.href = url;

        const startDate = this.storageLogCreatedAtStartTimeFilter
            ? moment(this.storageLogCreatedAtStartTimeFilter).format("YYYYMMDD") + "_"
            : ""
        const endDate = this.storageLogCreatedAtEndTimeFilter
          ? moment(this.storageLogCreatedAtEndTimeFilter).format("YYYYMMDD") + "_"
          : ""
        const filename = `${startDate}${endDate}storageLogs.xlsx`
        
        link.setAttribute("download", filename); // Specify the file name
        document.body.appendChild(link);
        link.click();

        // Clean up
        window.URL.revokeObjectURL(url);
        link.remove();

      })
    },
    toggleAutoRefresh() {
      if (this.autoRefresh) {
        if (this.autoRefreshTimer) clearInterval(this.autoRefreshTimer);

        this.autoRefreshTimer = setInterval(() => {
          this.getStorageInfo(true, false);
        }, this.autoRefreshIntervalInSec * 1000);
      } else {
        clearInterval(this.autoRefreshTimer);
      }
    },

    handlePageChange(value) {
      this.currentPage = value;
      this.getStorageInfo();
    },

    handlePageSizeChange(value) {
      this.perPage = value;
      this.getStorageInfo();
    },

    dynamicDestructure(objData, propertiesArr) {
      if (!objData) return {};
      if (propertiesArr.length > 0) {
        return propertiesArr.reduce(
          (acc, cur) => ({ ...acc, [cur]: objData[cur] }),
          {}
        );
      }
      return objData;
    },

    validateStringLength(value) {
      if (!value) return;
      if (value.length > 10) {
        return value.slice(0, 10) + "...";
      } else {
        return value;
      }
    },

    async getStorageInfo(hardRefresh = false, reRunOldQuery = false) {
      try {
        if (hardRefresh) {
          this.currentPage = 1;
        }
        this.refreshLoading = true;
        let params = null;

        if (reRunOldQuery && this.oldQuery) {
          params = this.oldQuery;
        } else {
          params = {
            perPage: this.perPage,
            pageNo: this.currentPage,
            storages: this.storageCodeFilter,
            createdAtStart:
              this.storageLogCreatedAtStartTimeFilter ?? undefined,
            createdAtEnd: this.storageLogCreatedAtEndTimeFilter ?? undefined,
          };
        }
        await SmLogsAPI.getStorageLogs(params)
          .then((res) => {
            let result = res.data.data;

            if (!result) return null;
            this.totalPages = res.data.pagination.totalPages;
            this.totalItem = res.data.pagination.totalRecords;
            result.forEach((storage) => {
              storage.tagNames = {
                key: storage.storageCode,
                tags:
                  storage.snapshot.tags && storage.snapshot.tags.length > 0
                    ? storage.snapshot.tags.map((x) => x).join(", ")
                    : null,
              };

              this.tags.push({
                key: storage.storageCode,
                tagNames: storage.snapshot.tags,
              });
            });

            this.storageLogs = result.map(r => ({
              ...r,
              createdAt: convertStringToLocal(r.createdAt, true),
              snapshot: {
                ...r.snapshot,
                lastMovedAt: r.snapshot.lastMovedAt ? convertStringToLocal(r.snapshot.lastMovedAt, true) : undefined,
                createdAt: r.snapshot.createdAt ? convertStringToLocal(r.snapshot.createdAt, true) : undefined,
                updatedAt: r.snapshot.updatedAt ? convertStringToLocal(r.snapshot.updatedAt, true) : undefined,
                deletedAt: r.snapshot.deletedAt ? convertStringToLocal(r.snapshot.deletedAt, true) : undefined
              }
            }));

            
            this.storageLogData["result"] = result;
            this.oldQuery = params;
          })
          .catch((err) => {
            console.error(err)

            this.errorMessage = err.response.data.error;
            this.hasError = true;
          });
      } catch (e) {
        this.$awn.alert(`Storage Page Get Data Exception - ${e.message}`);
      }

      this.refreshLoading = false;
    },

    rowColor(item) {
      if (!item) return "grey--text text--darken-1";
      switch (item.snapshot.lastMovement) {
        case "IN_CUBE":
          return "yellow--text text--accent-4";
        case "IN_TRANSIT":
          return "light-green--text text--accent-3";
        case "AT_BUFFER_IN":
        case "AT_BUFFER_OUT":
          return "light-blue--text text--lighten-1";
        case "AT_STATION_WORK":
          return "cyan--text text--accent-3";
        case "AT_GATEWAY_IN":
        case "AT_GATEWAY_OUT":
          return "pink--text text--lighten-3";
        default:
          return "";
      }
    },
  },
  watch: {
    "$route.params": "reloadComponent",
    storageCodeFilter() {
      this.getStorageInfo(true, false);
    },
    storageLogCreatedAtStartTimeFilter() {
      this.getStorageInfo(true, false);
    },
    storageLogCreatedAtEndTimeFilter() {
      this.getStorageInfo(true, false);
    },
  },
};
</script>

<style>
/* @import '~@fortawesome/fontawesome-free/css/fontawesome.min.css';
          @import '~@fortawesome/fontawesome-free/css/solid.min.css'; */
@import "../assets/Stars.css";
@import "../assets/Background.css";
.content {
  margin: 50px;
  margin-top: 25px;
}
</style>
