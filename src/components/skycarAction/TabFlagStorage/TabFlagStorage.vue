<template>
    <v-card>
        <v-col>
            <v-row>
                <v-col cols="6">
                    <v-text-field
                        v-model="skycar"
                        label="Skycar ID"
                        rounded
                        filled
                        readonly
                    ></v-text-field>
                </v-col>
                <v-col 
                    v-if="Object.keys(winch).length > 1"
                    cols="6"
                >
                    <v-select
                        v-model="mappedPosition"
                        label="Position"
                        :items="getPositions()"
                        @change="handlePosition"
                        rounded
                        filled
                    ></v-select>
                </v-col>
                <v-col cols="6">
                    <v-text-field
                        v-model="storageCode"
                        label="Storage Code"
                        rounded
                        filled
                        type="number"
                    ></v-text-field>
                </v-col>
            </v-row>
            <v-row>
                <v-col>
                    <v-alert
                        border="top"
                        color="red"
                        dark
                    >
                        <v-icon>mdi-alert</v-icon>
                        Warning! This process is irreversible. Please double confirm everything before triggering it.
                        <v-checkbox
                            v-model="checkbox"
                            :label="getText()"
                            color="white"
                        >
                        </v-checkbox>
                        
                    </v-alert>
                </v-col>
            </v-row>
            <v-card-actions>
                <v-spacer></v-spacer>
                <ProgressCircular :doneSync="doneSync"/>
                <v-btn
                    color="green darken-1"
                    text
                    @click="btnConfirm()"
                    :disabled="disableConfirm()"
                >Confirm
                </v-btn>
                <v-btn
                    color="green darken-1"
                    text
                    @click="closeDialog()"
                >Close
                </v-btn>
            </v-card-actions>
        </v-col>
    </v-card>
</template>

<script>
import { PositionMapping, RouteSkycar, StoragePosition } from "../../../helper/enums"
import { getHost, getRequestHeader, useRefreshToken } from "../../../helper/common"
import ProgressCircular from "../../shared/ProgressCircular.vue"
import axios from "axios"
export default {
    props: {
        skycar: {
            type: Number
        },
        winch: {
            type: Object,
            default: () => {}
        },
        cube: {
            type: String
        },
        closeDialog: {
            type: Function
        },
        showNotification: {
            type: Function
        },
        syncSkycar: {
            type: Function
        }
    },
    components: {
        ProgressCircular
    },
    data: () => ({
        PositionMapping,
        position: null,
        storageCode: null,
        checkbox: false,
        doneSync: true
    }),
    created() {
        this.updatePosition()
    },
    methods: {
        async btnConfirm() {
            try {
                let promise
                this.doneSync = false
                if (this.isFlagDroppedFlow()) {
                    promise = this.FlagDropped()
                } else {
                    promise = this.FlagPicked()
                }
                await promise.then(() => {
                    this.showNotification(true, "Storage is flagged successfully.")
                    this.closeDialog()
                    this.syncSkycar()
                }).catch((res) => {
                    if (res.response.status === 401){ // If access token is unauthorized
                        // use refresh token to get new access token from auth server
                        return useRefreshToken(this, this.btnConfirm)
                    }
                    this.showNotification(false, res.response.data.message)
                })
            } finally {
                this.doneSync = true
                this.checkbox = false
            }
        },
        async FlagPicked() {
            let url = getHost(this.cube) + RouteSkycar.FLAG_PICKED
            let promise = axios.post(url, {
                sid: this.skycar,
                position: this.position,
                // eslint-disable-next-line camelcase
                storage_code: this.storageCode
            }, { headers:getRequestHeader() })
            return promise
        },
        async FlagDropped() {
            let url = getHost(this.cube) + RouteSkycar.FLAG_DROPPED
            let promise = axios.post(url, {
                position: this.position,
                // eslint-disable-next-line camelcase
                storage_code: this.storageCode
            }, { headers:getRequestHeader() })
            return promise
        },
        disableConfirm() {
            if (!this.doneSync) {
                return true
            }
            if (!this.checkbox) {
                return true
            }
            if (this.storageCode === null) {
                return true
            }
            return false
        },
        getText() {
            if (this.isFlagDroppedFlow()) {
                return `Skycar ${this.skycar} dropped Storage ${this.storageCode} to its 
                        destination without informing TC. I would like to manually inform TC.`
            } else {
                return `Skycar ${this.skycar} picked Storage ${this.storageCode} without 
                        informing TC. I would like to manually inform TC.`
            }
        },
        isFlagDroppedFlow() {
            return Boolean(this.winch[this.position].storage_no)
        },
        updateStorageCode() {
            let storageNo = this.winch[this.position].storage_no
            if (storageNo) {
                this.storageCode = parseInt(
                    storageNo.match(/\d+/)
                    )
            } else {
                this.storageCode = null
            }
        },
        updatePosition() {
            this.mappedPosition = this.getPositions()[0]
            this.handlePosition()
        },
        getPositions() {
            return Object.keys(this.winch).map(winch => {
                switch (winch) {
                    case StoragePosition.LEFT:
                        return `${winch} (A)`
                    case StoragePosition.RIGHT:
                        return `${winch} (B)`
                    default:
                        return winch
                }
            })
        },
        handlePosition() {
            this.position = this.PositionMapping[this.mappedPosition]
        },
        reset() {
            this.updatePosition()
            this.updateStorageCode()
            this.checkbox = false
        }
    },
    watch: {
        winch() {
            this.updatePosition()
        },
        position() {
            this.updateStorageCode()
        }
    }
}
</script>
