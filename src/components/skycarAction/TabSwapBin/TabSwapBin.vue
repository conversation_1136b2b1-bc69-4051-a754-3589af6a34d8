<template>
    <v-card>
        <v-col>
            <v-row>
                <v-col>
                    <v-text-field
                        v-model="currentSkycar"
                        label="Current Skycar ID"
                        rounded
                        filled
                        readonly
                    ></v-text-field>
                </v-col>
                <v-col>
                    <v-select
                        v-model="mappedPosition"
                        label="Position"
                        rounded
                        filled
                        :items="getPositions()"
                        @change="handlePosition"
                    ></v-select>
                </v-col>
            </v-row>
            <v-row>
                <v-col>
                    <v-text-field
                        label="Current Storage"
                        rounded
                        filled
                        readonly
                        :value="targetedStorage()"
                    ></v-text-field>
                </v-col>
                <v-col cols="6">
                    <v-select
                        v-model="newSkycar"
                        label="New Skycar ID"
                        rounded
                        filled
                        type='number'
                        :items="filterSkycarOption()"
                    ></v-select>
                </v-col>
            </v-row>

            <v-row>
                <v-col>
                    <v-alert
                        border="top"
                        color="green"
                        dark
                    >
                        <v-checkbox
                            v-model="checkbox"
                            :label="getText()"
                            color="white"
                        >
                        </v-checkbox>
                    </v-alert>
                </v-col>
            </v-row>
            <v-card-actions>
                <v-spacer></v-spacer>
                <ProgressCircular :doneSync="doneSync"/>
                <v-btn
                    color="green darken-1"
                    text
                    @click="btnConfirm()"
                    :disabled="disableConfirm()"
                >Confirm
                </v-btn>
                <v-btn
                    color="green darken-1"
                    text
                    @click="closeDialog()"
                >Close
                </v-btn>
            </v-card-actions>
        </v-col>
    </v-card>
</template>

<script>
import { PositionMapping, RouteSkycar, SkycarStatus, StoragePosition } from "../../../helper/enums"
import { getHost, getRequestHeader, useRefreshToken } from "../../../helper/common"
import ProgressCircular from "../../shared/ProgressCircular.vue"
export default {
    components: {
        ProgressCircular
    },
    props: {
        currentSkycar: {
            type: Number
        },
        winch: {
            type: Object
        },
        newSkycarOptions: {
            type: Array
        },
        cube: {
            type: String
        },
        closeDialog: {
            type: Function
        },
        showNotification: {
            type: Function
        },
        syncSkycar: {
            type: Function
        }
    },
    data: () => ({
        PositionMapping,
        newSkycar: null,
        position: null,
        checkbox: false,
        doneSync: true
    }),
    created() {
        this.updatePosition()
    },
    methods: {
        async btnConfirm() {
            try {
                this.doneSync = false
                let url = getHost(this.cube) + RouteSkycar.SWAP_BIN

                let req = await fetch(url, {
                    method: "POST",
                    body: JSON.stringify({
                        previousSkycarId: this.currentSkycar,
                        newSkycarId: this.newSkycar,
                        position: this.position
                    }),
                    headers: getRequestHeader()
                })
                let res = JSON.parse(await req.text())
                if (res.code === 401){ // If access token is unauthorized
                    // use refresh token to get new access token from auth server
                    return useRefreshToken(this, this.btnConfirm)
                }
                if (res.status) {
                    this.showNotification(true, "Storage is swapped successfully.")
                    this.closeDialog()
                    this.syncSkycar()
                } else {
                    this.showNotification(false, res.message)
                }
            } catch (error) {
                this.showNotification(false, error)
            } finally {
                this.checkbox = false
                this.doneSync = true
            }
        },
        disableConfirm() {
            if (!this.doneSync) {
                return true
            }
            if (this.newSkycar==null) {
                return true
            }
            if (!this.checkbox) {
                return true
            }
            return false
        },
        getText() {
            return `I have manually swap the storage ${this.targetedStorage()} 
                    from Skycar ${this.currentSkycar} to Skycar ${this.newSkycar}.`
        },
        filterSkycarOption() {
            let skycars = this.newSkycarOptions.filter(skycar => {
                return skycar.status == SkycarStatus.MAINTENANCE &&
                !skycar.winch[this.position].storage_no
            })
            return skycars.map(skycar => skycar.skycar_id)
        },
        targetedStorage() {
            return this.winch[this.position].storage_no
        },
        updatePosition() {
            this.mappedPosition = this.getPositions()[0]
            this.handlePosition()
        },
        getPositions() {
            return Object.keys(this.winch).map(winch => {
                switch (winch) {
                    case StoragePosition.LEFT:
                        return `${winch} (A)`
                    case StoragePosition.RIGHT:
                        return `${winch} (B)`
                    default:
                        return winch
                }
            })
        },
        handlePosition() {
            this.position = this.PositionMapping[this.mappedPosition]
        }
    },
    watch: {
        winch() {
            this.updatePosition()
        },
        newSkycarOptions() {
            this.newSkycar = null
        }
    }
}
</script>
