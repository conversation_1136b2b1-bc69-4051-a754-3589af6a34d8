<template>
    <v-dialog
        v-model="dialog"
        width="800"
    >
        <v-card>
            <v-toolbar
                dark
                :color="responseStatus.color"
            >
                <v-toolbar-title>{{ responseStatus.message }}</v-toolbar-title>
            </v-toolbar>
            <v-progress-linear
                v-if="this.responseStatus.processing"
                indeterminate
                color="green"
            ></v-progress-linear>

            <v-col>
                <v-row>
                    <v-col
                        v-for="position in Object.keys(responseData)"
                        :key="position"
                    >
                        <v-card-title>{{ position }}</v-card-title>
                        <v-row
                            v-for="data in responseData[position]"
                            :key="data.storage_no"
                        >
                            <v-chip
                                color="green"
                                dark
                                class="ma-1"
                            >
                            <v-icon class='mx-1'>mdi-cube</v-icon>
                            <pre>{{ data.storage_no }}</pre> 
                            </v-chip>
                            <v-chip
                                color="orange"
                                dark
                                class="ma-1"
                                width="150"
                            >
                            <v-icon class='mx-1'>mdi-map-marker</v-icon>
                            <pre>{{ data.coord }}</pre> 
                            </v-chip>
                        </v-row>
                    </v-col>
                </v-row>
            </v-col>
            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn
                    color="green darken-1"
                    text
                    @click="closeDialog()"
                >Close
                </v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script>
import { getHost, getRequestHeader, useRefreshToken } from "../../helper/common"
import { RouteStorage } from "../../helper/enums"

export default {
    data: () => ({
        dialog: false,
        responseStatus: ResponseStatus.PENDING,
        responseData: {}
    }),
    methods: {
        async submit(cube, storageCode, coordX, coordY) {
            this.updateDialog(ResponseStatus.PENDING)
            this.openDialog()
            let url = getHost(cube) + RouteStorage.CHECK_BIN

            let req = await fetch(url, {
                method: "POST",
                body: JSON.stringify({
                    // eslint-disable-next-line camelcase
                    storage_code: storageCode,
                    // eslint-disable-next-line camelcase
                    coord_x: coordX,
                    // eslint-disable-next-line camelcase
                    coord_y: coordY
                }),
                headers: getRequestHeader()
            })
            let res = JSON.parse(await req.text())
            if (res.code === 401){ // If access token is unauthorized
                    // use refresh token to get new access token from auth server
                    return useRefreshToken(this, this.submit, cube, storageCode, coordX, coordY)
                }
            if (res.status) {
                this.updateDialog(ResponseStatus.SUCCESS, res.model)
            } else {
                this.closeDialog()
                this.$awn.alert(res.message)
            }
        },
        openDialog() {
            this.dialog = true
        },
        closeDialog() {
            this.dialog = false
        },
        updateDialog(status, data={}) {
            this.responseStatus = status
            this.responseData = data
        }
    }
}
const ResponseStatus = {
    SUCCESS: {
        message: "SUCCESS",
        color: "green",
        processing: false
    },
    PENDING: {
        message: "PENDING",
        color: "black",
        processing: true
    }
}
</script>
