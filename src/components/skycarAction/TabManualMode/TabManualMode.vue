<template>
  <v-card>
    <v-col>
      <v-row>
        <v-col>
          <v-text-field
            v-model="skycar"
            label="Skycar ID"
            rounded
            filled
            readonly
          />
        </v-col>
        <v-col>
          <v-text-field
            v-model="mode"
            label="Current Skycar Mode"
            rounded
            filled
            readonly
          />
        </v-col>
      </v-row>
      <v-row>
        <v-col>
          <v-select
            v-model="newMode"
            label="New Skycar Mode"
            :items="modes"
            rounded
            filled
            :disabled="mode === 'Error'"
          />
        </v-col>
      </v-row>
      <v-row>
        <v-col>
          <v-alert
            border="top"
            color="green"
            dark
          >
            <v-checkbox
              v-model="checkbox"
              :label="getText()"
            />
          </v-alert>
        </v-col>
      </v-row>
      <v-card-actions>
        <v-spacer />
        <progress-circular :doneSync="doneSync" />
        <v-btn
          color="green darken-1"
          text
          @click="btnConfirm()"
          :disabled="!doneSync || !checkbox || mode==='Error'"
        >
          Confirm
        </v-btn>
        <v-btn
          color="green darken-1"
          text
          @click="closeDialog"
        >
          Close
        </v-btn>
      </v-card-actions>
    </v-col>
  </v-card>
</template>

<script>
  const httpRequest = require("../../../helper/http_request");
  import { RouteSkycar } from "../../../helper/enums.js"
  import { getHost, useRefreshToken } from "../../../helper/common.js"
  import ProgressCircular from "../../shared/ProgressCircular.vue"

  export default {
    components: {
      ProgressCircular: ProgressCircular
    },
    props: {
      skycar: {
        type: Number,
        required: true
      },
      mode: {
        type: String,
        required: true
      },
      cube: {
        type: String,
        required: true
      },
      showNotification: {
        type: Function,
        required: true
      },
      closeDialog: {
        type: Function,
        required: true
      },
      syncSkycar: {
        type: Function,
        required: true
      },
    },
    data () {
      return {
        availableModes: ["Normal", "Manual"],
        // modes: ["Normal", "Manual"],
        newMode: null,
        checkbox: false,
        doneSync: true
      }
    },
    methods: {
      async btnConfirm() {
          this.doneSync = false;
          let data = { sid: this.skycar, mode: this.newMode };
          let res = await httpRequest.axiosRequest(
            "patch",
            getHost(this.cube),
            RouteSkycar.MANUAL_MODE,
            data
          )
          if (res.status === 401) {// If access token is unauthorized
            // use refresh token to get new access token from auth server 
            return useRefreshToken(this, this.btnConfirm);
          } else if (res.status === 200) {
            this.showNotification(true, "Manual mode has been update successfully.");
            this.closeDialog();
            this.syncSkycar();
          } else {
            this.showNotification(false, res.data.message);
          }
          this.doneSync = true;
      },
      getText() {
        return `I would like to change the mode from ${this.mode} to ${this.newMode} Mode.`;
      },
      disableConfirm() {
        return !this.doneSync || !this.checkbox
      }
    },
    computed: {
      modes: function() {
        let filteredModes = [];
        for (let i = 0; i < this.availableModes.length; i++) {
          if (this.availableModes[i] != this.mode) {
            filteredModes.push(this.availableModes[i]);
          }
        }
        return filteredModes;
      }
    },
    created() {
      this.newMode = this.modes[0];
    },
    beforeUpdate() {
      this.newMode = this.modes[0];
      this.checkbox = true;
    },
}
</script>
