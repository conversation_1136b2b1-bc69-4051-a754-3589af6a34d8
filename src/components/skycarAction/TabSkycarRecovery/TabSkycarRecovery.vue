<template>
    <v-card>
        <v-col>
            <v-row>
                <v-col>
                    <v-text-field
                        v-model="skycar"
                        label="Skycar ID"
                        filled
                        rounded
                        readonly
                    >
                    <template #append>
                        <v-btn 
                            v-if="isErrorOrMaintenance(status)"
                            icon 
                            @click="onCameraClick" 
                            :loading="!doneSync" 
                            class="icon-centered"
                        >
                            <v-icon>mdi-qrcode-scan</v-icon>
                        </v-btn>
                        </template>
                </v-text-field>
                </v-col>
                <v-col>
                    <v-select
                        v-model="recovery"
                        label="Recovery Mode"
                        :items="getRecoveryOption"
                        filled
                        rounded
                    ></v-select>
                </v-col>
            </v-row>
            <ModeAutoChargeOut
                v-show="recovery==SkycarRecovery.AUTO_CHARGE_OUT"
                :skycar="skycar"
                :coordX="localCoordX"
                :coordY="localCoordY"
                :cube="cube"
                :closeDialog="closeDialog"
                :status="status"
                :isDocked="isDocked"
                :showNotification="showNotification"
                :syncSkycar="syncSkycar"
                :openDialogCyclestart="openDialogCyclestart"
                :homeWhenRecoverSkycar="enableHomeWhenRecoverSkycar"
                ref="modeAutoChargeOut"
                :isCoordFieldsLocked="isCoordFieldsLocked"
            />
            <ModeCharging
                v-show="recovery==SkycarRecovery.CHARGING"
                :skycar="skycar"
                :battery="battery"
                :cube="cube"
                :closeDialog="closeDialog"
                :showNotification="showNotification"
                ref="modeCharging"
            />
            <ModeInspect
                v-show="recovery==SkycarRecovery.INSPECT"
                :skycar="skycar"
                :coordX="localCoordX"
                :coordY="localCoordY"
                :cube="cube"
                :closeDialog="closeDialog"
                :status="status"
                :isDocked="isDocked"
                :showNotification="showNotification"
                :syncSkycar="syncSkycar"
                :openDialogCyclestart="openDialogCyclestart"
                :homeWhenRecoverSkycar="enableHomeWhenRecoverSkycar"
                ref="modeInspect"
                :isCoordFieldsLocked="isCoordFieldsLocked"
            />
            <ModeManualChargeOut
                v-show="recovery==SkycarRecovery.MANUAL_CHARGE_OUT"
                :skycar="skycar"
                :winch="winch"
                :cube="cube"
                :closeDialog="closeDialog"
                :status="status"
                :showNotification="showNotification"
                :syncSkycar="syncSkycar"
                :openDialogCyclestart="openDialogCyclestart"
                ref="modeManualChargeOut"
                :isCoordFieldsLocked="isCoordFieldsLocked"
            />
            <ModeRevive
                v-show="recovery==SkycarRecovery.REVIVE"
                :skycar="skycar"
                :coordX="localCoordX"
                :coordY="localCoordY"
                :cube="cube"
                :closeDialog="closeDialog"
                :status="status"
                :isDocked="isDocked"
                :showNotification="showNotification"
                :syncSkycar="syncSkycar"
                :openDialogCyclestart="openDialogCyclestart"
                :homeWhenRecoverSkycar="enableHomeWhenRecoverSkycar"
                ref="modeRevive"
                :isCoordFieldsLocked="isCoordFieldsLocked"
            />
            <ModeTravel
                v-show="recovery==SkycarRecovery.TRAVEL"
                :skycar="skycar"
                :cube="cube"
                :closeDialog="closeDialog"
                :showNotification="showNotification"
                :syncSkycar="syncSkycar"
                ref="modeTravel"
            />


            <v-dialog
                v-model="showConfirmationDialog"
                max-width="500"
            >
                <v-card>
                <v-card-title class="headline">
                    Confirmation
                </v-card-title>
                <v-card-text>
                    Make sure skycar already stopped before scanning QR code
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn
                    color="green darken-1"
                    text
                    @click="handleConfirmScan"
                    >
                    Confirm
                    </v-btn>
                    <v-btn
                    color="grey darken-1"
                    text
                    @click="showConfirmationDialog = false"
                    >
                    Cancel
                    </v-btn>
                </v-card-actions>
                </v-card>
            </v-dialog>
        </v-col>
        <DialogCyclestart :showNotification="showNotification" ref="dialogCyclestart"/>
    </v-card>
</template>

<script>
import { RouteOperation, SkycarRecovery, SkycarStatus, Websocket, RouteError } from "../../../helper/enums"
import { getHost, getRequestHeader, useRefreshToken } from "../../../helper/common"
import DialogCyclestart from "./Mode/DialogCyclestart.vue"
import ModeAutoChargeOut from "./Mode/ModeAutoChargeOut.vue"
import ModeCharging from "./Mode/ModeCharging.vue"
import ModeInspect from "./Mode/ModeInspect.vue"
import ModeManualChargeOut from "./Mode/ModeManualChargeOut.vue"
import ModeRevive from "./Mode/ModeRevive.vue"
import ModeTravel from "./Mode/ModeTravel.vue"
import { socket } from "../../../App.vue"
import axios from "axios"
import { isErrorOrMaintenance } from "./Mode/utils.js"

export default {
    components: {
        DialogCyclestart,
        ModeAutoChargeOut,
        ModeCharging,
        ModeInspect,
        ModeManualChargeOut,
        ModeRevive,
        ModeTravel
    },
    props: {
        skycar: {
            type: Number
        },
        status: {
            type: String
        },
        coordX: {
            type: Number
        },
        coordY: {
            type: Number
        },
        winch: {
            type: Object
        },
        isDocked: {
            type: Boolean  
        },
        cube: {
            type: String
        },
        closeDialog: {
            type: Function
        },
        showNotification: {
            type: Function
        },
        syncSkycar: {
            type: Function
        },
        battery: {
            type: Number
        }
    },
    async created() {
        this.recovery = this.getRecoveryOption[0]
        this.homeWhenRecoverSkycar = await this.getHomeWhenRecoverSkycar()
        this.localCoordX = this.coordX;
        this.localCoordY = this.coordY;
        this.getMessage(socket);
    },
    data: () => ({
        SkycarRecovery,
        isErrorOrMaintenance,
        recoveryOption: [],
        recovery: null,
        homeWhenRecoverSkycar: false,
        localCoordX: null,
        localCoordY: null,
        doneSync: true,
        isCoordFieldsLocked: true,
        showConfirmationDialog: false,
    }),
    beforeDestroy() {
        if (socket) {
            socket.off(Websocket.UPDATE_MATRIX);
        }
    },
    computed: {
        getRecoveryOption() {
            switch (this.status) {
                case SkycarStatus.AVAILABLE: {
                    return [
                        SkycarRecovery.AUTO_CHARGE_OUT,
                        SkycarRecovery.INSPECT,
                        SkycarRecovery.TRAVEL,
                        SkycarRecovery.CHARGING
                    ]
                }
                case SkycarStatus.DOCKED: {
                    return [
                        SkycarRecovery.AUTO_CHARGE_OUT,
                        SkycarRecovery.INSPECT
                    ]
                }
                case SkycarStatus.ERROR: {
                    return [
                        SkycarRecovery.REVIVE,
                        SkycarRecovery.INSPECT,
                        SkycarRecovery.AUTO_CHARGE_OUT,
                        SkycarRecovery.MANUAL_CHARGE_OUT
                    ]
                }
                case SkycarStatus.MAINTENANCE: {
                    return [
                        SkycarRecovery.REVIVE,
                        SkycarRecovery.AUTO_CHARGE_OUT,
                        SkycarRecovery.INSPECT
                    ]
                }

                default:
                    return []
            }
        },
        enableHomeWhenRecoverSkycar() {
            if ([SkycarStatus.ERROR, SkycarStatus.MAINTENANCE].includes(this.status)) {
                return this.homeWhenRecoverSkycar
            }
            return false
        }
    },
    methods: {
        reset() {
            this.$refs.modeAutoChargeOut.reset()
            this.$refs.modeCharging.reset()
            this.$refs.modeInspect.reset()
            this.$refs.modeManualChargeOut.reset()
            this.$refs.modeRevive.reset()
            this.$refs.modeTravel.reset()
            this.recovery = this.getRecoveryOption[0]
            this.localCoordX = this.coordX
            this.localCoordY = this.coordY
            this.isCoordFieldsLocked = true
        },
        openDialogCyclestart(model, cube) {
            this.$refs.dialogCyclestart.openDialog(model, cube)
        },
        async getHomeWhenRecoverSkycar() {
            let host = new URL(getHost(this.cube) + RouteOperation.SKYCAR_ACTION_WITHOUT_SHELL)
            let requestOptions = {
                method: "GET",
                headers: getRequestHeader()
            }
            try {
                const response = await fetch(host, requestOptions)
                let res = JSON.parse(await response.text())
                if (res.code == 401) {
                    return useRefreshToken(this, this.getHomeWhenRecoverSkycar)
                }
                    return res["model"]["HOME_WHEN_RECOVER_SKYCAR"]
            } catch (error) {
                return false
            }
        },
        getMessage(socket) {
            console.log('Setting up WebSocket listeners');
            
            socket.on(Websocket.UPDATE_MATRIX, (message) => {
                console.log('Received QR code data:', message);
                
                // Access the nested item object
                const data = message.item;
                
                // Only process messages for this skycar
                if (data && data.skycar_id === this.skycar) {
                    // Check if this is the QR scan result (not just the scan request message)
                    if (data.data && data.data.includes("scan request sent")) {
                        return;
                    }

                    // Handle actual QR scan result
                    if (data.status && data.coordinates && data.coordinates.x !== null && data.coordinates.y !== null) {
                        console.log('Updating coordinates:', data.coordinates);
                        this.localCoordX = data.coordinates.x;
                        this.localCoordY = data.coordinates.y;
                        this.isCoordFieldsLocked = true;
                    } else {  // Only unlock for actual error response
                        if (data.data && !data.data.includes("scan request sent") || 
                        data.data.toLowerCase().includes("did not respond within")) {
                            this.isCoordFieldsLocked = false;  // Unlock fields only for error
                            this.innerCoordX = "";
                            this.innerCoordY = "";
                        }
                    }
                }
            });
        },

        onCameraClick() {
            this.showConfirmationDialog = true;
        },
        async handleConfirmScan() {
            this.showConfirmationDialog = false;
            try {
            this.doneSync = false;
            let url = getHost(this.cube) + RouteError.QR_CODE;
            let res = await axios.get(
                url,
                { 
                params: { sid: this.skycar },
                headers: getRequestHeader() 
                }
            );

            if (res.data.status) {
                this.localCoordX = res.data.data.x;
                this.localCoordY = res.data.data.y;
                this.isCoordFieldsLocked = true;
            } else {
                this.showNotification(false, res.data.message);
                if (res.data.message && res.data.message.includes("Failed to read QR code")) {
                this.isCoordFieldsLocked = false;
                this.localCoordX = "";
                this.localCoordY = "";
                }
            }
            } catch (error) {
            if (error.response?.status === 401) {
                return useRefreshToken(this, this.handleConfirmScan);
            }
            this.showNotification(false, error);
            } finally {
            this.doneSync = true;
            }
        },
    },
    watch: {
        getRecoveryOption(options) {
            this.recovery = options[0]
        },
        coordX(val) {
            this.localCoordX = val;
        },
        coordY(val) {
            this.localCoordY = val;
        }
    }
}
</script>

<style scoped>
.text-field-with-icon {
  position: relative;
}

.icon-centered {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1; /* Ensure it's above the input background */
}

.icon-centered v-icon {
  font-size: 20px; /* Adjust icon size as needed */
}
</style>
