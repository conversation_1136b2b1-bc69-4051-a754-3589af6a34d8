<template>
  <span>
    <v-row>
      <v-col>
        <v-text-field
          v-model="coordX"
          label="Coord X"
          filled
          rounded
          type="number"
          hint="Fill in the coord to leave a skycar at specific coord."
          persistent-hint
        >
          <template #prepend-inner>
            <v-icon 
              @click="openGrid()" 
              style="margin-right: 5px;"
            >
              mdi-grid
            </v-icon>
          </template>
        </v-text-field>
      </v-col>
      <v-col>
        <v-text-field
          v-model="coordY"
          label="Coord Y"
          filled
          rounded
          type="number"
        >
          <template #prepend-inner>
            <v-icon 
              @click="openGrid()" 
              style="margin-right: 5px;"
            >
              mdi-grid
            </v-icon>
          </template>
        </v-text-field>
      </v-col>
    </v-row>
    <v-row>
      <v-col>
        <v-alert 
          border="top" 
          color="green" 
          dark
        >
          <v-checkbox
            v-model="checkboxStorage"
            :label="getTextForStorage()"
            color="white"
          />
          <v-checkbox
            v-model="checkboxCoord"
            :label="getTextForCoord()"
            color="white"
          />
        </v-alert>
      </v-col>
    </v-row>
    <v-card-actions>
      <v-spacer />
      <ProgressCircular :doneSync="doneSync" />
      <v-btn
        color="green darken-1"
        text
        @click="btnConfirm()"
        :disabled="disableConfirm()"
      >
        Confirm
      </v-btn>
      <v-btn 
        color="green darken-1" 
        text 
        @click="closeDialog()"
      >
        Close 
      </v-btn>
    </v-card-actions>

    <DialogCoordinateSelection
      ref="dialogCoordinateSelection"
      @update-coord="updateCoord"
    />
  </span>
</template>

<script>
import { SkycarRecovery } from "../../../../helper/enums";
import { postRecoverSkycar } from "./utils.js";
import ProgressCircular from "../../../shared/ProgressCircular.vue";
import { getNoOfStorages, getStorageNo } from "../../../../helper/common";
import DialogCoordinateSelection from "../../../dialogs/DialogCoordinateSelection";

export default {
  components: {
    ProgressCircular,
    DialogCoordinateSelection,
  },
  props: {
    skycar: {
      type: Number,
    },
    winch: {
      type: Object,
    },
    cube: {
      type: String,
    },
    closeDialog: {
      type: Function,
    },
    status: {
      type: String,
    },
    showNotification: {
      type: Function,
    },
    syncSkycar: {
      type: Function,
    },
    openDialogCyclestart: {
      type: Function,
    },
  },
  data: () => ({
    coordX: null,
    coordY: null,
    checkboxCoord: false,
    checkboxStorage: false,
    doneSync: true,
  }),
  methods: {
    openGrid() {
      this.$refs.dialogCoordinateSelection.openDialog(this.cube);
    },
    updateCoord(selectedCells) {
      if (selectedCells.length > 0) {
        this.coordX = String(selectedCells[0].x);
        this.coordY = String(selectedCells[0].y);
      }
    },
    async btnConfirm() {
      try {
        this.doneSync = false;
        let res = await postRecoverSkycar(
          this.showNotification,
          this.cube,
          this.skycar,
          null,
          SkycarRecovery.MANUAL_CHARGE_OUT,
          this.getCoord()
        );

        if (!res.status) {
          return;
        }
        if (!res.model.status) {
          this.openDialogCyclestart(res.model, this.cube);
        }
        this.closeDialog();
        this.syncSkycar();
      } finally {
        this.checkboxCoord = false;
        this.checkboxStorage = false;
        this.doneSync = true;
      }
    },
    getTextForCoord() {
      let coord = this.getCoord();
      if (coord) {
        return `I have manually moved Skycar ${this.skycar} to coordinate (${coord}).`;
      } else {
        return `I have manually moved Skycar ${this.skycar} to maintenance zone.`;
      }
    },
    getTextForStorage() {
      if (getNoOfStorages(this.winch)) {
        return `I have confirmed that Skycar ${
          this.skycar
        } is carrying ${getStorageNo(this.winch)}.`;
      } else {
        return `I have confirmed that Skycar ${this.skycar} does not carry any storage.`;
      }
    },
    getCoord() {
      if (this.coordX || this.coordY) {
        return `${this.coordX},${this.coordY}`;
      }
      return null;
    },
    disableConfirm() {
      if (!this.doneSync) {
        return true;
      }
      if (!this.checkboxCoord) {
        return true;
      }
      if (!this.checkboxStorage) {
        return true;
      }
      return Boolean(
        (!this.coordX && this.coordY) | (this.coordX && !this.coordY)
      );
    },
    reset() {
      this.checkboxCoord = false;
      this.checkboxStorage = false;
      this.coordX = null;
      this.coordY = null;
    },
  },
};
</script>
