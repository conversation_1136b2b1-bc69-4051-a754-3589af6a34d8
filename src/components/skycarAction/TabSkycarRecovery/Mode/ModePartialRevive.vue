<template>
    <span>
        <v-row>
            <v-col>
                <v-alert
                    border="top"
                    color="green"
                    dark
                >
                    <v-checkbox
                        v-model="checkbox"
                        :label="getText()"
                        color="white"
                    >
                    </v-checkbox>
                </v-alert>
            </v-col>
        </v-row>
        <v-card-actions>
            <v-spacer></v-spacer>
            <ProgressCircular :doneSync="doneSync"/>
            <v-btn
                color="green darken-1"
                text
                @click="btnConfirm()"
                :disabled="disableConfirm()"
            >Confirm
            </v-btn>
            <v-btn
                color="green darken-1"
                text
                @click="closeDialog()"
            >Close
            </v-btn>
        </v-card-actions>
    </span>
</template>

<script>
import { SkycarRecovery } from '../../../../helper/enums'
import { postRecoverSkycar } from "./utils.js"
import ProgressCircular from '../../../shared/ProgressCircular.vue'
export default {
    components: {
        ProgressCircular
    },
    props: {
        skycar: {
            type: Number
        },
        coordX: {
            type: Number
        },
        coordY: {
            type: Number
        },
        cube: {
            type: String
        },
        closeDialog: {
            type: Function
        },
        status: {
            type: String
        },
        showNotification: {
            type: Function
        },
        openDialogCyclestart: {
            type: Function
        }
    },
    data: () => ({
        checkbox: false,
        doneSync: true
    }),
    methods: {
        async btnConfirm() {
            try {
                this.doneSync = false
                let res = await postRecoverSkycar(
                    this.showNotification, 
                    this.cube, 
                    this.skycar, 
                    null, 
                    SkycarRecovery.PARTIAL_REVIVE, 
                    null
                )
                if (!res.status) {
                    return
                }
                if (!res.model.status) {
                    this.openDialogCyclestart(res.model, this.cube)
                }
                this.closeDialog()
            } finally {
                this.checkbox = false
                this.doneSync = true
            }
        },
        getText() {
            return `I have confirmed that Skycar ${this.skycar} is currently at (${this.coordX}, ${this.coordY}) and I would like to clear the obstacles surrounded the skycar.`
        },
        disableConfirm() {
            if (!this.doneSync) {
                return true
            }
            if (!this.checkbox) {
                return true
            }
            return false
        },
        reset() {
            this.checkbox = false
        }
    }
}
</script>
