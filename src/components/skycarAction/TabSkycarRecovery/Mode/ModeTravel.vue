<template>
  <span>
    <v-row>
      <v-col 
        cols="12"
        sm="6" 
      >
        <v-text-field
          v-model="coordX"
          label="Coordinate X"
          filled
          rounded
          type="number"
        >
          <template #prepend-inner>
            <v-icon 
              @click="openGrid()" 
              style="margin-right: 5px;"
            >
              mdi-grid
            </v-icon>
          </template>
        </v-text-field>
      </v-col>
      <v-col
        cols="12"
        sm="6" 
      >
        <v-text-field
          v-model="coordY"
          label="Coordinate Y"
          filled
          rounded
          type="number"
        >
          <template #prepend-inner>
            <v-icon 
              @click="openGrid()" 
              style="margin-right: 5px;"
            >
              mdi-grid
            </v-icon>
          </template>
        </v-text-field>
      </v-col>
    </v-row>
    <v-row>
      <v-col>
        <v-alert 
          border="top" 
          color="green"
          dark
        >
          {{ getText() }}
        </v-alert>
      </v-col>
    </v-row>
    <v-card-actions>
      <v-spacer />
      <ProgressCircular :doneSync="doneSync" />
      <v-btn
        color="green darken-1"
        text
        @click="btnConfirm()"
        :disabled="disableConfirm()"
      >
        Confirm
      </v-btn>
      <v-btn 
        color="green darken-1" 
        text 
        @click="closeDialog()"
      >
        Close
      </v-btn>
    </v-card-actions>
    <DialogCoordinateSelection 
      ref="dialogCoordinateSelection" 
      @update-coord="updateCoord"
    />
  </span>
</template>

<script>
import {
  getHost,
  getRequestHeader,
  useRefreshToken,
} from "../../../../helper/common";
import { RouteOperation } from "../../../../helper/enums";
import ProgressCircular from "../../../shared/ProgressCircular.vue";
import DialogCoordinateSelection from "../../../dialogs/DialogCoordinateSelection";
export default {
  components: {
    ProgressCircular,
    DialogCoordinateSelection
  },
  props: {
    skycar: {
      type: Number,
    },
    cube: {
      type: String,
    },
    closeDialog: {
      type: Function,
    },
    showNotification: {
      type: Function,
    },
    syncSkycar: {
      type: Function,
    },
  },
  data: () => ({
    coordX: null,
    coordY: null,
    doneSync: true,
  }),
  methods: {
    openGrid(){
      this.$refs.dialogCoordinateSelection.openDialog(this.cube)
    },
    updateCoord(selectedCells){
      if (selectedCells.length > 0) {
        this.coordX = String(selectedCells[0].x);
        this.coordY = String(selectedCells[0].y);
      }
    },
    async btnConfirm() {
      try {
        this.doneSync = false;
        let url = getHost(this.cube) + RouteOperation.TRAVEL;
        let req = await fetch(url, {
          method: "POST",
          body: JSON.stringify({
            sid: this.skycar,
            coord: `${this.coordX},${this.coordY}`,
          }),
          headers: getRequestHeader(),
        });
        let res = JSON.parse(await req.text());
        if (res.code === 401) {
          // If access token is unauthorized
          // use refresh token to get new access token from auth server
          return useRefreshToken(this, this.btnConfirm);
        }
        if (res.status) {
          this.showNotification(true, "Travel job is created successfully.");
          this.closeDialog();
          this.syncSkycar();
        } else {
          this.showNotification(false, res.message);
        }
      } catch (error) {
        this.showNotification(false, error);
      } finally {
        this.doneSync = true;
      }
    },
    disableConfirm() {
      if (!this.doneSync) {
        return true;
      }
      return !(this.coordX && this.coordY);
    },
    getText() {
      return `I would like to create a request to move Skycar ${this.skycar}
                    to coord (${this.coordX}, ${this.coordY}). The skycar is free to 
                    move to other coord once it reaches its destination.`;
    },
    reset() {
      this.coordX = null;
      this.coordY = null;
    },
  },
};
</script>
