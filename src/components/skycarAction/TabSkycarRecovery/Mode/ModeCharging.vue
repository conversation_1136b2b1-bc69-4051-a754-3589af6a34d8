<template>
    <span>
        <v-row>
            <v-col cols="6">
                <v-select
                    v-model="cs"
                    label="Charging Station"
                    filled
                    rounded
                    type="number"
                    :items="csOptions"
                ></v-select>
            </v-col>
        </v-row>
        <v-row>
            <v-col>
                <v-alert
                    border="top"
                    color="green"
                    dark
                >
                    {{ getText() }}
                </v-alert>
            </v-col>
        </v-row>
        <v-card-actions>
            <v-spacer></v-spacer>
            <ProgressCircular :doneSync="doneSync"/>
            <v-btn
                color="green darken-1"
                text
                @click="btnConfirm()"
                :disabled="disableConfirm()"
            >Confirm
            </v-btn>
            <v-btn
                color="green darken-1"
                text
                @click="closeDialog()"
            >Close
            </v-btn>
        </v-card-actions>
    </span>
</template>

<script>
const Any = "Any"
import { getCMHost, getMapping, getRequestHeader } from "../../../../helper/common"
import { routeCM } from "../../../../helper/enums"
import ProgressCircular from "../../../shared/ProgressCircular.vue"
import axios from "axios"
export default {
    components: {
        ProgressCircular
    },
    props: {
        skycar: {
            type: Number
        },
        battery: {
            type: Number
        },
        cube: {
            type: String
        },
        closeDialog: {
            type: Function
        },
        showNotification: {
            type: Function
        }
    },
    async created() {
        await this.getChargingStation()
    },
    data: () => ({
        cs: Any,
        csCoord: {},
        csOptions: [Any],
        doneSync: true
    }),
    methods: {
        getText() {
            if (this.cs == Any) {
                return `Skycar ${this.skycar} currently has ${this.battery}% battery. 
                I would like to create a request to charge Skycar ${this.skycar}.`
            } else {
                return `Skycar ${this.skycar} currently has ${this.battery}% battery. 
                I would like to create a request to charge Skycar ${this.skycar} 
                at coordinate (${this.csCoord[this.cs]}).`
            }
        },
        async getChargingStation() {
            try {
                let url = getCMHost() + routeCM.CHECK_CHARGING_STATION
                let res = await axios.get(url, { headers: getRequestHeader() })
                let here = this
                res.data.data.forEach(cs => {
                    here.csCoord[cs.device_id] = `${cs.x_pos},${cs.y_pos}`
                })
                this.csOptions = Object.keys(this.csCoord)
                this.csOptions.unshift(Any)
            } catch (error) {
                this.showNotification(false, error)
            }
        },
        async btnConfirm() {
            this.doneSync = false
            try {
                let url = getCMHost() + routeCM.CREATE_REQUEST
                let req = await fetch(url, {
                    method: "POST",
                    body: JSON.stringify({
                        "cube": getMapping(this.cube),
                        "skycar_id": this.skycar,
                        "battery_orientation": null,
                        "charging_mode": null,
                        "station_id": this.cs == Any ? null : this.cs
                    }),
                    headers: getRequestHeader()
                })
                if (req.status == 200) {
                    this.showNotification(true, `Request to charge Skycar ${ this.skycar } is submitted.`)
                    this.closeDialog()
                } else {
                    let res = JSON.parse(await req.text())
                    this.showNotification(false, res.response)
                }
            } catch (error) {
                this.showNotification(false, error)
            } finally {
                setTimeout(() => {
                    this.doneSync = true
                }, 500)
            }
        },
        disableConfirm() {
            return !this.doneSync
        },
        reset() {
            this.cs = Any
        }
    }
}
</script>
