<template>
  <span>
    <v-row v-if="homeWhenRecoverSkycar">
        <v-col>
            <v-text-field
                v-model="fromCoordX"
                label="From Coordinate X"
                filled
                rounded
                type="number"
                persistent-hint
                :disabled="isCoordFieldsLocked"
            >
                <template #prepend-inner>
                    <v-icon 
                        @click="openFromGrid()" 
                        style="margin-right: 5px;"
                    >
                        mdi-grid
                    </v-icon>
                </template>
            </v-text-field>
        </v-col>
        <v-col>
            <v-text-field
                v-model="fromCoordY"
                label="From Coordinate Y"
                filled
                rounded
                type="number"
                :disabled="isCoordFieldsLocked"
            >
                <template #prepend-inner>
                    <v-icon 
                        @click="openFromGrid()" 
                        style="margin-right: 5px;"
                    >
                        mdi-grid
                    </v-icon>
                </template>
            </v-text-field>
        </v-col>
    </v-row>
    <v-row>
      <v-col 
        cols="12"
        sm="6" 
      >
        <v-text-field
          v-model="toCoordX"
          label="To Coordinate X"
          filled
          rounded
          type="number"
          hint="Fill in the coordinate to inspect a skycar at a specific coordinate."
          persistent-hint
        >
          <template #prepend-inner>
            <v-icon 
              @click="openToGrid()" 
              style="margin-right: 5px;"
            >
              mdi-grid
            </v-icon>
          </template>
        </v-text-field>
      </v-col>
      <v-col 
        cols="12"
        sm="6" 
      >
        <v-text-field
          v-model="toCoordY"
          label="To Coordinate Y"
          filled
          rounded
          type="number"
        >
          <template #prepend-inner>
            <v-icon 
              @click="openToGrid()" 
              style="margin-right: 5px;"
            >
              mdi-grid
            </v-icon>
          </template>
        </v-text-field>
      </v-col>
    </v-row>
    <v-row>
      <v-col>
        <v-alert 
          border="top" 
          color="green" 
          dark
        >
          <v-checkbox 
            v-model="checkbox" 
            :label="getText()" 
            color="white"
          />
          <CheckboxBypassCS
            :showCheckbox="isDocked && isErrorOrMaintenance(status)"
            ref="checkboxBypassCS"
          />
        </v-alert>
      </v-col>
    </v-row>
    <v-card-actions>
      <v-spacer />
      <ProgressCircular :doneSync="doneSync" />
      <v-btn
        color="green darken-1"
        text
        @click="btnConfirm()"
        :disabled="disableConfirm()"
      >
        Confirm
      </v-btn>
      <v-btn 
        color="green darken-1" 
        text 
        @click="closeDialog()"
      >
        Close
      </v-btn>
    </v-card-actions>
    <DialogCoordinateSelection 
      ref="dialogToCoordinateSelection" 
      @update-coord="updateToCoord"
    />
    <DialogCoordinateSelection 
      ref="dialogFromCoordinateSelection" 
      @update-coord="updateFromCoord"
    />
  </span>
</template>

<script>
import { SkycarRecovery } from "../../../../helper/enums";
import { isErrorOrMaintenance, postRecoverSkycar } from "./utils.js";
import CheckboxBypassCS from "../CheckboxBypassCS.vue";
import ProgressCircular from "../../../shared/ProgressCircular.vue";
import DialogCoordinateSelection from "../../../dialogs/DialogCoordinateSelection";
export default {
  components: {
    CheckboxBypassCS,
    ProgressCircular,
    DialogCoordinateSelection
  },
  props: {
    skycar: {
      type: Number,
    },
    coordX: {
        type: Number
    },
    coordY: {
        type: Number
    },
    cube: {
      type: String,
    },
    closeDialog: {
      type: Function,
    },
    status: {
      type: String,
    },
    isDocked: {
      type: Boolean,
    },
    showNotification: {
      type: Function,
    },
    syncSkycar: {
      type: Function,
    },
    openDialogCyclestart: {
      type: Function,
    },
    homeWhenRecoverSkycar: {
      type: Boolean
    },
    isCoordFieldsLocked: {
      type: Boolean,
      default: true
    }
  },
  async created() {
    this.fromCoordX = this.coordX
    this.fromCoordY = this.coordY
  },
  data: () => ({
    isErrorOrMaintenance,
    fromCoordX: null,
    fromCoordY: null,
    toCoordX: null,
    toCoordY: null,
    doneSync: true,
    checkbox: false,
  }),
  methods: {
    openToGrid(){
      this.$refs.dialogToCoordinateSelection.openDialog(this.cube)
    },
    updateToCoord(selectedCells){
      if (selectedCells.length > 0) {
        this.toCoordX = String(selectedCells[0].x);
        this.toCoordY = String(selectedCells[0].y);
      }
    },
    openFromGrid(){
      this.$refs.dialogFromCoordinateSelection.openDialog(this.cube)
    },
    updateFromCoord(selectedCells){
      if (selectedCells.length > 0) {
        this.fromCoordX = String(selectedCells[0].x);
        this.fromCoordY = String(selectedCells[0].y);
      }
    },
    async btnConfirm() {
      try {
        let coord;
        this.doneSync = false;
        if (!this.toCoordX && !this.toCoordY) {
          coord = null;
        } else {
          coord = `${this.toCoordX},${this.toCoordY}`;
        }
        let bypassCs = this.$refs.checkboxBypassCS.getBypassCs();
        let res = await postRecoverSkycar(
          this.showNotification,
          this.cube,
          this.skycar,
          null,
          SkycarRecovery.INSPECT,
          coord,
          this.homeWhenRecoverSkycar ? `${this.fromCoordX},${this.fromCoordY}` : null,
          bypassCs
        );
        if (!res.status) {
          return;
        }
        if (!res.model.status) {
          this.openDialogCyclestart(res.model, this.cube);
        }
        this.closeDialog();
        setTimeout(() => {
          this.syncSkycar();
        }, 500);
      } finally {
        this.doneSync = true;
      }
    },
    getDestination() {
      if (!this.toCoordX && !this.toCoordY) {
        return "the parking zone";
      } else {
        return `(${this.toCoordX}, ${this.toCoordY})`;
      }
    },
    disableConfirm() {
      if (!this.doneSync) {
        return true;
      }
      if (!this.checkbox) {
        return true;
      }
      if (this.fromCoordX === "" | this.fromCoordY === "") {
        return true
      }
      return Boolean(
        (!this.toCoordX && this.toCoordY) | (this.toCoordX && !this.toCoordY)
      );
    },
    getText() {
      let fromCoord = `(${this.fromCoordX}, ${this.fromCoordY})`
      if (!this.toCoordX && !this.toCoordY) {
        if (this.homeWhenRecoverSkycar) {
          return `I would like to create a request to home Skycar ${this.skycar} at coordinate ${fromCoord} 
                  and auto charge it out to the parking zone.`
        } else {
          return `I would like to create a request to auto charge out Skycar ${this.skycar} to the parking zone.`
        }
      } else {
        let coord = `(${this.toCoordX}, ${this.toCoordY})`;
        if (this.homeWhenRecoverSkycar) {
          return `I have confirmed that coordinate ${coord} is cleared, and I would 
                  like to create a request to home Skycar ${this.skycar} at coordinate ${fromCoord}
                  and auto charge it out to coordinate ${coord}.`;
        } else {
          return `I have confirmed that coordinate ${coord} is cleared, and I would 
                  like to create a request to auto charge out Skycar ${this.skycar} to coordinate ${coord}.`;
        }
      }
    },
    reset() {
      this.$refs.checkboxBypassCS.reset();
      this.checkbox = false;
      this.toCoordX = null;
      this.toCoordY = null;
    },
  },
    watch: {
        coordX(x) {
            this.fromCoordX = x
        },
        coordY(y) {
            this.fromCoordY = y
        }
    }
};
</script>
