<template>
    <v-dialog
        v-model="dialogBool"
        max-width="800"
    >
        <v-card>
            <v-toolbar
                dark
            >
                <v-toolbar-title>Cube Status</v-toolbar-title>
            </v-toolbar>
            <v-col>
                <v-card-text>
                    TC is currently stopped since 
                    <v-chip
                        color="red"
                        label
                        small
                        dark
                    >
                        {{ updatedAt }}
                    </v-chip>
                    due to 
                    <v-chip
                        color="red"
                        label
                        small
                        dark
                    >
                        {{ reason }}
                    </v-chip>
                    by 
                    <v-chip
                        color="green"
                        label
                        small
                        dark
                    >{{ username }}</v-chip>
                    . Would you like to start TC?
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <ProgressCircular :doneSync="doneSync"/>
                    <v-btn
                        color="green darken-1"
                        text
                        @click="btnCyclestart()"
                        :disabled="!doneSync"
                    >Start
                    </v-btn>
                    <v-btn
                        color="green darken-1"
                        text
                        @click="closeDialog()"
                    >Stop
                    </v-btn>
                </v-card-actions>
            </v-col>
        </v-card>
    </v-dialog>
</template>

<script>
import { convertStringToLocal, getHost, getRequestHeader, useRefreshToken } from "../../../../helper/common"
import { CycleStop, RouteOperation } from "../../../../helper/enums"
import ProgressCircular from "../../../shared/ProgressCircular.vue"
export default {
    components: {
        ProgressCircular
    },
    props: {
        showNotification: {
            type: Function
        }
    },
    data: () => ({
        dialogBool: false,
        doneSync: true,
        cube: null,
        reason: null,
        username: null,
        updatedAt: null
    }),
    methods: {
        openDialog(model, cube) {
            this.cube = cube
            this.reason = model.reason
            this.updatedAt = convertStringToLocal(model.updated_at, true)
            this.username = model.username
            this.dialogBool = true
        },
        closeDialog() {
            this.dialogBool = false
        },
        async btnCyclestart() {
            try {
                this.doneSync = false
                let url = getHost(this.cube) + RouteOperation.CYCLESTOP

                let req = await fetch(url, {
                    method: "POST",
                    body: JSON.stringify({
                        status: CycleStop.DISABLED
                    }),
                    headers: getRequestHeader()
                })
                let res = JSON.parse(await req.text())
                if (res.code === 401){ // If access token is unauthorized
                    // use refresh token to get new access token from auth server
                    return useRefreshToken(this, this.btnCyclestart)
                }
                if (res.status) {
                    this.showNotification(true, "TC is started successfully.")
                    this.closeDialog()
                } else {
                    this.showNotification(false, res.message)
                }
            } catch (error) {
                this.showNotification(false, error)
            } finally {
                setTimeout(() => {
                    this.doneSync = true
                }, 500)
            }
        }
    }
}
</script>
