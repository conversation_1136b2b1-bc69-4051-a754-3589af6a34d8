<template>
    <span>
        <v-row v-if="homeWhenRecoverSkycar">
            <v-col>
                <v-text-field
                    v-model="fromCoordX"
                    label="Coordinate X"
                    filled
                    rounded
                    type="number"
                    persistent-hint
                    :disabled="isCoordFieldsLocked"
                >
                    <template #prepend-inner>
                        <v-icon 
                            @click="openGrid()" 
                            style="margin-right: 5px;"
                        >
                            mdi-grid
                        </v-icon>
                    </template>
                </v-text-field>
            </v-col>
            <v-col>
                <v-text-field
                    v-model="fromCoordY"
                    label="Coordinate Y"
                    filled
                    rounded
                    type="number"
                    :disabled="isCoordFieldsLocked"
                >
                    <template #prepend-inner>
                        <v-icon 
                            @click="openGrid()" 
                            style="margin-right: 5px;"
                        >
                            mdi-grid
                        </v-icon>
                    </template>
                </v-text-field>
            </v-col>
        </v-row>
        <v-row>
            <v-col cols="6">
                <v-select
                    v-model="md"
                    label="Maintenance Dock"
                    filled
                    rounded
                    type="number"
                    :items="mdOptions"
                ></v-select>
            </v-col>
        </v-row>
        <v-row>
            <v-col>
                <v-alert
                    border="top"
                    color="green"
                    dark
                >
                    <v-checkbox
                        v-model="checkbox"
                        :label="getText()"
                        color="white"
                    >
                    </v-checkbox>
                    <CheckboxBypassCS
                        :showCheckbox="isDocked && isErrorOrMaintenance(status)"
                        ref="checkboxBypassCS"
                    />
                </v-alert>
            </v-col>
        </v-row>
        <v-card-actions>
            <v-spacer></v-spacer>
            <ProgressCircular :doneSync="doneSync"/>
            <v-btn
                color="green darken-1"
                text
                @click="btnConfirm()"
                :disabled="disableConfirm()"
            >Confirm
            </v-btn>
            <v-btn
                color="green darken-1"
                text
                @click="closeDialog()"
            >Close
            </v-btn>
        </v-card-actions>
        <DialogCoordinateSelection 
            ref="dialogCoordinateSelection" 
            @update-coord="updateCoord"
        />
    </span>
</template>

<script>
import { getHost, getRequestHeader, useRefreshToken } from "../../../../helper/common"
import { RouteMD, SkycarRecovery } from "../../../../helper/enums"
import { isErrorOrMaintenance, postRecoverSkycar } from "./utils.js"
import CheckboxBypassCS from "../CheckboxBypassCS.vue"
import DialogCoordinateSelection from "../../../dialogs/DialogCoordinateSelection"
import ProgressCircular from "../../../shared/ProgressCircular.vue"
import axios from "axios"
export default {
    components: {
        CheckboxBypassCS,
        DialogCoordinateSelection,
        ProgressCircular
    },
    props: {
        skycar: {
            type: Number
        },
        coordX: {
            type: Number
        },
        coordY: {
            type: Number
        },
        cube: {
            type: String
        },
        closeDialog: {
            type: Function
        },
        status: {
            type: String
        },
        isDocked: {
            type: Boolean
        },
        showNotification: {
            type: Function
        },
        syncSkycar: {
            type: Function
        },
        openDialogCyclestart: {
            type: Function
        },
        homeWhenRecoverSkycar: {
            type: Boolean
        },
        isCoordFieldsLocked: {
            type: Boolean,
            default: true
        }
    },
    async created() {
        await this.getMaintenanceDock()
        this.fromCoordX = this.coordX
        this.fromCoordY = this.coordY
    },
    data: () => ({
        isErrorOrMaintenance,
        fromCoordX: null,
        fromCoordY: null,
        md: null,
        mdCoord: {},
        mdOptions: [],
        checkbox: false,
        doneSync: true
    }),
    methods: {
        openGrid(){
            this.$refs.dialogCoordinateSelection.openDialog(this.cube)
        },
        updateCoord(selectedCells){
            if (selectedCells.length > 0) {
                this.fromCoordX = String(selectedCells[0].x)
                this.fromCoordY = String(selectedCells[0].y)
            }
        },
        getText() {
            if (this.homeWhenRecoverSkycar) {
                return `I have confirmed that coordinate (${this.mdCoord[this.md]}) is cleared, 
                        and I would like to create a request to 
                        home Skycar ${this.skycar} at coordinate (${this.fromCoordX}, ${this.fromCoordY})
                        and auto charge it out to Maintenance Dock ${this.md}.`
            } else {
                return `I have confirmed that coordinate (${this.mdCoord[this.md]}) is cleared, 
                        and I would like to create a request to
                        auto charge out Skycar ${this.skycar} to Maintenance Dock ${this.md}.`
            }
        },
        async getMaintenanceDock() {
            try {
                let url = getHost(this.cube) + RouteMD.MD
                let res = await axios.get(url, { headers:getRequestHeader() })
                res.data.data.forEach(md => {
                    if (md.type != "IN") {
                        this.mdCoord[md.maintenanceDockId] = md.nodeCoord
                    }
                })
                this.mdOptions = Object.keys(this.mdCoord)
                this.md = this.mdOptions[0]
            } catch (error) {
                if (error.response.status === 401){ // If access token is unauthorized
                    // use refresh token to get new access token from auth server
                    return useRefreshToken(this, this.getMaintenanceDock)
                }
                this.showNotification(false, error)
            }
        },
        async btnConfirm() {
            try {
                this.doneSync = false
                let bypassCs = this.$refs.checkboxBypassCS.getBypassCs()
                let res = await postRecoverSkycar(
                    this.showNotification, 
                    this.cube, 
                    this.skycar, 
                    this.md, 
                    SkycarRecovery.AUTO_CHARGE_OUT, 
                    null,
                    this.homeWhenRecoverSkycar ? `${this.fromCoordX},${this.fromCoordY}` : null,
                    bypassCs
                )
                if (!res.status) {
                    return
                }
                if (!res.model.status) {
                    this.openDialogCyclestart(res.model, this.cube)
                }
                this.closeDialog()
                setTimeout(() => {
                    this.syncSkycar()
                }, 500)
            } finally {
                this.checkbox = false
                this.doneSync = true
            }
        },
        disableConfirm() {
            if (!this.doneSync) {
                return true
            }
            if (this.md==null) {
                return true
            }
            if (!this.checkbox) {
                return true
            }
            return Boolean(
                this.fromCoordX === "" | this.fromCoordY === ""
            )
        },
        reset() {
            this.$refs.checkboxBypassCS.reset()
            this.checkbox = false
        }
    },
    watch: {
        coordX(x) {
            this.fromCoordX = x
        },
        coordY(y) {
            this.fromCoordY = y
        }
    }
}
</script>
