<template>
    <span>
        <v-row v-if="homeWhenRecoverSkycar">
            <v-col>
                <v-text-field
                    v-model="fromCoordX"
                    label="Coordinate X"
                    filled
                    rounded
                    type="number"
                    persistent-hint
                    :disabled="isCoordFieldsLocked"
                >
                    <template #prepend-inner>
                        <v-icon 
                            @click="openGrid()" 
                            style="margin-right: 5px;"
                        >
                            mdi-grid
                        </v-icon>
                    </template>
                </v-text-field>
            </v-col>
            <v-col>
                <v-text-field
                    v-model="fromCoordY"
                    label="Coordinate Y"
                    filled
                    rounded
                    type="number"
                    :disabled="isCoordFieldsLocked"
                >
                    <template #prepend-inner>
                        <v-icon 
                            @click="openGrid()" 
                            style="margin-right: 5px;"
                        >
                            mdi-grid
                        </v-icon>
                    </template>
                </v-text-field>
            </v-col>
        </v-row>
        <v-row>
            <v-col>
                <v-alert
                    border="top"
                    color="green"
                    dark
                >
                    <v-checkbox 
                        v-model="checkbox" 
                        :label="getText()" 
                        color="white"
                    />
                    <CheckboxBypassCS
                        :showCheckbox="isDocked && isErrorOrMaintenance(status)"
                        ref="checkboxBypassCS"
                    />
                </v-alert>
            </v-col>
        </v-row>
        <v-card-actions>
            <v-spacer></v-spacer>
            <ProgressCircular :doneSync="doneSync"/>
            <v-btn
                color="green darken-1"
                text
                @click="btnConfirm()"
                :disabled="disableConfirm()"
            >Confirm
            </v-btn>
            <v-btn
                color="green darken-1"
                text
                @click="closeDialog()"
            >Close
            </v-btn>
        </v-card-actions>
        <DialogCoordinateSelection 
            ref="dialogCoordinateSelection" 
            @update-coord="updateCoord"
        />
    </span>
</template>

<script>
import { SkycarRecovery } from "../../../../helper/enums"
import { isErrorOrMaintenance, postRecoverSkycar } from "./utils.js"
import CheckboxBypassCS from "../CheckboxBypassCS.vue"
import DialogCoordinateSelection from "../../../dialogs/DialogCoordinateSelection";
import ProgressCircular from "../../../shared/ProgressCircular.vue"
export default {
    components: {
        CheckboxBypassCS,
        DialogCoordinateSelection,
        ProgressCircular
    },
    props: {
        skycar: {
            type: Number
        },
        coordX: {
            type: Number
        },
        coordY: {
            type: Number
        },
        cube: {
            type: String
        },
        closeDialog: {
            type: Function
        },
        status: {
            type: String
        },
        isDocked: {
            type: Boolean
        },
        showNotification: {
            type: Function
        },
        syncSkycar: {
            type: Function
        },
        openDialogCyclestart: {
            type: Function
        },
        homeWhenRecoverSkycar: {
            type: Boolean
        },
        isCoordFieldsLocked: {
            type: Boolean,
            default: true
        }
    },
    created() {
        this.fromCoordX = this.coordX
        this.fromCoordY = this.coordY
    },
    data: () => ({
        isErrorOrMaintenance,
        fromCoordX: null,
        fromCoordY: null,
        doneSync: true,
        checkbox: false
    }),
    methods: {
        openGrid(){
            this.$refs.dialogCoordinateSelection.openDialog(this.cube)
        },
        updateCoord(selectedCells){
            if (selectedCells.length > 0) {
                this.fromCoordX = String(selectedCells[0].x)
                this.fromCoordY = String(selectedCells[0].y)
            }
        },
        async btnConfirm() {
            try {
                this.doneSync = false
                let bypassCs = this.$refs.checkboxBypassCS.getBypassCs()
                let res = await postRecoverSkycar(
                    this.showNotification, 
                    this.cube, 
                    this.skycar, 
                    null, 
                    SkycarRecovery.REVIVE, 
                    null,
                    this.homeWhenRecoverSkycar ? `${this.fromCoordX},${this.fromCoordY}` : null,
                    bypassCs
                )
                if (!res.status) {
                    return
                }
                if (!res.model.status) {
                    this.openDialogCyclestart(res.model, this.cube)
                }
                this.closeDialog()
            } finally {
                this.doneSync = true
            }
        },
        disableConfirm() {
            if (!this.doneSync) {
                return true
            }
            if (!this.checkbox) {
                return true
            }
            if (this.homeWhenRecoverSkycar) {
                return Boolean(
                    this.fromCoordX === "" || 
                    this.fromCoordY === "" || 
                    this.fromCoordX === undefined || 
                    this.fromCoordY === undefined || 
                    this.fromCoordX === "undefined" || 
                    this.fromCoordY === "undefined"
                )
            }
            return false
        },
        getText() {
            if (this.homeWhenRecoverSkycar) {
                return `I would like to home, pair, and charge in 
                    Skycar ${this.skycar} at (${this.fromCoordX}, ${this.fromCoordY }).`
            } else {
                return `I would like to pair, and charge in 
                    Skycar ${this.skycar} at (${this.fromCoordX}, ${this.fromCoordY }).`
            }
            },
        reset() {
            this.$refs.checkboxBypassCS.reset()
            this.checkbox = false
        }
    },
    watch: {
        coordX(x) {
            this.fromCoordX = x
        },
        coordY(y) {
            this.fromCoordY = y
        }
    }
}
</script>
