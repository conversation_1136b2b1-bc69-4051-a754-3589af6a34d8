const { getHost, getRequestHeader, useRefreshToken } = require("../../../../helper/common")
const { RouteOperation, SkycarStatus } = require("../../../../helper/enums")

module.exports = {
    postRecoverSkycar,
    isErrorOrMaintenance
}

async function postRecoverSkycar(showNotification, cube, skycar, dock, mode, coord, fromCoord=null, bypassCs=true) {
  try {
    let url = getHost(cube) + RouteOperation.SKYCAR_ACTION_WITHOUT_SHELL

    let req = await fetch(url, {
      method: "POST",
      body: JSON.stringify({
        sid: skycar,
        mid: dock,
        mode: mode,
        coord: coord,
        "from_coord": fromCoord,
        // eslint-disable-next-line camelcase
        bypass_cs: bypassCs
      }),
      headers: getRequestHeader()
    })
    let res = JSON.parse(await req.text())

    if (res.code === 401){ // If access token is unauthorized
      // use refresh token to get new access token from auth server 
      return useRefreshToken(this, 
        this.postRecoverSkycar, 
        showNotification, 
        cube, skycar, dock, 
        mode, coord, fromCoord, bypassCs)
    }

    if (res.status) {
      showNotification(true, `Request to recover Skycar ${ skycar } is submitted.`)
      return { status: true, model: res.model }
    } else {
      showNotification(false, res.message)
      return { status: false }
    }
  } catch (error) {
    showNotification(false, error)
    return { status: false }
  }
}

function isErrorOrMaintenance(status) {
  return Boolean(status == SkycarStatus.ERROR | status == SkycarStatus.MAINTENANCE)
}
