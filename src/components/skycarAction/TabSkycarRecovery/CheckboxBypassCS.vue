<template>
    <v-checkbox
        v-if="showCheckbox"
        v-model="bypassCS"
        :label="getText()"
        color="white"
    >
    </v-checkbox>
</template>

<script>
export default {
    props: {
        showCheckbox: {
            type: Boolean
        }
    },
    data: () => ({
        bypassCS: false
    }),
    methods: {
        getText() {
            return "I have confirmed that Charging Station is homed, and I would like to bypass the validation."
        },
        getBypassCs() {
            if (!this.showCheckbox) {
                return false
            }
            return this.bypassCS
        },
        reset() {
            this.bypassCS = false
        }
    }
}
</script>
