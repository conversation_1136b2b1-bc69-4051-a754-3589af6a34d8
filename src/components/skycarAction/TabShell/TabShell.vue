<template>
    <v-card>
        <v-col>
            <v-row v-if="history">
                <v-col>
                    <v-stepper
                        v-if="shell.message"
                        v-model="step" 
                        vertical 
                        dark
                    >
                        <v-card-title style="color: white;">Latest Message</v-card-title>
                        <v-stepper-step
                            step="1"
                            complete
                        >Message "{{ shell.message }}" is sent.
                        </v-stepper-step>

                        <v-stepper-step
                            step="2"
                            :complete="Boolean(shell.ackedAt)"
                        >Message is acked at {{ shell.ackedAt }}.
                        </v-stepper-step>

                        <span v-if="shell.errorCode">
                            <v-stepper-step
                                step="4"
                                :complete="Boolean(shell.doneAt)"
                                :rules="[() => false]"
                            >Message is errored at {{ shell.doneAt }}.
                            </v-stepper-step>
                            <v-stepper-content step="4">
                                <v-card dark>
                                    <v-card-text>
                                        <div>
                                            <span>Error Code: {{ shell.errorCode }}</span><br>
                                            <span>Error Name: {{ shell.errorName }}</span><br>
                                            <span>Error Detail: {{ shell.errorDetail }}</span>
                                        </div>
                                    </v-card-text>
                                </v-card>
                            </v-stepper-content>
                        </span>
                        <v-stepper-step
                            v-else
                            step="3"
                            :complete="Boolean(shell.doneAt)"
                        >Message is completed at {{ shell.doneAt }}.
                        </v-stepper-step>
                    </v-stepper>
                    <span v-else>Skycar {{ skycar }} has no shell message.</span>
                </v-col>
            </v-row>
            <v-row v-if="hasPending()">
                <v-col>
                    <v-checkbox
                        v-model="checkbox"
                        :label="getClearText()"
                        color="red"
                    >
                    </v-checkbox>
                </v-col>
            </v-row>
            <span v-else>
                <v-row>
                    <v-col cols="4">
                        <v-select
                            v-model="mode"
                            label="Winch Mode"
                            :items="modes"
                            filled
                            rounded
                        ></v-select>
                    </v-col>
                    <v-col 
                        cols="4"
                        v-if="mode != Mode.HOME"
                    >
                        <v-select
                            v-model="position"
                            label="Position"
                            rounded
                            filled
                            :items="Object.values(StoragePosition)"
                        ></v-select>
                    </v-col>
                    <v-col 
                        cols="4"
                        v-if="mode != Mode.HOME"
                    >
                        <v-text-field
                            v-model="z"
                            label="Quantity"
                            rounded
                            filled
                            type='number'
                        ></v-text-field>
                    </v-col>
                </v-row>
                <v-row>
                    <v-col>
                        <v-checkbox
                            v-model="checkbox"
                            :label="getConfirmText()"
                            color="red"
                        >
                        </v-checkbox>
                    </v-col>
                </v-row>
            </span>
            <v-card-actions>
                <v-btn
                    v-if="history"
                    color="green darken-1"
                    text
                    @click="btnHistory(false)"
                >Hide Latest Status
                </v-btn>
                <v-btn
                    v-else
                    color="green darken-1"
                    text
                    @click="btnHistory(true)"
                >Show Latest Status
                </v-btn>
                <v-spacer></v-spacer>
                <ProgressCircular :doneSync="refresh"/>
                <v-btn
                    color="green darken-1"
                    text
                    @click="btnRefresh()"
                    :disabled="disableRefresh()"
                >Refresh
                </v-btn>
                <span v-if="hasPending()">
                    <ProgressCircular :doneSync="clear"/>
                    <v-btn
                        color="green darken-1"
                        text
                        @click="btnClear()"
                        :disabled="disableClear()"
                    >Confirm
                    </v-btn>
                </span>
                <span v-else>
                    <ProgressCircular :doneSync="confirm"/>
                    <v-btn
                        color="green darken-1"
                        text
                        @click="btnConfirm()"
                        :disabled="disableConfirm()"
                    >Confirm
                    </v-btn>
                </span>
                <v-btn
                    color="green darken-1"
                    text
                    @click="closeDialog()"
                >Close
                </v-btn>
            </v-card-actions>
        </v-col>
    </v-card>
</template>

<script>
import { RouteMatrix, StoragePosition } from "../../../helper/enums"
import { convertStringToLocal, getRequestHeader, useRefreshToken } from "../../../helper/common"
import { getHost } from "../../../helper/common"
import ProgressCircular from "../../shared/ProgressCircular.vue"
import axios from "axios"
export default {
    components: {
        ProgressCircular
    },
    props: {
        skycar: {
            type: Number
        },
        coordX: {
            type: Number
        },
        coordY: {
            type: Number
        },
        cube: {
            type: String
        },
        refreshShell: {
            type: Number
        },
        closeDialog: {
            type: Function
        },
        showNotification: {
            type: Function
        }
    },
    created() {
        this.btnRefresh()
    },
    data: () => ({
        StoragePosition,
        Mode,
        step: 4,
        history: false,
        checkbox: false,
        refresh: true,
        confirm: true,
        clear: true,
        shell: {
            message: null,
            ackedAt: null,
            doneAt: null,
            errorCode: null,
            errorName: null,
            errorDetail: null
        },
        z: 1,
        position: StoragePosition.BOTH,
        mode: Mode.HOME,
        modes: Object.values(Mode)
    }),
    methods: {
        async btnHistory(history) {
            if (history) {
                await this.btnRefresh()
            }
            this.history = history
        },
        async btnRefresh() {
            try {
                this.refresh = false
                let url = getHost(this.cube) + RouteMatrix.SHELL
                let res = await axios.get(url, { headers:getRequestHeader() })
                let obj = res.data.model[this.skycar]
                if (obj) {
                    this.shell = {
                        message: obj.full_msg,
                        ackedAt: obj.acked_at ? convertStringToLocal(obj.acked_at, true) : null,
                        doneAt: obj.done_at ? convertStringToLocal(obj.done_at, true) : null,
                        errorCode: obj.error_code,
                        errorName: obj.error_name,
                        errorDetail: obj.error_detail
                    }
                } else {
                    this.shell = {
                        message: null,
                        ackedAt: null,
                        doneAt: null,
                        errorCode: null,
                        errorName: null,
                        errorDetail: null
                    }
                }
            } catch(error){
                console.log(error)
                if (error.response.status === 401){ // If access token is unauthorized
                    // use refresh token to get new access token from auth server
                    return useRefreshToken(this, this.btnRefresh)
                }
            }
            finally {
                this.checkbox = false
                setTimeout(() => {
                    this.refresh = true
                }, 500)
            }
        },
        async btnClear() {
            try {
                this.clear = false
                let url = getHost(this.cube) + RouteMatrix.SHELL
                await axios.delete(url, {
                    params: {
                        sid: this.skycar
                    }
                }, { headers:getRequestHeader() })
                this.showNotification(true, "Shell message is cleared successfully.")
            } catch (error) {
                if (error.response.status === 401){ // If access token is unauthorized
                    // use refresh token to get new access token from auth server
                    return useRefreshToken(this, this.btnClear)
                }
                this.showNotification(false, error)
            } finally {
                this.checkbox = false
                this.clear = true
            }
        },
        disableRefresh() {
            if (!this.refresh) {
                return true
            }
            return false
        },
        showCheckbox() {
            if (!this.shell.message) {
                return false
            }
            if (this.shell.doneAt) {
                return false
            }
            return true
        },
        disableClear() {
            if (!this.clear) {
                return true
            }
            if (!this.shell.message) {
                return true
            }
            if (this.shell.doneAt) {
                return true
            }
            if (!this.checkbox) {
                return true
            }
            return false
        },
        async btnConfirm() {
            try {
                this.confirm = false

                let url = getHost(this.cube) + RouteMatrix.SHELL
                let req = await fetch(url, {
                    method: "POST",
                    body: JSON.stringify({
                        sid: this.skycar,
                        mode: this.mode,
                        z: this.z,
                        position: this.position
                    }),
                    headers: getRequestHeader()
                })
                let res = JSON.parse(await req.text())
                if (res.code === 401){ // If access token is unauthorized
                    // use refresh token to get new access token from auth server
                    return useRefreshToken(this, this.btnConfirm)
                }
                if (res.status) {
                    this.showNotification(true, "Shell message is triggered successfully.")
                } else {
                    this.showNotification(false, res.message)
                }
            } catch (error) {
                this.showNotification(false, error)
            } finally {
                this.checkbox = false
                this.confirm = true
            }
        },
        disableConfirm() {
            if (!this.confirm) {
                return true
            }
            if (!this.checkbox) {
                return true
            }
            if (!this.z) {
                return true
            }
            return false
        },
        getClearText() {
            return `I would like to trigger error on behalf of the skycar. I am sure that the 
                    skycar will remain at its current location to avoid unsynchronized 
                    data between software and hardware.`
        },
        getConfirmText() {
            return `Skycar ${this.skycar} is currently at (${this.coordX}, ${this.coordY}). 
                    I would like to manually winch the skycar.`
        },
        hasPending() {
            if (!this.shell.message) {
                return false
            }
            if (this.shell.doneAt) {
                return false
            }
            return true
        }
    },
    watch: {
        refreshShell() {
            this.btnRefresh()
        },
        skycar() {
            this.btnRefresh()
        }
    }
}

const Mode = {
    HOME: "HOME",
    DOWN: "DOWN",
    DROP: "DROP",
    PICK: "PICK",
    UP: "UP"
}
</script>
