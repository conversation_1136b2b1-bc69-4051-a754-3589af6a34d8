<template>
  <v-dialog
    v-model="dialogBool"
    max-width="800"
  >
    <v-tabs
      dark
      v-model="tabs"
      align-with-title
    >
      <v-tabs-slider></v-tabs-slider>
      <v-tab
        v-for="item in tabOptions"
        :key="item"
        :href="'#' + item"
      >{{ item }}</v-tab>
    </v-tabs>
    <v-tabs-items v-model="tabs">
      <v-tab-item :value="TabItem.SkycarRecovery" eager>
        <TabSkycarRecovery
          :skycar="skycar"
          :status="status"
          :coordX="coordX"
          :coordY="coordY"
          :winch="winch"
          :isDocked="isDocked"
          :cube="cube"
          :closeDialog="closeDialog"
          :showNotification="showNotification"
          :syncSkycar="syncSkycar"
          :battery="battery"
          ref="tabSkycarRecovery"
        />
      </v-tab-item>
      <v-tab-item :value="TabItem.TriggerError" eager>
        <TabTriggerError
          :skycar="skycar"
          :winch="winch"
          :coordX="coordX"
          :coordY="coordY"
          :cube="cube"
          :closeDialog="closeDialog"
          :showNotification="showNotification"
          :syncSkycar="syncSkycar"
          ref="tabTriggerError"
        />
      </v-tab-item>
      <v-tab-item :value="TabItem.SwapBin">
        <TabSwapBin
          :currentSkycar="skycar"
          :winch="winch"
          :newSkycarOptions="swapBinOptions"
          :cube="cube"
          :closeDialog="closeDialog"
          :showNotification="showNotification"
          :syncSkycar="syncSkycar"
        />
      </v-tab-item>
      <v-tab-item :value="TabItem.OverwriteStorage">
        <TabOverwriteStorage
          :skycar="skycar"
          :winch="winch"
          :cube="cube"
          :closeDialog="closeDialog"
          :showNotification="showNotification"
          :syncSkycar="syncSkycar"
        />
      </v-tab-item>
      <v-tab-item :value="TabItem.Winch">
        <TabWinch
          :skycar="skycar"
          :winch="winch"
          :cube="cube"
          :closeDialog="closeDialog"
          :showNotification="showNotification"
          :syncSkycar="syncSkycar"
        />
      </v-tab-item>
      <v-tab-item :value="TabItem.FlagStorage" eager>
        <TabFlagStorage
          :skycar="skycar"
          :winch="winch"
          :cube="cube"
          :closeDialog="closeDialog"
          :showNotification="showNotification"
          :syncSkycar="syncSkycar"
          ref="tabFlagStorage"
        />
      </v-tab-item>
      <v-tab-item :value="TabItem.ManualMode">
        <TabManualMode
          :skycar="skycar"
          :mode="mode"
          :cube="cube"
          :showNotification="showNotification"
          :closeDialog="closeDialog"
          :syncSkycar="syncSkycar"
        />
      </v-tab-item>
    </v-tabs-items>
  </v-dialog>
</template>

<script>
import TabFlagStorage from "./TabFlagStorage/TabFlagStorage.vue"
import TabManualMode from "./TabManualMode/TabManualMode.vue"
import TabOverwriteStorage from "./TabOverwriteStorage/TabOverwriteStorage.vue"
import TabSkycarRecovery from "../skycarAction/TabSkycarRecovery/TabSkycarRecovery.vue"
import TabSwapBin from "./TabSwapBin/TabSwapBin.vue"
import TabTriggerError from "./TabTriggerError/TabTriggerError.vue"
import TabWinch from "./TabWinch/TabWinch.vue"
import { SkycarStatus } from "../../helper/enums"
import { getNoOfStorages } from "../../helper/common"
export default {
    components: {
        TabFlagStorage,
        TabManualMode,
        TabOverwriteStorage,
        TabSkycarRecovery,
        TabSwapBin,
        TabTriggerError,
        TabWinch
    },
    props: {
        showNotification: {
            type: Function
        },
        syncSkycar: {
            type: Function
        }
    },
    data: () => ({
        TabItem,
        SkycarStatus,
        dialogBool: false,
        skycar: null,
        status: null,
        coordX: null,
        coordY: null,
        winch: null,
        isDocked: null,
        cube: null,
        swapBinOptions: [],
        battery: null,
        mode: "",
        tabs: TabItem.SkycarRecovery,
        tabOptions: [],
        refreshShell: -1
    }),
    methods: {
        openDialog(skycar, status, coordX, coordY, winch, isDocked, cube, swapBinOptions, battery, mode) {
            this.dialogBool = true
            this.skycar = skycar
            this.status = status
            this.coordX = coordX
            this.coordY = coordY
            this.winch = winch
            this.isDocked = isDocked
            this.cube = cube
            this.swapBinOptions = swapBinOptions
            this.battery = battery
            this.mode = mode
            this.getTabs()
        },
        closeDialog() {
            this.dialogBool = false
        },
        refreshResetShell() {
            this.refreshShell *= -1
        },
        getTabs() {
            let isDualWinch = Object.keys(this.winch).length > 1
            this.tabOptions = [TabItem.SkycarRecovery]
            if (
                [SkycarStatus.AVAILABLE, SkycarStatus.DOCKED].includes(this.status) ||
                (this.status == SkycarStatus.ERROR && isDualWinch)
            ) {
                this.tabOptions.push(TabItem.TriggerError)
            }
            if (this.status==SkycarStatus.MAINTENANCE && getNoOfStorages(this.winch) !== 0) {
                this.tabOptions.push(TabItem.SwapBin)
            }
            // if (this.status==SkycarStatus.ERROR && getNoOfStorages(this.winch) !== 0) {
            //     this.tabOptions.push(TabItem.OverwriteStorage)
            // }
            if (this.status==SkycarStatus.ERROR) {
                this.tabOptions.push(TabItem.FlagStorage)
            }
            if (isDualWinch) {
                this.tabOptions.push(TabItem.Winch)
            }
            if (this.mode != "Error") {
              this.tabOptions.push(TabItem.ManualMode);
            }
            this.tabs = this.tabOptions[0]
        },
        reset() {
            this.$refs.tabSkycarRecovery.reset()
            this.$refs.tabTriggerError.reset()
            this.$refs.tabFlagStorage.reset()
        }
    },
    watch: {
        dialogBool(bool) {
            if (!bool) {
                this.reset()
                return
            }
            if (this.tabs != TabItem.ResetShell) {
                return
            }
            this.refreshResetShell()
        },
        tabs() {
            this.reset()
        }
    }
}

const TabItem = {
    SkycarRecovery: "Skycar Recovery",
    TriggerError: "Trigger Error",
    SwapBin: "Swap Bin",
    OverwriteStorage: "Overwrite Storage",
    Winch: "Winch",
    FlagStorage: "Flag Storage",
    ManualMode: "Manual Mode"
}
</script>
