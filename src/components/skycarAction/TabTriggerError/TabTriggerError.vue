<template>
  <v-card>
      <v-col>
      <v-row>
        <v-col>
          <v-text-field
            v-model="skycar"
            label="Skycar ID"
            rounded
            filled
            readonly
            class="text-field-with-icon"
          >
            <template #append>
              <v-btn 
                icon 
                @click="onCameraClick"
                :loading="!doneSync" 
                class="icon-centered"
              >
                <v-icon>mdi-qrcode-scan</v-icon>
              </v-btn>
            </template>
          </v-text-field>
        </v-col>
        <v-col>
          <v-text-field
            v-model="storage"
            label="Storage"
            rounded
            filled
            readonly
          />
        </v-col>
      </v-row>
      <v-row>
        <v-col 
          cols="12"
          sm="6" 
        >
          <v-text-field
            v-model="innerCoordX"
            label="Coordinate X"
            rounded
            filled
            type="number"
            :disabled="isCoordFieldsLocked"
          >
            <template #prepend-inner>
              <v-icon 
                @click="openGrid()" 
                style="margin-right: 5px;"
                :disabled="isCoordFieldsLocked"
              >
                mdi-grid
              </v-icon>
            </template>
          </v-text-field>
        </v-col>
        <v-col 
          cols="12"
          sm="6" 
        >
          <v-text-field
            v-model="innerCoordY"
            label="Coordinate Y"
            rounded
            filled
            type="number"
            :disabled="isCoordFieldsLocked"
          >
            <template #prepend-inner>
              <v-icon 
                @click="openGrid()" 
                style="margin-right: 5px;"
                :disabled="isCoordFieldsLocked"
              >
                mdi-grid
              </v-icon>
            </template>
          </v-text-field>
        </v-col>
      </v-row>
      <v-row v-if="Object.keys(winch).length > 1">
        <v-col>
          <v-select
            rounded
            v-model="errorPosition"
            label="Error Type Skycar | Winch"
            :items="errorPositionItems"
            filled
          />
        </v-col>
      </v-row>
      <v-row>
        <v-col>
          <v-textarea
            v-model="remark"
            clearable
            label="Remark"
            rounded
            filled
          />
        </v-col>
      </v-row>
      <v-row>
        <v-col>
          <v-alert 
            border="top" 
            color="red" 
            dark
          >
            <v-checkbox 
              v-model="checkbox" 
              :label="getText()" 
              color="white"
            />
          </v-alert>
        </v-col>
      </v-row>
      <v-card-actions>
        <v-spacer />
        <ProgressCircular :doneSync="doneSync" />
        <v-btn
          color="green darken-1"
          text
          @click="btnConfirm()"
          :disabled="disableConfirm()"
        >
          Confirm
        </v-btn>
        <v-btn 
          color="green darken-1" 
          text 
          @click="closeDialog()"
        >
          Close 
        </v-btn>
      </v-card-actions>
      
      <v-dialog
        v-model="showConfirmationDialog"
        max-width="500"
      >
        <v-card>
          <v-card-title class="headline">
            Confirmation
          </v-card-title>
          <v-card-text>
            Make sure skycar already stopped before scanning QR code
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn
              color="green darken-1"
              text
              @click="handleConfirmScan"
            >
              Confirm
            </v-btn>
            <v-btn
              color="grey darken-1"
              text
              @click="showConfirmationDialog = false"
            >
              Cancel
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
      <DialogCoordinateSelection 
        ref="dialogCoordinateSelection" 
        @update-coord="updateCoord"
      />
    </v-col>
  </v-card>
</template>

<script>
import { RouteError, Websocket } from "../../../helper/enums";
import {
  getHost,
  getStorageNo,
  getRequestHeader,
  useRefreshToken,
} from "../../../helper/common";
import ProgressCircular from "../../shared/ProgressCircular.vue";
import DialogCoordinateSelection from "../../dialogs/DialogCoordinateSelection";
import axios from "axios";
import { socket } from "../../../App.vue";

export default {
  components: {
    ProgressCircular,
    DialogCoordinateSelection
  },
  props: {
    skycar: {
      type: Number,
    },
    winch: {
      type: Object,
    },
    coordX: {
      type: Number,
    },
    coordY: {
      type: Number,
    },
    cube: {
      type: String,
    },
    closeDialog: {
      type: Function,
    },
    showNotification: {
      type: Function,
    },
    syncSkycar: {
      type: Function,
    },
  },
  created() {
    this.innerCoordX = "";
    this.innerCoordY = "";
    this.updateStorageNo();
    this.getMessage(socket);
  },
  beforeDestroy() {
    // Clean up socket listeners
    if (socket) {
      socket.off(Websocket.UPDATE_MATRIX);
    }
  },
  data: () => ({
    getStorageNo,
    remark: null,
    checkbox: false,
    storage: null,
    innerCoordX: null,
    innerCoordY: null,
    doneSync: true,
    errorPosition: "",
    errorPositionItems: [
      { text: "Skycar is not operational", value: "" },
      { text: "Left winch unavailable (A)", value: "A" },
      { text: "Right winch unavailableh (B)", value: "B" },
    ],
    isCoordFieldsLocked: true,
    showConfirmationDialog: false,
  }),
  methods: {
    openGrid(){
      this.$refs.dialogCoordinateSelection.openDialog(this.cube)
    },
    updateCoord(selectedCells){
      if (selectedCells.length > 0) {
        this.innerCoordX = String(selectedCells[0].x);
        this.innerCoordY = String(selectedCells[0].y);
      }
    },
    async btnConfirm() {
      try {
        this.doneSync = false;
        let url = getHost(this.cube) + RouteError.MOCK_ERROR;
        let res = await axios.post(
          url,
          {
            sid: this.skycar,
            position: this.errorPosition,
            x: this.innerCoordX,
            y: this.innerCoordY,
            remark: this.remark
          },
          { headers: getRequestHeader() }
        );
        if (res.data.status) {
          this.closeDialog();
          setTimeout(() => {
            this.syncSkycar();
          }, 500);
        } else {
          this.showNotification(false, res.data.message);
        }
      } catch (error) {
        if (error.response.status === 401) {
          // If access token is unauthorized
          // use refresh token to get new access token from auth server
          return useRefreshToken(this, this.btnConfirm);
        }
        this.showNotification(false, error);
      } finally {
        this.checkbox = false;
        this.remark = null
        this.doneSync = true;
      }
    },
    disableConfirm() {
      if (!this.doneSync) {
        return true;
      }
      if (this.innerCoordX === "") {
        return true;
      }
      if (this.innerCoordY === "") {
        return true;
      }
      if (!this.checkbox) {
        return true;
      }
      if (!this.remark) {
        return true
      }
      return false;
    },
    getText() {
      return `Skycar ${this.skycar} is not responding. 
                    I would like to trigger error on behalf of the skycar. 
                    I am sure that the skycar will remain at (${this.innerCoordX},${this.innerCoordY}) 
                    to avoid unsynchronized data between software and hardware.`;
    },
    updateStorageNo() {
      this.storage = this.getStorageNo(this.winch);
    },
    reset() {
      this.checkbox = false;
      this.remark = null;
      this.isCoordFieldsLocked = true;
      this.innerCoordX = "";  
      this.innerCoordY = "";
    },
    onCameraClick() {
      this.showConfirmationDialog = true;
    },
    
    async handleConfirmScan() {
      this.showConfirmationDialog = false;
      try {
        this.doneSync = false;
        let url = getHost(this.cube) + RouteError.QR_CODE;

        // Create a timeout promise specifically for QR scan
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => {
                reject(new Error('QR scan request timeout - no response received'));
            }, 10000); // 10 second timeout
        });

        // Create the QR scan API request promise
        const apiPromise = axios.get(
            url,
            { 
                params: { sid: this.skycar },
                headers: getRequestHeader() 
            }
        );

        // Race between timeout and QR scan request
        const res = await Promise.race([apiPromise, timeoutPromise]);

        if (res.data.status) {
            this.innerCoordX = res.data.data.x;
            this.innerCoordY = res.data.data.y;
            this.isCoordFieldsLocked = true;
        } else {
            console.log('Error message from API:', res.data.message);
            this.showNotification(false, res.data.message);
            // Only unlock for QR code reading errors
            if (res.data.message && res.data.message.includes("Failed to read QR code")) {
                this.isCoordFieldsLocked = false;
                this.innerCoordX = "";
                this.innerCoordY = "";
            }
        }
    } catch (error) {
        if (error.response?.status === 401) {
            return useRefreshToken(this, this.handleConfirmScan);
        }
        // Unlock coordinates for QR scan timeout/failure or network errors
        if (error.message?.includes('QR scan request timeout') || 
            error.message?.includes('Network Error') ||
            !error.response) { // This catches cases where API is unreachable
            this.isCoordFieldsLocked = false;
            this.innerCoordX = "";
            this.innerCoordY = "";
        }
        this.showNotification(false, error.message || 'Failed to scan QR code');
    } finally {
        this.doneSync = true;
    }
    },
    getMessage(socket) {
      console.log('Setting up WebSocket listeners');
      
      socket.on(Websocket.UPDATE_MATRIX, (message) => {
          console.log('Received QR code data:', message);
          
          const data = message.item;
          
          if (data && data.skycar_id === this.skycar) {
              // Handle scan request confirmation - don't unlock fields
              if (data.data && data.data.includes("scan request sent")) {
                  this.showNotification(true, data.data);
                  return;
              }

              // Handle successful coordinate update
              if (data.status && data.coordinates && data.coordinates.x !== null && data.coordinates.y !== null) {
                  console.log('Updating coordinates:', data.coordinates);
                  this.innerCoordX = String(data.coordinates.x);
                  this.innerCoordY = String(data.coordinates.y);
                  this.isCoordFieldsLocked = true;
                  
                  if (data.data) {
                      this.showNotification(true, data.data);
                  }
              } 
              // Handle errors - unlock fields for all errors except "scan request pending"
              else if (!data.status && data.data) {
                  const errorMessage = data.data;
                  const isPendingMessage = errorMessage.includes("scan request is already pending");
                  
                  if (!isPendingMessage) {
                      this.isCoordFieldsLocked = false;
                      this.innerCoordX = "";
                      this.innerCoordY = "";
                  }
                  
                  this.showNotification(false, errorMessage);
              }
          }
      });
    }
  },
  watch: {
    coordX(x) {
      this.innerCoordX = x;
    },
    coordY(y) {
      this.innerCoordY = y;
    },
    winch() {
      this.updateStorageNo();
    },
  },
};
</script>

<style scoped>
.text-field-with-icon {
  position: relative;
}

.icon-centered {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1; /* Ensure it's above the input background */
}

.icon-centered v-icon {
  font-size: 20px; /* Adjust icon size as needed */
}
</style>
