<template>
    <v-card>
        <v-col>
            <v-row>
                <v-col cols="6">
                    <v-text-field
                        v-model="skycar"
                        label="Skycar ID"
                        rounded
                        filled
                        readonly
                    ></v-text-field>
                </v-col>
                <v-col>
                    <v-text-field
                        v-model="currentStorage"
                        label="Current Storage No."
                        rounded
                        filled
                        readonly
                    ></v-text-field>
                </v-col>
            </v-row>
            <v-row>
                <v-col>
                    <v-text-field
                        v-model="currentStorageCode"
                        label="Current Storage Code"
                        rounded
                        filled
                        type='number'
                        hint="Please insert storage code without prefix."
                    ></v-text-field>
                </v-col>
                <v-col>
                    <v-text-field
                        v-model="newStorageCode"
                        label="New Storage Code"
                        rounded
                        filled
                        type='number'
                        hint="Please insert storage code without prefix."
                    ></v-text-field>
                </v-col>
            </v-row>
            <v-row>
                <v-col>
                    <v-alert
                        border="top"
                        color="red"
                        dark
                    >
                        <v-checkbox
                            v-model="checkbox"
                            :label="getText()"
                            color="white"
                        >
                        </v-checkbox>
                    </v-alert>
                </v-col>
            </v-row>
            <v-card-actions>
                <v-spacer></v-spacer>
                <ProgressCircular :doneSync="doneSync"/>
                <v-btn
                    color="green darken-1"
                    text
                    @click="btnConfirm()"
                    :disabled="disableConfirm()"
                >Confirm
                </v-btn>
                <v-btn
                    color="green darken-1"
                    text
                    @click="closeDialog()"
                >Close
                </v-btn>
            </v-card-actions>
        </v-col>
    </v-card>
</template>

<script>
import { RouteRunTime } from "../../../helper/enums"
import { getHost, getStorageNo, getRequestHeader, useRefreshToken } from "../../../helper/common"
import ProgressCircular from "../../shared/ProgressCircular.vue"
import axios from "axios"
export default {
    props: {
        skycar: {
            type: Number
        },
        winch: {
            type: Object 
        },
        cube: {
            type: String
        },
        closeDialog: {
            type: Function
        },
        showNotification: {
            type: Function
        },
        syncSkycar: {
            type: Function
        }
    },
    components: {
        ProgressCircular
    },
    data: () => ({
        getStorageNo,
        currentStorageCode: null,
        newStorageCode: null,
        checkbox: false,
        doneSync: true
    }),
    created() {
        this.updateStorageNo()
    },
    methods: {
        async btnConfirm() {
            try {
                this.doneSync = false
                let url = getHost(this.cube) + RouteRunTime.UPDATE_STORAGE_CODE
                let promise = axios.patch(url, {
                    // eslint-disable-next-line camelcase
                    storage_code: this.currentStorageCode,
                    // eslint-disable-next-line camelcase
                    new_storage_code: this.newStorageCode
                }, { headers:getRequestHeader() })
                await promise.then(() => {
                    this.showNotification(true, "Storage is overwrited successfully.")
                    this.closeDialog()
                    this.syncSkycar()
                }).catch((res) => {
                    if (res.response.status === 401){ // If access token is unauthorized
                        // use refresh token to get new access token from auth server
                        return useRefreshToken(this, this.btnConfirm)
                    }
                    this.showNotification(false, res.response.data.message)
                })
            } finally {
                this.doneSync = true
                this.checkbox = false
            }
        },
        disableConfirm() {
            if (!this.doneSync) {
                return true
            }
            if (this.currentStorageCode==="" | this.currentStorageCode==null) {
                return true
            }
            if (this.newStorageCode==="" | this.newStorageCode==null) {
                return true
            }
            if (!this.checkbox) {
                return true
            }
            return false
        },
        getText() {
            return `Skycar ${this.skycar} detects mismatch storage during enrollment process. 
                    I have confirmed that the current bin carried is Storage 
                    ${this.newStorageCode} instead of Storage ${this.currentStorageCode}.`
        },
        updateStorageNo() {
            this.currentStorage = this.getStorageNo(this.winch)
        }
    },
    watch: {
        winch() {
            this.updateStorageNo()
        }
    }
}
</script>
