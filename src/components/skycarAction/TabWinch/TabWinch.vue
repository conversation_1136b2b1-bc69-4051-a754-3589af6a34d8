<template>
    <v-card>
        <v-col>
            <v-row>
                <v-col>
                    <v-text-field
                        v-model="skycar"
                        label="Skycar ID"
                        rounded
                        filled
                        readonly
                    ></v-text-field>
                </v-col>
                <v-col>
                    <v-select
                        v-model="mappedPosition"
                        label="Position"
                        rounded
                        filled
                        :items="getPositions()"
                        @change="handlePosition"
                    ></v-select>
                </v-col>
            </v-row>
            <v-row>
                <v-col>
                    <v-alert
                        v-if="!winch[position].is_active"
                        border="top"
                        color="green"
                        dark
                    >
                        <v-checkbox
                            v-model="active_checkbox"
                            :label="getActiveText()"
                            color="white"
                        >
                        </v-checkbox>
                    </v-alert>
                    <v-alert
                        v-if="winch[position].is_active"
                        border="top"
                        color="red"
                        dark
                    >
                        <v-checkbox
                            v-model="inactive_checkbox"
                            :label="getInactiveText()"
                            color="white"
                        >
                        </v-checkbox>
                    </v-alert>
                </v-col>
            </v-row>
            <v-card-actions>
                <v-spacer></v-spacer>
                <ProgressCircular :doneSync="doneSync"/>
                <v-btn
                    color="green darken-1"
                    text
                    @click="btnConfirm()"
                    :disabled="disableConfirm()"
                >Confirm
                </v-btn>
                <v-btn
                    color="green darken-1"
                    text
                    @click="closeDialog()"
                >Close
                </v-btn>
            </v-card-actions>
        </v-col>
    </v-card>
</template>

<script>
import { PositionMapping, RouteSkycar, StoragePosition  } from "../../../helper/enums"
import { getHost, getRequestHeader, useRefreshToken } from "../../../helper/common"
import ProgressCircular from "../../shared/ProgressCircular.vue"
export default {
    components: {
        ProgressCircular
    },
    props: {
        skycar: {
            type: Number
        },
        winch: {
            type: Object
        },
        cube: {
            type: String
        },
        closeDialog: {
            type: Function
        },
        showNotification: {
            type: Function
        },
        syncSkycar: {
            type: Function
        }
    },
    data: () => ({
        active_checkbox: false,
        inactive_checkbox: false,
        doneSync: true,
        position: null,
        PositionMapping
    }),
    created() {
        this.updatePosition()
    },
    methods: {
        async btnConfirm() {
            try {
                this.doneSync = false
                let url = getHost(this.cube) + RouteSkycar.WINCH

                let req = await fetch(url, {
                    method: "PUT",
                    body: JSON.stringify({
                        sid: this.skycar,
                        position: this.position,
                        is_active: this.active_checkbox
                    }),
                    headers: getRequestHeader()
                })
                let res = JSON.parse(await req.text())
                if (res.code === 401){ // If access token is unauthorized
                    // use refresh token to get new access token from auth server
                    return useRefreshToken(this, this.btnConfirm)
                }
                if (res.status) {
                    this.showNotification(true, "Winch status is updated successfully.")
                    this.closeDialog()
                    this.syncSkycar()
                } else {
                    this.showNotification(false, res.message)
                }
            } catch (error) {
                this.showNotification(false, error)
            } finally {
                this.active_checkbox = false
                this.inactive_checkbox = false
                this.doneSync = true
            }
        },
        disableConfirm() {
            if (!this.doneSync) {
                return true
            }
            if (!this.active_checkbox && !this.inactive_checkbox) {
                return true
            }
            return false
        },
        getActiveText() {
            return `I would like to flag the winch at position ${this.position} of Skycar ${this.skycar} as ACTIVE.`
        },
        getInactiveText() {
            return `I would like to flag the winch at position ${this.position} of Skycar ${this.skycar} as INACTIVE.`
        },
        updatePosition() {
            this.mappedPosition = this.getPositions()[0]
            this.handlePosition()
        },
        getPositions() {
            return Object.keys(this.winch).map(winch => {
                switch (winch) {
                    case StoragePosition.LEFT:
                        return `${winch} (A)`
                    case StoragePosition.RIGHT:
                        return `${winch} (B)`
                    default:
                        return winch
                }
            })
        },
        handlePosition() {
            this.position = this.PositionMapping[this.mappedPosition]
        }
    },
    watch: {
        active_checkbox(value) {
            if (value) {
                this.inactive_checkbox = false
            }
        },
        inactive_checkbox(value) {
            if (value) {
                this.active_checkbox = false
            }
        },
        winch() {
            this.updatePosition()
        }
    }
}
</script>
