<template>
    <v-container>
        
        <v-row>
            <v-col>
                <v-card>
                    <v-card-title>
                        Station Conveyor Setting
                        <v-spacer></v-spacer> 
                        <v-icon @click="init()" class="icon">mdi-refresh</v-icon>
                    </v-card-title>

                <v-divider></v-divider>

                <v-data-table
                    :headers="headers"
                    :items="Object.values(stationNodes)"
                    :items-per-page="maxPageSize"
                    hide-default-footer
                    
                >
                <template v-slot:items="{ item }">
                    {{ item }}
                </template>

                <template v-slot:[`item.twoDim`]="{ item }">
                    <td>
                        {{ `${item.x}, ${item.y}` }}
                    </td>
                </template>

                <template v-slot:[`item.controls`]="{ item }">
                    <v-icon  @click="openUpdateDialog(item)">
                        mdi-pencil
                    </v-icon>
                </template>


                </v-data-table>
                </v-card>
            </v-col>
        </v-row>

        <v-row class="px-5 mt-2 purple--text text--accent-1">
            <v-col>
                <v-dialog v-model="isUpdateDialogOpen" max-width="600px">
                <v-card>
                    <v-card-title
                    >Update Station Node</v-card-title
                    >

                    <v-card-text>
                        
                    <v-row>
                        <v-col cols="4" md="4">
                            Station: {{ nodeToUpdate.station }}
                        </v-col>
                        <v-col cols="4" md="4">
                            Position: {{ nodeToUpdate.type }}
                        </v-col>
                        <v-col cols="4" md="4">
                            Position: {{ nodeToUpdate.zoneGroup }}
                        </v-col>
                    </v-row>
                    <v-row>
                        <v-col>
                        <v-text-field
                            v-model.number="nodeToUpdate.storageCap"
                            type="number"
                            label="Storage Capacity"
                        ></v-text-field>
                        </v-col>
                        <v-col>
                        <v-text-field
                            v-model.number="nodeToUpdate.extraRetrieveQty"
                            type="number"
                            :min="0"
                            label="Extra Retrieve Qty"
                        ></v-text-field>
                        </v-col>
                    </v-row>
                    </v-card-text>

                    <v-card-actions>
                        <v-btn color="primary" text @click="updateNode">
                            Update
                        </v-btn>
                        <v-btn text @click="isUpdateDialogOpen = false">
                            Close
                        </v-btn>
                    </v-card-actions>

                    <v-card-text>
                        <v-expand-transition>
                            <v-card-text v-show="stringifiedResponse">
                            Response:
                            <CodeBlock>{{ stringifiedResponse }}</CodeBlock>
                            </v-card-text>
                        </v-expand-transition>
                    </v-card-text>
                </v-card>
                </v-dialog>
            </v-col>
        </v-row>
    </v-container>
</template>

<script>
import CodeBlock from "@/dashboard/model/CodeBlock.vue";
import { NodeAPI } from "../api/node";
import { SmNodeType } from "../helper/enums";
import { StationAPI } from "../api/station";

export default {
    name: "StationNode",
    components: {
        CodeBlock,
    },
    data() {
        return {
            isLoading: true,
            stringifiedResponse: "",
            isBtnLoading: false,
            stationNodes: [],
            maxPageSize: 500,
            isUpdateDialogOpen: false,
            nodeToUpdate: {
                storageCap: null
            },
            headers: [
                { 
                    text: "STATION",
                    value: "station",
                    groupable: false,
                },
                { 
                    text: "TYPE",
                    value: "type",
                    groupable: false,
                },
                { 
                    text: "COORD",
                    value: "twoDim",
                    groupable: false,
                },
                { 
                    text: "POSITION",
                    value: "zoneGroup",
                    groupable: false,
                },
                { 
                    text: "HW INDEX",
                    value: "hardwareIndex",
                    groupable: false,
                },
                { 
                    text: "STORAGE CAP",
                    value: "storageCap",
                    groupable: false,
                },
                { 
                    text: "EXTRA RETRIEVE QTY",
                    value: "extraRetrieveQty",
                    groupable: false,
                },
                { 
                    text: "ACTION",
                    value: "controls",
                    groupable: false,
                    sortable: false
                },
            ]
        }
    },
    methods: {
        async init() {
            this.isLoading = true
            this.stringifiedResponse = ""

            const stationsDto = await StationAPI.getAll()

            // get all station nodes
            await NodeAPI.getNodes({
                type: [SmNodeType.Gateway, SmNodeType.GatewayIn, SmNodeType.GatewayOut
                ],
                perPage: -1 // take all stations' nodes
            }).then(res => {
                const data = res.data

                if(data) {
                    const sorted = data.data.sort((a,b) => a.station - b.station)
                    sorted.forEach(x => {
                        const station = stationsDto.find(s => s.id === x.station)
                        if (station) {
                            x.extraRetrieveQty = station.extraRetrieveQty
                        }
                    })
                    this.stationNodes = sorted
                }
            })
            .catch(err => {
                this.$awn.alert(
                    `Failed to get station nodes. ${err.message}`
                );
            })
            .finally(() => {
                this.isLoading = false;
            })

           
        },
        openUpdateDialog(item) {
            this.nodeToUpdate = item

            this.isUpdateDialogOpen = true
        },
        async updateNode() {
            this.stringifiedResponse = ""
            await StationAPI.updateStation(this.nodeToUpdate.station, {
                id: this.nodeToUpdate.station, 
                extraRetrieveQty: this.nodeToUpdate.extraRetrieveQty
            }).then(async() => {
                this.$awn.info("Station dto updated successfully.");
            }).catch((err) => {
                this.$awn.alert(
                    `Station dto update failed. ${err.message}`
                );
            });

            await NodeAPI.updateNode({
                id: this.nodeToUpdate.id,
                storageCap: this.nodeToUpdate.storageCap,
                type: this.nodeToUpdate.type,
                x: this.nodeToUpdate.x,
                y: this.nodeToUpdate.y,
                z: this.nodeToUpdate.z,
                zoneGroup: this.nodeToUpdate.zoneGroup
            }).then(async() => {
                this.$awn.info("Station node updated successfully.");
                this.isUpdateDialogOpen = false;
                await this.init();
            }).catch((err) => {
                this.$awn.alert(
                    `Station node update failed. ${err.message}`
                );

                this.stringifiedResponse = JSON.stringify(
                { errObj: err, response: err.response },
                undefined,
                2
                );
            });
        }
    },
    beforeMount() {
        this.init()
    }
}
</script>