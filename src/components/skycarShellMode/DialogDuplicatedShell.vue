<template>
    <v-dialog 
        v-model="dialogBool"
        width="800"
    >
        <v-card>
            <v-toolbar
                color="red"
                dark
            >
                <v-toolbar-title>Warning</v-toolbar-title>
            </v-toolbar>
            <v-col>
                <v-chip
                    dark
                    color="orange"
                >
                    {{ message }}
                </v-chip>
                <br/>
                <span>
                    This message was sent at {{ lastShellAt }}. Are you sure you want to resend this message?
                </span>
                <v-card-actions>
                    <v-spacer />
                    <ProgressCircular :done-sync="doneSync" />
                    <v-btn
                        color="green darken-1"
                        text
                        @click="sendMessageAndCloseDialog()"
                        :disabled="!doneSync"
                    >
                        Yes
                    </v-btn>
                    <v-btn
                        color="green darken-1"
                        text
                        @click="closeDialog()"
                    >
                        No
                    </v-btn>
                </v-card-actions>
            </v-col>
        </v-card>
    </v-dialog>
</template>
  
<script>
import { getCurrentDateTime, getHost } from "../../helper/common"
import { SkycarShellCommand } from "../../helper/enums"
import { mypost } from "../../helper/http_request.js"
import ProgressCircular from "../shared/ProgressCircular.vue"
export default {
        components: {
            ProgressCircular
        },
        props: {

        },
        data: () => ({
            dialogBool: false,
            doneSync: true,
            lastShell: null,
            lastShellAt: null,
            timeout: null,
            zone: null,
            item: null,
            repeat: null,
            delay: null
        }),
        methods: {
            showDialog() {
                this.dialogBool = true
            },
            closeDialog() {
                this.dialogBool = false
            },
            async validateAndSendMessage(zone, item, repeat, delay, all) {
                this.zone = zone
                this.item = item
                this.repeat = repeat
                this.delay = delay
                if (all) {
                    await this.sendMessage()
                    return
                } 
                if (
                    this.lastShell != null &&
                    this.lastShell.action == item.action &&
                    this.lastShell.repeat == item.repeat &&
                    this.lastShell.sid == item.sid &&
                    this.lastShell.value == item.value
                ) {
                    this.showDialog()
                    return
                }
                this.lastShell = item
                await this.sendMessage()
            },
            async sendMessageAndCloseDialog() {
                this.doneSync = false
                this.lastShell = this.item
                await this.sendMessage()
                this.doneSync = true
                this.closeDialog()
            },
            async sendMessage() {
                for (let i = 0; i < this.repeat; i++) {
                    let host = new URL(
                        getHost(this.zone) + SkycarShellCommand.MCU_COMMAND
                    )
                    await mypost(host, this.item)
                    await new Promise((resolve) => setTimeout(resolve, this.delay * 1000))
                }
            },
        },
        computed: {
            message() {
                if (this.item) {
                    return `SC,${this.item.sid},Q,${this.item.action},${this.item.value}`
                }
                return null
            }
        },
        watch: {
            lastShell(value) {
                if (value == null) {
                    return
                }
                this.lastShellAt = getCurrentDateTime()
                clearTimeout(this.timeout)
                this.timeout = setTimeout(() => {
                    this.lastShell = null
                }, 3000)
            }
        }
    }
</script>
