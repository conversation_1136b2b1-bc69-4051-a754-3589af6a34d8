<template>
  <!-- Advance Shell Mode -->
  <v-card dark class="mt-4 mb-10">
    <v-card-actions>
      <v-btn text @click="maintenanceShell.bool = !maintenanceShell.bool">
        MAINTENANCE SHELL MODE
        <v-icon>{{
          maintenanceShell.bool ? "mdi-chevron-up" : "mdi-chevron-down"
        }}</v-icon>
      </v-btn>
    </v-card-actions>
    <v-expand-transition>
      <div v-show="maintenanceShell.bool">
        <!-- Skycar Selection -->
        <v-col>
          <v-row class="mt-3">
            <v-col>
              <v-btn
                v-model="maintenanceShell['all']"
                color="orange"
                rounded
                class="ma-1"
                @click="btnUpdateSid('all')"
              >
                <v-icon v-if="maintenanceShell['all']"
                  >mdi-sticker-check-outline</v-icon
                >
                <v-icon v-else>mdi-sticker-remove-outline</v-icon>
                <span>All</span>
              </v-btn>
              <v-btn
                v-for="sid in getSkycarID()"
                :key="sid"
                rounded
                :color="maintenanceShell['sid'].includes(sid) ? 'green' : 'red'"
                class="ma-1"
                @click="btnUpdateSid(sid)"
              >
                <v-icon v-if="maintenanceShell['sid'].includes(sid)"
                  >mdi-sticker-check-outline</v-icon
                >
                <v-icon v-else>mdi-sticker-remove-outline</v-icon>
                <span>SC {{ sid }}</span>
              </v-btn>
            </v-col>
          </v-row>
        </v-col>
        <v-card dark>
          <div class="ml-4">
            <v-row>
              <!-- Button Area  -->
              <v-col>
                <v-row
                  v-for="action in maintenanceShell['maintenance']"
                  :key="action['title']"
                >
                  <v-col>
                    <pre v-if="action['title']" class="mx-2">{{
                      action["title"]
                    }}</pre>
                    <span v-for="data in action['data']" :key="data['title']">
                      <v-tooltip bottom>
                        <template v-slot:activator="{ on, attrs }">
                          <v-btn
                            width="130"
                            v-bind="attrs"
                            v-on="on"
                            class="ma-2"
                            :color="data['color']"
                            dark
                            rounded
                            @click="
                              btnSendShell({
                                sid: maintenanceShell['sid'],
                                action: data['action'],
                                value: data['data'],
                              })
                            "
                          >
                            <v-icon class="mx-1">{{ data["icon"] }}</v-icon>
                            {{ data["title"] }}
                          </v-btn>
                        </template>
                        <span>{{ data["data"] }}</span>
                      </v-tooltip>
                    </span>
                  </v-col>
                </v-row>
              </v-col>
              <!-- Move -->
              <v-col>
                <v-row>
                  <v-col class="mx-2">
                    <h2 class="ma-2">Maintenance Move</h2>
                    <!-- Axis -->
                    <v-row>
                      <v-col>
                        <pre>Axis</pre>
                        <span
                          v-for="item in [
                            {
                              title: 'axis-X',
                              data: 'x',
                              text: 'Horizontal',
                              icon: 'mdi-arrow-expand-horizontal',
                              text: 'Move Horizontally',
                            },
                            {
                              title: 'axis-Y',
                              data: 'y',
                              text: 'Vertical',
                              icon: 'mdi-arrow-expand-vertical',
                              text: 'Move Vertically',
                            },
                          ]"
                          :key="item['title']"
                        >
                          <v-tooltip bottom>
                            <template v-slot:activator="{ on, attrs }">
                              <v-btn
                                v-on="on"
                                v-bind="attrs"
                                v-model="maintenanceShell['valueAxis']"
                                rounded
                                :color="
                                  forceButtonColor(
                                    item['data'],
                                    maintenanceShell['valueAxis']
                                  )
                                "
                                @click="
                                  maintenanceShell['valueAxis'] = item['data']
                                "
                                class="mx-1"
                                width="130"
                              >
                                <v-icon>{{ item["icon"] }}</v-icon>
                                <span>{{ item["title"] }}</span>
                              </v-btn>
                            </template>
                            <span>{{ item["text"] }}</span>
                          </v-tooltip>
                        </span>
                      </v-col>
                    </v-row>
                    <!-- Direction -->
                    <v-row>
                      <v-col>
                        <pre>Direction</pre>
                        <span
                          v-for="item in [
                            {
                              title: 'Forward',
                              data: 'f',
                              text: 'Move Forward',
                              icon: 'mdi-arrow-expand-up',
                            },
                            {
                              title: 'Backward',
                              data: 'b',
                              text: 'Move Backward',
                              icon: 'mdi-arrow-expand-down',
                            },
                          ]"
                          :key="item['title']"
                        >
                          <v-tooltip bottom>
                            <template v-slot:activator="{ on, attrs }">
                              <v-btn
                                v-on="on"
                                v-bind="attrs"
                                v-model="maintenanceShell['valueDirection']"
                                rounded
                                :color="
                                  forceButtonColor(
                                    item['data'],
                                    maintenanceShell['valueDirection']
                                  )
                                "
                                @click="
                                  maintenanceShell['valueDirection'] =
                                    item['data']
                                "
                                class="ma-1"
                                width="130"
                              >
                                <v-icon>{{ item["icon"] }}</v-icon>
                                {{ item["title"] }}
                              </v-btn>
                            </template>
                            <span>{{ item["text"] }}</span>
                          </v-tooltip>
                        </span>
                      </v-col>
                    </v-row>
                    <!-- Confirm -->
                    <v-row>
                      <v-col>
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <v-btn
                              v-bind="attrs"
                              v-on="on"
                              color="green"
                              rounded
                              @click="
                                btnSendMove({
                                  sid: maintenanceShell['sid'],
                                  axis: maintenanceShell['valueAxis'],
                                  direction: maintenanceShell['valueDirection'],
                                })
                              "
                              class="ma-2"
                            >
                              <v-icon>mdi-check</v-icon>
                              Confirm
                            </v-btn>
                          </template>
                          <span>
                            skycab maint
                            {{ maintenanceShell["valueAxis"] }}
                            {{ maintenanceShell["valueDirection"] }}
                          </span>
                        </v-tooltip>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-col>
              <!-- Force Winch -->
              <v-col>
                <v-row>
                  <v-col class="mx-2"  v-if="this.$store.state.cubeConfig.dual">
                    <h2 class="mx-2">Force Winch</h2>
                    <!-- Winch Side -->
                    <v-row>
                      <v-col>
                        <pre>Winch Side</pre>
                        <span
                          v-for="item in [
                            {
                              title: 'A',
                              data: 'a',
                              icon: 'mdi-alpha-a-box',
                              text: 'A Winch',
                            },
                            {
                              title: 'B',
                              data: 'b',
                              icon: 'mdi-alpha-b-box',
                              text: 'B Winch',
                            },
                          ]"
                          :key="item['title']"
                        >
                          <v-tooltip bottom>
                            <template v-slot:activator="{ on, attrs }">
                              <v-btn
                                v-on="on"
                                v-bind="attrs"
                                v-model="maintenanceShell['valueWinch']"
                                rounded
                                :color="
                                  maintenanceShell['valueWinch'] == item['data']
                                    ? 'red'
                                    : 'black'
                                "
                                @click="
                                  maintenanceShell['valueWinch'] = item['data']
                                "
                                class="ma-1"
                                width="130"
                              >
                                <v-icon>{{ item["icon"] }}</v-icon>
                                <span>{{ item["title"] }}</span>
                              </v-btn>
                            </template>
                            <span>{{ item["text"] }}</span>
                          </v-tooltip>
                        </span>
                      </v-col>
                    </v-row>
                    <!-- Force Winch -->
                    <div>
                      <!-- Speed -->
                      <v-row>
                        <v-col>
                          <pre>Speed</pre>
                          <v-text-field
                            v-model="maintenanceShell['valueZSpeed']"
                            type="number"
                            label="Value"
                            solo
                            light
                            rounded
                          >
                          </v-text-field>
                        </v-col>
                        <v-col>
                          <!-- Plus -->
                          <v-row class="mt-3">
                            <v-btn
                              @click="
                                maintenanceShell['valueZSpeed'] =
                                  parseInt(maintenanceShell['valueZSpeed']) + 1
                              "
                              rounded
                              class="ma-1"
                              color="red"
                              small
                              :disabled="maintenanceShell['valueZSpeed'] >= 10"
                            >
                              <v-icon>mdi-plus</v-icon>
                            </v-btn>
                          </v-row>
                          <!-- Minus -->
                          <v-row>
                            <v-btn
                              @click="maintenanceShell['valueZSpeed'] -= 1"
                              rounded
                              class="ma-1"
                              :disabled="maintenanceShell['valueZSpeed'] <= 1"
                              small
                              light
                            >
                              <v-icon>mdi-minus</v-icon>
                            </v-btn>
                          </v-row>
                        </v-col>
                      </v-row>
                      <v-row>
                        <v-col>
                          <pre>Force Winch</pre>
                          <span
                            v-for="item in [
                              {
                                title: 'Up',
                                data: `skycab dual force ${maintenanceShell['valueWinch']} u ${maintenanceShell['valueZSpeed']}`,
                                icon: 'mdi-arrow-expand-up',
                                color: 'red',
                              },
                              {
                                title: 'Down',
                                data: `skycab dual force ${maintenanceShell['valueWinch']} d ${maintenanceShell['valueZSpeed']}`,
                                icon: 'mdi-arrow-expand-down',
                                color: 'pink',
                              },
                              {
                                title: 'Stop',
                                data: `skycab dual force ${maintenanceShell['valueWinch']} n 0`,
                                icon: 'mdi-pause-octagon-outline',
                                color: 'purple',
                              },
                            ]"
                            :key="item.title"
                          >
                            <v-tooltip bottom>
                              <template v-slot:activator="{ on, attrs }">
                                <v-btn
                                  width="130"
                                  v-on="on"
                                  v-bind="attrs"
                                  class="ma-1"
                                  :color="item.color"
                                  :disabled="
                                    maintenanceShell['valueZSpeed'] > 10 ||
                                      maintenanceShell['valueZSpeed'] < 1
                                  "
                                  @click="
                                    btnSendShell({
                                      sid: maintenanceShell.sid,
                                      action: '1',
                                      value: item.data,
                                    })
                                  "
                                  rounded
                                >
                                  <v-icon>{{ item.icon }}</v-icon>
                                  <span>{{ item.title }}</span>
                                </v-btn>
                              </template>
                              <span>{{ item.data }}</span>
                            </v-tooltip>
                          </span>
                        </v-col>
                      </v-row>
                    </div>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </div>
        </v-card>
      </div>
    </v-expand-transition>

    <DialogSkycarShellMode
      :boolEng="boolEng"
      :txtEng="txtEng"
      :sids="maintenanceShell.sid"
      @close-dialog="closeSkycarDialog"
    />
    <DialogSkycarShellModeAll
      :boolAll="boolAll"
      :txtAll="txtAll"
      :sids="maintenanceShell.sid"
      @close-dialog="closeAllSkycarDialog"
      @send-all="btnSendAll"
    />
    <DialogDuplicatedShell ref="dialogDuplicatedShell" />
  </v-card>
</template>

<script>
import axios from "axios";
import { socket } from "../../App.vue";
import { getHost, getRequestHeader, isTCSubang } from "../../helper/common.js"; 
import { RouteOperation, Websocket } from "../../helper/enums.js";
import DialogSkycarShellMode from "../dialogs/DialogSkycarShellMode.vue";
import DialogSkycarShellModeAll from "../dialogs/DialogSkycarShellModeAll.vue";
import DialogDuplicatedShell from "./DialogDuplicatedShell.vue";

export default {
  name: "MaintenanceShellMode",
  props: {
    modelSkycar: {
      skycar: [],
      dtSelected: [],
      alert: null,
    },
    currentZone: null,
    parentShellSid: {
      type: Array,
    },
  },
  components: {
    DialogSkycarShellMode,
    DialogSkycarShellModeAll,
    DialogDuplicatedShell,
  },
  created() {
    this.getMessage(socket);
    this.isSubang = isTCSubang();
    console.log(this.isSubang)
  },
  data: () => ({
    isSubang: false, /**To hide dual winch Force Winch*/
    boolEng: false,
    txtEng: null,
    boolAll: false,
    txtAll: null,
    command: null,
    maintenanceShell: {
      bool: false,
      tab: null,
      sid: Array(),
      socketItem: null,
      all: false,
      value: null,
      valueAxis: "x",
      valueWinch: "a",
      valueDirection: "f",
      valueZSpeed: 1,
      valueZType: "u",
      valueZQuantity: 1,
      valueZWeight: 1,
      valueZDes: "cb",
      maintenance: [
        {
          title: "Shell",
          data: [
            {
              title: "Enable",
              action: 0,
              color: "green",
              data: 1,
              icon: "mdi-access-point-check",
            },
            {
              title: "Disable",
              action: 0,
              color: "red",
              data: 0,
              icon: "mdi-access-point-remove",
            },
          ],
        },
        {
          title: "Wex",
          data: [
            {
              title: "Wex X",
              action: 1,
              color: "green",
              data: "skycab wex x",
              icon: "mdi-arrow-expand-horizontal",
            },
            {
              title: "Wex Y",
              action: 1,
              color: "orange",
              data: "skycab wex y",
              icon: "mdi-arrow-expand-vertical",
            },
            {
              title: "Wex XY",
              action: 1,
              color: "lime",
              data: "skycab wex xy",
              icon: "mdi-arrow-expand-all",
            },
          ],
        },
        {
          title: "Battery ",
          data: [
            {
              title: "Release",
              action: 1,
              color: "orange",
              data: "skycab battery action r",
              icon: "mdi-lock-open",
            },
          ],
        },
        {
          title: "System Reboot",
          data: [
            {
              title: "Reboot",
              action: 1,
              color: "red",
              data: "skycab system reboot",
              icon: "mdi-autorenew",
            },
          ],
        },
      ],
    },
    btnUpdateSid: async function(sid) {
      let res = await axios.get(
        `${getHost(this.currentZone)}${RouteOperation.CUBE}`,
        { headers: getRequestHeader() }
      );
      let json = res.data;
      if (json.status) {
        let data = json.data;

        if (data.HALT_CUBE.status != "AVAILABLE")
          this.$awn.tip(
            "Cube is currently halted, please ensure no people in the cube if you require to perform any skycar movement."
          );
      }

      if (sid == "all") {
        this.maintenanceShell["all"] = !this.maintenanceShell["all"];
        if (this.maintenanceShell["all"]) {
          this.maintenanceShell["sid"] = this.getSkycarID();
        } else {
          this.maintenanceShell["sid"] = Array();
        }
      } else {
        if (this.maintenanceShell["sid"].includes(sid)) {
          const index = this.maintenanceShell["sid"].indexOf(sid);
          this.maintenanceShell["sid"].splice(index, 1);
        } else {
          this.maintenanceShell["sid"].push(sid);
        }
      }
      this.txtEng = {
        status: "Accepted",
        reason: "Updated current skycar selection.",
        data: Array(),
      };
      this.boolEng = true;
      this.$emit("updateSid", this.maintenanceShell.sid, "Advanced");
    },
    btnSendShell: async function(json, repeat = 1, delay = 0) {
      let here = this;
      try {
        here.command = json;
        here.command.repeat = repeat;
        var data = Array();
        if (json.sid.length == 0) {
          data = Array();
          here.txtEng = {
            status: "Rejected",
            reason: "Please select a skycar to send command.",
            data: data,
          };
          here.boolEng = true;
        } else if (
          json.sid.length > 1 ||
          (json.action == 0 && json.value == 1) ||
          json.value == "skycab system reboot"
        ) {
          const sidList = json.sid;
          sidList.forEach(function(sid) {
            data.push(`SC,${sid},Q,${json.action},${json.value}`);
          });
          here.command = json;
          here.boolAll = true;
          here.txtAll = {
            status: "Warning",
            reason: "Are you sure to send the following command?",
            data: data,
          };
        } else if (json.sid.length == 1) {
          json.sid = json.sid[0];
          here.sendMessage({ type: "request", json: json }, repeat, delay);
        }
      } catch (error) {
        here.txtEng = { status: "Rejected", reason: error, data: data };
        here.boolEng = true;
      }
    },
    btnSendMove: async function(moveValue) {
      let here = this;
      let json = {};
      json = {
        sid: moveValue["sid"],
        action: "1",
        value: `skycab maint ${moveValue.axis} ${moveValue.direction}`,
      };
      here.btnSendShell(json);
    },
    btnSendWinch: async function(winchValue) {
      let here = this;
      let commandPrefix = "skycar";
      let json = {};
      if (winchValue.valueZType == "u" || winchValue.valueZType == "d") {
        commandPrefix = `skycab dual winch z ${winchValue.valueZType} ${winchValue.valueWinch}`;
      } else {
        commandPrefix = `skycab dual ${winchValue.valueZType} ${winchValue.valueWinch}`;
      }
      json = {
        sid: winchValue.sid,
        action: "1",
        value: `${commandPrefix} ${winchValue.valueZQuantity} ${winchValue.valueZWeight} ${winchValue.valueZDes}`,
      };
      here.btnSendShell(json);
    },
    forceButtonColor: function(value, selectedValue) {
      if (selectedValue != value) {
        return "black";
      } else {
        return "orange";
      }
    },
  }),
  methods: {
    getSkycarID() {
      const skycarIDs = this.modelSkycar.skycar.map(
        (skycar) => skycar.skycar_id
      );
      return skycarIDs.sort(function(a, b) {
        return a - b;
      });
    },
    closeSkycarDialog() {
      (this.boolEng = false), (this.txtEng = null);
    },
    closeAllSkycarDialog() {
      (this.boolAll = false), (this.txtAll = null);
    },
    getMessage(socket) {
      var here = this;
      socket.on(Websocket.SKYCAR, function(item) {
        here.maintenanceShell.socketItem = item.item;
      });
    },
    async sendMessage(item, repeat = 1, delay = 0, all = false) {
      this.$refs.dialogDuplicatedShell.validateAndSendMessage(
        this.currentZone,
        item.json,
        repeat,
        delay,
        all
      );
    },
    async btnSendAll() {
      let here = this;
      this.boolAll = false;
      let json = here.command;
      if (Array.isArray(json.sid)) {
        var sid_list = json.sid;
      } else {
        sid_list = [json.sid];
      }
      sid_list.forEach(async function(sid) {
        json.sid = parseInt(sid);
        here.sendMessage({ type: "request", json: json }, json.repeat, 0, true);
      });
    },
  },
  computed: {
    catchMessage() {
      return this.maintenanceShell.socketItem;
    },
  },
  watch: {
    parentShellSid: function(newVal) {
      this.maintenanceShell.sid = newVal;
    },
  },
};
</script>
