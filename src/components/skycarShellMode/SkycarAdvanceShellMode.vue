<template>
  <!-- Advance Shell Mode -->
  <v-card dark class="mt-4 mb-10">
    <v-card-actions>
      <v-btn text @click="advanceShell.bool = !advanceShell.bool">
        ADVANCE SHELL MODE
        <v-icon>{{
          advanceShell.bool ? "mdi-chevron-up" : "mdi-chevron-down"
        }}</v-icon>
      </v-btn>
    </v-card-actions>
    <v-expand-transition>
      <div v-show="advanceShell.bool">
        <!-- Skycar Selection -->
        <v-col>
          <v-row class="mt-3">
            <v-col>
              <v-btn
                v-model="advanceShell['all']"
                color="orange"
                rounded
                class="ma-1"
                @click="btnUpdateSid('all')"
              >
                <v-icon v-if="advanceShell['all']"
                  >mdi-sticker-check-outline</v-icon
                >
                <v-icon v-else>mdi-sticker-remove-outline</v-icon>
                <span>All</span>
              </v-btn>
              <v-btn
                v-for="sid in getSkycarID()"
                :key="sid"
                rounded
                :color="advanceShell['sid'].includes(sid) ? 'green' : 'red'"
                class="ma-1"
                @click="btnUpdateSid(sid)"
              >
                <v-icon v-if="advanceShell['sid'].includes(sid)"
                  >mdi-sticker-check-outline</v-icon
                >
                <v-icon v-else>mdi-sticker-remove-outline</v-icon>
                <span>SC {{ sid }}</span>
              </v-btn>
            </v-col>
          </v-row>
        </v-col>
        <v-tabs align-with-title v-model="advanceShell['tab']">
          <v-tabs-slider></v-tabs-slider>
          <v-tab
            v-for="advanceShellItem in advanceShell['item']"
            :key="advanceShellItem"
          >
            {{ advanceShellItem }}
          </v-tab>
        </v-tabs>
        <v-card dark>
          <v-tabs-items v-model="advanceShell['tab']" dark>
            <!-- Status -->
            <v-tab-item>
              <v-row>
                <v-col>
                  <v-row
                    v-for="status in advanceShell['status']"
                    :key="status['title']"
                  >
                    <v-col>
                      <pre v-if="status['title']" class="mx-2">{{
                        status["title"]
                      }}</pre>
                      <span v-for="data in status['data']" :key="data['title']">
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <v-btn
                              width="130"
                              v-bind="attrs"
                              v-on="on"
                              class="ma-2"
                              :color="data['color']"
                              dark
                              rounded
                              @click="
                                btnSendShell({
                                  sid: advanceShell['sid'],
                                  action: data['action'],
                                  value: data['data'],
                                })
                              "
                            >
                              <v-icon class="mx-1">{{ data["icon"] }}</v-icon>
                              {{ data["title"] }}
                            </v-btn>
                          </template>
                          <span>{{ data["data"] }}</span>
                        </v-tooltip>
                      </span>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-tab-item>
            <!-- Control -->
            <v-tab-item>
              <v-row>
                <!-- Move -->
                <v-col class="mx-2">
                  <h2 class="ma-2">Move</h2>
                  <!-- Force -->
                  <v-row>
                    <v-col>
                      <v-checkbox
                        v-model="advanceShell['valueForceMove']"
                        label="Force Move"
                      >
                      </v-checkbox>
                    </v-col>
                  </v-row>
                  <!-- Axis -->
                  <v-row>
                    <v-col>
                      <pre>Axis</pre>
                      <span
                        v-for="item in [
                          {
                            title: 'axis-X',
                            data: 'x',
                            text: 'Vertical',
                            icon: 'mdi-arrow-expand-vertical',
                            text: 'Move Vertically',
                          },
                          {
                            title: 'axis-Y',
                            data: 'y',
                            text: 'Horizontal',
                            icon: 'mdi-arrow-expand-horizontal',
                            text: 'Move Horizontally',
                          },
                        ]"
                        :key="item['title']"
                      >
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <v-btn
                              v-on="on"
                              v-bind="attrs"
                              v-model="advanceShell['valueAxis']"
                              rounded
                              :color="
                                forceButtonColor(
                                  item['data'],
                                  advanceShell['valueAxis'],
                                  advanceShell['valueForceMove']
                                )
                              "
                              @click="advanceShell['valueAxis'] = item['data']"
                              class="mx-1"
                              width="130"
                            >
                              <v-icon>{{ item["icon"] }}</v-icon>
                              <span>{{ item["title"] }}</span>
                            </v-btn>
                          </template>
                          <span>{{ item["text"] }}</span>
                        </v-tooltip>
                      </span>
                    </v-col>
                  </v-row>
                  <!-- Direction -->
                  <v-row>
                    <v-col>
                      <pre>Direction</pre>
                      <span
                        v-for="item in [
                          {
                            title: 'Forward',
                            data: 'f',
                            text: 'Move Forward',
                            icon: 'mdi-arrow-expand-up',
                          },
                          {
                            title: 'Backward',
                            data: 'b',
                            text: 'Move Backward',
                            icon: 'mdi-arrow-expand-down',
                          },
                        ]"
                        :key="item['title']"
                      >
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <v-btn
                              v-on="on"
                              v-bind="attrs"
                              v-model="advanceShell['valueDirection']"
                              rounded
                              :color="
                                forceButtonColor(
                                  item['data'],
                                  advanceShell['valueDirection'],
                                  advanceShell['valueForceMove']
                                )
                              "
                              @click="
                                advanceShell['valueDirection'] = item['data']
                              "
                              class="ma-1"
                              width="130"
                            >
                              <v-icon>{{ item["icon"] }}</v-icon>
                              {{ item["title"] }}
                            </v-btn>
                          </template>
                          <span>Move {{ item["text"] }}</span>
                        </v-tooltip>
                      </span>
                    </v-col>
                  </v-row>
                  <!-- Quantity -->
                  <v-row>
                    <v-col>
                      <pre>Quantity</pre>
                      <v-text-field
                        v-model="advanceShell['valueQuantity']"
                        type="number"
                        solo
                        light
                        rounded
                      >
                      </v-text-field>
                    </v-col>
                    <v-col>
                      <!-- Plus -->
                      <v-row class="mt-3">
                        <v-btn
                          @click="
                            advanceShell['valueQuantity'] =
                              parseInt(advanceShell['valueQuantity']) + 1
                          "
                          rounded
                          class="ma-1"
                          :color="
                            advanceShell['valueForceMove'] ? 'red' : 'orange'
                          "
                          small
                        >
                          <v-icon>mdi-plus</v-icon>
                        </v-btn>
                      </v-row>
                      <!-- Minus -->
                      <v-row>
                        <v-btn
                          @click="advanceShell['valueQuantity'] -= 1"
                          rounded
                          class="ma-1"
                          :color="
                            advanceShell['valueForceMove'] ? 'red' : 'orange'
                          "
                          :disabled="advanceShell['valueQuantity'] <= 1"
                          small
                        >
                          <v-icon>mdi-minus</v-icon>
                        </v-btn>
                      </v-row>
                    </v-col>
                  </v-row>
                  <!-- Confirm -->
                  <v-row>
                    <v-col>
                      <v-tooltip bottom>
                        <template v-slot:activator="{ on, attrs }">
                          <v-btn
                            v-bind="attrs"
                            v-on="on"
                            :color="
                              advanceShell['valueForceMove'] ? 'red' : 'green'
                            "
                            rounded
                            @click="
                              btnSendMove({
                                sid: advanceShell['sid'],
                                axis: advanceShell['valueAxis'],
                                direction: advanceShell['valueDirection'],
                                quantity: advanceShell['valueQuantity'],
                                force: advanceShell['valueForceMove'],
                              })
                            "
                            class="ma-2"
                            :disabled="advanceShell['valueQuantity'] < 1"
                          >
                            <v-icon>mdi-check</v-icon>
                            Confirm
                          </v-btn>
                        </template>
                        <span v-if="advanceShell['valueForceMove']">
                          skycab actuator mcube fmove
                          {{ advanceShell["valueAxis"] }}
                          {{ advanceShell["valueDirection"] }}
                          {{ advanceShell["valueQuantity"] }}
                        </span>
                        <span v-else>
                          skycab mcube {{ advanceShell["valueAxis"] }}
                          {{ advanceShell["valueDirection"] }}
                          {{ advanceShell["valueQuantity"] }}
                        </span>
                      </v-tooltip>
                    </v-col>
                  </v-row>
                </v-col>
                <v-divider vertical></v-divider>
                <!-- Winch -->
                <v-col class="mx-2">
                  <h2 class="mx-2">Winch</h2>
                  <!-- Force -->
                  <v-row>
                    <v-col>
                      <v-checkbox
                        v-model="advanceShell['valueForceWinch']"
                        label="Force Winch"
                      >
                      </v-checkbox>
                    </v-col>
                  </v-row>
                  <!-- Winch Side -->
                  <v-row>
                    <v-col>
                      <pre>Winch Side</pre>
                      <span
                        v-for="item in [
                          {
                            title: 'A',
                            data: 'a',
                            icon: 'mdi-alpha-a-box',
                            text: 'A Winch',
                          },
                          {
                            title: 'B',
                            data: 'b',
                            icon: 'mdi-alpha-b-box',
                            text: 'B Winch',
                          },
                        ]"
                        :key="item['title']"
                      >
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <v-btn
                              v-on="on"
                              v-bind="attrs"
                              v-model="advanceShell['valueWinch']"
                              rounded
                              :color="
                                advanceShell['valueWinch'] == item['data']
                                  ? advanceShell['valueForceWinch']
                                    ? 'red'
                                    : 'orange'
                                  : 'black'
                              "
                              @click="advanceShell['valueWinch'] = item['data']"
                              class="ma-1"
                              width="130"
                            >
                              <v-icon>{{ item["icon"] }}</v-icon>
                              <span>{{ item["title"] }}</span>
                            </v-btn>
                          </template>
                          <span>{{ item["text"] }}</span>
                        </v-tooltip>
                      </span>
                    </v-col>
                  </v-row>

                  <!-- Force Winch -->
                  <div v-if="advanceShell['valueForceWinch']">
                    <!-- Speed -->
                    <v-row>
                      <v-col>
                        <pre>Speed</pre>
                        <v-text-field
                          v-model="advanceShell['valueZSpeed']"
                          type="number"
                          label="Value"
                          solo
                          light
                          rounded
                        >
                        </v-text-field>
                      </v-col>
                      <v-col>
                        <!-- Plus -->
                        <v-row class="mt-3">
                          <v-btn
                            @click="
                              advanceShell['valueZSpeed'] =
                                parseInt(advanceShell['valueZSpeed']) + 1
                            "
                            rounded
                            class="ma-1"
                            color="red"
                            small
                            :disabled="advanceShell['valueZSpeed'] >= 50"
                          >
                            <v-icon>mdi-plus</v-icon>
                          </v-btn>
                        </v-row>
                        <!-- Minus -->
                        <v-row>
                          <v-btn
                            @click="advanceShell['valueZSpeed'] -= 1"
                            rounded
                            class="ma-1"
                            :disabled="advanceShell['valueZSpeed'] <= 1"
                            small
                            light
                          >
                            <v-icon>mdi-minus</v-icon>
                          </v-btn>
                        </v-row>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col>
                        <pre>Force Winch</pre>
                        <span
                          v-for="item in [
                            {
                              title: 'Up',
                              data: `skycab dual force ${advanceShell['valueWinch']} u ${advanceShell['valueZSpeed']}`,
                              icon: 'mdi-arrow-expand-up',
                              color: 'red',
                            },
                            {
                              title: 'Down',
                              data: `skycab dual force ${advanceShell['valueWinch']} d ${advanceShell['valueZSpeed']}`,
                              icon: 'mdi-arrow-expand-down',
                              color: 'pink',
                            },
                            {
                              title: 'Stop',
                              data: `skycab dual force ${advanceShell['valueWinch']} n 0`,
                              icon: 'mdi-pause-octagon-outline',
                              color: 'purple',
                            },
                          ]"
                          :key="item.title"
                        >
                          <v-tooltip bottom>
                            <template v-slot:activator="{ on, attrs }">
                              <v-btn
                                width="130"
                                v-on="on"
                                v-bind="attrs"
                                class="ma-1"
                                :color="item.color"
                                :disabled="
                                  advanceShell['valueZSpeed'] > 50 ||
                                    advanceShell['valueZSpeed'] < 1
                                "
                                @click="
                                  btnSendShell({
                                    sid: advanceShell.sid,
                                    action: '1',
                                    value: item.data,
                                  })
                                "
                                rounded
                              >
                                <v-icon>{{ item.icon }}</v-icon>
                                <span>{{ item.title }}</span>
                              </v-btn>
                            </template>
                            <span>{{ item.data }}</span>
                          </v-tooltip>
                        </span>
                      </v-col>
                    </v-row>
                  </div>
                  <!-- Non Force Winch -->
                  <div v-else>
                    <!-- Direction -->
                    <v-row>
                      <v-col>
                        <pre>Direction</pre>
                        <span
                          v-for="item in [
                            {
                              title: 'Up',
                              data: 'u',
                              icon: 'mdi-arrow-expand-up',
                              text: 'Winch Up',
                            },
                            {
                              title: 'Down',
                              data: 'd',
                              icon: 'mdi-arrow-expand-down',
                              text: 'Winch Down',
                            },
                            {
                              title: 'Pick',
                              data: 'pick',
                              icon: 'mdi-lock-outline',
                              text: 'Pick',
                            },
                            {
                              title: 'Drop',
                              data: 'drop',
                              icon: 'mdi-lock-open-outline',
                              text: 'Drop',
                            },
                          ]"
                          :key="item['title']"
                        >
                          <v-tooltip bottom>
                            <template v-slot:activator="{ on, attrs }">
                              <v-btn
                                v-on="on"
                                v-bind="attrs"
                                v-model="advanceShell['valueZType']"
                                rounded
                                :color="
                                  advanceShell['valueZType'] == item['data']
                                    ? 'orange'
                                    : 'black'
                                "
                                @click="
                                  advanceShell['valueZType'] = item['data']
                                "
                                class="ma-1"
                                width="130"
                              >
                                <v-icon>{{ item["icon"] }}</v-icon>
                                <span>{{ item["title"] }}</span>
                              </v-btn>
                            </template>
                            <span>{{ item["text"] }}</span>
                          </v-tooltip>
                        </span>
                      </v-col>
                    </v-row>
                    <!-- Quantity -->
                    <v-row>
                      <v-col>
                        <pre>Quantity</pre>
                        <v-text-field
                          v-model="advanceShell['valueZQuantity']"
                          type="number"
                          label="Value"
                          solo
                          light
                          rounded
                        >
                        </v-text-field>
                      </v-col>
                      <v-col>
                        <!-- Plus -->
                        <v-row class="mt-3">
                          <v-btn
                            @click="
                              advanceShell['valueZQuantity'] =
                                parseInt(advanceShell['valueZQuantity']) + 1
                            "
                            rounded
                            class="ma-1"
                            color="orange"
                            small
                            :disabled="advanceShell['valueZQuantity'] >= 14"
                          >
                            <v-icon>mdi-plus</v-icon>
                          </v-btn>
                        </v-row>
                        <!-- Minus -->
                        <v-row>
                          <v-btn
                            @click="advanceShell['valueZQuantity'] -= 1"
                            rounded
                            class="ma-1"
                            :disabled="advanceShell['valueZQuantity'] <= 1"
                            small
                            light
                          >
                            <v-icon>mdi-minus</v-icon>
                          </v-btn>
                        </v-row>
                      </v-col>
                    </v-row>
                    <!-- Weight -->
                    <v-row>
                      <v-col>
                        <pre>Weight</pre>
                        <v-text-field
                          v-model="advanceShell['valueZWeight']"
                          solo
                          light
                          type="number"
                          label="Weight"
                          rounded
                        >
                        </v-text-field>
                      </v-col>
                      <v-col>
                        <!-- Plus -->
                        <v-row class="mt-3">
                          <v-btn
                            @click="
                              advanceShell['valueZWeight'] =
                                parseInt(advanceShell['valueZWeight']) + 1
                            "
                            rounded
                            class="ma-1"
                            color="orange"
                            small
                          >
                            <v-icon>mdi-plus</v-icon>
                          </v-btn>
                        </v-row>
                        <!-- Minus -->
                        <v-row>
                          <v-btn
                            @click="advanceShell['valueZWeight'] -= 1"
                            rounded
                            class="ma-1"
                            :disabled="advanceShell['valueZWeight'] <= 1"
                            small
                            light
                          >
                            <v-icon>mdi-minus</v-icon>
                          </v-btn>
                        </v-row>
                      </v-col>
                    </v-row>
                    <!-- Destination -->
                    <v-row>
                      <v-col>
                        <pre>Destination</pre>
                        <span
                          v-for="item in [
                            {
                              title: 'STL',
                              data: 'stl',
                              icon: 'mdi-arrow-bottom-left-thin-circle-outline',
                              text: 'STL',
                            },
                            {
                              title: 'STH',
                              data: 'sth',
                              icon: 'mdi-arrow-top-right-thin-circle-outline',
                              text: 'STH',
                            },
                            {
                              title: 'QCL',
                              data: 'qcl',
                              icon: 'mdi-arrow-bottom-left-thin-circle-outline',
                              text: 'QCL',
                            },
                            {
                              title: 'QCH',
                              data: 'qch',
                              icon: 'mdi-arrow-top-right-thin-circle-outline',
                              text: 'QCH',
                            },
                            {
                              title: 'USD',
                              data: 'usd',
                              icon: 'mdi-arrow-bottom-left-thin-circle-outline',
                              text: 'USD',
                            },
                            {
                              title: 'USP',
                              data: 'usp',
                              icon: 'mdi-arrow-top-right-thin-circle-outline',
                              text: 'USP',
                            },
                            {
                              title: 'STI',
                              data: 'sti',
                              icon: 'mdi-arrow-bottom-left-thin-circle-outline',
                              text: 'STI',
                            },
                            {
                              title: 'CB',
                              data: 'cb',
                              icon: 'mdi-cube',
                              text: 'CB',
                            },
                          ]"
                          :key="item['title']"
                        >
                          <v-tooltip bottom>
                            <template v-slot:activator="{ on, attrs }">
                              <v-btn
                                v-on="on"
                                v-bind="attrs"
                                v-model="advanceShell['valueZDes']"
                                rounded
                                :color="
                                  advanceShell['valueZDes'] == item['data']
                                    ? 'orange'
                                    : 'black'
                                "
                                @click="
                                  advanceShell['valueZDes'] = item['data']
                                "
                                class="ma-1"
                                width="100"
                              >
                                <v-icon>{{ item["icon"] }}</v-icon>
                                <span>{{ item["title"] }}</span>
                              </v-btn>
                            </template>
                            <span>{{ item["text"] }}</span>
                          </v-tooltip>
                        </span>
                      </v-col>
                    </v-row>
                    <!-- Confirm -->
                    <v-row>
                      <v-col>
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <v-btn
                              v-on="on"
                              v-bind="attrs"
                              color="green"
                              rounded
                              class="ma-1"
                              @click="
                                btnSendWinch({
                                  sid: advanceShell['sid'],
                                  action: '1',
                                  valueZType: advanceShell['valueZType'],
                                  valueZQuantity:
                                    advanceShell['valueZQuantity'],
                                  valueZWeight: advanceShell['valueZWeight'],
                                  valueZDes: advanceShell['valueZDes'],
                                  valueWinch: advanceShell['valueWinch'],
                                })
                              "
                              :disabled="
                                15 > advanceShell['valueZQuantity'] < 1 ||
                                  advanceShell['valueZWeight'] < 1
                              "
                            >
                              <v-icon>mdi-check</v-icon>
                              Confirm
                            </v-btn>
                          </template>
                          <span>
                            {{ getWinchTip }}
                          </span>
                        </v-tooltip>
                      </v-col>
                    </v-row>
                  </div>
                </v-col>
                <v-divider vertical></v-divider>
                <!-- Other -->
                <v-col class="mx-2">
                  <h2 class="mx-2">Other</h2>
                  <!-- Wex -->
                  <v-row>
                    <v-col>
                      <pre>Wex</pre>
                      <v-checkbox
                        v-model="advanceShell['valueForceWex']"
                        label="Force Wex"
                      >
                      </v-checkbox>
                      <!-- Force Wex -->
                      <div v-if="advanceShell['valueForceWex']">
                        <span
                          v-for="item in [
                            {
                              title: 'Wex X',
                              data: 'skycab actuator wex x',
                              icon: 'mdi-arrow-expand-horizontal',
                              color: 'red',
                            },
                            {
                              title: 'Wex Y',
                              data: 'skycab actuator wex y',
                              icon: 'mdi-arrow-expand-vertical',
                              color: 'pink',
                            },
                            {
                              title: 'Wex XY',
                              data: 'skycab actuator wex xy',
                              icon: 'mdi-arrow-expand-all',
                              color: 'purple',
                            },
                          ]"
                          :key="item.title"
                        >
                          <v-tooltip bottom>
                            <template v-slot:activator="{ on, attrs }">
                              <v-btn
                                v-bind="attrs"
                                v-on="on"
                                class="ma-1"
                                :color="item.color"
                                @click="
                                  btnSendShell({
                                    sid: advanceShell.sid,
                                    action: '1',
                                    value: item.data,
                                  })
                                "
                                rounded
                                width="130"
                              >
                                <v-icon>{{ item.icon }}</v-icon>
                                <span>{{ item.title }}</span>
                              </v-btn>
                            </template>
                            <span>{{ item.data }}</span>
                          </v-tooltip>
                        </span>
                      </div>
                      <!-- Wex -->
                      <div v-else>
                        <span
                          v-for="item in [
                            {
                              title: 'Wex X',
                              data: 'skycab wex x',
                              icon: 'mdi-arrow-expand-horizontal',
                              color: 'green',
                            },
                            {
                              title: 'Wex Y',
                              data: 'skycab wex y',
                              icon: 'mdi-arrow-expand-vertical',
                              color: 'orange',
                            },
                            {
                              title: 'Wex XY',
                              data: 'skycab wex xy',
                              icon: 'mdi-arrow-expand-all',
                              color: 'lime',
                            },
                          ]"
                          :key="item['title']"
                        >
                          <v-tooltip bottom>
                            <template v-slot:activator="{ on, attrs }">
                              <v-btn
                                v-bind="attrs"
                                v-on="on"
                                class="ma-1"
                                :color="item['color']"
                                @click="
                                  btnSendShell({
                                    sid: advanceShell['sid'],
                                    action: '1',
                                    value: item['data'],
                                  })
                                "
                                rounded
                                width="130"
                              >
                                <v-icon>{{ item["icon"] }}</v-icon>
                                <span>{{ item["title"] }}</span>
                              </v-btn>
                            </template>
                            <span>{{ item["data"] }}</span>
                          </v-tooltip>
                        </span>
                      </div>
                    </v-col>
                  </v-row>
                  <!-- Gripper -->
                  <v-row>
                    <v-col>
                      <pre>Gripper</pre>
                      <v-checkbox
                        v-model="advanceShell['valueForceGrip']"
                        label="Force Grip"
                      >
                      </v-checkbox>
                      <!-- Winch Side -->
                      <v-row>
                        <v-col>
                          <span
                            v-for="item in [
                              {
                                title: 'A',
                                data: 'a',
                                icon: 'mdi-alpha-a-box',
                                text: 'A Winch',
                              },
                              {
                                title: 'B',
                                data: 'b',
                                icon: 'mdi-alpha-b-box',
                                text: 'B Winch',
                              },
                            ]"
                            :key="item['title']"
                          >
                            <v-tooltip bottom>
                              <template v-slot:activator="{ on, attrs }">
                                <v-btn
                                  v-on="on"
                                  v-bind="attrs"
                                  v-model="advanceShell['valueWinch']"
                                  rounded
                                  :color="
                                    advanceShell['valueWinch'] == item['data']
                                      ? advanceShell['valueForceGrip']
                                        ? 'red'
                                        : 'orange'
                                      : 'black'
                                  "
                                  @click="
                                    advanceShell['valueWinch'] = item['data']
                                  "
                                  class="ma-1"
                                  width="130"
                                >
                                  <v-icon>{{ item["icon"] }}</v-icon>
                                  <span>{{ item["title"] }}</span>
                                </v-btn>
                              </template>
                              <span>{{ item["text"] }}</span>
                            </v-tooltip>
                          </span>
                        </v-col>
                      </v-row>
                      <!-- Force Grip -->
                      <div v-if="advanceShell['valueForceGrip']">
                        <span
                          v-for="item in [
                            {
                              title: 'Attach',
                              data: `skycab dual force open ${advanceShell['valueWinch']}`,
                              icon: 'mdi-lock-outline',
                              color: 'red',
                            },
                            {
                              title: 'Release',
                              data: `skycab dual force close ${advanceShell['valueWinch']}`,
                              icon: 'mdi-lock-open-outline',
                              color: 'pink',
                            },
                          ]"
                          :key="item.title"
                        >
                          <v-tooltip bottom>
                            <template v-slot:activator="{ on, attrs }">
                              <v-btn
                                width="130"
                                v-on="on"
                                v-bind="attrs"
                                class="ma-1"
                                :color="item.color"
                                @click="
                                  btnSendShell({
                                    sid: advanceShell.sid,
                                    action: '1',
                                    value: item.data,
                                  })
                                "
                                rounded
                              >
                                <v-icon>{{ item.icon }}</v-icon>
                                <span>{{ item.title }}</span>
                              </v-btn>
                            </template>
                            <span>{{ item.data }}</span>
                          </v-tooltip>
                        </span>
                      </div>
                      <!-- Grip -->
                      <div v-else>
                        <span
                          v-for="item in [
                            {
                              title: 'Attach',
                              data: `skycab dual open ${advanceShell['valueWinch']}`,
                              icon: 'mdi-lock-outline',
                              color: 'green',
                            },
                            {
                              title: 'Release',
                              data: `skycab dual close  ${advanceShell['valueWinch']}`,
                              icon: 'mdi-lock-open-outline',
                              color: 'orange',
                            },
                          ]"
                          :key="item['title']"
                        >
                          <v-tooltip bottom>
                            <template v-slot:activator="{ on, attrs }">
                              <v-btn
                                width="130"
                                v-on="on"
                                v-bind="attrs"
                                class="ma-1"
                                :color="item['color']"
                                @click="
                                  btnSendShell({
                                    sid: advanceShell.sid,
                                    action: '1',
                                    value: item.data,
                                  })
                                "
                                rounded
                              >
                                <v-icon>{{ item["icon"] }}</v-icon>
                                <span>{{ item["title"] }}</span>
                              </v-btn>
                            </template>
                            <span>{{ item["data"] }}</span>
                          </v-tooltip>
                        </span>
                      </div>
                    </v-col>
                  </v-row>
                  <!-- Bin -->
                  <v-row>
                    <v-col>
                      <pre>Bin</pre>
                      <v-row>
                        <v-col>
                          <v-text-field
                            v-model="advanceShell['binX']"
                            solo
                            type="number"
                            label="X"
                            light
                            rounded
                          >
                            <template #prepend-inner>
                              <v-icon 
                                @click="openGrid()" 
                                style="margin-right: 5px;"
                              >
                                mdi-grid
                              </v-icon>
                            </template>
                          </v-text-field>
                        </v-col>
                        <v-col>
                          <v-text-field
                            v-model="advanceShell['binY']"
                            solo
                            type="number"
                            label="Y"
                            light
                            rounded
                          >
                            <template #prepend-inner>
                              <v-icon 
                                @click="openGrid()" 
                                style="margin-right: 5px;"
                              >
                                mdi-grid
                              </v-icon>
                            </template>
                          </v-text-field>
                        </v-col>
                        <v-col>
                          <v-tooltip bottom>
                            <template v-slot:activator="{ on, attrs }">
                              <v-btn
                                v-on="on"
                                v-bind="attrs"
                                color="green"
                                rounded
                                @click="btnCheckBin(null, advanceShell['binX'], advanceShell['binY'])"
                                class="ma-1"
                                dark
                                :disabled="advanceShell['binX'] < 0 || advanceShell['binY'] < 0"
                              >
                                <v-icon>mdi-check</v-icon>
                                Confirm
                              </v-btn>
                            </template>
                            Check bins at current coordinates
                          </v-tooltip>
                        </v-col>
                      </v-row>
                      <v-row>
                        <v-col>
                          <v-text-field
                            v-model="advanceShell['binCode']"
                            solo
                            type="number"
                            label="Bin Code"
                            light
                            rounded
                          />
                        </v-col>
                        <v-col>
                          <v-tooltip bottom>
                            <template v-slot:activator="{ on, attrs }">
                              <v-btn
                                v-on="on"
                                v-bind="attrs"
                                color="green"
                                rounded
                                @click="btnCheckBin(advanceShell['binCode'], null, null)"
                                class="ma-1"
                                dark
                                :disabled="advanceShell['binCode'] < 0"
                              >
                                <v-icon>mdi-check</v-icon>
                                Confirm
                              </v-btn>
                            </template>
                            Check the current coordinates of bin
                          </v-tooltip>
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-tab-item>
            <!-- Custom -->
            <v-tab-item>
              <v-col>
                <h2 class="ma-2">Custom Command</h2>
                <v-row>
                  <v-col>
                    <v-text-field
                      ref="shell_value"
                      solo
                      rounded
                      light
                      label="Type here..."
                      v-model="advanceShell['value']"
                      clearable
                      @keydown.enter="
                        btnSendShell({
                          sid: advanceShell['sid'],
                          action: '1',
                          value: advanceShell['value'],
                        }),
                          (advanceShell['focus'] = true)
                      "
                      @focus="$event.target.select()"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="2">
                    <v-btn
                      @click="
                        btnSendShell({
                          sid: advanceShell['sid'],
                          action: '1',
                          value: advanceShell['value'],
                        }),
                          (advanceShell['focus'] = true)
                      "
                      class="ma-2"
                      color="green"
                      rounded
                      :disabled="!advanceShell['value']"
                    >
                      <v-icon>mdi-check</v-icon>
                      Confirm
                    </v-btn>
                  </v-col>
                </v-row>
              </v-col>
            </v-tab-item>
            <!-- Maintenance -->
            <v-tab-item>
              <!-- Encoder -->
              <pre class="mx-2">{{ burnTest.encoder.title }}</pre>
              <v-row>
                <v-col cols="2" class="mx-2">
                  <v-select
                    v-model="burnTest.encoder.input.mode.value"
                    :items="burnTest.encoder.input.mode.option"
                    rounded
                    filled
                    :label="burnTest.encoder.input.mode.title"
                  ></v-select>
                </v-col>
                <v-col cols="2" class="mx-2">
                  <v-select
                    v-model="burnTest.encoder.input.axis.value"
                    :items="burnTest.encoder.input.axis.option"
                    rounded
                    filled
                    :label="burnTest.encoder.input.axis.title"
                  ></v-select>
                </v-col>
                <v-col cols="2" class="mx-2">
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        width="130"
                        v-bind="attrs"
                        v-on="on"
                        class="ma-2"
                        color="green"
                        dark
                        rounded
                        @click="
                          btnSendShell({
                            sid: advanceShell.sid,
                            action: burnTest.encoder.action,
                            value: `${
                              burnTest.encoder.value[
                                burnTest.encoder.input.mode.value
                              ]
                            } ${burnTest.encoder.input.axis.value}`,
                          })
                        "
                      >
                        Start
                      </v-btn>
                    </template>
                    <span
                      >{{
                        burnTest.encoder.value[
                          burnTest.encoder.input.mode.value
                        ]
                      }}
                      {{ burnTest.encoder.input.axis.value }}</span
                    >
                  </v-tooltip>
                </v-col>
                <v-col cols="2" class="mx-2">
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        width="130"
                        v-bind="attrs"
                        v-on="on"
                        class="ma-2"
                        color="red"
                        dark
                        rounded
                        @click="
                          btnSendShell({
                            sid: advanceShell.sid,
                            action: burnTest.encoder.action,
                            value: burnTest.encoder.valueStop,
                          })
                        "
                      >
                        Stop
                      </v-btn>
                    </template>
                    <span>{{ burnTest.encoder.valueStop }}</span>
                  </v-tooltip>
                </v-col>
              </v-row>
              <v-divider></v-divider>
              <!-- Gyems Position -->
              <pre class="mx-2">{{ burnTest.gyems.position.title }}</pre>
              <v-row>
                <v-col cols="2" class="mx-2">
                  <v-select
                    v-model="burnTest.gyems.position.input.axis.value"
                    :items="burnTest.gyems.position.input.axis.option"
                    rounded
                    filled
                    :label="burnTest.gyems.position.input.axis.title"
                  ></v-select>
                </v-col>
                <v-col cols="2" class="mx-2">
                  <v-select
                    v-model="burnTest.gyems.position.input.direction.value"
                    :items="burnTest.gyems.position.input.direction.option"
                    rounded
                    filled
                    :label="burnTest.gyems.position.input.direction.title"
                  ></v-select>
                </v-col>
                <v-col cols="2" class="mx-2">
                  <v-text-field
                    v-model="burnTest.gyems.position.input.position1.value"
                    rounded
                    filled
                    :label="burnTest.gyems.position.input.position1.title"
                  ></v-text-field>
                </v-col>
                <v-col cols="2" class="mx-2">
                  <v-text-field
                    v-model="burnTest.gyems.position.input.position2.value"
                    rounded
                    filled
                    :label="burnTest.gyems.position.input.position2.title"
                  ></v-text-field>
                </v-col>
                <v-col cols="2" class="mx-2">
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        width="130"
                        v-bind="attrs"
                        v-on="on"
                        class="ma-2"
                        color="green"
                        dark
                        rounded
                        :disabled="
                          !burnTest.gyems.position.input.position1.value ||
                            !burnTest.gyems.position.input.position2.value
                        "
                        @click="
                          btnSendShell({
                            sid: advanceShell.sid,
                            action: burnTest.gyems.position.action,
                            value: `${burnTest.gyems.position.value} ${burnTest.gyems.position.input.axis.value} ${burnTest.gyems.position.input.direction.value} ${burnTest.gyems.position.input.position1.value} ${burnTest.gyems.position.input.position2.value}`,
                          })
                        "
                      >
                        Confirm
                      </v-btn>
                    </template>
                    <span
                      >{{ burnTest.gyems.position.value }}
                      {{ burnTest.gyems.position.input.axis.value }}
                      {{ burnTest.gyems.position.input.direction.value }}
                      {{ burnTest.gyems.position.input.position1.value }}
                      {{ burnTest.gyems.position.input.position2.value }}</span
                    >
                  </v-tooltip>
                </v-col>
              </v-row>
              <v-divider></v-divider>
              <!-- Gyems Speed -->
              <pre class="mx-2">{{ burnTest.gyems.speed.title }}</pre>
              <v-row>
                <v-col cols="2" class="mx-2">
                  <v-select
                    v-model="burnTest.gyems.speed.input.axis.value"
                    :items="burnTest.gyems.speed.input.axis.option"
                    rounded
                    filled
                    :label="burnTest.gyems.speed.input.axis.title"
                  ></v-select>
                </v-col>
                <v-col cols="2" class="mx-2">
                  <v-text-field
                    v-model="burnTest.gyems.speed.input.speed.value"
                    rounded
                    filled
                    :label="burnTest.gyems.speed.input.speed.title"
                  ></v-text-field>
                </v-col>
                <v-col cols="2" class="mx-2">
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        width="130"
                        v-bind="attrs"
                        v-on="on"
                        class="ma-2"
                        color="green"
                        dark
                        rounded
                        :disabled="!burnTest.gyems.speed.input.speed.value"
                        @click="
                          btnSendShell({
                            sid: advanceShell.sid,
                            action: burnTest.gyems.speed.action,
                            value: `${burnTest.gyems.speed.value} ${burnTest.gyems.speed.input.axis.value} ${burnTest.gyems.speed.input.speed.value}`,
                          })
                        "
                      >
                        Confirm
                      </v-btn>
                    </template>
                    <span
                      >{{ burnTest.gyems.speed.value }}
                      {{ burnTest.gyems.speed.input.axis.value }}
                      {{ burnTest.gyems.speed.input.speed.value }}</span
                    >
                  </v-tooltip>
                </v-col>
                <v-col cols="2" class="mx-2">
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        width="130"
                        v-bind="attrs"
                        v-on="on"
                        class="ma-2"
                        color="orange"
                        dark
                        rounded
                        @click="
                          btnSendShell({
                            sid: advanceShell.sid,
                            action: burnTest.gyems.speed.action,
                            value: burnTest.gyems.speed.valueStop,
                          })
                        "
                      >
                        Stop
                      </v-btn>
                    </template>
                    <span>{{ burnTest.gyems.speed.valueStop }}</span>
                  </v-tooltip>
                </v-col>
                <v-col cols="2" class="mx-2">
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        width="130"
                        v-bind="attrs"
                        v-on="on"
                        class="ma-2"
                        color="red"
                        dark
                        rounded
                        @click="
                          btnSendShell({
                            sid: advanceShell.sid,
                            action: burnTest.gyems.speed.action,
                            value: burnTest.gyems.speed.valueOff,
                          })
                        "
                      >
                        Off
                      </v-btn>
                    </template>
                    <span>{{ burnTest.gyems.speed.valueOff }}</span>
                  </v-tooltip>
                </v-col>
              </v-row>
              <v-divider></v-divider>
              <!-- Calibration -->
              <pre class="mx-2">{{ burnTest.gyems.calibrate.title }}</pre>
              <v-row>
                <v-col cols="2" class="mx-2">
                  <v-select
                    v-model="burnTest.gyems.calibrate.input.mode.value"
                    :items="burnTest.gyems.calibrate.input.mode.option"
                    rounded
                    filled
                    :label="burnTest.gyems.calibrate.input.mode.title"
                  ></v-select>
                </v-col>
                <v-col cols="2" class="mx-2">
                  <v-select
                    v-model="burnTest.gyems.calibrate.input.axis.value"
                    :items="burnTest.gyems.calibrate.input.axis.option"
                    rounded
                    filled
                    :label="burnTest.gyems.calibrate.input.axis.title"
                  ></v-select>
                </v-col>
                <v-col cols="2" class="mx-2">
                  <v-select
                    v-model="burnTest.gyems.calibrate.input.direction.value"
                    :items="burnTest.gyems.calibrate.input.direction.option"
                    rounded
                    filled
                    :label="burnTest.gyems.calibrate.input.direction.title"
                  ></v-select>
                </v-col>
                <v-col cols="2" class="mx-2">
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        width="130"
                        v-bind="attrs"
                        v-on="on"
                        class="ma-2"
                        color="green"
                        dark
                        rounded
                        @click="
                          btnSendShell({
                            sid: advanceShell.sid,
                            action: burnTest.gyems.calibrate.action,
                            value: `${burnTest.gyems.calibrate.value} ${burnTest.gyems.calibrate.input.mode.value} ${burnTest.gyems.calibrate.input.axis.value} ${burnTest.gyems.calibrate.input.direction.value}`,
                          })
                        "
                      >
                        Confirm
                      </v-btn>
                    </template>
                    <span
                      >{{ burnTest.gyems.calibrate.value }}
                      {{ burnTest.gyems.calibrate.input.mode.value }}
                      {{ burnTest.gyems.calibrate.input.axis.value }}
                      {{ burnTest.gyems.calibrate.input.direction.value }}</span
                    >
                  </v-tooltip>
                </v-col>
              </v-row>
              <v-divider></v-divider>
              <!-- Pick Drop -->
              <pre class="mx-2">{{ burnTest.winching.winch.title }}</pre>
              <v-row>
                <v-col cols="2" class="mx-2">
                  <v-text-field
                    v-model="burnTest.winching.winch.input.count.value"
                    rounded
                    filled
                    :label="burnTest.winching.winch.input.count.title"
                  ></v-text-field>
                </v-col>
                <v-col cols="2" class="mx-2">
                  <v-text-field
                    v-model="burnTest.winching.winch.input.level.value"
                    rounded
                    filled
                    :label="burnTest.winching.winch.input.level.title"
                  ></v-text-field>
                </v-col>
                <v-col cols="2" class="mx-2">
                  <v-text-field
                    v-model="burnTest.winching.winch.input.weight.value"
                    rounded
                    filled
                    :label="burnTest.winching.winch.input.weight.title"
                  ></v-text-field>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="2" class="mx-2">
                  <v-select
                    v-model="burnTest.winching.winch.input.type.value"
                    :items="burnTest.winching.winch.input.type.option"
                    rounded
                    filled
                    :label="burnTest.winching.winch.input.type.title"
                  ></v-select>
                </v-col>
                <v-col cols="2" class="mx-2">
                  <v-select
                    v-model="burnTest.winching.winch.input.winchType.value"
                    :items="burnTest.winching.winch.input.winchType.option"
                    rounded
                    filled
                    :label="burnTest.winching.winch.input.winchType.title"
                  ></v-select>
                </v-col>
                <v-col cols="2" class="mx-2">
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        width="130"
                        v-bind="attrs"
                        v-on="on"
                        class="ma-2"
                        color="green"
                        dark
                        rounded
                        :disabled="
                          !burnTest.winching.winch.input.count.value ||
                            !burnTest.winching.winch.input.level.value ||
                            !burnTest.winching.winch.input.weight.value
                        "
                        @click="
                          btnSendShell({
                            sid: advanceShell.sid,
                            action: burnTest.winching.winch.action,
                            value: `${burnTest.winching.winch.value} ${burnTest.winching.winch.input.type.value} ${burnTest.winching.winch.input.count.value} ${burnTest.winching.winch.input.level.value} ${burnTest.winching.winch.input.weight.value} ${burnTest.winching.winch.input.winchType.value}`,
                          })
                        "
                      >
                        Confirm
                      </v-btn>
                    </template>
                    <span
                      >{{ burnTest.winching.winch.value }}
                      {{ burnTest.winching.winch.input.type.value }}
                      {{ burnTest.winching.winch.input.count.value }}
                      {{ burnTest.winching.winch.input.level.value }}
                      {{ burnTest.winching.winch.input.weight.value }}
                      {{ burnTest.winching.winch.input.winchType.value }}</span
                    >
                  </v-tooltip>
                </v-col>
              </v-row>
              <v-divider></v-divider>
              <!-- Winch Calibrate -->
              <pre class="mx-2">{{ burnTest.winching.calibrate.title }}</pre>
              <v-row>
                <v-col cols="2" class="mx-2">
                  <v-select
                    v-model="burnTest.winching.calibrate.input.mode.value"
                    :items="burnTest.winching.calibrate.input.mode.option"
                    rounded
                    filled
                    :label="burnTest.winching.calibrate.input.mode.title"
                  ></v-select>
                </v-col>
                <v-col cols="2" class="mx-2">
                  <v-text-field
                    v-model="burnTest.winching.calibrate.input.level.value"
                    rounded
                    filled
                    :label="burnTest.winching.calibrate.input.level.title"
                  ></v-text-field>
                </v-col>
                <v-col cols="2" class="mx-2">
                  <v-select
                    v-model="burnTest.winching.calibrate.input.destType.value"
                    :items="burnTest.winching.calibrate.input.destType.option"
                    rounded
                    filled
                    :label="burnTest.winching.calibrate.input.destType.title"
                  ></v-select>
                </v-col>
                <v-col cols="2" class="mx-2">
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        width="130"
                        v-bind="attrs"
                        v-on="on"
                        class="ma-2"
                        color="green"
                        dark
                        rounded
                        :disabled="
                          !burnTest.winching.calibrate.input.level.value
                        "
                        @click="
                          btnSendShell({
                            sid: advanceShell.sid,
                            action: burnTest.winching.calibrate.action,
                            value: `${burnTest.winching.calibrate.value} ${burnTest.winching.calibrate.input.mode.value} ${burnTest.winching.calibrate.input.level.value} ${burnTest.winching.calibrate.input.destType.value}`,
                          })
                        "
                      >
                        Confirm
                      </v-btn>
                    </template>
                    <span
                      >{{ burnTest.winching.calibrate.value }}
                      {{ burnTest.winching.calibrate.input.mode.value }}
                      {{ burnTest.winching.calibrate.input.level.value }}
                      {{
                        burnTest.winching.calibrate.input.destType.value
                      }}</span
                    >
                  </v-tooltip>
                </v-col>
              </v-row>
              <v-divider></v-divider>
              <!-- RSSI -->
              <pre class="mx-2">{{ burnTest.rssi.title }}</pre>
              <v-row>
                <v-col cols="2" class="mx-2">
                  <v-text-field
                    v-model="burnTest.rssi.input.repeat.value"
                    rounded
                    filled
                    :label="burnTest.rssi.input.repeat.title"
                    type="number"
                  ></v-text-field>
                </v-col>
                <v-col cols="2" class="mx-2">
                  <v-text-field
                    v-model="burnTest.rssi.input.delay.value"
                    rounded
                    filled
                    :label="burnTest.rssi.input.delay.title"
                    type="number"
                  ></v-text-field>
                </v-col>
                <v-col cols="2" class="mx-2">
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        width="130"
                        v-bind="attrs"
                        v-on="on"
                        class="ma-2"
                        color="green"
                        dark
                        rounded
                        :disabled="!burnTest.rssi.input.repeat.value"
                        @click="
                          btnSendShell(
                            {
                              sid: advanceShell.sid,
                              action: burnTest.rssi.action,
                              value: burnTest.rssi.value,
                            },
                            burnTest.rssi.input.repeat.value,
                            burnTest.rssi.input.delay.value
                          )
                        "
                      >
                        Confirm
                      </v-btn>
                    </template>
                    <span>{{ burnTest.rssi.value }}</span>
                  </v-tooltip>
                </v-col>
              </v-row>
            </v-tab-item>
          </v-tabs-items>
        </v-card>
      </div>
    </v-expand-transition>
    <DialogCheckBin ref="checkBinDialog" />
    <DialogSkycarShellMode
      :boolEng="boolEng"
      :txtEng="txtEng"
      :sids="advanceShell.sid"
      @close-dialog="closeSkycarDialog"
    />
    <DialogSkycarShellModeAll
      :boolAll="boolAll"
      :txtAll="txtAll"
      :sids="advanceShell.sid"
      @close-dialog="closeAllSkycarDialog"
      @send-all="btnSendAll"
    />
    <DialogCoordinateSelection 
      ref="dialogCoordinateSelection" 
      @update-coord="updateCoord"
    />
    <DialogDuplicatedShell
      ref="dialogDuplicatedShell"
    />
  </v-card>
</template>

<script>
import axios from "axios";
import { socket } from "../../App.vue";
import { getHost, getRequestHeader } from "../../helper/common.js";
import {
  RouteOperation,
  Websocket,
} from "../../helper/enums.js";
import DialogSkycarShellMode from "../dialogs/DialogSkycarShellMode.vue";
import DialogSkycarShellModeAll from "../dialogs/DialogSkycarShellModeAll.vue";
import DialogCheckBin from "../skycarAction/CheckBin.vue"
import DialogCoordinateSelection from "../dialogs/DialogCoordinateSelection";
import DialogDuplicatedShell from "./DialogDuplicatedShell.vue";

export default {
  name: "AdvanceShellMode",
  props: {
    modelSkycar: {
      skycar: [],
      dtSelected: [],
      alert: null,
    },
    currentZone: null,
    parentShellSid: {
      type: Array,
    },
  },
  components: {
    DialogSkycarShellMode,
    DialogSkycarShellModeAll,
    DialogCheckBin,
    DialogCoordinateSelection,
    DialogDuplicatedShell
  },
  created() {
    this.getMessage(socket);
  },
  data: () => ({
    boolEng: false,
    txtEng: null,
    boolAll: false,
    txtAll: null,
    command: null,
    burnTest: {
      encoder: {
        title: "Gyems Encoder",
        action: 1,
        value: {
          Once: "skycab actuator mcube enc once",
          Continuous: "skycab actuator mcube enc cont",
        },
        valueStop: "skycab actuator mcube enc stop",
        input: {
          mode: {
            title: "Mode",
            option: ["Once", "Continuous"],
            value: "Once",
          },
          axis: {
            title: "Axis",
            option: ["x", "y", "xy"],
            value: "x",
          },
        },
      },
      gyems: {
        position: {
          title: "Gyems Position",
          action: 1,
          value: "skycab actuator mcube pos",
          input: {
            axis: {
              title: "Axis",
              option: ["x", "y"],
              value: "x",
            },
            direction: {
              title: "Direction",
              option: ["f", "b"],
              value: "f",
            },
            position1: {
              title: "Position",
              value: null,
            },
            position2: {
              title: "Position",
              value: null,
            },
          },
        },
        speed: {
          title: "Gyems Speed",
          action: 1,
          value: "skycab actuator mcube speed",
          valueOff: "skycab actuator mcube off",
          valueStop: "skycab actuator mcube stop",
          input: {
            axis: {
              title: "Axis",
              option: ["x", "y"],
              value: "x",
            },
            speed: {
              title: "Speed",
              value: null,
            },
          },
        },
        calibrate: {
          title: "Mcube Calibrate",
          action: 1,
          value: "skycab system calibrate",
          input: {
            mode: {
              title: "Mode",
              value: "m",
              option: ["m"],
            },
            axis: {
              title: "Axis",
              value: "x",
              option: ["x", "y"],
            },
            direction: {
              title: "Direction",
              value: "f",
              option: ["f", "b"],
            },
          },
        },
      },
      winching: {
        winch: {
          title: "Pick and Drop",
          action: 1,
          value: "skycab system burnTest",
          input: {
            type: {
              title: "Type",
              value: "winch",
              option: ["winch"],
            },
            count: {
              title: "Count",
              value: null,
            },
            level: {
              title: "Level",
              value: null,
            },
            weight: {
              title: "Weight",
              value: null,
            },
            winchType: {
              title: "Winch Type",
              value: "cb",
              option: ["cb", "stl", "sth", "qcl", "qch"],
            },
          },
        },
        calibrate: {
          title: "Winching Calibrate",
          action: 1,
          value: "skycab system calibrate",
          input: {
            mode: {
              title: "Mode",
              option: ["w"],
              value: "w",
            },
            level: {
              title: "Level",
              value: null,
            },
            destType: {
              title: "Destination",
              option: ["cb", "stl", "sth", "qcl", "qch"],
              value: "cb",
            },
          },
        },
      },
      rssi: {
        title: "RSSI",
        action: 2,
        value: "RSSI",
        input: {
          repeat: {
            title: "Repeat",
            value: 1,
          },
          delay: {
            title: "Delay",
            value: 0,
          },
        },
      },
    },
    advanceShell: {
      bool: false,
      tab: null,
      sid: Array(),
      socketItem: null,
      all: false,
      value: null,
      valueForceMove: false,
      valueAxis: "x",
      valueWinch: "a",
      valueDirection: "f",
      valueQuantity: 1,
      valueForceWinch: false,
      valueZSpeed: 1,
      valueZType: "u",
      valueZQuantity: 1,
      valueZWeight: 1,
      valueZDes: "cb",
      valueForceWex: false,
      valueForceGrip: false,
      binX: null,
      binY: null,
      binCode: 0,
      item: ["Status", "Control", "Custom"],
      status: [
        {
          title: "Shell",
          data: [
            {
              title: "Enable",
              action: 0,
              color: "green",
              data: 1,
              icon: "mdi-access-point-check",
            },
            {
              title: "Disable",
              action: 0,
              color: "red",
              data: 0,
              icon: "mdi-access-point-remove",
            },
          ],
        },
        {
          title: "System",
          data: [
            {
              title: "Wake Up",
              action: 1,
              color: "green",
              data: "skycab system wakeup",
              icon: "mdi-clock-check-outline",
            },
            {
              title: "Hibernate",
              action: 1,
              color: "red",
              data: "skycab system hibernate",
              icon: "mdi-bed-clock",
            },
          ],
        },
        {
          title: "Winching Board",
          data: [
            {
              title: "WB A On",
              action: 1,
              color: "green",
              data: "skycab power pwr_ctrl invdl set winch_platform_a 1",
              icon: "mdi-access-point-check",
            },
            {
              title: "WB A Off",
              action: 1,
              color: "red",
              data: "skycab power pwr_ctrl invdl set winch_platform_a 0",
              icon: "mdi-access-point-remove",
            },
            {
              title: "Data A",
              action: 1,
              color: "lime",
              data: "skycab scanner bin_value_ch a",
              icon: "mdi-numeric",
            },
            {
              title: "Read A",
              action: 1,
              color: "orange",
              data: "skycab wnet read status a",
              icon: "mdi-book-open",
            },
          ],
        },
        {
          title: "",
          data: [
            {
              title: "WB B On",
              action: 1,
              color: "green",
              data: "skycab power pwr_ctrl invdl set winch_platform_b 1",
              icon: "mdi-access-point-check",
            },
            {
              title: "WB B Off",
              action: 1,
              color: "red",
              data: "skycab power pwr_ctrl invdl set winch_platform_b 0",
              icon: "mdi-access-point-remove",
            },
            {
              title: "Data B",
              action: 1,
              color: "lime",
              data: "skycab scanner bin_value_ch b",
              icon: "mdi-numeric",
            },
            {
              title: "Read B",
              action: 1,
              color: "orange",
              data: "skycab wnet read status b",
              icon: "mdi-book-open",
            },
          ],
        },
        {
          title: "Motor Power",
          data: [
            {
              title: "On",
              action: 1,
              color: "green",
              data: "skycab system set flag auto_contactor 1",
              icon: "mdi-access-point-check",
            },
            {
              title: "Off",
              action: 1,
              color: "red",
              data: "skycab system set flag auto_contactor 0",
              icon: "mdi-access-point-remove",
            },
          ],
        },
        {
          title: "Battery ",
          data: [
            { 
              title: "Release", 
              action: 1, 
              color: "orange", 
              data: "skycab battery action r", 
              icon: "mdi-lock-open" 
            }
          ],
        },
        {
          title: "System Reboot",
          data: [
            {
              title: "Reboot",
              action: 1,
              color: "red",
              data: "skycab system reboot",
              icon: "mdi-autorenew",
            },
          ],
        },
      ],
    },
    btnCheckBin: async function(code, coordX, coordY) {
      await this.$refs.checkBinDialog.submit(this.currentZone, code, coordX, coordY)
    },
    btnUpdateSid: async function(sid) {
      let res = await axios.get(
        `${getHost(this.currentZone)}${RouteOperation.CUBE}`,
        { headers: getRequestHeader() }
      );
      let json = res.data;
      if (json.status) {
        let data = json.data;

        if (data.HALT_CUBE.status != "AVAILABLE")
          this.$awn.tip(
            "Cube is currently halted, please ensure no people in the cube if you require to perform any skycar movement."
          );
      }

      if (sid == "all") {
        this.advanceShell["all"] = !this.advanceShell["all"];
        if (this.advanceShell["all"]) {
          this.advanceShell["sid"] = this.getSkycarID();
        } else {
          this.advanceShell["sid"] = Array();
        }
      } else {
        if (this.advanceShell["sid"].includes(sid)) {
          const index = this.advanceShell["sid"].indexOf(sid);
          this.advanceShell["sid"].splice(index, 1);
        } else {
          this.advanceShell["sid"].push(sid);
        }
      }
      this.txtEng = {
        status: "Accepted",
        reason: "Updated current skycar selection.",
        data: Array(),
      };
      this.boolEng = true;
      this.$emit("updateSid", this.advanceShell.sid, "Advanced");
    },
    btnSendShell: async function(json, repeat = 1, delay = 0) {
      let here = this;
      try {
        here.command = json;
        here.command.repeat = repeat;
        var data = Array();
        if (json.sid.length == 0) {
          data = Array();
          here.txtEng = {
            status: "Rejected",
            reason: "Please select a skycar to send command.",
            data: data,
          };
          here.boolEng = true;
        } else if (
          json.sid.length > 1 ||
          (json.action == 0 && json.value == 1) ||
          json.value == "skycab system reboot"
        ) {
          const sidList = json.sid;
          sidList.forEach(function(sid) {
            data.push(`SC,${sid},Q,${json.action},${json.value}`);
          });
          here.command = json;
          here.boolAll = true;
          here.txtAll = {
            status: "Warning",
            reason: "Are you sure to send the following command?",
            data: data,
          };
        } else if (json.sid.length == 1) {
          json.sid = json.sid[0];
          here.sendMessage({ type: "request", json: json }, repeat, delay);
        }
      } catch (error) {
        here.txtEng = { status: "Rejected", reason: error, data: data };
        here.boolEng = true;
      }
    },
    btnSendMove: async function(moveValue) {
      let here = this;
      let json = {};
      if (moveValue["force"]) {
        json = {
          sid: moveValue["sid"],
          action: "1",
          value: `skycab actuator mcube fmove ${moveValue.axis} ${moveValue.direction} ${moveValue.quantity}`,
        };
      } else {
        json = {
          sid: moveValue["sid"],
          action: "1",
          value: `skycab mcube ${moveValue.axis} ${moveValue.direction} ${moveValue.quantity}`,
        };
      }
      here.btnSendShell(json);
    },
    btnSendWinch: async function(winchValue) {
      let here = this;
      let commandPrefix = "skycar";
      let json = {};
      if (winchValue.valueZType == "u" || winchValue.valueZType == "d") {
        commandPrefix = `skycab dual winch z ${winchValue.valueZType} ${winchValue.valueWinch}`;
      } else {
        commandPrefix = `skycab dual ${winchValue.valueZType} ${winchValue.valueWinch}`;
      }
      json = {
        sid: winchValue.sid,
        action: "1",
        value: `${commandPrefix} ${winchValue.valueZQuantity} ${winchValue.valueZWeight} ${winchValue.valueZDes}`,
      };
      here.btnSendShell(json);
    },
    forceButtonColor: function(value, selectedValue, force) {
      if (selectedValue != value) {
        return "black";
      } else {
        if (force) {
          return "red";
        } else {
          return "orange";
        }
      }
    },
  }),
  methods: {
    openGrid(){
      this.$refs.dialogCoordinateSelection.openDialog(this.currentZone)
    },
    updateCoord(selectedCells){
      if (selectedCells.length > 0) {
        this.advanceShell["binX"] = String(selectedCells[0].x);
        this.advanceShell["binY"] = String(selectedCells[0].y);
      }
    },
    getSkycarID() {
      const skycarIDs = this.modelSkycar.skycar.map(
        (skycar) => skycar.skycar_id
      );
      return skycarIDs.sort(function(a, b) {
        return a - b;
      });
    },
    closeSkycarDialog() {
      (this.boolEng = false), (this.txtEng = null);
    },
    closeAllSkycarDialog() {
      (this.boolAll = false), (this.txtAll = null);
    },
    getMessage(socket) {
      var here = this;
      socket.on(Websocket.SKYCAR, function(item) {
        here.advanceShell.socketItem = item.item;
      });
    },
    async sendMessage(item, repeat = 1, delay = 0, all = false) {
      this.$refs.dialogDuplicatedShell.validateAndSendMessage(this.currentZone, item.json, repeat, delay, all)      
    },
    async btnSendAll() {
      let here = this;
      this.boolAll = false;
      let json = here.command;
      if (Array.isArray(json.sid)) {
        var sid_list = json.sid;
      } else {
        sid_list = [json.sid];
      }
      sid_list.forEach(async function(sid) {
        json.sid = parseInt(sid);
        here.sendMessage({ type: "request", json: json }, json.repeat, 0, true);
      });
    },
  },
  computed: {
    catchMessage() {
      return this.advanceShell.socketItem;
    },
    getWinchTip() {
      let here = this;
      let commandPrefix = "skycar";
      if (
        here.advanceShell.valueZType == "u" ||
        here.advanceShell.valueZType == "d"
      ) {
        commandPrefix = `skycab dual winch z ${here.advanceShell.valueZType} ${here.advanceShell.valueWinch}`;
      } else {
        commandPrefix = `skycab dual ${here.advanceShell.valueZType} ${here.advanceShell.valueWinch}`;
      }
      let value = `${commandPrefix} ${here.advanceShell.valueZQuantity} ${here.advanceShell.valueZWeight} ${here.advanceShell.valueZDes}`;
      return value;
    },
  },
  watch: {
    parentShellSid: function(newVal) {
      this.advanceShell.sid = newVal;
    },
  },
};
</script>
