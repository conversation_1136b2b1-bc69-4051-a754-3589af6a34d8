<template>
  <div>
    <!-- Skycar Selection -->
    <v-col>
      <v-row class="mt-3">
        <v-col>
          <v-btn
            v-for="sid in getSkycarID()"
            :key="sid"
            rounded
            :color="shell['sid'] == sid ? 'green' : 'red'"
            class="ma-1"
            @click="startShellMode(sid)"
          >
            <v-icon v-if="shell['sid'] == sid"
              >mdi-sticker-check-outline</v-icon
            >
            <v-icon v-else>mdi-sticker-remove-outline</v-icon>
            <span>SC {{ sid }}</span>
          </v-btn>
        </v-col>
      </v-row>
    </v-col>
    <div class="center_container">
      <v-btn v-if="shell.sid" color="green" @click="btnRefreshError()">
        Refresh
      </v-btn>
    </div>
    <v-row v-if="shell.sid">
      <!-- Left Panel -->
      <v-col class="col-50">
        <v-card dark>
          <h1 class="center_container">
            <v-icon class="mr-1">
              mdi-car
            </v-icon>
            Skycar {{ shell.sid }}
          </h1>
          <v-card-text style="color: white;">
            <!-- Shell Status -->
            <v-row>
              <v-col>
                <pre>Shell</pre>
                <span
                  v-for="item in [
                    {
                      title: 'Enable',
                      action: 0,
                      color: 'green',
                      data: 1,
                      icon: 'mdi-access-point-check',
                    },
                    {
                      title: 'Disable',
                      action: 0,
                      color: 'lime',
                      data: 0,
                      icon: 'mdi-access-point-remove',
                    },
                  ]"
                  :key="item['title']"
                >
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        width="110"
                        v-bind="attrs"
                        v-on="on"
                        class="ml-2 mt-2"
                        :color="item['color']"
                        dark
                        rounded
                        @click="
                          btnSendShell({
                            sid: shell['sid'],
                            action: item['action'],
                            value: item['data'],
                          })
                        "
                      >
                        <v-icon class="mx-1">{{ item["icon"] }}</v-icon>
                        {{ item["title"] }}
                      </v-btn>
                    </template>
                    <span>{{ item["data"] }}</span>
                  </v-tooltip>
                </span>
              </v-col>
            </v-row>
            <!-- Pairing Status -->
            <v-row>
              <!-- Pairing Request -->
              <v-col>
                <pre>Pairing</pre>
                <span
                  v-for="item in [
                    {
                      title: 'Pairing',
                      action: 1,
                      color: 'green',
                      value: 'skycab tc pairing request',
                      icon: 'mdi-car-connected',
                    },
                  ]"
                  :key="item['title']"
                >
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        width="110"
                        v-bind="attrs"
                        v-on="on"
                        class="mt-2 ml-2"
                        :color="
                          mcuSkycar.pairingError.normalError != null ||
                          mcuSkycar.pairingError.winchError != null
                            ? 'red'
                            : item['color']
                        "
                        dark
                        rounded
                        @click="
                          btnSendShell({
                            sid: shell['sid'],
                            action: item['action'],
                            value: item['value'],
                          })
                        "
                      >
                        <v-icon class="mx-1">{{ item["icon"] }}</v-icon>
                        {{ item["title"] }}
                      </v-btn>
                    </template>
                    <span>{{ item["value"] }}</span>
                  </v-tooltip>
                </span>
                <v-row>
                  <!-- Reject Reason -->
                  <v-col
                    class="red--text"
                    v-if="
                      mcuSkycar.pairingError.normalError != null ||
                        mcuSkycar.pairingError.winchError != null
                    "
                  >
                    <pre>{{ " " }}</pre>
                    <v-row class="ml-3">
                      <span v-if="mcuSkycar.pairingError.normalError != null">
                        <v-icon class="mr-1" color="red"
                          >mdi-alert-circle</v-icon
                        >
                        {{ mcuSkycar.pairingError.normalError.error_code }} -
                        {{ mcuSkycar.pairingError.normalError.error_name }}
                      </span>
                    </v-row>
                    <v-row class="ml-3">
                      <div v-if="mcuSkycar.pairingError.winchError != null">
                        <span
                          v-for="(value, key) in mcuSkycar.pairingError
                            .winchError"
                          :key="key"
                        >
                          <v-icon class="mr-1" color="red"
                            >mdi-alert-circle</v-icon
                          >
                          {{ key }} Winch : {{ value.error_code }} -
                          {{ value.error_name }}
                        </span>
                      </div>
                    </v-row>
                  </v-col>
                </v-row>
              </v-col>
              <!-- Force Pairing -->
              <v-col class="col-75">
                <pre>Force Pairing</pre>
                <v-row>
                  <!-- 
							<v-col v-if="!skycar.isDualWinch">
								<v-select
									v-model="shell.forcePairing.axis.value"
									:items="shell.forcePairing.axis.option"
									:label="shell.forcePairing.axis.title"
								></v-select>
							</v-col>
								-->
                  <v-col>
                    <v-text-field
                      v-model="shell.forcePairing.binValue.value"
                      :label="shell.forcePairing.binValue.title"
                      type="number"
                    ></v-text-field>
                  </v-col>
                  <v-col v-if="skycar.isDualWinch">
                    <v-text-field
                      v-model="shell.forcePairing.bBinValue.value"
                      :label="shell.forcePairing.bBinValue.title"
                      type="number"
                    ></v-text-field>
                  </v-col>
                  <v-col>
                    <v-tooltip bottom>
                      <template v-slot:activator="{ on, attrs }">
                        <v-btn
                          v-on="on"
                          v-bind="attrs"
                          color="green"
                          rounded
                          @click="
                            btnSendShell({
                              sid: shell.sid,
                              action: shell.forcePairing.action,
                              value: skycar.isDualWinch
                                ? `${shell.forcePairing.value} ${shell.forcePairing.binValue.value} ${shell.forcePairing.bBinValue.value}`
                                : `${shell.forcePairing.value} ${shell.forcePairing.binValue.value}`,
                            })
                          "
                          class="ma-1"
                          dark
                          width="110"
                          :disabled="
                            (shell.forcePairing.binValue.value == null &&
                              shell.forcePairing.bBinValue.value == null) ||
                              (shell.forcePairing.binValue.value == '' &&
                                shell.forcePairing.bBinValue.value == '')
                          "
                        >
                          <v-icon>{{ shell.forcePairing.icon }}</v-icon>
                          Confirm
                        </v-btn>
                      </template>
                      <span v-if="skycar.isDualWinch">
                        {{ shell.forcePairing.value }}
                        {{ shell.forcePairing.binValue.value }}
                        {{ shell.forcePairing.bBinValue.value }}
                      </span>
                      <span v-else>
                        {{ shell.forcePairing.value }}
                        {{ shell.forcePairing.binValue.value }}
                      </span>
                    </v-tooltip>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
            <!-- Recovery -->
            <v-row>
              <!-- Home -->
              <v-col>
                <pre>Recovery</pre>
                <span
                  v-for="item in [
                    {
                      title: 'Home',
                      action: 1,
                      data: 'skycab home',
                      color: 'green',
                      icon: 'mdi-home',
                    },
                  ]"
                  :key="item['title']"
                >
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        width="110"
                        v-bind="attrs"
                        v-on="on"
                        class="ml-2 mt-2"
                        :color="item['color']"
                        dark
                        rounded
                        @click="
                          btnSendShell({
                            sid: shell['sid'],
                            action: item['action'],
                            value: item['data'],
                          })
                        "
                      >
                        <v-icon class="mx-1">{{ item["icon"] }}</v-icon>
                        {{ item["title"] }}
                      </v-btn>
                    </template>
                    <span>{{ item["data"] }}</span>
                  </v-tooltip>
                </span>
              </v-col>
              <!-- Set Skycar Position -->
              <v-col class="col-75">
                <pre>Set Skycar Position</pre>
                <v-row>
                  <v-col>
                    <v-text-field
                      v-model="shell['coordX']"
                      type="number"
                      label="X"
                    >
                    </v-text-field>
                  </v-col>
                  <v-col>
                    <v-text-field
                      v-model="shell['coordY']"
                      type="number"
                      label="Y"
                    >
                    </v-text-field>
                  </v-col>
                  <v-col>
                    <v-tooltip bottom>
                      <template v-slot:activator="{ on, attrs }">
                        <v-btn
                          v-on="on"
                          v-bind="attrs"
                          color="green"
                          rounded
                          @click="
                            btnSendShell({
                              sid: shell['sid'],
                              action: '1',
                              value:
                                'skycab system set position ' +
                                shell['coordX'] +
                                ' ' +
                                shell['coordY'],
                            })
                          "
                          class="ma-1"
                          dark
                          width="110"
                          :disabled="
                            shell['coordX'] < 0 ||
                              shell['coordY'] < 0 ||
                              shell['coordX'] == null ||
                              shell['coordY'] == null
                          "
                        >
                          <v-icon>mdi-check</v-icon>
                          Confirm
                        </v-btn>
                      </template>
                      skycab system set position {{ shell["coordX"] }}
                      {{ shell["coordY"] }}
                    </v-tooltip>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
            <!-- Winch -->
            <v-row v-if="!skycar.isMoving || skycar.errorMsgList.length === 0">
              <v-col>
                <pre>Winch</pre>
                <v-row>
                  <v-col v-if="this.$store.state.cubeConfig.dual">
                    <v-select
                      v-model="shell.winch.side.value"
                      :items="shell.winch.side.option"
                      rounded
                      filled
                      :label="shell.winch.side.title"
                    ></v-select>
                  </v-col>
                  <v-col>
                    <v-select
                      v-model="shell.winch.type.value"
                      :items="shell.winch.type.option"
                      rounded
                      filled
                      :label="shell.winch.type.title"
                    ></v-select>
                  </v-col>
                  <v-col>
                    <v-text-field
                      v-model="shell.winch.quantity.value"
                      rounded
                      filled
                      :label="shell.winch.quantity.title"
                      type="number"
                    ></v-text-field>
                  </v-col>
                  <v-col>
                    <v-text-field
                      v-model="shell.winch.weight.value"
                      rounded
                      filled
                      :label="shell.winch.weight.title"
                      type="number"
                    ></v-text-field>
                  </v-col>
                  <v-col>
                    <v-select
                      v-model="shell.winch.des.value"
                      :items="shell.winch.des.option"
                      rounded
                      filled
                      :label="shell.winch.des.title"
                    ></v-select>
                  </v-col>
                  <!-- Confirm -->
                  <v-col>
                    <v-tooltip bottom>
                      <template v-slot:activator="{ on, attrs }">
                        <v-btn
                          v-on="on"
                          v-bind="attrs"
                          color="green"
                          width="120"
                          rounded
                          class="mt-4 ml-1"
                          @click="
                            btnSendShell({
                              sid: shell.sid,
                              action: '1',
                              value: this.$store.state.cubeConfig.dual
                              ? `skycab dual ${shell.winch.type.value} ${shell.winch.side.value} ${shell.winch.quantity.value} ${shell.winch.weight.value} ${shell.winch.des.value}`
                              : `skycab ${shell.winch.type.value} ${shell.winch.quantity.value} ${shell.winch.weight.value} ${shell.winch.des.value}`                                           
                            })
                          "
                          :disabled="
                            shell.winch.quantity.value < 1 ||
                              shell.winch.quantity.value > 15 ||
                              shell.winch.weight.value < 1
                          "
                        >
                          <v-icon>mdi-check</v-icon>
                          Confirm
                        </v-btn>
                      </template>
                        <span v-if="this.$store.state.cubeConfig.dual">
                              {{
                                `skycab dual ${shell.winch.type.value}  ${shell.winch.side.value} ${shell.winch.quantity.value} ${shell.winch.weight.value} ${shell.winch.des.value}`
                              }}
                            </span>
                            <span v-else>
                              {{
                                `skycab ${shell.winch.type.value} ${shell.winch.quantity.value} ${shell.winch.weight.value} ${shell.winch.des.value}`
                              }}
                        </span>
                    </v-tooltip>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
            <!-- Auto Jog -->
            <v-row v-if="skycar.isMoving || skycar.errorMsgList.length === 0">
              <v-col>
                <pre>Auto Jogging</pre>
                <v-row>
                  <v-col>
                    <v-select
                      v-model="shell.autoJog.axis.value"
                      :items="shell.autoJog.axis.option"
                      rounded
                      filled
                      :label="shell.autoJog.axis.title"
                    ></v-select>
                  </v-col>
                  <v-col>
                    <v-select
                      v-model="shell.autoJog.direction.value"
                      :items="shell.autoJog.direction.option"
                      rounded
                      filled
                      :label="shell.autoJog.direction.title"
                    ></v-select>
                  </v-col>
                  <v-col>
                    <v-text-field
                      v-model="shell.autoJog.speed.value"
                      rounded
                      filled
                      :label="shell.autoJog.speed.title"
                      type="number"
                    ></v-text-field>
                  </v-col>
                  <v-col>
                    <v-tooltip bottom>
                      <template v-slot:activator="{ on, attrs }">
                        <v-btn
                          v-on="on"
                          v-bind="attrs"
                          color="green"
                          rounded
                          @click="
                            btnSendShell({
                              sid: shell.sid,
                              action: shell.autoJog.action,
                              value: `${shell.autoJog.value} ${shell.autoJog.axis.value} ${shell.autoJog.direction.value} ${shell.autoJog.speed.value}`,
                            })
                          "
                          class="mt-4 mx-1"
                          dark
                          :disabled="!shell.autoJog.speed.value"
                        >
                          <v-icon>{{ shell.autoJog.icon }}</v-icon>
                          Confirm
                        </v-btn>
                      </template>
                      {{ shell.autoJog.value }} {{ shell.autoJog.axis.value }}
                      {{ shell.autoJog.direction.value }}
                      {{ shell.autoJog.speed.value }}
                    </v-tooltip>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
      <!-- Right Panel -->
      <v-col class="col-50">
        <!-- Status Panel  -->
        <v-card dark class="mb-5">
          <v-card-text style="color: white;">
            <v-row>
              <v-col>
                <!-- Error code and description -->
                <span v-if="skycar.errorCodeList.length === 0">
                  <v-col class="center_container">
                    <v-chip color="green">
                      No Skycar Error
                    </v-chip>
                  </v-col>
                </span>
                <span
                  v-else
                  v-for="err in skycar.errorCodeList"
                  v-bind:key="err.error_code"
                >
                  <v-col class=center_container>
                    <v-chip color="red">
                      <v-icon class="mr-1">mdi-alert-circle</v-icon>
                      {{ err.error_code }} -
                      {{ err.error_description }}
                    </v-chip>
                  </v-col>
                </span>
                <!-- TC Skycar Status -->
                <v-row class="mt-3 ml-3">
                  <h4>TC Skycar Status</h4>
                </v-row>
                <v-row>
                  <!-- Coordinate -->
                  <v-col>
                    <div class="center_container">
                      <v-tooltip bottom>
                        <template v-slot:activator="{ on, attrs }">
                          <v-chip
                            v-bind="attrs"
                            v-on="on"
                            :color="
                              skycar.coordX == null && skycar.coordY == null
                                ? 'black'
                                : 'orange'
                            "
                          >
                            <span
                              v-if="
                                skycar.coordX == null && skycar.coordY == null
                              "
                            >
                              N/A
                            </span>
                            <span v-else>
                              {{ skycar.coordX }}, {{ skycar.coordY }}
                            </span>
                          </v-chip>
                        </template>
                        <span>Coordinate</span>
                      </v-tooltip>
                    </div>
                  </v-col>
                  <!-- Storage -->
                  <v-col v-if="skycar.isDualWinch">
                    <div class="center_container">
                      <v-tooltip bottom>
                        <template v-slot:activator="{ on, attrs }">
                          <v-chip
                            v-bind="attrs"
                            v-on="on"
                            :color="skycar.storageA ? 'orange' : 'black'"
                          >
                            <span v-if="skycar.storageA">
                              A {{ skycar.storageA }}
                            </span>
                            <span v-else>
                              A No Bin
                            </span>
                          </v-chip>
                        </template>
                        <span>A Storage</span>
                      </v-tooltip>
                    </div>
                  </v-col>
                  <v-col v-if="skycar.isDualWinch">
                    <div class="center_container">
                      <v-tooltip bottom>
                        <template v-slot:activator="{ on, attrs }">
                          <v-chip
                            v-bind="attrs"
                            v-on="on"
                            :color="skycar.storageB ? 'orange' : 'black'"
                          >
                            <span v-if="skycar.storageB">
                              B {{ skycar.storageB }}
                            </span>
                            <span v-else>
                              B No Bin
                            </span>
                          </v-chip>
                        </template>
                        <span>B Storage</span>
                      </v-tooltip>
                    </div>
                  </v-col>
                  <v-col v-if="!skycar.isDualWinch">
                    <div class="center_container">
                      <v-tooltip bottom>
                        <template v-slot:activator="{ on, attrs }">
                          <v-chip
                            v-bind="attrs"
                            v-on="on"
                            :color="skycar.storageA ? 'orange' : 'black'"
                          >
                            <span v-if="skycar.storageA">
                              {{ skycar.storageA }}
                            </span>
                            <span v-else>
                              No Bin
                            </span>
                          </v-chip>
                        </template>
                        <span>Storage</span>
                      </v-tooltip>
                    </div>
                  </v-col>
                  <!-- Battery -->
                  <v-col>
                    <div class="center_container">
                      <v-tooltip bottom>
                        <template v-slot:activator="{ on, attrs }">
                          <v-chip
                            v-bind="attrs"
                            v-on="on"
                            :color="skycar.battery == null ? 'black' : 'orange'"
                          >
                            <span v-if="skycar.battery == null">
                              N/A
                            </span>
                            <span v-else> {{ skycar.battery }}% </span>
                          </v-chip>
                        </template>
                        <span>Battery</span>
                      </v-tooltip>
                    </div>
                  </v-col>
                </v-row>
                <v-divider></v-divider>
                <!-- MCU Skycar Status -->
                <v-row class="mt-2">
                  <v-card dark>
                    <v-card-actions>
                      <v-btn text @click="mcuSkycar.bool = !mcuSkycar.bool">
                        MCU Skycar Status
                        <v-icon>{{
                          mcuSkycar.bool ? "mdi-chevron-up" : "mdi-chevron-down"
                        }}</v-icon>
                      </v-btn>
                    </v-card-actions>
                    <v-expand-transition>
                      <div class="ml-8" v-if="mcuSkycar.bool">
                        <v-row class="mb-3">
                          <span
                            >Last Update: {{ mcuSkycar.lastUpdateTime }}</span
                          >
                        </v-row>
                        <v-row
                          class="mb-3"
                          v-if="mcuSkycar.coordStatus != null"
                        >
                          <span>
                            Invalid Coord :
                          </span>
                          <v-chip color="red" class="ml-2">
                            {{ mcuSkycar.coordStatus }}
                          </v-chip>
                        </v-row>
                        <span
                          v-for="(value, key) in mcuSkycar.sensorStatus"
                          :key="key"
                        >
                          <v-chip
                            class="mx-1 my-1"
                            :color="
                              value == null ? 'black' : value ? 'green' : 'red'
                            "
                          >
                            {{ key }}
                          </v-chip>
                        </span>
                      </div>
                    </v-expand-transition>
                  </v-card>
                </v-row>
              </v-col>
              <!-- Error Message To Handle -->
              <v-col v-if="skycar.errorMsgList.length > 0">
                <div class="center_container">
                  <h3 class="mb-4" style="color: red;">
                    <v-icon class="mr-2" :style="'color: red'"
                      >mdi-alert-circle</v-icon
                    >
                    Skycar Error Message
                  </h3>
                </div>
                <v-card light>
                  <v-card-text style="color: black;">
                    <h4 class="ml-2 center_container">
                      {{ skycar.handlingMsg.full_msg }}
                    </h4>
                  </v-card-text>
                </v-card>
                <span
                  class="center_container"
                  v-for="(errorMsg, index) in skycar.errorMsgList"
                  v-bind:key="errorMsg.full_msg"
                >
                  <v-btn
                    class="mx-2 mt-3"
                    light
                    :color="errorMsg == skycar.handlingMsg ? 'orange' : 'white'"
                    @click="handleErrorMsg(errorMsg)"
                  >
                    MSG {{ index + 1 }}
                  </v-btn>
                </span>
                <!--  
							<v-tabs v-model="tab" align-with-title>
								<v-tabs-slider></v-tabs-slider>
								<v-tab
									v-for="(tab,i) in skycar.errorMsgList"
									:key="tab.error_code"
									@click="handleErrorMsg(tab)"
								>
									Error Msg {{ i+1 }}
								</v-tab>
							</v-tabs>
							<v-tabs-items dark v-model="tab">
								<v-tab-item v-for="(errorMsg,i) in skycar.errorMsgList" :key="i">
									<v-card
										dark
										outlined
										class="my-2 mr-2">
										<v-card-text class="ml-2">
											<v-icon class="mr-2">mdi-alert-circle</v-icon>
											{{ errorMsg.full_msg }}
										</v-card-text>
									</v-card>
								</v-tab-item>
							</v-tabs-items>
							-->
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
        <!-- Grid Panel -->
        <v-card style="background-color: #f5eeee;" v-if="shell.sid">
          <Grid
            ref="gridComponent"
            :cubeDescName="currentZone"
            :gridObject="gridObject"
            :obstacleObject="gridObstacle"
            :skycarObject="gridSkycar"
            @grid-selected="handleGridSelect"
            @grid-selected-reset="handleGridReset"
          >
          </Grid>
        </v-card>
      </v-col>
    </v-row>
    <DialogSkycarShellMode
      :boolEng="boolEng"
      :txtEng="txtEng"
      :sids="shell.sid ? [shell.sid] : []"
      @close-dialog="closeSkycarDialog"
    />
    <DialogSkycarShellModeAll
      :boolAll="boolAll"
      :txtAll="txtAll"
      :sids="shell.sid ? [shell.sid] : []"
      @close-dialog="closeAllSkycarDialog"
      @send-all="btnSendAll"
    />

    <DialogCheckBin ref="checkBinDialog" />
    <DialogDuplicatedShell
      ref="dialogDuplicatedShell"
    />
  </div>
</template>

<script>
import Grid from "../cube/Grid.vue";
import axios from "axios";
import {
  getHost,
  getJobStatus,
  getRequestHeader,
  convertStringToLocal,
} from "../../helper/common.js";
import {
  RouteOperation,
  RouteError,
  SkycarJob,
} from "../../helper/enums.js";
import DialogSkycarShellMode from "../dialogs/DialogSkycarShellMode.vue";
import DialogSkycarShellModeAll from "../dialogs/DialogSkycarShellModeAll.vue";
import DialogCheckBin from "../skycarAction/CheckBin.vue";
import DialogDuplicatedShell from "./DialogDuplicatedShell.vue";
import { initializeGrid } from "../../api/grid.js";

export default {
  name: "ShellMode",
  props: {
    modelSkycar: {
      skycar: [],
      dtSelected: [],
      alert: null,
    },
    currentZone: null,
    parentShellSid: {
      type: Number,
      default: null,
    },
  },
  components: {
    DialogSkycarShellMode,
    DialogSkycarShellModeAll,
    Grid,
    DialogCheckBin,
    DialogDuplicatedShell
  },
  data: () => ({
    tab: null,
    boolEng: false,
    txtEng: null,
    boolAll: false,
    txtAll: null,
    shell: {
      sid: null,
      resBool: false,
      resTxt: null,
      coordX: null,
      coordY: null,
      status: [
        {
          title: "Shell",
          data: [
            {
              title: "Enable",
              action: 0,
              color: "green",
              data: 1,
              icon: "mdi-access-point-check",
            },
            {
              title: "Disable",
              action: 0,
              color: "lime",
              data: 0,
              icon: "mdi-access-point-remove",
            },
          ],
        },
        {
          title: "Pairing",
          data: [
            {
              title: "Pairing",
              action: 1,
              color: "green",
              data: "skycab system pairing",
              icon: "mdi-car-connected",
            },
          ],
        },
      ],
      forcePairing: {
        title: "Force Pairing",
        action: 1,
        value: "skycab tc pairing force",
        icon: "mdi-check",
        axis: {
          title: "Axis",
          value: "x",
          option: ["x", "y", "xy"],
        },
        binValue: {
          title: "A Bin Value",
          value: null,
        },
        bBinValue: {
          title: "B Bin Value",
          value: null,
        },
      },
      winch: {
        title: "Winch",
        action: 1,
        value: "skycab",
        icon: "mdi-check",
        side: {
          title: "Side",
          value: "a",
          option: ["a", "b"],
        },
        type: {
          title: "Type",
          value: "pick",
          option: ["pick", "drop"],
        },
        quantity: {
          title: "Quantity",
          value: 1,
        },
        weight: {
          title: "Weight",
          value: 30,
        },
        des: {
          title: "Destination",
          value: "stl",
          option: ["stl", "sth", "qcl", "qch", "cb","usp","usd","sti"],
        },
      },
      autoJog: {
        title: "Auto Jog",
        action: 1,
        value: "skycab actuator jog",
        icon: "mdi-check",
        axis: {
          title: "Axis",
          value: "x",
          option: ["x", "y"],
        },
        direction: {
          title: "Direction",
          value: "f",
          option: ["f", "b"],
        },
        speed: {
          title: "Speed",
          value: 50,
        },
      },
    },
    skycar: {
      coordX: null,
      coordY: null,
      isMoving: true,
      isDualWinch: true,
      storageA: null,
      storageB: null,
      battery: null,
      error: {
        bool: false,
        errorCode: null,
        errorDescription: null,
      },
      errorCodeList: Array(),
      handlingMsg: null,
      errorMsgList: Array(),
    },
    mcuSkycar: {
      bool: false,
      sensorStatus: null,
      lastUpdateTime: null,
      pairingError: {
        normalError: null,
        winchError: null,
      },
    },
    // grid region
    gridObject: {
      gridName: "C",
      gridMinX: 0,
      gridMaxX: 0,
      gridMinY: 0,
      gridMaxY: 0,
    },
    gridObstacle: {},
    gridSkycar: {},

    // grid component output
    gridData: {
      selectedCells: [],
    },

    startShellMode: async function(sid) {
      this.resetData();

      let res = await axios.get(
        `${getHost(this.currentZone)}${RouteOperation.CUBE}`,
        { headers: getRequestHeader() }
      );
      let json = res.data;
      if (json.status) {
        let data = json.data;

        if (data.HALT_CUBE.status != "AVAILABLE")
          this.$awn.tip(
            "Cube is currently halted, please ensure no people in the cube if you require to perform any skycar movement."
          );
      }

      if (this.shell.sid) {
        let prevSid = this.shell.sid
        if (this.shell.sid == sid) {
          // Deselect
          this.shell.sid = null;
          this.txtEng = {
            status: "Accepted",
            reason: "Updated current skycar selection.",
            data: Array(),
          };
          this.boolEng = true;
          this.$emit("updateSid", this.shell.sid, "Normal",prevSid);
          //TODO clear all the variable value
          return;
        }
        this.$emit("updateSid", null, "Normal",prevSid);
      }
      this.shell.sid = sid;
      this.$emit("updateSid", this.shell.sid, "Normal");
      // TODO call the api to request all the related information
      await this.getSkycarErrorMsg(this.shell.sid);
      this.txtEng = {
        status: "Accepted",
        reason: "Updated current skycar selection.",
        data: Array(),
      };
      this.boolEng = true;

      // Initialize Grid only when shell mode enable
      this.fetchGridData();
    },
    btnSendShell: async function(json, repeat = 1, delay = 0) {
      let here = this;
      try {
        here.command = json;
        here.command.repeat = repeat;
        var data = Array();
        if (json.action == 0 && json.value == 1) {
          data.push(`SC,${json.sid},Q,${json.action},${json.value}`);
          here.command = json;
          here.boolAll = true;
          here.txtAll = {
            status: "Warning",
            reason: "Are you sure to send the following command?",
            data: data,
          };
        } else {
          here.shell.resTxt = Array();
          here.sendMessage({ type: "request", json: json }, repeat, delay);
          here.shell.resBool = true;
        }
      } catch (error) {
        here.txtEng = { status: "Rejected", reason: error, data: data };
        here.boolEng = true;
      }
    },
    btnRefreshError: async function() {
      let here = this;
      here.resetData();
      await here.getSkycarErrorMsg(here.shell.sid);
      await this.fetchGridData();
    },
  }),
  methods: {
    getJobStatus,
    getSkycarID() {
      const skycarIDs = this.modelSkycar.skycar.map(
        (skycar) => skycar.skycar_id
      );
      return skycarIDs.sort(function(a, b) {
        return a - b;
      });
    },
    closeSkycarDialog() {
      (this.boolEng = false), (this.txtEng = null);
    },
    closeAllSkycarDialog() {
      (this.boolAll = false), (this.txtAll = null);
    },
    handleErrorMsgResponse(model) {
      if (model) {
        this.skycar.isDualWinch = model.is_dual;
        this.skycar.battery = model.battery;
        this.skycar.coordX = model.x;
        this.skycar.coordY = model.y;
        this.skycar.errorMsgList =
          typeof model.error_messages === "undefined"
            ? []
            : model.error_messages;

        if (this.skycar.isDualWinch) {
            this.skycar.storageA = model.storage_codes["LEFT"];
            this.skycar.storageB = model.storage_codes["RIGHT"];
          }
        else { 
          this.skycar.storageA = model.storage_codes["BOTH"];
        }
        
        if (this.skycar.errorMsgList.length > 0) {
          this.handleErrorMsg(this.skycar.errorMsgList[0]);
        } else {
          this.skycar.errorCodeList =
            typeof model.errors === "undefined" ? [] : model.errors;
        }
        this.handleHardwareStatus(model.hardware_status);
      }
    },
    handleHardwareStatus(hardwareStatus) {
      let data = hardwareStatus;
      this.mcuSkycar.sensorStatus = data.sensor_status;
      this.mcuSkycar.lastUpdateTime = convertStringToLocal(
        data.status_updated_at
      );
      this.mcuSkycar.coordStatus = data.invalid_coord;
      this.mcuSkycar.pairingError.normalError = data.error;
      this.mcuSkycar.pairingError.winchError =
        Object.keys(data.winch_error).length === 0 ? null : data.winch_error;
    },
    handleErrorMsg(data) {
      let here = this;
      here.skycar.handlingMsg = data;
      here.skycar.errorCodeList = [
        {
          error_code: data.error_code,
          error_description: data.error_name,
        }
      ];
      here.shell.coordX = data.x;
      here.shell.coordY = data.y;
      here.shell.forcePairing.axis.value = data.axis;
      here.shell.forcePairing.binValue.value = here.skycar.storageA;
      here.shell.forcePairing.bBinValue.value = here.skycar.storageB;
      here.shell.autoJog.axis.value = data.axis;
      here.shell.winch.quantity.value = data.quantity;
      here.shell.winch.des.value = data.des_type.toLowerCase();
      let action = data.action.toLowerCase();
      if (action == SkycarJob.FORWARD || action == SkycarJob.BACKWARD) {
        here.skycar.isMoving = true;
        here.shell.autoJog.direction.value = action;
      } else if (action == SkycarJob.PICK || action == SkycarJob.DROP) {
        here.skycar.isMoving = false;
        if (data.position == null) here.shell.winch.side.value == "a";
        else here.shell.winch.side.value = data.position.toLowerCase();
        if (action == SkycarJob.PICK) {
          here.shell.winch.type.value = "pick";
        } else {
          here.shell.winch.type.value = "drop";
        }
      }
    },
    resetData() {
      let here = this;
      here.shell.coordX = null;
      here.shell.coordY = null;
      here.shell.forcePairing.axis.value = "x";
      here.shell.forcePairing.binValue.value = null;
      here.shell.forcePairing.bBinValue.value = null;
      here.shell.winch.side.value = "a";
      here.shell.winch.type.value = "pick";
      here.shell.winch.quantity.value = 1;
      here.shell.winch.weight.value = 30;
      here.shell.winch.des.value = "stl";
      here.shell.autoJog.axis.value = "x";
      here.shell.autoJog.direction.value = "f";
      here.shell.autoJog.speed.value = 50;
      here.skycar.handlingMsg = null;
      here.skycar.coordX = null;
      here.skycar.coordY = null;
      here.skycar.isMoving = true;
      here.skycar.isDualWinch = true;
      here.skycar.storageA = null;
      here.skycar.storageB = null;
      here.skycar.battery = null;
      here.skycar.errorCodeList = Array();
      here.skycar.errorMsgList = Array();
      here.mcuSkycar.bool = false;
      here.mcuSkycar.sensorStatus = null;
      here.mcuSkycar.lastUpdateTime = null;
      here.mcuSkycar.pairingError.normalError = null;
      here.mcuSkycar.pairingError.winchError = null;
    },
    async sendMessage(item, repeat = 1, delay = 0, all = false) {
      this.$refs.dialogDuplicatedShell.validateAndSendMessage(this.currentZone, item.json, repeat, delay, all)
    },
    async btnSendAll() {
      let here = this;
      this.boolAll = false;
      let json = here.command;
      here.shell.resTxt = Array();
      json.sid = parseInt(json.sid);
      here.sendMessage({ type: "request", json: json }, json.repeat, 0, true);
      here.shell.resBool = true;
    },
    async getSkycarErrorMsg(sid) {
      var searchParams = new URLSearchParams({ skycar_id: sid });
      let res = await axios.get(
        `${getHost(this.currentZone)}${RouteError.ERROR_MSG}?${searchParams}`,
        { headers: getRequestHeader() }
      );
      let json = res.data;
      if (json.status) {
        this.handleErrorMsgResponse(json.model);
      } else {
        this.$awn.tip("Failed to get skycar error message");
      }
    },

    //#region grid
    async fetchGridData() {
      const gridData = await initializeGrid(this.currentZone);
      console.log(gridData);
      if (gridData) {
        this.$refs.gridComponent.updateAll(
          gridData.gridObstacle,
          gridData.gridSkycar,
          gridData.gridObject
        );
        this.lastUpdated = gridData.lastUpdated;
      }
    },

    async handleGridSelect(selectedCells) {
      this.gridData.selectedCells = selectedCells;

      // handle single cell click
      // a. popup Bin Dialog
      // b. fill up skycar xy TODO
      if (this.gridData.selectedCells) {
        if (this.gridData.selectedCells.length === 1) {
          let cell = this.gridData.selectedCells[0];
          await this.$refs.checkBinDialog.submit(
            this.currentZone,
            null,
            cell.x,
            cell.y
          );
        }
      }
    },
    handleGridReset() {},
    //#endregion
  },
  computed: {
    getWinchTip() {
      let here = this;
      let commandPrefix =
        here.shell.winch.side.value === "both"
          ? `skycab ${here.shell.winch.type.value}`
          : `skycab dual ${here.shell.winch.type.value} ${here.shell.winch.side.value}`;
      let value = `${commandPrefix} ${here.shell.winch.quantity.value} ${here.shell.winch.weight.value} ${here.shell.winch.des.value}`;
      return value;
    },
  },
  watch: {
    "skycar.isDualWinch": function(newValue) {
      if (newValue) {
        this.shell.forcePairing.binValue.title = "A Bin Value";
      } else {
        this.shell.forcePairing.binValue.title = "Bin Value";
      }
    },
    "shell.sid": function(value) {
      if (value == null) this.resetData();
    },
    currentZone() {
      this.shell.sid = null;
      this.resetData();
    },
    parentShellSid: function(newVal) {
      this.shell.sid = newVal;
    },
  },
};
</script>

<style scoped>
.center_container {
  display: flex;
  justify-content: center;
  /* Horizontal alignment */
  align-items: center;
  /* Vertical alignment */
}

.col-75 {
  flex: 0 0 75%;
  max-width: 75%;
}
.col-50 {
  flex: 0 0 50%;
  max-width: 50%;
}

@media (max-width: 780px) {
  .col-50 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .col-75 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}
</style>
