<template>
  <div class="views">
    <div class="views__head">
      <div class="views__tabs">
        <v-tabs dark :background-color="'transparent'" v-model="tab">
          <v-tab class="tab" href="#kanban" @click="kanbanTabClickEmit"
            ><v-icon class="pr-2">mdi-clipboard-edit-outline</v-icon> Kanban
            View</v-tab
          >
          <v-tab class="tab" href="#list" @click="listTabClickEmit"
            ><v-icon class="pr-2">mdi-server</v-icon> List View</v-tab
          >
        </v-tabs>
      </div>
      <div class="views__actions">
        <!-- <v-switch
          class="mt-0"
          dark
          hide-details
          v-model="autoRefresh"
          :label="`Auto Refresh (${autoRefreshIntervalInSec}s)`"
          @click="toggleAutoRefresh()"
        ></v-switch> -->
        <v-btn
          dark
          class="blue-grey lighten-0 ml-5"
          @click="reloadData()"
          :loading="refreshLoading"
          >Refresh</v-btn
        >
      </div>
    </div>
    <div class="views__body">
      <v-tabs-items class="transparent views__wrapper" v-model="tab">
        <!-- #region KANBAN View -->
        <v-tab-item value="kanban" dark class="transparent views__container">
          <div class="mb-3 px-4 text-right flex-grow-0">
            <v-btn
              small
              dark
              outlined
              color="primary"
              v-if="enableEdit"
              @click="onEnableEditClick"
            >
              <v-icon start class="mr-1">mdi-table-edit</v-icon>
              Enable Edit Rank
            </v-btn>

            <span
              v-if="!enableEdit"
              no-gutters
              style="flex-wrap: nowrap; justify-content:flex-end"
            >
              <v-btn
                small
                dark
                outlined
                color="success"
                :disabled="isSaveButtonDisabled"
                @click="saveRankingKanban"
              >
                <v-icon start class="mr-1" color="lightgreen"
                  >mdi-content-save-edit</v-icon
                >
                Save Ranking
              </v-btn>

              <v-btn
                class="ml-2"
                small
                dark
                outlined
                color="warning"
                @click="onDisableEditClick"
              >
                <v-icon start class="mr-1">mdi-table-off</v-icon>
                Disable Edit Rank
              </v-btn>
            </span>
          </div>

          <Kanban
            class="flex-grow-1"
            :dropzones="dropGroups"
            :inPlace="true"
            :enableEdit="enableEdit"
            @dropInDestinationBucket="destinationBucketDropEvent"
          >
            <template #dd-card="{ cardData }">
              <CustomCard
                :data="cardData"
                @pull-drawer="handlePullDrawer"
                @storage="handleStorageDrawer"
              />
            </template>
          </Kanban>
        </v-tab-item>

        <StorageDrawer
          :advOrder="drawer.storage"
          :drawerAppear="drawer.pullDrawer"
          @drawer-closed="handleDrawerClose"
          @saving="savingReload"
        ></StorageDrawer>
        <!-- #endregion KANBAN View -->

        <!-- #region LIST View -->
        <v-tab-item value="list" dark class="transparent views__container">
          <div class="mb-3 px-4 d-flex justify-space-between">
            <div>
              <span class="grey--text">
                {{ selected.length }} rows selected. Total records:
                {{ advOrder.length }}
              </span>
            </div>
            <div>
              <!-- <v-btn
                  v-if="enableEdit"
                  style="font-size: x-small; background-color: transparent; border: 2px solid rgb(174, 233, 119) ; color: rgb(174, 233, 119)"
                  @click="onEnableEditClick"
                >
                  <v-icon start color="lightgreen">mdi-table-edit</v-icon>
                  Enable Edit Rank
                </v-btn> -->
              <v-btn
                small
                dark
                color="red"
                outlined
                :disabled="isDeleteButtonDisabled"
                @click="deleteAdvOrder"
              >
                <v-icon start class="mr-1">mdi-delete-outline</v-icon>
                Remove Selected
              </v-btn>
              <!-- <v-row v-if="!enableEdit" no-gutters style="flex-wrap: nowrap;">
                          <v-btn
                            style="font-size: x-small; background-color: transparent; border: 2px solid lightcoral ; color: lightcoral"
                            @click="onEnableEditClick"
                          >
                            <v-icon start color="red">mdi-table-off</v-icon>
                            Disable Edit Rank
                          </v-btn>
                          <div style="padding: 1%;"></div>
                          <v-btn
                            style="font-size: x-small; background-color: transparent; border: 2px solid rgb(174, 233, 119) ; color: rgb(174, 233, 119)"
                            @click="saveRanking"
                          >
                            <v-icon start color="lightgreen"
                              >mdi-content-save-edit</v-icon
                            >
                            Save Ranking
                          </v-btn>
                          <div style="padding: 1%;"></div>
                        </v-row> -->
            </div>
          </div>

          <v-data-table
            v-model="selected"
            :headers="advancedHeaders"
            :items="advOrder"
            item-key="orderNo"
            multi-sort
            dark
            dense
            class="transparent"
            style="max-height: max-content; padding: 1rem;"
            :footer-props="{
              'items-per-page-options': [25, 50, 100, -1],
            }"
          >
            <template v-slot:body="{ items, isSelected }">
              <draggable
                :list="items"
                :disabled="true"
                tag="tbody"
                @start="onDragBefore"
                @end="onDragEnd"
                :class="{ 'drag-handle': !enableEdit }"
              >
                <tr
                  v-for="order in items"
                  :key="order.id"
                  :class="rowColor(order)"
                >
                  <td>
                    <!-- <v-checkbox
                            :input-value="isSelected(order)"
                            hide-details
                            @click="select(order, !isSelected(order))"
                          ></v-checkbox> -->
                    <v-checkbox
                      inline
                      class="my-2 py-1 d-inline-flex"
                      :input-value="isSelected(order)"
                      hide-details
                      @click="handleCheckboxClick(order)"
                    ></v-checkbox>
                  </td>

                  <td>{{ order.rank ? order.rank : 0 }}</td>
                  <td>{{ order.orderNo }}</td>
                  <td>{{ order.overallStatus }}</td>
                  <td>{{ order.storages.length }}</td>
                  <td>{{ order.optimised }}</td>
                  <td>{{ order.createdAt }}</td>
                </tr>
              </draggable>
            </template>
          </v-data-table>
        </v-tab-item>
        <!-- #endregion LIST View -->
      </v-tabs-items>
    </div>
  </div>
</template>

<script>
//import { AdvancedOrderAPI } from '../../api/advanced-order'
import { StatisticsAPI } from "../api/statistics";
import { axiosInstance } from "../api/base";
//import { Component } from '../../constants/component.enum'
import Kanban from "./kanban/KanBan.vue";
import CustomCard from "./CustomCard.vue";
import StorageDrawer from "./kanban/AdvancedOrderStorages.vue";
import draggable from "vuedraggable";
import { AdvancedOrderAPI } from "../api/advanced-order"

export default {
  name: "app",
  components: {
    Kanban,
    CustomCard,
    StorageDrawer,
    draggable,
  },
  data() {
    const threeDSort = (a, b) => {
      const node1 = this.advOrder.node[a];
      const node2 = this.advOrder.node[b];
      if (node1 && node2) {
        return node1.x - node2.x || node1.y - node2.y || node1.z - node2.z;
      }
      return 0;
    };

    const storageCodeSort = (a, b) => {
      const storage1 = this.storages[a];
      const storage2 = this.storages[b];
      if (storage1 && storage2) {
        return storage1.storageCode - storage2.storageCode;
      }
      return 0;
    };
    return {
      refreshLoading: false,
      autoRefreshIntervalInSec: 10,
      autoRefreshTimer: null,
      autoRefresh: false,

      selected: [],
      advOrder: [],
      oldAdvancedOrderList: [],

      enableEdit: true,
      oldRank: null,
      newRank: null,
      tab: null,
      advancedOrderNoFilter: null,
      saveDisabled: true,

      startIndex: null,
      endIndex: null,
      rankChangesAtKanban: null,
      dropGroups: [
        {
          name: "New",
          displayName: "✨ New",
          description: "Untouched or not yet scanned",
          children: [],
        },
        {
          name: "Optimising",
          displayName: "🎛️ Optimising",
          description: "Scanned or partially optimised",
          children: [],
        },
        {
          name: "Optimised",
          displayName: "✅ Optimised",
          description: "All storages are optimised",
          children: [],
        },
        {
          name: "Retrieving",
          displayName: "📜 Retrieving",
          description: "Advanced order with Retrieving",
          children: [],
        },
      ],
      advancedHeaders: [
        { text: "/", value: "", align: "left", groupable: false },
        { text: "RANK", value: "rank", align: "left", groupable: false },
        {
          text: "ORDER NO",
          value: "orderNo",
          align: "left",
          groupable: false,
        },
        {
          text: "STATUS",
          value: "overallStatus",
          align: "left",
          groupable: false,
          sort: storageCodeSort,
        },
        {
          text: "STORAGES",
          value: "storage.length",
          align: "left",
          groupable: false,
        },
        {
          text: "OPTIMISED",
          value: "optimised",
          align: "left",
          groupable: false,
          sort: threeDSort,
        },
        {
          text: "RECEIVED AT",
          value: "createdAt",
          align: "left",
          groupable: false,
        },
      ],
      drawer: {
        pullDrawer: false,
        storage: [],
      },
    };
  },
  beforeMount() {
    this.reloadData();
  },
  watch: {
    options: {
      async handler() {
        this.reloadData();
      },
      deep: true,
    },
  },
  computed: {
    isDeleteButtonDisabled() {
      return this.selected.length === 0;
    },
    isSaveButtonDisabled() {
      return this.saveDisabled;
    },
  },
  methods: {
    savingReload(isSave) {
      if (isSave) this.reloadData();
    },
    // log(...data) {
    //   //console.log(...data)
    // },
    kanbanTabClickEmit() {
      // Pass the information up to the parent
      this.$emit("view-tab-clicked", false);
    },
    listTabClickEmit() {
      // Pass the information up to the parent
      this.$emit("view-tab-clicked", true);
    },
    //Checkbox acts as Radio (temporary)
    handleCheckboxClick(clickedItem) {
      // Handle checkbox clicks to act like radio buttons
      if (!this.selected.includes(clickedItem)) {
        // If the clicked item is not in the selected array, select only that item
        this.selected = [clickedItem];
      } else {
        // If the clicked item is already selected, deselect it
        this.selected = [];
      }
    },

    onEnableEditClick() {
      this.enableEdit = !this.enableEdit;
    },
    onDisableEditClick() {
      this.enableEdit = !this.enableEdit;
      this.reloadData();
    },
    onDragBefore() {
      this.oldRanks = this.advOrder.map((item) => item.rank);
    },
    onDragEnd(event) {
      // Get the dragged item and the new index
      //const draggedItem = event.item

      const newIndex = event.newIndex;
      const oldIndex = event.oldIndex;

      this.startIndex =
        this.startIndex == null ||
        this.startIndex > Math.min(oldIndex, newIndex)
          ? Math.min(oldIndex, newIndex)
          : this.startIndex;
      this.endIndex =
        this.endIndex == null || this.endIndex < Math.max(oldIndex, newIndex)
          ? Math.max(oldIndex, newIndex)
          : this.endIndex;

      // Clone the current advOrder array
      const updatedOrder = [...this.advOrder];

      // Remove the dragged item from its old position
      const [removedItem] = updatedOrder.splice(event.oldIndex, 1);

      // Insert the dragged item at the new position
      updatedOrder.splice(newIndex, 0, removedItem);

      // Update the ranks based on the new order
      // for (let i = 0; i < updatedOrder.length; i++) {
      //   updatedOrder[i].rank = i + 1
      // }

      // Restore the saved ranks for the affected range
      const startIdx = Math.min(event.oldIndex, newIndex);
      const endIdx = Math.max(event.oldIndex, newIndex);

      for (let i = startIdx; i <= endIdx; i++) {
        updatedOrder[i].rank = this.oldRanks[i];
      }

      // Update the advOrder with the new order and ranks
      this.advOrder = updatedOrder;
    },

    rowColor(item) {
      if (!item) return "grey--text text--darken-1";
      switch (item.overallStatus) {
        case "New":
          return "light-blue--text text--lighten-1";
        case "Optimising":
          return "yellow--text text--lighten-2";
        case "Optimised":
          return "light-green--text text--accent-2";
        case "Retrieving":
          return "green--text text--lighten-1";
      }
    },

    async saveRanking() {
      const rankOrderChanges = [];
      let advOrderRank = {};

      for (let i = this.startIndex; i <= this.endIndex; i++) {
        advOrderRank = {
          orderNo: this.advOrder[i].orderNo,
          rank: this.advOrder[i].rank,
        };
        rankOrderChanges.push(advOrderRank);
      }

      await axiosInstance.patch(`/advanced-orders/rank`, {
        orders: rankOrderChanges,
        debounceEnable: false,
      });

      this.startIndex = 0;
      this.endIndex = 0;
      this.saveDisabled = true;
      this.enableEdit = !this.enableEdit
    },

    async deleteAdvOrder() {
      try {
        if (
          confirm(
            `are you sure you want to delete Advanced Order ${this.selected[0].orderNo}?`,
          )
        ) {
          await AdvancedOrderAPI.deleteAdvancedOrder(this.selected[0].orderNo)
           
          alert(
            `removed advanced order: ${this.selected[0].orderNo}`,
          )
        }
      } catch (ex) {
        alert(`something went wrong`)
        console.error(ex.message)
      } finally {
        await this.reloadData()
      }
    },

    handlePullDrawer(value) {
      this.$set(this.drawer, "pullDrawer", value);
    },

    handleStorageDrawer(value) {
      console.log(value)
      this.$set(this.drawer, "storage", value);
    },

    handleDrawerClose(value) {
      this.drawer.pullDrawer = value;
    },

    addToGroup(groupName, data) {
      const groupIndex = this.dropGroups.findIndex(
        (group) => group.name === groupName
      );
      if (groupIndex !== -1) {
        this.dropGroups[groupIndex].children.push(data);
      } else {
        console.error(`Group with name ${groupName} not found.`);
      }
    },

    async loadAdvancedOrders() {
      try {
        this.refreshLoading = true;

        const order = await StatisticsAPI.getAdvancedOrderWithRank();

        this.dropGroups.forEach((group) => {
          group.children = [];
        });

        //set & group
        let checkRetrieve,
          checkScanned = false;

        let optimisedCount = 0,
          optimisingCount = 0,
          pendingCount = 0;

        order.map((o) => {
          o.storages.map((st, index) => {
            st.index = index + 1;
            st.coor3D =
              st.coordinate.x != null &&
              st.coordinate.y != null &&
              st.coordinate.z != null
                ? `${st.coordinate.x},${st.coordinate.y},${st.coordinate.z}`
                : `STATION ${st.station}`;
            if (st.hasRetrieving) {
              st.statusGroup = "Retrieving";
              checkRetrieve = true;
            } else if (st.isOptimised) {
              st.statusGroup = "Optimised";
              optimisedCount++;
            } else if (st.lastScannedAt) {
              st.statusGroup = "Optimising";
              optimisingCount++;
              checkScanned = true;
            } else {
              pendingCount++;
              st.statusGroup = "Pending";
            }
          });

          o.optimised = optimisedCount;
          o.optimising = optimisingCount;
          o.pending = pendingCount;

          if (checkRetrieve) {
            o.overallStatus = "Retrieving";
            this.addToGroup("Retrieving", o);
          } else if ((o.storages ? o.storages.length : 0) === optimisedCount) {
            o.overallStatus = "Optimised";
            this.addToGroup("Optimised", o);
          } else if (checkScanned) {
            o.overallStatus = "Optimising";
            this.addToGroup("Optimising", o);
          } else {
            o.overallStatus = "New";
            this.addToGroup("New", o);
          }

          //reset
          checkRetrieve = false;
          checkScanned = false;
          optimisedCount = 0;
          optimisingCount = 0;
          pendingCount = 0;
        });

        this.advOrder = order;
      } catch (ex) {
        alert(`something went wrong`);
        console.error(ex.message);
      } finally {
        this.refreshLoading = false;
      }
    },

    toggleAutoRefresh() {
      if (this.autoRefresh) {
        if (this.autoRefreshTimer) clearInterval(this.autoRefreshTimer);

        this.autoRefreshTimer = setInterval(() => {
          this.reloadData();
        }, this.autoRefreshIntervalInSec * 1000);
      } else {
        clearInterval(this.autoRefreshTimer);
      }
    },

    async reloadData() {
      console.log(`active advanced order reloading...`);
      await this.loadAdvancedOrders();
    },

    async destinationBucketDropEvent(start, end, advOrder) {
      this.startIndex = start;
      this.endIndex = end;
      this.rankChangesAtKanban = advOrder;

      this.saveDisabled = false;
    },

    async saveRankingKanban() {
      const rankOrderChanges = [];
      let advOrderRank = {};

      for (let i = this.startIndex; i <= this.endIndex; i++) {
        advOrderRank = {
          orderNo: this.rankChangesAtKanban.children[i].orderNo,
          rank: this.rankChangesAtKanban.children[i].rank,
        };
        rankOrderChanges.push(advOrderRank);
      }

      await axiosInstance.patch(`/advanced-orders/rank`, {
        orders: rankOrderChanges,
        //debounceEnable: false,
      });

      alert(`Ranking Updated`);

      this.startIndex = 0;
      this.endIndex = 0;

      this.saveDisabled = true;
      this.enableEdit = !this.enableEdit
    },
  },
};
</script>

<style scoped lang="scss">
.views {
  font-family: "Avenir", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  display: flex;
  height: 100%;
  flex-direction: column;

  &__head {
    display: flex;
    padding: 1rem;
  }

  &__tabs {
    flex: 1 1 auto;
  }

  &__actions {
    flex: 0 1 auto;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  &__body {
    height: 100%;
    position: relative;
  }

  &__wrapper,
  &__container {
    height: 100%;
  }

  &__container {
    display: flex;
    flex-direction: column;
  }
}

.tab {
  font-size: 0.75rem;
}

.drag-handle {
  cursor: grab;
}
</style>
