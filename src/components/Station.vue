/* eslint camelcase: 0 */

<template>
  <v-app app>
    <v-container fluid>
      <v-card dark>
        <v-col>
          <v-row>
            <v-select
              v-model="currentZone"
              :items="zones"
              @change="viewStationDetail()"
              class="ma-2"
              prepend-icon="mdi-cube"
            ></v-select>
            <v-tooltip bottom>
              <template v-slot:activator="{ on, attrs }">
                                 <v-chip
                   v-bind="attrs"
                   v-on="on"
                   color="blue"
                   dark
                   class="ma-2"
                   @click="showRuntimeStationsDialog()"
                 >
                  <v-icon left>mdi-server-network</v-icon>
                  {{ runtimeStationCount }}
                </v-chip>
              </template>
              <span>TC Stations: {{ runtimeStationCount }} detected</span>
            </v-tooltip>
            <v-btn
              @click="viewStationDetail()"
              color="green"
              dark
              class="ma-2"
              :disabled="!stationDetailData.doneSync"
            >
              <span>Sync</span>
            </v-btn>
          </v-row>
        </v-col>
        <v-progress-linear
          v-if="!stationDetailData.doneSync"
          color="green"
          indeterminate
        ></v-progress-linear>
      </v-card>
      <v-data-table
        :headers="stationDetailData.headers"
        item-key="station_id"
        :items="stationDetailData.response.data"
        :items-per-page="-1"
        class="elevation-1"
        dark
        sort-by="id"
      >
        <!-- cell filtering -->
        <template v-slot:[`item.station_id`]="{ item }">
          <span :class="getStationIdColor(item.is_recovery)">
            {{ item.station_id }}
          </span>
        </template>
        <template v-slot:[`item.storage_quantity`]="{ item }">
          <v-tooltip bottom>
            <template v-slot:activator="{ on, attrs }">
              <span v-bind="attrs" v-on="on">
                <v-chip class="mr-4" color="green">
                  <v-icon class="mr-2">mdi-cube-send</v-icon>
                  {{ item.storage_quantity[0].length }}
                </v-chip>
              </span>
            </template>
            <span>Before: {{ item.storage_quantity[0] }}</span>
          </v-tooltip>
          <v-tooltip bottom>
            <template v-slot:activator="{ on, attrs }">
              <span v-bind="attrs" v-on="on">
                <v-chip
                  v-if="item.bin_at_worker"
                  class="mr-4"
                  :color="item.is_overweight ? 'red' : 'lime'"
                >
                  <v-icon class="mr-2">mdi-alpha-w-box</v-icon>
                  {{ item.bin_at_worker }}
                </v-chip>
              </span>
            </template>
            <span v-if="item.is_overweight"> Overweight </span>
            <span v-else> Worker </span>
          </v-tooltip>
          <v-tooltip bottom>
            <template v-slot:activator="{ on, attrs }">
              <span v-bind="attrs" v-on="on">
                <v-chip color="orange">
                  <v-icon class="mr-2">mdi-account-check</v-icon>
                  {{ item.storage_quantity[1].length }}
                </v-chip>
              </span>
            </template>
            <span>After: {{ item.storage_quantity[1] }}</span>
          </v-tooltip>
        </template>
        <template v-slot:[`item.connectivity`]="{ item }">
          <v-chip :color="getColor(item.is_connected)" dark class="ml-1">
            Connect
          </v-chip>
          <v-chip :color="getColor(item.is_active)" dark class="ml-1">
            Pair
          </v-chip>
        </template>
        <template v-slot:[`item.maintenance`]="{ item }">
          <v-chip
            :color="getMaintStatus(item.is_maintenance)[0]"
            dark
            class="ml-1"
          >
            {{ getMaintStatus(item.is_maintenance)[1] }}
          </v-chip>
        </template>
        <template v-slot:[`item.movement`]="{ item }">
          <v-btn
            small
            class="mr-2"
            @click="viewStationMovement(item.station_id , item.bin_at_worker)"
            light
          >
            Detail
          </v-btn>
        </template>
        <template v-slot:[`item.is_enroll`]="{ item }">
          <v-btn
            small
            :color="getStatus(item.is_enroll)[0]"
            @click="showEnrollConfirmation(item.station_id, item)"
            dark
          >
            {{ getStatus(item.is_enroll)[1] }}
          </v-btn>
        </template>

        <template v-slot:[`item.error_code`]="{ item }">
          <v-btn
            small
            :color="getErrorColor(item.error_code)[0]"
            @click="viewStationError(item.station_id)"
            dark
          >
            {{ getErrorColor(item.error_code)[1] }}
          </v-btn>
        </template>

        <template v-slot:[`item.recovery`]="{ item }">
          <v-btn
            v-if="!item.is_active"
            small
            @click="showBinStatusDialog(item.station_id, item)"
            light
          >
            <v-icon color="black">
              mdi-restore
            </v-icon>
          </v-btn>
          <span v-else>
            -
          </span>
        </template>
        
        <template v-slot:[`item.call_bin`]="{ item }">
          <v-btn
            small
            class="mr-2"
            @click="viewStationBin(item.station_id, item.bin_at_worker)"
            light
          >
            Call Bin
          </v-btn>
        </template>
        <template v-slot:[`item.history`]="{ item }">
          <v-btn
            small
            class="mr-2"
            @click="viewStationHistory(item.station_id)"
            light
          >
            <v-icon>
              mdi-clipboard-text-clock
            </v-icon>
          </v-btn>
        </template>
        <template v-slot:top> </template>
      </v-data-table>
      <v-card dark>
        <v-row>
          <v-col cols="4">
            <v-card 
              class="ml-2"
            >
              <v-row>
                <v-col cols="7">
                  <v-text-field
                    class="mr-2"
                    v-model="binHistoryData.bin_no"
                    label="Bin Number"
                    outlined
                    hint="Track History of Bin on Today"
                    @keydown.enter="getBinHistory()"
                    type="number"
                    persistent-hint
                    autofocus
                  >
                    <template v-slot:append>
                      <v-icon
                        v-if="checkNumber(binHistoryData.bin_no)"
                        @click="getBinHistory()"
                        color="green"
                      >
                        mdi-check
                      </v-icon>
                      <v-icon
                        v-else
                        disabled
                      >
                        mdi-check
                      </v-icon>
                    </template>
                  </v-text-field>
              </v-col>
              </v-row>
            </v-card>
          </v-col>
        </v-row>
      </v-card>
      <v-divider></v-divider>
      <!-- Maintenance dialog -->
      <BinStatusDialog :stationBinStatus="stationBinStatus" />

      <!-- view staiton error -->
      <v-dialog v-if="stationErrorData.bool" v-model="stationErrorData.bool">
        <v-card>
          <v-toolbar dark>
            <v-toolbar-title
              >Station Error of Station
              {{ stationErrorData.station_id }}</v-toolbar-title
            >
            <v-spacer></v-spacer>
            <v-btn
              class="mx-1"
              color="green"
              @click="viewStationError(stationErrorData.station_id)"
              :disabled="!stationErrorData.doneSync"
            >
              Refresh
            </v-btn>
            <v-btn
              class="mx-1"
              color="red"
              @click="stationErrorData.bool = false"
            >
              Close
            </v-btn>
          </v-toolbar>
          <v-progress-linear
            v-if="!stationErrorData.doneSync"
            color="green"
            indeterminate
          ></v-progress-linear>
          <v-col>
            <span v-if="stationErrorData.response.error">
              {{ stationErrorData.response.error }}
            </span>
            <span v-else>
              <v-data-table
                :headers="stationErrorData.headers"
                item-key="errorCode"
                :items="stationErrorData.response.data"
                :items-per-page="15"
                class="elevation-1"
                group-by="module"
              >
              </v-data-table>
            </span>
          </v-col>
        </v-card>
      </v-dialog>

      <!-- view station movement -->
      <v-dialog
        v-if="stationMovementData.bool"
        v-model="stationMovementData.bool"
      >
        <v-card>
          <v-toolbar dark>
            <v-toolbar-title>
              Station Movement of Station {{ stationMovementData.station_id }} -
              Zoning -
              {{
                stationMovementData.position
                  ? stationMovementData.position.message
                  : ""
              }}
            </v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              class="mx-1"
              @click="storeBinBtnDialog(stationMovementData.station_id , stationMovementData.bin_at_worker)"
              light
              v-if="env === 'development'"
            >
              Store
            </v-btn>
            <v-btn
              class="mx-1"
              color="green"
              @click="viewStationMovement(stationMovementData.station_id,stationMovementData.bin_at_worker)"
              :disabled="!stationMovementData.doneSync"
            >
              Refresh
            </v-btn>
            <v-btn
              class="mx-1"
              color="red"
              @click="stationMovementData.bool = false"
            >
              Close
            </v-btn>
          </v-toolbar>
          <v-progress-linear
            v-if="!stationMovementData.doneSync"
            color="green"
            indeterminate
          ></v-progress-linear>
          <v-col>
            <span v-if="stationMovementData.response.error">
              {{ stationMovementData.response.error }}
            </span>
            <span v-else>
              <v-data-table
                :headers="stationMovementData.headers"
                item-key="id"
                :items="stationMovementData.response.data"
                :items-per-page="50"
                :dense="true"
                class="elevation-1"
                group-by="order_id"
                sort-by="created_at"
              >
                <template v-slot:[`item.created_at`]="{ item }">
                  {{ convertStringToLocal(item.created_at, true) }}
                </template>

                <template v-slot:[`item.from_index`]="{ item }">
                  <span> {{ item.from_index }} - {{ item.to_index }} </span>
                </template>

                <template v-slot:[`item.status`]="{ item }">
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <span v-bind="attrs" v-on="on">
                        <v-chip
                          small
                          :color="getStatusStr(item.status)[0]"
                          dark
                        >
                          {{ getStatusStr(item.status)[1] }}
                        </v-chip>
                      </span>
                    </template>
                    <span>
                      {{ item.status }}
                    </span>
                  </v-tooltip>
                </template>

                <template v-slot:[`item.plc_ack`]="{ item }">
                  <span v-if="item.plc_ack === null">
                    -
                  </span>
                  <v-chip v-else small :color="getStatus(item.plc_ack)[0]" dark>
                    {{ getStatus(item.plc_ack)[1] }}
                  </v-chip>
                </template>

                <template v-slot:[`item.shell`]="{ item }">
                  <span v-if="item.plc_ack === null">
                    -
                  </span>
                  <v-btn
                    v-else
                    small
                    @click="shellAddBtn(stationMovementData.station_id, item)"
                  >
                    SHELL
                  </v-btn>
                </template>
              </v-data-table>
            </span>
          </v-col>
        </v-card>
      </v-dialog>

      <!-- view station bin / call bin -->
      <v-dialog  v-if="stationCallBinData.bool"
        v-model="stationCallBinData.bool"   max-width="700">
        <v-card>
          <v-toolbar dark>
           <v-toolbar-title>
            
              Call Bin
            </v-toolbar-title>
          </v-toolbar>
          <v-row>
            <v-col>            
              <v-text-field
                  v-model.number="stationCallBinData.storage_to_call"
                  label="Totebin"
                  rounded
                  filled
                ></v-text-field>
            </v-col>
            <v-col>   
              <v-select
                v-model="stationCallBinData.storage_to_call"
                :items="displayedStorages"
                label="Select a totebin"  
                item-value="storage"        
          ></v-select> 
            </v-col>
          </v-row>
          <v-row>
            <v-col>
            <v-btn
              class="mx-1"
              light
              @click="callBinBtn(stationCallBinData)"
            >Confirm
            </v-btn>
          </v-col>
          <v-col>
            <v-btn
              class="mx-1"
              color="red"
              @click="stationCallBinData.bool = false"
            >
              Close
            </v-btn>
          </v-col>
          </v-row>
            <v-spacer></v-spacer>    
          </v-card>
      </v-dialog>

      <!-- Store Bin dialog -->
      <v-dialog
        v-if="storeBinData.bool"
        v-model="storeBinData.bool"
        max-width="700"
      >
        <v-card>
          <v-toolbar
            dark
            color="black"
          >
            <v-toolbar-title>Store Bin </v-toolbar-title>
          </v-toolbar>
          <v-card-text class="pt-6">
          <span>Only use this in isolation testing flow.</span>
          <v-row>
            <v-chip
                class="ma-2"
                color="primary"
                label
              >
                <v-icon left>
                  mdi-account-circle-outline
                </v-icon>
                Bin at worker point : {{storeBinData.bin_at_worker}}
              </v-chip>
          </v-row>
            <v-row>
              <v-text-field
                v-model="storeBinData.from_station"
                label="From Station"
                rounded
                filled
                readonly
              ></v-text-field>
              <v-col>
                <v-checkbox
                  v-model="storeBinData.to_hcc"
                  label="To HCC"
                  clearable
                ></v-checkbox>
              </v-col>
            </v-row>
            <v-row v-if="!storeBinData.to_hcc">
              <v-col>
                <v-select
                  v-model="storeBinData.position"
                  :items="stationPosition"
                  label="Position"
                  :rules="[(v) => !!v || 'Required']"
                  rounded
                  filled
                ></v-select>
              </v-col>
              <v-col>
                <v-text-field
                  v-model.number="storeBinData.to_station"
                  label="Store To Station"
                  rounded
                  filled
                ></v-text-field>
              </v-col>
              <v-col>
                <v-text-field
                  v-model="storeBinData.to_coor"
                  label="Store To Coor"
                  rounded
                  filled
                ></v-text-field>
              </v-col>
            </v-row>
            <v-row>
            <v-col>
              <v-text-field
                  v-model.number="storeBinData.storage_code"
                  label="Storage Code"
                  rounded
                  filled>
                </v-text-field>
              </v-col>
            </v-row>
            <v-row
              v-if="storeBinData.to_hcc">
              <v-col>
                <v-text-field
                  v-model="storeBinData.storage_code"
                  label="Storage Code"
                  rounded
                  filled
                >
                </v-text-field>
              </v-col>
              <v-col>
                <v-text-field
                  v-model="storeBinData.to_index"
                  label="To Index"
                  rounded
                  filled
                >
                </v-text-field>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn
              color="green darken-1"
              text
              @click="storeBinBtn(storeBinData)"
              >Store
            </v-btn>
            <v-btn
              color="green darken-1"
              text
              @click="storeBinData.bool = false"
              >Cancel
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
      
      <!-- view station history -->
      <v-dialog
        v-if="stationHistoryData.bool"
        v-model="stationHistoryData.bool"
        max-width="1000"
      >
        <v-card>
          <v-toolbar dark>
            <v-toolbar-title>
              Station Bin History
            </v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              class="mx-1"
              color="green"
              @click="viewStationHistory(stationHistoryData.station_id)"
              :disabled="!stationHistoryData.doneSync"
            >
              Refresh
            </v-btn>
            <v-btn
              class="mx-1"
              color="red"
              @click="stationHistoryData.bool = false"
            >
              Close
            </v-btn>
          </v-toolbar>
          <v-progress-linear
            v-if="!stationHistoryData.doneSync"
            color="green"
            indeterminate
          ></v-progress-linear>
          <v-col>
            <span v-if="stationHistoryData.response.error">
              {{ stationHistoryData.response.error }}
            </span>
            <span v-else>
              <v-data-table
                :headers="stationHistoryData.headers"
                item-key="id"
                :items="stationHistoryData.response.data"
                :items-per-page="15"
                :dense="true"
                class="elevation-1"
              >
                <template v-slot:[`item.arrived_at`]="{ item }">
                  {{ convertStringToLocal(item.arrived_at, true) }}
                </template>
                <template v-slot:[`item.leaved_at`]="{ item }">
                  {{ convertStringToLocal(item.leaved_at, true) }}
                </template>
                <template v-slot:[`item.processed_at`]="{ item }">
                  {{ convertStringToLocal(item.processed_at, true) }}
                </template>
              </v-data-table>
            </span>
          </v-col>
        </v-card>
      </v-dialog>

      <!-- bin history dialog -->
      <v-dialog
        v-if="binHistoryData.bool"
        v-model="binHistoryData.bool"
        max-width="700"
      >
        <v-card>
          <v-toolbar dark>
            <v-toolbar-title>
              Bin History of {{ binHistoryData.bin_no }}
            </v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              class="mx-1"
              color="green"
              @click="getBinHistory()"
              :disabled="!binHistoryData.doneSync"
            >
              Refresh
            </v-btn>
            <v-btn
              class="mx-1"
              color="red"
              @click="binHistoryData.bool = false"
            >
              Close
            </v-btn>
          </v-toolbar>
          <v-progress-linear
            v-if="!binHistoryData.doneSync"
            color="green"
            indeterminate
          ></v-progress-linear>
          <v-col>
            <span v-if="binHistoryData.response.error">
              {{ binHistoryData.response.error }}
            </span>
            <span v-else>
              <v-data-table
                :headers="binHistoryData.headers"
                item-key="id"
                :items="binHistoryData.response.data"
                :items-per-page="15"
                :dense="true"
                class="elevation-1"
              >
                <template v-slot:[`item.happened_at`]="{ item }">
                  {{ convertStringToLocal(item.happened_at, true) }}
                </template>

              </v-data-table>
            </span>
          </v-col>
        </v-card>
      </v-dialog>

      <!-- Runtime Stations Dialog -->
      <RuntimeStationsDialog
        :dialogData="runtimeStationData"
        :stationCount="runtimeStationCount"
        @refresh="onRuntimeStationsRefresh"
        @close="onRuntimeStationsClose"
      />
     
      <v-divider></v-divider>
      <!-- Tab -->
      <v-toolbar color="black" dark flat>
        <!-- <v-toolbar-title>HardwareX Mock</v-toolbar-title> -->
        <v-spacer></v-spacer>
        <template v-slot:extension>
          <!-- <v-row> -->
          <v-tabs v-model="selectedTab" align-with-title>
            <v-tabs-slider></v-tabs-slider>
            <v-tab v-for="tab in tabItems" :key="tab">
              {{ tab }}
            </v-tab>
          </v-tabs>
        </template>
      </v-toolbar>
      <v-tabs-items v-model="selectedTab">
        <!-- Shell -->
        <v-tab-item>
          <v-card dark>
            <v-row class="mx-4">
              <v-select
                v-model="shellModeModel.station_code"
                :items="this.station_list"
                label="Station Code"
                density="compact"
                dark
                prepend-icon="mdi-alpha-s-box"
                @change="shell.listOfRes = Array()"
                class="mt-2"
              >
              </v-select>
              <v-chip
                dark
                class="mt-3 mr-2"
                :color="getStationColor(shellModeModel.station_code)"
              >
                <v-icon v-if="shellModeModel.station_code == null" left>
                  mdi-close-network
                </v-icon>
                <v-icon v-else left>
                  mdi-check-network
                </v-icon>
                <b>Selected Station: </b>
              </v-chip>
              <v-chip
                dark
                close
                @click:close="
                  shellModeModel.station_code = null;
                  shell.listOfRes = Array();
                "
                :color="getStationColor(shellModeModel.station_code)"
                v-if="shellModeModel.station_code != null"
                class="mr-2 mt-3"
              >
                <b>ST{{ shellModeModel.station_code }}</b>
              </v-chip>
            </v-row>
          </v-card>
          <v-card dark>
            <v-row
              v-for="status in shell['status']"
              :key="status['title']"
              class="ml-3"
            >
              <v-col>
                <pre class="mx-2">{{ status["title"] }}</pre>
                <span v-for="data in status['data']" :key="data['title']">
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        width="130"
                        v-bind="attrs"
                        v-on="on"
                        class="ma-2"
                        :color="data['color']"
                        dark
                        @click="showShellDialog(data['command'], null)"
                      >
                        <v-icon class="mx-1">{{ data["icon"] }}</v-icon>
                        {{ data["title"] }}
                      </v-btn>
                    </template>
                    <span>{{ data["command"] }}</span>
                  </v-tooltip>
                </span>
              </v-col>
            </v-row>
            <!-- Remove Bin -->
            <v-row class="ml-3">
              <v-col cols="3">
                <pre class="mx-2">Remove Bin</pre>
                <v-row>
                  <v-text-field
                    solo
                    rounded
                    light
                    label="Bin Number"
                    class="ma-2 ml-4"
                    type="number"
                    v-model="shellModeModel.bin_number"
                  >
                    Bin Number
                  </v-text-field>
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        v-bind="attrs"
                        v-on="on"
                        dark
                        color="green"
                        rounded
                        @click="
                          showShellDialog('REMOVE', shellModeModel.bin_number)
                        "
                        class="ma-4"
                        :disabled="
                          shellModeModel.bin_number === null ||
                            shellModeModel.bin_number === '' ||
                            shellModeModel.bin_number <= 0
                        "
                      >
                        <v-icon>mdi-check</v-icon>
                        Confirm
                      </v-btn>
                    </template>
                    REMOVE,{{ shellModeModel.bin_number }}
                  </v-tooltip>
                </v-row>
              </v-col>
            </v-row>
            <!-- Add Bin -->
            <v-row class="ml-3">
              <v-col cols="7">
                <pre class="mx-2">Add Bin </pre>
                <v-row>
                  <v-col>
                    <v-text-field
                      solo
                      rounded
                      light
                      label="Bin Number"
                      class="ma-2"
                      type="number"
                      v-model="shellModeModel.add_bin.bin_number"
                    >
                      Bin Number
                    </v-text-field>
                  </v-col>
                  <v-col>
                    <v-text-field
                      solo
                      rounded
                      light
                      label="Job ID"
                      class="ma-2"
                      type="number"
                      v-model="shellModeModel.add_bin.job_id"
                    >
                      Job ID
                    </v-text-field>
                  </v-col>
                  <v-col>
                    <v-text-field
                      solo
                      rounded
                      light
                      label="Current Index"
                      class="ma-2"
                      type="number"
                      v-model="shellModeModel.add_bin.current_index"
                    >
                      Curent Index
                    </v-text-field>
                  </v-col>
                  <v-col>
                    <v-text-field
                      solo
                      rounded
                      light
                      label="From"
                      class="ma-2"
                      type="number"
                      v-model="shellModeModel.add_bin.from_index"
                    >
                      From
                    </v-text-field>
                  </v-col>
                  <v-col>
                    <v-text-field
                      solo
                      rounded
                      light
                      label="To"
                      class="ma-2"
                      type="number"
                      v-model="shellModeModel.add_bin.to_index"
                    >
                      To
                    </v-text-field>
                  </v-col>
                  <v-col>
                    <v-tooltip bottom>
                      <template v-slot:activator="{ on, attrs }">
                        <v-btn
                          v-bind="attrs"
                          v-on="on"
                          dark
                          color="green"
                          rounded
                          @click="
                            showShellDialog(
                              'ADD',
                              `${shellModeModel.add_bin.current_index},${shellModeModel.add_bin.bin_number},${shellModeModel.add_bin.job_id},${shellModeModel.add_bin.from_index},${shellModeModel.add_bin.to_index}`
                            )
                          "
                          class="ma-4"
                          :disabled="!addBinValidation()"
                        >
                          <v-icon>mdi-check</v-icon>
                          Confirm
                        </v-btn>
                      </template>
                      ADD,{{ shellModeModel.add_bin.current_index }},{{
                        shellModeModel.add_bin.bin_number
                      }},{{ shellModeModel.add_bin.job_id }},{{
                        shellModeModel.add_bin.from_index
                      }},{{ shellModeModel.add_bin.to_index }};
                    </v-tooltip>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
            <!-- Set Bin Status -->
            <v-row class="ml-3">
              <v-col cols="4">
                <pre class="mx-2">Set Bin ID</pre>
                <v-row>
                  <v-text-field
                    solo
                    rounded
                    light
                    label="Ex: 1234|1235||||1238"
                    class="ma-2 ml-4"
                    v-model="shellModeModel.bin_status"
                  >
                    Bin Status
                  </v-text-field>
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        v-bind="attrs"
                        v-on="on"
                        dark
                        color="green"
                        rounded
                        @click="
                          showShellDialog(
                            'set_bin_id',
                            shellModeModel.bin_status
                          )
                        "
                        class="ma-4"
                        :disabled="
                          shellModeModel.bin_status === null ||
                            shellModeModel.bin_status === ''
                        "
                      >
                        <v-icon>mdi-check</v-icon>
                        Confirm
                      </v-btn>
                    </template>
                    set_bin_id,{{ shellModeModel.bin_status }}
                  </v-tooltip>
                </v-row>
              </v-col>
            </v-row>
          </v-card>
        </v-tab-item>
        <v-dialog
          v-if="shellModeModel.bool"
          v-model="shellModeModel.bool"
          max-width="700"
        >
          <v-card>
            <v-toolbar dark color="red">
              <v-toolbar-title>Status: Warning</v-toolbar-title>
            </v-toolbar>
            <v-card-text class="mt-6">
              <span>Are you sure you want to send following command?</span>
              <v-row class="mt-3 ml-2">
                <v-col>
                  <v-chip color="green" dark>
                    <v-icon left>
                      mdi-check-network
                    </v-icon>
                    ST {{ shellModeModel.station_code }}
                  </v-chip>
                  <v-chip color="orange" dark class="ml-3">
                    <v-icon left>
                      mdi-message-cog
                    </v-icon>
                    ST,{{ shellModeModel.station_code }},SHELL,{{
                      shellModeModel.command
                    }};
                  </v-chip>
                </v-col>
              </v-row>
            </v-card-text>
            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn
                v-if="!shellModeModel.error"
                color="green darken-1"
                text
                @click="sendShellBtn()"
                >Yes
              </v-btn>
              <v-btn
                color="green darken-1"
                text
                @click="shellModeModel.bool = false"
                >No
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>
        <v-dialog
          v-if="shell.boolRes"
          v-model="shell.boolRes"
          @keydown.enter="shell.boolRes = false"
          scrollable
          max-width="700"
        >
          <v-card>
            <v-toolbar dark color="blue">
              <v-toolbar-title>Last Message</v-toolbar-title>
            </v-toolbar>
            <v-card-text class="pt-6">
              <v-card dark v-if="shell.listOfRes.length != 0">
                <v-col>
                  <v-row v-for="(item, index) in shell.listOfRes" :key="index">
                    <v-spacer v-if="!item.from_station"></v-spacer>
                    <v-chip :color="getShellMessageColor(item)" class="ma-1">
                      <pre>{{ item.message }}</pre>
                    </v-chip>
                  </v-row>
                </v-col>
              </v-card>
              <span v-else>No History</span>
            </v-card-text>
            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn color="green darken-1" text @click="shell.boolRes = false"
                >Close
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>
        <!-- Mock -->
        <v-tab-item>
          <v-card dark color="black">
            <v-tabs v-model="mockTab" align-with-title>
              <v-tabs-slider></v-tabs-slider>
              <v-tab
                v-for="mock_item in mockTabItems"
                :key="mock_item"
                @click="resetForm()"
              >
                {{ mock_item }}
              </v-tab>
            </v-tabs>
          </v-card>
          <v-card dark>
            <v-tabs-items v-model="mockTab" dark>
              <!-- Mock Complete Available Job -->
              <v-tab-item>
                <v-toolbar dark>
                  <v-row class="mt-7">
                    <v-col>
                      <!-- Station Code -->
                      <v-select
                        class="ma-1"
                        v-model="stationNextJobRunModel.station_code"
                        label="Station Code"
                        prepend-icon="mdi-alpha-s-box"
                        variant="outlined"
                        :items="this.station_list"
                        :rules="stationRules"
                      >
                        {{ stationNextJobRunModel.station_code }}
                      </v-select>
                    </v-col>
                    <v-col>
                      <v-btn
                        class="ma-1"
                        @click="getNextJobRun()"
                        color="green"
                      >
                        <v-icon>mdi-restart</v-icon>
                        <span>Refresh</span>
                      </v-btn>
                    </v-col>
                  </v-row>
                </v-toolbar>
                <v-progress-linear
                  v-if="!stationNextJobRunData.doneSync"
                  color="green"
                  indeterminate
                >
                </v-progress-linear>
                <v-data-table
                  :headers="stationNextJobRunData.headers"
                  :items="stationNextJobRunData.response.data"
                  :items-per-page="50"
                  group-by="storage_code"
                  class="elevation-1"
                  sort-by="time"
                  dark
                  dense
                >
                  <!-- <template v-if="env === 'production'" v-slot:[`item.complete`]>
                </template> -->
                  <template v-slot:[`item.complete`]="{ item }">
                    <v-btn
                      small
                      class="mr-2"
                      @click="completeNextJobRun(item.message)"
                      light
                    >
                      Complete
                    </v-btn>
                  </template>
                </v-data-table>
              </v-tab-item>
              <!-- Mock Recv Station Msg -->
              <v-tab-item>
                <v-card flat width="1500">
                  <v-card-text>
                    <v-row>
                      <v-col>
                        <v-card
                          min-width="550"
                          min-height="210"
                          hover
                          class="mx-auto"
                        >
                          <v-col>
                            <v-form ref="mock" v-model="m_valid">
                              <v-row>
                                <v-col>
                                  <v-text-field
                                    v-model="input_1"
                                    label="Station Code"
                                    placeholder="e.g. 1"
                                    clearable
                                    type="number"
                                    :rules="stationRules"
                                  ></v-text-field>
                                </v-col>
                              </v-row>
                              <v-row>
                                <v-col>
                                  <v-text-field
                                    v-model="input_3"
                                    label="Message"
                                    placeholder="e.g. ST,1,J,1,0|2|C|1234 or ST,1,STOP;"
                                    clearable
                                    :rules="[(v) => !!v || 'Required']"
                                  ></v-text-field>
                                </v-col>
                              </v-row>
                            </v-form>
                          </v-col>
                        </v-card>
                      </v-col>
                      <v-col>
                        <v-card
                          min-width="550"
                          min-height="210"
                          hover
                          class="mx-auto"
                        >
                          <v-card-title>Preview:</v-card-title>
                          <v-col>
                            <pre
                              >{{
                                showPreview({
                                  station_code: input_1,
                                  message: input_3,
                                })
                              }}
                          </pre
                            >
                          </v-col>
                        </v-card>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col lg=""
                        ><v-btn
                          :disabled="!m_valid"
                          @click="
                            btnMock('mock_station_msg', {
                              station_code: input_1,
                              message: input_3,
                            }),
                              resetForm()
                          "
                          >Mock Receive Station</v-btn
                        >
                      </v-col>
                      <v-col lg=""
                        ><v-btn @click="resetForm()">Clear</v-btn>
                      </v-col>
                      <v-col>
                        <v-alert
                          v-if="bolMock"
                          v-model="bolMock"
                          border="left"
                          colored-border
                          color="deep-purple accent-4"
                          elevation="2"
                        >
                          {{ txtMock }}
                        </v-alert>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-card>
              </v-tab-item>
              <!-- Send Station -->
              <v-tab-item>
                <v-card flat width="1500">
                  <v-card-text>
                    <v-row>
                      <v-col>
                        <v-card
                          min-width="550"
                          min-height="210"
                          hover
                          class="mx-auto"
                        >
                          <v-col>
                            <v-form ref="mock" v-model="m_valid">
                              <v-row>
                                <v-col>
                                  <v-text-field
                                    v-model="input_1"
                                    label="Station Code"
                                    placeholder="e.g. 1"
                                    clearable
                                    type="number"
                                    :rules="stationRules"
                                  ></v-text-field>
                                </v-col>
                              </v-row>
                              <v-row>
                                <v-col>
                                  <v-text-field
                                    v-model="input_3"
                                    label="Message"
                                    placeholder="e.g. ST,1,M,1,0|2|C|1234"
                                    clearable
                                    :rules="[(v) => !!v || 'Required']"
                                  ></v-text-field>
                                </v-col>
                              </v-row>
                            </v-form>
                          </v-col>
                        </v-card>
                      </v-col>
                      <v-col>
                        <v-card
                          min-width="550"
                          min-height="210"
                          hover
                          class="mx-auto"
                        >
                          <v-card-title>Preview:</v-card-title>
                          <v-col>
                            <pre
                              >{{
                                showPreview({
                                  station_code: input_1,
                                  message: input_3,
                                })
                              }}
                          </pre
                            >
                          </v-col>
                        </v-card>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col lg=""
                        ><v-btn
                          :disabled="!m_valid"
                          @click="
                            btnMock('send_station_msg', {
                              station_code: input_1,
                              message: input_3,
                            }),
                              resetForm()
                          "
                          >Send Station</v-btn
                        >
                      </v-col>
                      <v-col lg=""
                        ><v-btn @click="resetForm()">Clear</v-btn>
                      </v-col>
                      <v-col>
                        <v-alert
                          v-if="bolMock"
                          v-model="bolMock"
                          border="left"
                          colored-border
                          color="deep-purple accent-4"
                          elevation="2"
                        >
                          {{ txtMock }}
                        </v-alert>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-card>
              </v-tab-item>
              <!-- Send Socketio Event -->
              <v-tab-item>
                <v-card flat width="1500">
                  <v-card-text>
                    <v-row>
                      <v-col>
                        <v-card
                          min-width="550"
                          min-height="210"
                          hover
                          class="mx-auto"
                        >
                          <v-col>
                            <v-form ref="mock" v-model="m_valid">
                              <v-row>
                                <v-col>
                                  <v-select
                                    v-model="input_1"
                                    :items="socketEventItems"
                                    label="Event"
                                    placeholder="select your event"
                                    clearable
                                    :rules="[(v) => !!v || 'Required']"
                                  ></v-select>
                                </v-col>
                              </v-row>
                              <v-row>
                                <v-col>
                                  <v-select
                                    v-model="entity"
                                    :items="['TC_A', 'TC_B', 'TC_C', 'SM']"
                                    label="Entity"
                                    placeholder="select your entity"
                                    clearable
                                  ></v-select>
                                </v-col>
                              </v-row>
                              <p>Data</p>
                              <!-- data field -->
                              <v-row>
                                <v-col>
                                  <v-select
                                    v-model.number="station_code"
                                    :items="createListForStationNo()"
                                    label="Station Code"
                                    placeholder="select your station code"
                                    clearable
                                    :rules="[(v) => !!v || 'Required']"
                                  ></v-select>
                                </v-col>
                              </v-row>
                              <v-row>
                                <v-col>
                                  <v-text-field
                                    v-model.number="storage_code"
                                    label="Storage Code"
                                    placeholder="e.g. 1234"
                                    clearable
                                    type="number"
                                    :rules="[(v) => !!v || 'Required']"
                                  ></v-text-field>
                                </v-col>
                              </v-row>
                              <v-row v-if="input_1 == 'pick_arrival'">
                                <v-col>
                                  <v-text-field
                                    v-model.number="pick_index"
                                    label="Pick Index"
                                    placeholder="e.g. 10"
                                    clearable
                                    type="number"
                                    :rules="[
                                      (v) =>
                                        (!!v && 0 <= v && v <= 11) ||
                                        'Invalid Zone',
                                    ]"
                                  ></v-text-field>
                                </v-col>
                              </v-row>
                              <v-row v-if="input_1 == 'pick_arrival'">
                                <v-col>
                                  <v-select
                                    v-model="cube"
                                    :items="zones"
                                    label="Cube"
                                    placeholder="select your cube"
                                    clearable
                                  ></v-select>
                                </v-col>
                              </v-row>
                              <v-row v-if="input_1 == 'work_arrival'">
                                <v-col>
                                  <v-text-field
                                    v-model="time_arrive"
                                    label="Time Arrive"
                                    placeholder="e.g. 2023-04-12T12:14:22.523302"
                                    clearable
                                    :rules="[(v) => !!v || 'Required']"
                                  ></v-text-field>
                                </v-col>
                              </v-row>
                              <v-row v-if="input_1 == 'junction_arrival'">
                                <v-col>
                                  <v-checkbox
                                    v-model="is_bridge"
                                    label="is_bridge"
                                    clearable
                                  ></v-checkbox>
                                </v-col>
                              </v-row>
                            </v-form>
                          </v-col>
                        </v-card>
                      </v-col>
                      <v-col>
                        <v-card
                          min-width="550"
                          min-height="210"
                          hover
                          class="mx-auto"
                        >
                          <v-card-title>Preview:</v-card-title>
                          <v-col>
                            <pre
                              >{{
                                showPreview({
                                  event: input_1,
                                  entity: entity,
                                  namespace: namespace,
                                  data: getWsRequestData(
                                    input_1,
                                    station_code,
                                    storage_code,
                                    pick_index,
                                    cube,
                                    time_arrive,
                                    is_bridge
                                  ),
                                })
                              }}
                          </pre
                            >
                          </v-col>
                        </v-card>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col lg=""
                        ><v-btn
                          :disabled="!m_valid"
                          @click="
                            btnMock('mock_ws_request', {
                              event: input_1,
                              entity: entity,
                              data: getWsRequestData(
                                input_1,
                                station_code,
                                storage_code,
                                pick_index,
                                cube,
                                time_arrive,
                                is_bridge
                              ),
                              namespace: namespace,
                            })
                          "
                          >Mock SocketIO Event</v-btn
                        >
                      </v-col>
                      <v-col lg=""
                        ><v-btn @click="resetForm()">Clear</v-btn>
                      </v-col>
                      <v-col>
                        <v-alert
                          v-if="bolMock"
                          v-model="bolMock"
                          border="left"
                          colored-border
                          color="deep-purple accent-4"
                          elevation="2"
                        >
                          {{ txtMock }}
                        </v-alert>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-card>
              </v-tab-item>
            </v-tabs-items>
          </v-card>
        </v-tab-item>
        <!-- Station Record -->
        <v-tab-item>
          <v-toolbar dark>
            <v-row class="mt-7">
              <v-menu
                v-model="stationRecordModel.bolmenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    class="ma-1"
                    v-model="stationRecordModel.dtfromto"
                    label="Choose dates"
                    prepend-icon="mdi-calendar"
                    readonly
                    v-bind="attrs"
                    v-on="on"
                  >
                    {{ stationRecordModel.dtfromto }}
                  </v-text-field>
                </template>
                <v-date-picker
                  range
                  v-model="stationRecordModel.dtfromto"
                  @input="stationRecordModel.bolmenu = false"
                  dark
                >
                </v-date-picker>
              </v-menu>
              <v-btn class="ma-1" @click="getStationRecord()" color="green">
                <v-icon>mdi-restart</v-icon>
                <span>Refresh</span>
              </v-btn>
              <v-btn class="ma-1" color="blue">
                <download-csv
                  :data="stationRecordData.response.data"
                  :fields="stationRecordModel.exportFields"
                  :name="StationRecordExportFileName"
                >
                  <v-icon>mdi-download</v-icon> DOWNLOAD CSV
                </download-csv>
              </v-btn>
            </v-row>
          </v-toolbar>
          <v-progress-linear
            v-if="!stationRecordData.doneSync"
            color="green"
            indeterminate
          ></v-progress-linear>
          <v-data-table
            :headers="stationRecordData.headers"
            :items="stationRecordData.response.data"
            :items-per-page="15"
            sort-by="station_code"
            class="elevation-1"
            dark
          >
          </v-data-table>
        </v-tab-item>
        <!-- Station Error Record -->
        <v-tab-item>
          <v-toolbar dark>
            <v-row class="mt-7">
              <v-select
                class="ma-1"
                v-model="stationErrorRecordModel.station_code"
                label="Station Code"
                prepend-icon="mdi-alpha-s-box"
                variant="outlined"
                :items="this.station_list"
                clearable
              >
                {{ stationErrorRecordModel.station_code }}
              </v-select>
              <v-menu
                v-model="stationErrorRecordModel.bolmenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    class="ma-1"
                    v-model="stationErrorRecordModel.dtfromto"
                    label="Choose dates"
                    prepend-icon="mdi-calendar"
                    readonly
                    v-bind="attrs"
                    v-on="on"
                  >
                    {{ stationErrorRecordModel.dtfromto }}
                  </v-text-field>
                </template>
                <v-date-picker
                  range
                  v-model="stationErrorRecordModel.dtfromto"
                  @input="stationErrorRecordModel.bolmenu = false"
                  dark
                >
                </v-date-picker>
              </v-menu>
              <v-btn
                class="ma-1"
                @click="getStationErrorRecord()"
                color="green"
              >
                <v-icon>mdi-restart</v-icon>
                <span>Refresh</span>
              </v-btn>
              <v-btn class="ma-1" color="blue">
                <download-csv
                  :data="stationErrorRecordData.response.data"
                  :fields="stationErrorRecordModel.exportFields"
                  :name="StationErrorRecordExportFileName"
                >
                  <v-icon>mdi-download</v-icon> DOWNLOAD CSV
                </download-csv>
              </v-btn>
            </v-row>
          </v-toolbar>
          <v-progress-linear
            v-if="!stationErrorRecordData.doneSync"
            color="green"
            indeterminate
          ></v-progress-linear>
          <v-data-table
            :headers="stationErrorRecordData.headers"
            :items="stationErrorRecordData.response.data"
            :items-per-page="15"
            class="elevation-1"
            sort-by="created_at"
            sort-desc="true"
            dark
          >
            <template v-slot:[`item.error_message`]="{ item }">
              <v-tooltip bottom>
                <template v-slot:activator="{ on, attrs }">
                  <v-icon
                    v-bind="attrs"
                    v-on="on"
                    @click="btnShowErrorAction(item.action)"
                    class="mx-1"
                    >mdi-information</v-icon
                  >
                </template>
                <span>Show Action To Solve</span>
              </v-tooltip>
              <span>{{ item.error_message }}</span>
            </template>
            <template v-slot:[`item.created_at`]="{ item }">
              {{ convertStringToLocal(item.created_at, true) }}
            </template>
          </v-data-table>
          <!-- Action -->
          <v-dialog
            v-if="stationErrorRecordData.showAction"
            v-model="stationErrorRecordData.showAction"
            max-width="500"
            @keydown.enter="stationErrorRecordData.showAction = false"
          >
            <v-card>
              <v-toolbar dark color="green">
                <v-toolbar-title>Action To Solve Error</v-toolbar-title>
              </v-toolbar>
              <v-card-text class="pt-6">
                <pre class="text-wrap">{{ stationErrorRecordData.action }}</pre>
              </v-card-text>
              <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn
                  color="green darken-1"
                  text
                  @click="stationErrorRecordData.showAction = false"
                  >Close
                </v-btn>
              </v-card-actions>
            </v-card>
          </v-dialog>
        </v-tab-item>
        <!-- Station Inner Error Log -->
        <v-tab-item>
          <v-toolbar dark>
            <v-row class="mt-7">
              <v-col>
                <v-select
                  class="ma-1"
                  v-model="stationInnerErrorLogModel.station_code"
                  label="Station Code"
                  prepend-icon="mdi-alpha-s-box"
                  variant="outlined"
                  :items="this.station_list"
                  clearable
                >
                  {{ stationInnerErrorLogModel.station_code }}
                </v-select>
              </v-col>
              <v-col>
                <v-menu
                  v-model="stationInnerErrorLogModel.bolmenu"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      class="ma-1"
                      v-model="stationInnerErrorLogModel.date"
                      label="Choose dates"
                      prepend-icon="mdi-calendar"
                      readonly
                      v-bind="attrs"
                      v-on="on"
                    >
                      {{ stationInnerErrorLogModel.date }}
                    </v-text-field>
                  </template>
                  <v-date-picker
                    v-model="stationInnerErrorLogModel.date"
                    @input="stationInnerErrorLogModel.bolmenu = false"
                    dark
                  >
                  </v-date-picker>
                </v-menu>
              </v-col>
              <v-col>
                <v-btn
                  class="ma-1"
                  @click="getStationInnerErrorLog()"
                  color="green"
                >
                  <v-icon>mdi-restart</v-icon>
                  <span>Refresh</span>
                </v-btn>
              </v-col>
            </v-row>
          </v-toolbar>
          <v-progress-linear
            v-if="!stationInnerErrorLogData.doneSync"
            color="green"
            indeterminate
          >
          </v-progress-linear>
          <v-data-table
            :headers="stationInnerErrorLogData.headers"
            :items="stationInnerErrorLogData.response.data"
            :items-per-page="10"
            class="elevation-1"
            dark
            dense
          >
          </v-data-table>
        </v-tab-item>
        <!-- Log -->
        <v-tab-item>
          <v-toolbar dark>
            <v-row class="mt-7">
              <v-col>
                <!-- Station Code -->
                <v-select
                  class="ma-1"
                  v-model="stationLogModel.station_code"
                  label="Station Code"
                  prepend-icon="mdi-alpha-s-box"
                  variant="outlined"
                  :rules="stationRules"
                  :items="this.station_list"
                >
                  {{ stationLogModel.station_code }}
                </v-select>
              </v-col>
              <v-col>
                <!-- Date -->
                <v-menu
                  v-model="stationLogModel.bolmenu"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      class="ma-1"
                      v-model="stationLogModel.date"
                      label="Choose dates"
                      prepend-icon="mdi-calendar"
                      readonly
                      v-bind="attrs"
                      v-on="on"
                    >
                      {{ stationLogModel.date }}
                    </v-text-field>
                  </template>
                  <v-date-picker
                    v-model="stationLogModel.date"
                    @input="stationLogModel.bolmenu = false"
                    dark
                  >
                  </v-date-picker>
                </v-menu>
              </v-col>
            </v-row>
            <v-row class="mt-7">
              <v-col>
                <!-- From Time -->
                <v-menu v-model="stationLogModel.menuFrom">
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      v-model="stationLogModel.timeFrom"
                      label="From"
                      prepend-icon="mdi-clock-start"
                      readonly
                      v-bind="attrs"
                      v-on="on"
                    >
                      {{ stationLogModel.timeFrom }}
                    </v-text-field>
                  </template>
                  <v-time-picker
                    v-if="stationLogModel.menuFrom"
                    v-model="stationLogModel.timeFrom"
                    scrollable
                    format="24hr"
                    dark
                    use-seconds
                  ></v-time-picker>
                </v-menu>
              </v-col>
              <v-col>
                <!-- To Time -->
                <v-menu v-model="stationLogModel.menuTo">
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      v-model="stationLogModel.timeTo"
                      label="To"
                      prepend-icon="mdi-clock-end"
                      readonly
                      v-bind="attrs"
                      v-on="on"
                    >
                      {{ stationLogModel.timeTo }}
                    </v-text-field>
                  </template>
                  <v-time-picker
                    v-if="stationLogModel.menuTo"
                    v-model="stationLogModel.timeTo"
                    scrollable
                    format="24hr"
                    dark
                    use-seconds
                  ></v-time-picker>
                </v-menu>
              </v-col>
              <v-col>
                <v-btn class="ma-1" @click="getStationLog()" color="green">
                  <v-icon>mdi-restart</v-icon>
                  <span>Refresh</span>
                </v-btn>
              </v-col>
            </v-row>
          </v-toolbar>
          <v-progress-linear
            v-if="!stationLogData.doneSync"
            color="green"
            indeterminate
          >
          </v-progress-linear>
          <v-data-table
            :headers="stationLogData.headers"
            :items="stationLogData.response.data"
            :items-per-page="50"
            class="elevation-1"
            sort-by="time"
            dark
            dense
            :item-class="getMessageColor"
          >
            <template v-slot:[`item.time`]="{ item }">
              {{ convertStringToLocal(item.time, true) }}
            </template>
          </v-data-table>
        </v-tab-item>
      </v-tabs-items>
    </v-container>

    <!-- Station Enrollment Confirmation Dialog -->
    <v-dialog
      v-model="showEnrollConfirmDialog"
      width="500"
    >
      <v-card dark>
        <v-toolbar color="orange darken-2" dark>
          <v-toolbar-title>Confirm Station Enrollment</v-toolbar-title>
        </v-toolbar>
        <v-card-text class="pt-4">
          <div class="text-h6 mb-3">
            Are you sure you want to start enroll mode for Station {{ pendingEnrollment.station_code }}?
          </div>
          <div class="text-body-2 grey--text">
            This is a critical system operation that will affect station operations.
          </div>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            color="green darken-1"
            text
            @click="cancelEnrollment"
          >
            No
          </v-btn>
          <v-btn
            color="green darken-1"
            text
            @click="confirmEnrollment"
          >
            Yes
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-app>
</template>

<script>
import {
  getCurrentDateTime,
  getHccUrl,
  convertStringToLocal,
  getHost,
  getEnv,
  getCube,
  getMapping,
  getRequestHeader,
  useRefreshToken,
} from "../helper/common.js";
import { socket } from "../App.vue";
import { Websocket } from "../helper/enums.js";
import axios from "axios";
import BinStatusDialog from "./station/BinStatusDialog.vue";
import RuntimeStationsDialog from "./station/RuntimeStationsDialog.vue";

export default {
  name: "App",
  components: {
    BinStatusDialog,
    RuntimeStationsDialog,
  },
  created() {
    this.viewStationDetail();
    this.getCurrEnv();
    this.getShellMessage(socket);
    this.getRuntimeStations();

    // If production, hide Call Bin features
    if (this.env === "production") {
    const index = this.stationDetailData.headers.findIndex(header => header.text === "Call Bin");
    if (index !== -1) {
      this.stationDetailData.headers.splice(index, 1);
    }
  }
    

  },
  methods: {
    getCurrEnv(){
      this.env = getEnv()
    },
    checkNumber(number){
      if (number === null){
        return false
      }else if(number<=0){
        return false
      }else{
        return true
      }
    },
    timeColor(status) {
      if (status === "COMPLETED") {
        return "green";
      } else if (status == "PROCESSING") {
        return "purple";
      } else {
        return "blue";
      }
    },
    getColor(boolean) {
      if (boolean === true) {
        return "green";
      } else {
        return "red";
      }
    },
    getStationColor(code) {
      if (code === null) {
        return "red";
      } else {
        return "green";
      }
    },
    getStationIdColor(bool) {
      if (bool) {
        return "yellow--text";
      } else {
        return "white--text";
      }
    },
    getStatus(bol) {
      if (bol == true) {
        return ["green", "✓"];
      } else {
        return ["red", "✗"];
      }
    },
    getMaintStatus(boolean) {
      if (boolean === true) {
        return ["red", "✓"];
      } else {
        return ["green", "✗"];
      }
    },
    getStatusStr(status) {
      if (status == "COMPLETED") {
        return ["green", "✓"];
      } else if (status == "PROCESSING") {
        return ["orange", "..."];
      } else if (status == "AVAILABLE") {
        return ["red", "✗"];
      } else {
        return ["red", status];
      }
    },
    getErrorColor(errorCode) {
      if (errorCode.length == 0) {
        return ["green", errorCode];
      } else {
        if (errorCode.length > 10) {
          let slicedArray = errorCode.slice(0, 10);
          slicedArray.push("...");
          let stringArray = slicedArray.join(", ");

          return ["red", stringArray];
        } else {
          return ["red", errorCode.join(", ")];
        }
      }
    },
    getShellMessage(socket) {
      var here = this;
      socket.on(Websocket.STATION, function(item) {
        here.shell.wsMessage = item.item;
        console.log(item.item);
      });
    },
    getRuntimeStations: async function() {
      try {
        let tcUrl = getHost(this.currentZone);
        let apiUrl = `${tcUrl}/station/runtime-station`;
        const response = await axios.get(apiUrl, {
          headers: getRequestHeader(),
          validateStatus: function(status) {
            return (status >= 200 && status < 300) || status == 400;
          }
        });
        
        if (response.data && Array.isArray(response.data.data)) {
          this.runtimeStationCount = response.data.data.length;
          this.runtimeStationData.stations = response.data.data;
        } else {
          this.runtimeStationCount = 0;
          this.runtimeStationData.stations = [];
        }
      } catch (error) {
        if (error.response && error.response.status === 401) {
          // If access token is unauthorized, use refresh token to get new access token
          return useRefreshToken(this, this.getRuntimeStations);
        }
        console.error('Error fetching runtime stations:', error);
        this.runtimeStationCount = 0;
        this.runtimeStationData.stations = [];
        this.$awn.alert('Failed to fetch runtime stations');
      }
    },
    showRuntimeStationsDialog: function() {
      this.runtimeStationData.bool = true;
    },
    onRuntimeStationsRefresh: function() {
      this.getRuntimeStations();
    },
    onRuntimeStationsClose: function() {
      this.runtimeStationData.bool = false;
    },
    resetForm() {
      this.bolMock = false;
      this.$refs.mock.reset();
    },
    showPreview(json) {
      // console.log(json)
      var res = new Object();
      for (var [key, val] of Object.entries(json)) {
        if (val == null) {
          delete res[key];
        } else {
          res[key] = val;
        }
      }
      if (Object.entries(res).length === 0) {
        return "  Put some value!";
      } else {
        return res;
      }
    },

    getString(input) {
      var station_id = input["station_id"];
      var job_id = input["job_id"];
      var bin_id = input["bin_id"];
      var from_pos = input["from_pos"];
      var to_pos = input["to_pos"];
      var code = input["code"];
      var direction = input["direction"];
      if (station_id == null || station_id == "") {
        station_id = "";
      } else {
        station_id = "ST," + station_id + ",";
      }
      if (job_id == null || job_id == "") {
        job_id = "";
      } else {
        job_id += ",";
      }
      if (bin_id == null || bin_id == "") {
        bin_id = "";
      }
      if (from_pos == null || from_pos == "") {
        from_pos = "";
      } else {
        from_pos += "|";
      }
      if (to_pos == null || to_pos == "") {
        to_pos = "";
      } else {
        to_pos += "|";
      }
      if (code == null) {
        code = "";
      } else {
        code += ",";
      }
      if (direction == null || direction == "none") {
        direction = "";
      } else {
        direction += "|";
      }
      var movement_string =
        station_id + code + job_id + from_pos + to_pos + direction + bin_id;
      return movement_string;
    },
    createListForStationNo() {
      const arr = Array.from({ length: 17 }, (_, index) => index + 1);
      return arr;
    },
    getWsRequestData(
      event,
      stationCode,
      storageCode,
      pickIndex,
      cube,
      timeArrive,
      isBridge
    ) {
      var dict = {};
      dict["station_code"] = stationCode;
      dict["storage_code"] = storageCode;
      if (event == "pick_arrival") {
        dict["pick_index"] = pickIndex;
        dict["cube"] = cube;
      }
      if (event == "work_arrival") dict["time_arrive"] = timeArrive;
      if (event == "junction_arrival") dict["is_bridge"] = isBridge;
      return JSON.stringify(dict);
    },
    getMessageColor(item) {
      if (item.color) {
        switch (item.color) {
          case "cyan":
            return "cyan--text text--accent-4";
          case "yellow":
            return "yellow--text text--accent-4";
          case "blue":
            return "blue--text text--accent-4";
        }
      }
    },
    getShellMessageColor(data) {
      switch (data.from_station) {
        case false: {
          return data.status ? "green" : "red";
        }
        case true:
          return "orange";
      }
    },
    addBinValidation() {
      let here = this.shellModeModel.add_bin;
      if (
        here.bin_number === null ||
        here.bin_number === "" ||
        here.bin_number <= 0
      ) {
        return false;
      }

      if (here.job_id === null || here.job_id === "" || here.job_id <= 0) {
        return false;
      }

      if (
        here.current_index === null ||
        here.current_index === "" ||
        here.current_index < 0
      ) {
        return false;
      }

      return true;
    },
  },
  computed: {
    StationRecordExportFileName() {
      let from = this.stationRecordModel.dtfromto[0];
      let to =
        this.stationRecordModel.dtfromto[1] ??
        this.stationRecordModel.dtfromto[0];
      return `station_record_${from}_${to}.csv`;
    },
    StationErrorRecordExportFileName() {
      let from = this.stationErrorRecordModel.dtfromto[0];
      let to =
        this.stationErrorRecordModel.dtfromto[1] ??
        this.stationErrorRecordModel.dtfromto[0];
      if (this.stationErrorRecordModel.station_code !== null) {
        let code = this.stationErrorRecordModel.station_code;
        return `station${code}_error_record_${from}_${to}.csv`;
      } else {
        return `station_error_record_${from}_${to}.csv`;
      }
    },
    catchShellMessage(){
      return this.shell.wsMessage
    },
    displayedStorages() {
    return this.stationCallBinData.all_storages_code.map(storage => ({ text: `Totebin ${storage}`, storage }));
  }
  },
  data: () => ({
    convertStringToLocal,
    currentZone: getCube()[0],
    zones: getCube(),
    viewStationDetail: async function() {
      let here = this.stationDetailData;
      here.doneSync = false;
      here.bool = true;
      here.response = await getStationQ("station_details", {
        zone: getMapping(this.currentZone),
      });
      here.doneSync = true;
      for (let i = 0; i < here.response.data.length; i++) {
        this.station_list.push(here.response.data[i].station_id);
      }
      // Also refresh runtime stations when syncing
      this.getRuntimeStations();
    },
    viewStationHistory: async function(station_id) {
      let here = this.stationHistoryData;
      here.bool = true
      here.doneSync = false;
      here.station_id = station_id
      here.response = await getStationQ("station_history", {
          code: station_id,
        }),
      here.doneSync = true;
    },
    viewStationMovement: async function (station_id, bin_at_worker) {
      let here = this.stationMovementData
      here.bin_at_worker = bin_at_worker
      here.doneSync = false
      here.station_id = station_id
      here.bool = true
      here.response = await getStationQ("station_movements",{ code: station_id })
      here.position = await getStationQ("station_movements/index",{ code: station_id })
      here.doneSync = true
    },
    viewStationBin: async function (station_id, bin_at_worker){
      let here = this.stationCallBinData
      let response = await getStorageCoordinate(null,this.currentZone)
      here.all_storages_code = response.data
      here.bool = true
      here.station_id = station_id
      here.bin_at_worker = bin_at_worker
      here.doneSync = true
    },
    storeBinBtnDialog: async function (station_id, bin_at_worker) {
      let here = this.storeBinData
      here.bin_at_worker = bin_at_worker
      here.bool = true
      here.from_station = station_id
      here.position = "BOTH"
      here.to_station = 0
      here.to_coor = null
    },
    storeBinBtn: async function() {
      let here = this.storeBinData;
      here.doneSync = false;
      let send_data = {};
      if (here.to_hcc) {
        send_data = {
          station_code: here.from_station,
          storage_code: here.storage_code,
        };
        if (here.to_index !== null && here.to_index !== "")
          this.$set(send_data, "to_index", here.to_index);
        here.response = await storeBinHcc(send_data);
      } else {
        send_data = {
          inputs: [
            {
              pickup_stacks: [],
              pickup_coords: [],
              dropoff_stacks: [],
              dropoff_coords: [],
              from_station: here.from_station,
              position: here.position ,
              worker_storage : here.storage_code
            }],
          dry_run_mode: false
        }
        if (here.to_station !== null && here.to_station !== "")
          this.$set(send_data.inputs[0], "to_station", here.to_station);
        if (here.to_coor !== null && here.to_coor !== "")
          this.$set(send_data.inputs[0], "dropoff_coords", [here.to_coor]);
        here.response = await storeBin(send_data, this.currentZone);
      }

      here.doneSync = true;
      if (here.response.status) {
        this.$awn.success(here.response.message);
      } else {
        this.$awn.alert(here.response.message);
      }
      here.bool = false;
      here.to_hcc = false;
      here.storage_code = null;
      here.to_index = null;
      here.position = null;
      here.to_coor = null;
      here.to_station = null;
    },
    callBinBtn: async function () {
      let here = this.stationCallBinData
      let response = await getStorageCoordinate(here.storage_to_call, this.currentZone)

      if (Array.isArray(response.data) && response.data.length === 0) {
        this.$awn.alert(`Bin ${here.storage_to_call} not found.`);
        return
      }
      // e.g response.data
      // {
      //   "storage_code": 12,
      //   "weight": 30.0,
      //   "position": "RIGHT",
      //   "node_id": 6,
      //   "storage_no": "MAS-000012",
      //   "x": 1,
      //   "y": 1,
      //   "z": 4,
      //   "type": "STORAGE" ,
      //   "qty_to_dig": 1
      // }
      here.storage_data = response.data
      let two_d = `${here.storage_data.x},${here.storage_data.y}`
      let pickup_stack_qty = here.storage_data.qty_to_dig + 1
      let send_data = {
        inputs: [
          {
            pickup_stacks: [pickup_stack_qty],
            pickup_coords: [two_d],
            dropoff_stacks: [here.storage_data.qty_to_dig],
            dropoff_coords: [],
            from_station: 0,
            to_station: here.station_id,
            position: here.storage_data.position,
            worker_storage: 0
          }],
        dry_run_mode: false
      }

      here.response = await storeBin(send_data, this.currentZone)
      if (here.response.status) { this.$awn.success(here.response.message); }
      else { this.$awn.alert(here.response.message); }

      this.stationCallBinData.storage_to_call = null
      here.storage_data = null
      here.resposne = null
      here.bool = false
    },
    shellAddBtn: async function (station_id,bin_job) {
      let here = this.shellModeModel
      let moveMentData = this.stationMovementData
      this.selectedTab = 0
      moveMentData.bool = false
      here.station_code = station_id
      here.bin_number = bin_job.bin_no
      here.add_bin.bin_number = bin_job.bin_no
      here.add_bin.job_id = bin_job.id
      here.add_bin.from_index = bin_job.from_index
      here.add_bin.to_index = bin_job.to_index
      window.scrollTo({
        top: document.documentElement.scrollHeight,
        behavior: "smooth", // Optional: adds smooth scrolling animation
      });
    },
    btnShowErrorAction: function(action) {
      this.stationErrorRecordData.showAction = true;
      this.stationErrorRecordData.action = action;
    },
    viewStationError: async function(station_id) {
      let here = this.stationErrorData;
      here.doneSync = false;
      here.station_id = station_id;
      here.bool = true;
      here.response = await getStationQ("station_errors", { code: station_id });
      here.doneSync = true;
    },
    // Station enrollment confirmation methods
    showEnrollConfirmation: function(station_id, item) {
      this.pendingEnrollment.station_id = station_id;
      this.pendingEnrollment.station_code = station_id;
      this.pendingEnrollment.item = item;
      this.showEnrollConfirmDialog = true;
    },
    confirmEnrollment: function() {
      // Call the original updateEnrollStation method
      this.updateEnrollStation(
        this.pendingEnrollment.station_id,
        this.pendingEnrollment.item
      );
      this.cancelEnrollment();
    },
    cancelEnrollment: function() {
      this.showEnrollConfirmDialog = false;
      this.pendingEnrollment.station_id = null;
      this.pendingEnrollment.station_code = null;
      this.pendingEnrollment.item = null;
    },
    updateEnrollStation: async function(station_id, item) {
      let here = this.stationEnrollData;
      here.doneSync = false;
      here.bool = true;
      here.response = await updateStation("operation/station_enroll", "POST", {
        station_code: station_id,
        is_enroll: !item.is_enroll,
      });
      here.doneSync = true;
      if (here.response.status) {
        item.is_enroll = !item.is_enroll;
        this.$awn.success(here.response.message);
      } else {
        this.$awn.alert(here.response.message);
      }
    },
    showBinStatusDialog: async function(station_id, item) {
      let here = this.stationBinStatus;
      here.doneSync = false;
      here.bool = true;
      here.station = item;
      here.stationCell = item.cell;
      here.station_id = station_id;
      here.userKeyInStatus = "";
      here.response = await getStationQ("station_plc_status", {
        code: station_id,
      });
      if (!here.response.status) {
        here.color = "red";
        here.error = true;
        here.error_msg = here.response.message;
      } else {
        here.color = "#1565C0";
        (here.error = false), (here.error_msg = null);
        here.checkingCell = new Array(here.stationCell).fill(false);
      }
      here.doneSync = true;
    },
    updateMaintButton: async function(is_maint) {
      let here = this.stationMaintData;
      let dialog = this.stationBinStatus;
      here.doneSync = false;
      here.response = await updateStation("operation/station_maint", "POST", {
        station_code: dialog.station_id,
        is_maint: !is_maint,
      });
      dialog.bool = false;
      if (here.response.status) {
        dialog.station.is_maint = !is_maint;
        this.$awn.success(here.response.message);
      } else {
        this.$awn.alert(here.response.message);
      }
    },
    showShellDialog: async function(command, extraCommand) {
      let here = this.shellModeModel;
      here.command = command;

      if (here.station_code === null || here.station_code === "") {
        this.$awn.alert("Please select the staiton to send shell command");
        return;
      }

      if (extraCommand) {
        here.command = here.command + "," + extraCommand;
      }

      here.bool = true;
    },
    sendShellBtn: async function() {
      let here = this.shellModeModel;
      here.bool = false;
      here.bin_status = null;
      here.response = await sendShellCommand({
        station_code: here.station_code,
        command: here.command,
      });

      this.shell.boolRes = true;
    },
    getStationRecord: async function() {
      let here = this.stationRecordData;
      let daterange = this.stationRecordModel.dtfromto;
      let df = daterange[0];
      let dt = daterange[1] ?? daterange[0];
      var d1 = Date.parse(daterange[0]);
      var d2 = Date.parse(daterange[1]);
      if (d1 > d2) {
        df = daterange[1];
        dt = daterange[0];
      }
      here.doneSync = false;
      here.bool = true;
      here.response = await getStationQ("station_records", {
        from: df,
        to: dt,
      });
      here.doneSync = true;
    },
    getStationErrorRecord: async function() {
      let here = this.stationErrorRecordData;
      let code = this.stationErrorRecordModel.station_code;
      let daterange = this.stationErrorRecordModel.dtfromto;
      let df = daterange[0];
      let dt = daterange[1] ?? daterange[0];
      var d1 = Date.parse(daterange[0]);
      var d2 = Date.parse(daterange[1]);
      if (d1 > d2) {
        df = daterange[1];
        dt = daterange[0];
      }
      let send_model = { from: df, to: dt };
      if (code !== null && code !== "" && code !== undefined) {
        this.$set(send_model, "code", code);
      }
      here.doneSync = false;
      here.bool = true;
      here.response = await getStationQ("station_error_records", send_model);
      here.doneSync = true;
    },
    getStationInnerErrorLog: async function() {
      let here = this.stationInnerErrorLogData;
      let code = this.stationInnerErrorLogModel.station_code;
      let date = this.stationInnerErrorLogModel.date;
      let send_model = {
        date: date,
      };
      if (code !== null && code !== "" && code !== undefined) {
        this.$set(send_model, "code", code);
      }
      here.doneSync = false;
      here.bool = true;
      here.response = await getStationQ(
        "station_job_done_error_log",
        send_model
      );
      here.doneSync = true;
      if (!here.response.status) {
        this.$awn.alert(here.response.message);
      }
    },
    getStationLog: async function() {
      let here = this.stationLogData;
      let code = this.stationLogModel.station_code;
      if (code === null || code === "") {
        this.$awn.alert("Station Code is required!");
        return;
      }
      let date = this.stationLogModel.date;
      let tf = this.stationLogModel.timeFrom;
      let tt = this.stationLogModel.timeTo;
      var t1 = Date.parse(tf);
      var t2 = Date.parse(tt);
      if (t1 > t2) {
        let temp = tf;
        tf = tt;
        tt = temp;
      }
      here.doneSync = false;
      here.bool = true;
      here.response = await getStationQ("station_log", {
        code: code,
        date: date,
        from: tf,
        to: tt,
      });
      here.doneSync = true;
    },
    getNextJobRun: async function() {
      let here = this.stationNextJobRunData;
      let code = this.stationNextJobRunModel.station_code;
      if (code === null || code === "") {
        this.$awn.alert("Station Code is required!");
        return;
      }
      (here.doneSync = false),
        (here.response = await getMockQ("job_able_to_mock", { code: code }));
      here.doneSync = true;
    },
    completeNextJobRun: async function(message) {
      let response = await Mock("mock_complete_next_job", { message: message });
      if (response.status) {
        this.$awn.info("Success");
      } else {
        this.$awn.alert("Failed : ");
      }

      this.getNextJobRun();
    },
    getBinHistory: async function() {
      let here = this.binHistoryData;
      let bin_no = here.bin_no
      if (bin_no === null || bin_no === "" || bin_no <=0) {
        this.$awn.alert("Invalid Bin Number!");
        return;
      }
      here.doneSync = false;
      here.bool = true;
      here.response = await getOperationQ("bin_history", {
        bin_no:bin_no
      });
      here.doneSync = true;
    },
    env: "",
    tab: null,
    mockTab: null,
    mjd_valid: true,
    mm_valid: true,
    mu_valid: true,
    mpa_valid: true,
    m_valid: true,
    bolMock: false,
    txtMock: null,
    input_1: null,
    input_2: null,
    input_3: null,
    input_4: "",
    input_5: "",
    input_6: false,
    input_7: null,
    txtPreview: null,
    entity: " ",
    approve: false,
    namespace: "station",
    // socketio data
    station_code: null,
    storage_code: "",
    cube: "",
    time_arrive: "", //only for work_arrival
    pick_index: null,
    job_id: null,
    message: "",
    req_status: false,
    is_bridge: false,
    station_list: [],
    runtimeStationCount: 0,
    runtimeStationData: {
      bool: false,
      stations: [],
      doneSync: true,
    },
    tabItems: [
      "Shell",
      "Mock",
      "Station Record",
      "Station Error Record",
      "Station Inner Error Log",
      "Log",
    ],
    selectedTab: 0,
    mockTabItems: [
      "Mock Complete Next Job",
      "Mock Station MSG",
      "Send Station MSG",
      "Mock Socketio Event",
    ],
    socketEventItems: ["work_arrival", "junction_arrival", "pick_arrival"],
    actionItems: ["P", "D"],
    codeItems: ["B", "M", "J", "U"],
    directionItems: ["none", "C", "D", "P"],
    stationPosition: ["LEFT", "RIGHT", "BOTH"],
    zoneRules: [
      (v) => !!v || "Required",
      (v) => (v >= 0 && v <= 12) || "Invalid Zone",
    ],
    stationRules: [
      (v) => !!v || "Required",
      (v) => (v >= 1 && v <= 17) || "Invalid Station",
    ],
    stationHistoryData: {
      bool: false,
      station_id: null,
      response: [],
      doneSynce: false,
      headers:[
        { text: "Bin Number ",value: "storage_code" },
        { text: "Type", value: "type" },
        { text: "Status", value: "status" },
        { text: "Arrived At", value: "arrived_at" },
        { text: "Processed At", value: "processed_at" },
        { text: "Leaved At", value: "leaved_at" },
      ]
    },
    stationDetailData: {
      bool: false,
      text: null,
      headers: [
        { text: "ID", align: "start", sortable: true, value: "station_id" },
        { text: "Matrix Code", sortable: true, value: "matrix_code" },
        { text: "Type", value: "type" },
        { text: "Cell", value: "cell" },
        { text: "Mode", value: "mode" },
        { text: "Connectivity", value: "connectivity" },
        { text: "Storage Qty", value: "storage_quantity" },
        // { text: "Station GW Status", value: "gw_status" },
        { text: "Call Bin", value: "call_bin" },
        { text: "Station Movement", value: "movement" },
        { text: "Error Code", value: "error_code" },
        { text: "Enroll Mode", value: "is_enroll" },
        { text: "Recovery", value: "recovery" },
        { text: "History", value: "history" },

      ],
      response: {},
      doneSync: false,
    },
    stationMovementData: {
      bool: false,
      text: null,
      station_id: null,
      headers: [
        { text: "ID", align: "start", sortable: true, value: "id" },
        { text: "Bin No", value: "bin_no" },
        { text: "Order ID", value: "order_id" },
        { text: "Order Type", value: "order_type" },
        { text: "Job Type", value: "type" },
        { text: "From To", value: "from_index" },
        { text: "ACK", value: "plc_ack" },
        { text: "Status", value: "status" },
        { text: "Created At", value: "created_at" },
        { text: "Shell", value: "shell" },
      ],
      response: {},
      doneSync: false,
    },
    stationErrorData: {
      bool: false,
      text: null,
      station_id: null,
      headers: [
        {
          text: "Error Code",
          align: "start",
          sortable: true,
          value: "error_name",
        },
        { text: "Module", value: "module" },
        { text: "Error Message", value: "error_message" },
        { text: "Action", value: "action" },
        {
          text: "Error Level ( 1=Self Service, 2=Technician )",
          value: "error_level",
        },
      ],
      response: {},
      doneSync: false,
    },
    stationBinStatus: {
      bool: false,
      color: "black",
      station: null,
      station_id: null,
      stationCell: null,
      checkingCell: [],
      response: {},
      error_msg: null,
      error: false,
      doneSync: false,
      userKeyInStatus: "",
    },
    stationMaintData: {
      bool: false,
      response: {},
      doneSync: false,
    },
    storeBinData: {
      bool: false,
      text: null,
      to_hcc: false,
      to_index: null,
      from_station: null,
      position:null,
      to_station:0,
      to_coor:null,
      response: {},
      doneSync: false,
    },
    stationCallBinData:{
      bool: false,
      storage_code:null,
      doneSync: false
    },
    stationEnrollData:{
      bool: false,
      text: null,
      station_id: null,
      response: {},
      doneSync: false,
    },
    // bin history data
    binHistoryData:{
      bool:false,
      doneSync: false,
      bin_no:"",
      response:{},
      headers:[
        { text: "History ",value: "history" },
        { text: "Happened At", value: "happened_at" }
      ],
    },
    // Shell Mode (send)
    shellModeModel: {
      bool: false,
      bin_number: null,
      bin_status: null,
      add_bin: {
        bin_number: "",
        job_id: "",
        current_index: "",
        from_index: "",
        to_index: "",
      },
      station_code: null,
      command: null,
      response: {},
      doneSync: false,
    },
    // Shell Menu
    shell: {
      status: [
        {
          title: "Shell",
          data: [
            {
              title: "Enable",
              color: "green",
              command: 1,
              icon: "mdi-access-point-check",
            },
            {
              title: "Disable",
              color: "lime",
              command: 0,
              icon: "mdi-access-point-remove",
            },
          ],
        },
        {
          title: "Control",
          data: [
            {
              title: "START",
              color: "green",
              command: "START",
              icon: "mdi-power-on",
            },
            {
              title: "RESET",
              color: "lime",
              command: "RESET",
              icon: "mdi-restart",
            },
            {
              title: "STOP",
              color: "orange",
              command: "STOP",
              icon: "mdi-power-off",
            },
          ],
        },
        {
          title: "Request",
          data: [
            {
              title: "Status",
              color: "green",
              command: "S",
              icon: "mdi-alpha-s-box",
            },
            {
              title: "Worker",
              color: "lime",
              command: "read_worker",
              icon: "mdi-text-box",
            },
          ],
        },
        {
          title: "Curtain Sensor",
          data: [
            {
              title: "BP Status",
              color: "lime",
              command: "CS,read_by_pass",
              icon: "mdi-alpha-s-box",
            },
            {
              title: "BP ON",
              color: "orange",
              command: "CS,flag_by_pass,1",
              icon: "mdi-power-on",
            },
            {
              title: "BP OFF",
              color: "red",
              command: "CS,flag_by_pass,0",
              icon: "mdi-power-off",
            },
          ],
        },
        {
          title: "Load Cell",
          data: [
            {
              title: "BP Status",
              color: "lime",
              command: "LC,read_by_pass",
              icon: "mdi-alpha-s-box",
            },
            {
              title: "BP ON",
              color: "orange",
              command: "LC,flag_by_pass,1",
              icon: "mdi-power-on",
            },
            {
              title: "BP OFF",
              color: "red",
              command: "LC,flag_by_pass,0",
              icon: "mdi-power-off",
            },
          ],
        },
        {
          title: "QR Code",
          data: [
            {
              title: "BP Status",
              color: "lime",
              command: "QR,read_by_pass",
              icon: "mdi-alpha-s-box",
            },
            {
              title: "BP ON",
              color: "orange",
              command: "QR,flag_by_pass,1",
              icon: "mdi-power-on",
            },
            {
              title: "BP OFF",
              color: "red",
              command: "QR,flag_by_pass,0",
              icon: "mdi-power-off",
            },
          ],
        },
        {
          title: "Force",
          data: [
            { title: "Light Up ", color: "lime", command: "L,ON", icon: "mdi-lightbulb-on-10" },
            { title: "Light Down ", color: "orange", command: "L,OFF", icon: "mdi-lightbulb" }
          ]
        }

      ],
      wsMessage: null,
      listOfRes: [],
      boolRes: false,
    },
    // Mock Next Job To Run (send)
    stationNextJobRunModel: {
      station_code: null,
    },
    // Mock Next Job To Run (receive)
    stationNextJobRunData: {
      headers: [
        { text: "Bin No", value: "storage_code" },
        { text: "Message", value: "message" },
        { text: "Complete", value: "complete" },
      ],
      response: {},
      doneSync: true,
    },
    // Station Record V-model  (send)
    stationRecordModel: {
      dtfromto: [
        new Date().toISOString().slice(0, 10),
        new Date().toISOString().slice(0, 10),
      ],
      exportFields: [
        "station_code",
        "station_down_count",
        "station_down_total_time",
        "station_error_count",
        "number_of_bin_enrolled",
        "station_order_count"
      ]
    },
    // Station Record V-model (receive)
    stationRecordData: {
      headers: [
        { text: "Code", value: "station_code" },
        { text: "Down Count", value: "station_down_count" },
        { text: "Total Down Time",value: "station_down_total_time" },
        { text: "Error Count",value: "station_error_count" },
        { text: "Number of Bin Enrolled",value: "number_of_bin_enrolled" },
        { text: "Order Count",value: "station_order_count" }
      ],  
      response: {},
      doneSync: true
    },
    // Station Error Record V-model (send)
    stationErrorRecordModel: {
      station_code: null,
      dtfromto: [
        new Date().toISOString().slice(0, 10),
        new Date().toISOString().slice(0, 10),
      ],
      exportFields: [
        "station_code",
        "error_name",
        "error_message",
        "module",
        "error_level",
        "created_at",
      ],
    },
    stationErrorRecordData: {
      headers: [
        { text: "Station Code", value: "station_code" },
        { text: "Error Code", value: "error_name" },
        { text: "Error Message", value: "error_message" },
        { text: "Module", value: "module" },
        { text: "Error Level", value: "error_level" },
        { text: "Time Error Occur", value: "created_at" },
      ],
      response: {},
      doneSync: true,
      showAction: false,
      action: "",
    },
    stationInnerErrorLogModel: {
      station_code: null,
      date: new Date().toISOString().slice(0, 10),
    },
    stationInnerErrorLogData: {
      bool: false,
      text: null,
      station_id: null,
      headers: [
        { text: "Date", value: "date" },
        { text: "Station Code", value: "code" },
        { text: "Error Message", value: "error_msg" },
        { text: "Error Reason", value: "error_reason" },
      ],
      response: {},
      doneSync: true,
    },
    stationLogModel: {
      station_code: null,
      date: new Date().toISOString().slice(0, 10),
      timeFrom: "00:00:00",
      timeTo: "23:59:59",
    },
    stationLogData: {
      headers: [
        { text: "Time", value: "time" },
        { text: "Message", value: "message" },
      ],
      response: {},
      doneSync: true,
    },
    btnMock: async function(url, json) {
      let res = await Mock(url, json);
      if (res.status) {
        this.txtMock = "Mock msg is receveid by HCC";
      } else {
        this.txtMock = "Failed to send mock message to HCC";
      }
      this.bolMock = true;
    },
    // Station enrollment confirmation dialog
    showEnrollConfirmDialog: false,
    pendingEnrollment: {
      station_id: null,
      station_code: null,
      item: null
    },
  }),
  watch: {
    catchShellMessage(newValue) {
      var here = this;
      var command = "SHELL";
      if (newValue.message.includes(command)) {
        here.shell.listOfRes.push(newValue);
      }
    },
  },
};

async function getStationQ(url, params) {
  let hccUrl = getHccUrl();
  var searchParams = new URLSearchParams(params);
  var hwxHost = `${hccUrl}/cube/station/${url}`;
  try {
    const res = await axios.get(`${hwxHost}?${searchParams}`, {
      headers: getRequestHeader(),
      validateStatus: function(status) {
        return (status >= 200 && status < 300) || status == 400; // Only consider 2xx (success) codes as valid
      },
    });
    return res.data;
  } catch (error) {
    return {
      data: [],
      error: error,
    };
  }
}

async function getOperationQ(url, params) {
  let hccUrl = getHccUrl();
  var searchParams = new URLSearchParams(params);
  var hwxHost = `${hccUrl}/cube/operation/${url}`;
  try {
    const res = await axios.get(`${hwxHost}?${searchParams}`, {
      headers: getRequestHeader(),
      validateStatus: function(status) {
        return (status >= 200 && status < 300) || status == 400; // Only consider 2xx (success) codes as valid
      },
    });
    return res.data;
  } catch (error) {
    return {
      data: [],
      error: error,
    };
  }
}

async function getMockQ(url, params) {
  let hccUrl = getHccUrl();
  var searchParams = new URLSearchParams(params);
  var hwxHost = `${hccUrl}/cube/mock/${url}`;
  try {
    const res = await axios.get(`${hwxHost}?${searchParams}`, {
      headers: getRequestHeader(),
    });
    return res.data;
  } catch (error) {
    return {
      data: [],
      error: error,
    };
  }
}

async function getStorageCoordinate(storageCode,zone) {
  let tcUrl = getHost(zone)
  var tcHost = `${tcUrl}/api/v2/isolation/storages${storageCode ? `?storage_code=${storageCode}` :""}`;

  try {
    const res = await axios.get(tcHost);
    return res.data
  } catch (error) {
    return {
      data: [],
      error: error
    }
  }

}


async function Mock(url, json) {
  let hccUrl = getHccUrl();
  var hwxHost = new URL(`${hccUrl}/cube/mock/${url}`);
  var requestOptions = {
    method: "POST",
    body: JSON.stringify(json),
    headers: getRequestHeader(),
  };
  try {
    let res = await fetch(hwxHost, requestOptions);
    return res;
  } catch (error) {
    let msg =
      "Remote end point " +
      hwxHost +
      " not accessible, please ensure there is valid Input selected " +
      getCurrentDateTime();
    return msg;
  }
}

async function updateStation(url, method, body) {
  let hccUrl = getHccUrl();
  var hwxHost = `${hccUrl}/cube/${url}`;
  var requestOptions = {
    method: method,
    body: JSON.stringify(body),
    headers: getRequestHeader(),
  };
  try {
    let res = await fetch(hwxHost, requestOptions);
    return res.json();
  } catch (error) {
    return {
      data: [],
      error: error,
    };
  }
}

async function sendShellCommand(body) {
  let hccUrl = getHccUrl();
  var hwxHost = `${hccUrl}/cube/station/shell_command`;
  var requestOptions = {
    method: "POST",
    body: JSON.stringify(body),
    headers: getRequestHeader(),
  };
  try {
    let res = await fetch(hwxHost, requestOptions);
    return res.json();
  } catch (error) {
    return {
      data: [],
      error: error,
    };
  }
}

async function storeBin(body, zone) {
  let tcUrl = getHost(zone);
  var tcHost = `${tcUrl}/api/v2/isolation/job/mock`;
  var requestOptions = {
    method: "POST",
    body: JSON.stringify(body),
    headers: getRequestHeader(),
  };
  try {
    let res = await fetch(tcHost, requestOptions);
    const myJson = await res.json();
    if (myJson.code === 401) {
      // If access token is unauthorized
      // use refresh token to get new access token from auth server
      return useRefreshToken(this, this.storeBin, body, zone);
    }
    return myJson;
  } catch (error) {
    return {
      data: [],
      error: error,
    };
  }
}

async function storeBinHcc(body) {
  let hccUrl = getHccUrl();
  var hwxHost = `${hccUrl}/cube/operation/next_bin`;
  var requestOptions = {
    method: "POST",
    body: JSON.stringify(body),
    headers: getRequestHeader(),
  };
  try {
    let res = await fetch(hwxHost, requestOptions);
    return res.json();
  } catch (error) {
    return {
      data: [],
      error: error,
    };
  }
}
</script>

<style>
@import "~vue-awesome-notifications/dist/styles/style.css";
</style>
