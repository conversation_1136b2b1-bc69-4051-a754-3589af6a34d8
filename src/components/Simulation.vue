<template>
  <v-app>
    <v-container fluid>
      <SnackbarNotification ref="snackbar" />
      <v-card dark class="mb-4">
        <v-row align="center">
          <v-col cols="3">
            <v-select
              v-model="currentZone"
              :items="zones"
              class="ma-2"
              prepend-icon="mdi-cube"
              @change="onCubeChanged"
              right
          /></v-col>
          <v-col>
            <v-btn
              @click="fetchStationConfig()"
              :disabled="!doneSync"
              class="ma-2"
            >
              Refresh
            </v-btn>
          </v-col>
        </v-row>

        <v-card-title>Simulation</v-card-title>
        <setting-list-item
          v-for="(attrs, key) in params"
          :key="key"
          @submitChange="postSetting($event, key)"
          @submitNestedChange="postNestedSetting"
          :param-attr="attrs"
          :disabled="loading"
          ref="settingItems"
        />
      </v-card>
      <v-card dark class="mb-4">
        <v-card-title dark>Station</v-card-title>

        <v-data-table
          v-model="modelStationConfig.selected"
          :headers="modelStationConfig.headers"
          item-key="station_id"
          :items="modelStationConfig.rows"
          :items-per-page="15"
          class="elevation-1"
          dark
          sort-by="station_id"
        >
          <template v-slot:[`item.station_id`]="{ item }">
            <router-link
              :to="{
                path: `/simulation/${item.station_id}`,
                query: {
                  station_id: item.station_id,
                  url: url,
                },
              }"
            >
              {{ item.station_id }}</router-link
            >
          </template>

          <template v-slot:[`item.session_start_time`]="{ item }">
            {{ convertTimestampToLocal(item.session_start_time, true) }}
          </template>
        </v-data-table>
      </v-card>

      <!-- <v-card dark class="mb-4">
        <v-card-title>Skycar</v-card-title>
      </v-card> -->
    </v-container>
  </v-app>
</template>

<script>
import settingListItem from "../components/planningSetting/settingListItem.vue";
import SnackbarNotification from "../components/shared/SnackbarNotification.vue";
import {
  convertTimestampToLocal,
  getCube,
  getHost,
  useRefreshToken,
} from "../helper/common";
const httpRequest = require("../helper/http_request.js");
import { RouteIsolation } from "../helper/enums";

let positiveIntegerRule = [
  (value) =>
    !value || Number.isInteger(Number(value)) || "Must be positive integer",
  (value) => !value || Number(value) > 0 || "Must be positive integer",
];
export default {
  name: "App",
  components: { settingListItem, SnackbarNotification },
  created() {
    this.fetchStationConfig();
    this.doneSync = true;
  },
  computed: {
    url() {
      return getHost(this.currentZone);
    },
  },
  data: () => ({
    params: {
      MIN_X: {
        name: "Grid Min X",
        type: "number",
        indent: 0,
        unit: "block",
        hint: "Qty in block",
        value: 0,
        rule: positiveIntegerRule,
      },
      MIN_Y: {
        name: "Grid Min Y",
        type: "number",
        indent: 0,
        unit: "block",
        hint: "Qty in block",
        value: 0,
        rule: positiveIntegerRule,
      },
      MAX_X: {
        name: "Grid Max X",
        type: "number",
        indent: 0,
        unit: "block",
        hint: "Qty in block",
        value: 0,
        rule: positiveIntegerRule,
      },
      MAX_Y: {
        name: "Grid Max Y",
        type: "number",
        indent: 0,
        unit: "block",
        hint: "Qty in block",
        value: 0,
        rule: positiveIntegerRule,
      },
      MAX_Z: {
        name: "Grid Max Z",
        type: "number",
        indent: 0,
        unit: "block",
        hint: "Qty in block",
        value: 0,
        rule: positiveIntegerRule,
      },
      SEED_SKYCAR_QTY: {
        name: "Skycar / Skyvan",
        type: "number",
        indent: 0,
        unit: "Unit",
        hint: "Qty in Unit",
        value: 0,
        rule: positiveIntegerRule,
      },

      IS_DRY_RUN_ON: {
        name: "Dry run mode",
        type: "toggle",
        indent: 0,
        value: false,
        runtime: true,
      },
      IS_ALL_STATION: {
        name: "Generate job for all stations",
        type: "toggle",
        indent: 0,
        value: false,
      },
      IS_AUTO_STORE: {
        name: "Auto store bin after worker processed",
        type: "toggle",
        indent: 0,
        value: false,
      },
      IS_GREEDY_DESTINATION: {
        name: "Digging store to nearest destination",
        type: "toggle",
        indent: 0,
        value: false,
      },
      IS_MOCK_WORK_ARRIVAL_TO_SM: {
        name: "Mock work arrival to SM",
        type: "toggle",
        indent: 0,
        value: false,
      },
      JOB_GENERATION_RULES: {
        name: "Generate Next Batch Job Criteria",
        type: "choice",
        indent: 0,
        value: null,
        choices: [
          {
            name: "WHEN_EXCEED_DURATION",
            code: 0,
            nested: {
              name: "WHEN_EXCEED_DURATION_SECONDS",
              type: "number",
              indent: 0,
              unit: "seconds",
              hint: "Time in seconds",
              value: 100,
              rule: positiveIntegerRule,
            },
          },
          {
            name: "WHEN_JOB_CLEARED",
            code: 1,
          },
          {
            name: "WHEN_JOB_LESS_THAN_SKYCAR",
            code: 2,
          },
        ],
      },
    },

    zones: getCube(),
    currentZone: getCube()[0],
    doneSync: false,
    loading: false,
    convertTimestampToLocal,
    modelIsolationConfig: {},
    modelStationConfig: {
      rows: [],
      headers: [
        { text: "Station", value: "station_id" },
        {
          text: "Retrieving Spawn Per Batch (Qty)",
          value: "uph_spawn_per_iteration",
        },
        { text: "Dig Ratio Per Retrieving", value: "qty_dig_per_uph" },
        { text: "UPH Target Per Hour (Qty)", value: "max_uph_targer_per_hour" },
        {
          text: "Retrieving Created For Current Session (Qty)",
          value: "current_uph_job_counter",
        },
        { text: "Current Session Start Time", value: "session_start_time" },
        { text: "Worker Processing Time", value: "worker_processing_time" },
      ],
      selected: [],
    },
  }),

  methods: {
    onCubeChanged() {
      this.fetchStationConfig();
    },
    updateSimParams() {
      console.log(this.modelIsolationConfig);
      for (let key in this.modelIsolationConfig) {
        if (!(key in this.params)) {
          continue;
        }
        this.params[key].value = this.modelIsolationConfig[key];

        if (!(this.params[key].type == "choice")) {
          continue;
        }

        for (let choice in this.params[key].choices) {
          if (this.params[key].choices[choice].nested) {
            let nestedKey = this.params[key].choices[choice].nested.name;
            this.params[key].choices[
              choice
            ].nested.value = this.modelIsolationConfig[nestedKey];
          }
        }
      }

      //To init once after v-model update, to render nested component
      for (let i = 0; i < Object.keys(this.params).length; i++) {

        this.$refs.settingItems[i].initialize();
      }
    },
    async fetchStationConfig() {
      const response = await httpRequest.axiosRequest(
        "GET",
        this.url,
        RouteIsolation.CONFIG_JOB
      );

      if (response != null) {
        if (response.data.code === 401) {
          return useRefreshToken(this, this.fetchStationConfig);
        } else if (response.data.code != 200) {
          this.$awn.alert(response.data.message);
        } else {
          this.modelIsolationConfig = response.data.data;
          this.modelStationConfig.rows = response.data.data.station_config;
          this.updateSimParams();

        }
      }
    },
    async postSetting(value, settingName) {
      this.updateSetting(value, settingName);
    },
    async postNestedSetting(value, settingName) {
      this.updateSetting(value, settingName);
    },
    async updateSetting(value, settingName) {
      let data = { [settingName]: value };

      const response = await httpRequest.axiosRequest(
        "PUT",
        this.url,
        RouteIsolation.CONFIG,
        data
      );

      if (response != null) {
        if (response.data.code === 401) {
          return useRefreshToken(this, this.updateSetting, data);
        } else if (response.data.code != 200) {
          let errorMessage = response.data.message ? JSON.stringify(response.data.message) : JSON.stringify(response.data.errors);
          this.$refs.snackbar.showNotification(
            false,
            `Failed to set ${settingName} to ${value}. ${errorMessage}`
          );
        } else {
          this.$refs.snackbar.showNotification(
            true,
            `Successfully set ${settingName} to ${value}`
          );
          this.fetchStationConfig();
          return;
        }
      }
    },
    hasNestedValue(choices) {
      return choices.some(
        (choice) => choice.nested && choice.nested.value !== null
      );
    },
  },
};
</script>
