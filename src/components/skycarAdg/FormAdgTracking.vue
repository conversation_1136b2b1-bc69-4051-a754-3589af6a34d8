<template>
    <span>
        <v-form
            v-model="form"
            @keydown.enter.native="btnConfirm()"
        >
            <v-row>
                <v-col>
                    <v-select
                        rounded
                        v-model="cube"
                        label="Cube"
                        filled
                        :items="cubeOptions"
                        prepend-icon="mdi-cube"
                        :rules="[v => !!v || 'Required']"
                    ></v-select>
                </v-col>
            </v-row>
            <v-row>
                <v-col>
                    <v-combobox
                        v-model="sids"
                        label="Skycar ID"
                        clearable
                        filled
                        type="number"
                        rounded
                        prepend-icon="mdi-car"
                        multiple
                        chips
                    >
                        <template v-slot:selection="data">
                            <v-chip
                                color="green"
                            >{{ data.item }}</v-chip>
                        </template>
                    </v-combobox>
                </v-col>
            </v-row>
            <v-row>
                <v-col>
                    <v-btn
                        :disabled="!form"
                        @click="btnConfirm()"
                        class="ma-2"
                        color="green"
                    >
                        <v-icon>mdi-check</v-icon>
                        Confirm
                    </v-btn>
                </v-col>
            </v-row>
        </v-form>
        <DialogAdgTracking ref="dialogAdgTracking"/>
    </span>
</template>

<script>
import DialogAdgTracking from "./DialogAdgTracking.vue";
import { getCube } from "../../helper/common";

export default {
    props: {
        cubeOptions: {
            type: Array
        }
    },
    components: {
        DialogAdgTracking
    },
    created() {
    },
    data: () => ({
        form: null,
        cube: getCube()[0],
        sids: []
    }),
    methods: {
        async btnConfirm() {
            await this.$refs.dialogAdgTracking.openDialog(this.cube, this.sids)
        }
    }
}
</script>
