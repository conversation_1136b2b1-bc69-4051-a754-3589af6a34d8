<template>
    <v-dialog
        v-model="dialogBool"
    >
        <v-card dark>
            <v-card-title>TC Adg</v-card-title>
            <v-data-table
                :headers="headers"
                :items="result"
                :items-per-page="-1"
                dense
                item-key="msg"
                dark
                hide-default-footer
                :item-class="getColor"
                group-by="sid"
            >
                <template v-slot:[`item.msg`]="{ item }">
                    <v-tooltip bottom>
                        <template v-slot:activator="{ on, attrs }">
                            <span
                                v-bind="attrs"
                                v-on="on"
                            >{{ item.msg }}
                            </span>
                        </template>
                        <v-card dark>
                            <v-data-table
                                :headers="childrenHeaders"
                                :items="item.children_list"
                                :items-per-page="-1"
                                dense
                                dark
                                hide-default-footer
                                :item-class="getChildrenColor"
                            >
                                <template v-slot:[`item.is_completed`]="{ item }">
                                    <v-icon
                                        v-if="item.is_completed"
                                        color="green"
                                    >mdi-check
                                    </v-icon>
                                    <v-icon
                                        v-else
                                        color="orange"
                                    >mdi-close
                                    </v-icon>
                                </template>
                            </v-data-table>
                        </v-card>
                    </v-tooltip>
                </template>
                <template v-slot:[`item.msg_sent`]="{ item }">
                    <v-icon
                        v-if="item.msg_sent"
                        color="green"
                    >mdi-check
                    </v-icon>
                    <v-icon
                        v-else
                        color="orange"
                    >mdi-close
                    </v-icon>
                </template>
                <template v-slot:[`item.ack_recv`]="{ item }">
                    <v-icon
                        v-if="item.ack_recv"
                        color="green"
                    >mdi-check
                    </v-icon>
                    <v-icon
                        v-else
                        color="orange"
                    >mdi-close
                    </v-icon>
                </template>
                <template v-slot:[`item.processing`]="{ item }">
                    <v-icon
                        v-if="item.processing"
                        color="green"
                    >mdi-check
                    </v-icon>
                    <v-icon
                        v-else
                        color="orange"
                    >mdi-close
                    </v-icon>
                </template>

                <template
                    v-if="isolation"
                    v-slot:[`item.action`]="{ item }"
                >
                    <v-btn
                        color="green"
                        text
                        @click="flagCompleted(item.sid, item.msg, false)"
                        small
                    >
                        Flag Completed
                    </v-btn>
                    <v-btn
                        v-if="item.request_status=='WAITING FOR APPROVAL'"
                        color="green"
                        text
                        @click="flagCompleted(item.sid, item.msg, true)"
                        small
                    >
                        Flag Approved
                    </v-btn>
                </template>
            </v-data-table>

            <v-card-actions>
                <v-spacer></v-spacer>
                <ProgressCircular :doneSync="doneSync"/>
                <v-btn
                    @click="getAdg()"
                    class="mr-6"
                    color="green"
                >Refresh
                </v-btn>
                <v-btn
                    color="red"
                    @click="closeDialog()"
                    class="mr-6"
                >Close
                </v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script>
import { getCube, getHost, getRequestHeader, useRefreshToken } from "../../helper/common"
import { RouteSkycar } from "../../helper/enums"
import ProgressCircular from "../shared/ProgressCircular.vue"
import { store } from "../../main.js"

export default {
    components: {
        ProgressCircular
    },
    data: () => ({
        isolation: store.state.cubeConfig.mode,
        doneSync: true,
        dialogBool: false,
        cube: getCube()[0],
        sid: null,
        headers: [
            { text: "Message", value: "msg" },
            { text: "Request Status", value: "request_status" },
            { text: "Is Sent", value: "msg_sent" },
            { text: "Is Acknowledged", value: "ack_recv" },
            { text: "Is Processed", value: "processing" },
            { text: "Action", value: "action" }
        ],
        childrenHeaders: [
            { text: "Message", value: "msg" },
            { text: "Is Completed", value: "is_completed" }
        ],
        result: []
    }),
    methods: {
        async getAdg() {
            try {
                this.doneSync = false
                let url = getHost(this.cube) + RouteSkycar.GET_ADG

                let req = await fetch(url, {
                    method: "POST",
                    body: JSON.stringify({
                        sids: this.sids
                    }),
                    headers: getRequestHeader()
                })
                let res = JSON.parse(await req.text())
                if (res.code === 401){ // If access token is unauthorized
                    // use refresh token to get new access token from auth server
                    return useRefreshToken(this, this.getAdg)
                }
                if (res.status) {
                    this.result = res.data
                    this.showDialog()
                } else {
                    this.$awn.alert(res.message)
                }
            } catch (error) {
                this.$awn.alert(error)
            } finally {
                setTimeout(() => {
                    this.doneSync = true
                }, 500)
            }
        },
        async flagCompleted(sid, msg, approval) {
            try {
                let url = getHost(this.cube) + RouteSkycar.GET_ADG

                let req = await fetch(url, {
                    method: "PUT",
                    body: JSON.stringify({
                        sid: sid,
                        // eslint-disable-next-line camelcase
                        msg_id: msg.split(",")[3],
                        approval
                    }),
                    headers: getRequestHeader()
                })
                let res = JSON.parse(await req.text())
                if (res.code === 401){ // If access token is unauthorized
                    // use refresh token to get new access token from auth server
                    return useRefreshToken(this, this.flagCompleted, sid, msg)
                }
                if (res.status) {
                    await this.getAdg()
                } else {
                    this.$awn.alert(res.message)
                }
            } catch (error) {
                this.$awn.alert(error)
            }
        },
        async openDialog(cube, sids) {
            this.cube = cube
            this.sids = sids.map(str => parseInt(str))
            await this.getAdg()
        },
        showDialog() {
            this.dialogBool = true
        },
        closeDialog() {
            this.dialogBool = false
        },
        getColor(item) {
            switch (item.msg_sent) {
                case true:
                    return "green--text text--accent-3"
                case false:
                    return "yellow--text text--accent-4"
            }
        },
        getChildrenColor(item) {
            switch (item.is_completed) {
                case true:
                    return "green--text text--accent-3"
                case false:
                    return "yellow--text text--accent-4"
            }
        }
    }
}
</script>
