<template>
  <v-container>
    <div class="dark-cloud" />
    <div class="stars" />
    <div class="stars2" />
    <div class="stars3" />

    <v-tabs dark :background-color="'transparent'" class="pa-4" v-model="tab">
      <v-tab href="#smOrderLogs">Sm Order Logs</v-tab>
    </v-tabs>

    <v-divider></v-divider>
    <v-tabs-items class="transparent" v-model="tab">
      <v-tab-item value="smOrderLogs">
        <v-card flat class="pa-4 transparent">
          <v-btn
            large
            @click="getSmOrderInfo(true, false)"
            :loading="refreshLoading"
            dark
            style="position: fixed; top: 10%; right: 3%; z-index: 999"
            >Refresh</v-btn
          >
          <v-data-table
            :headers="headers"
            :items="Object.values(smOrderLogs)"
            :items-per-page="maxPageSize"
            :item-class="rowColor"
            hide-default-footer
            dark
            dense
            class="transparent"
          >
            <template v-slot:top>
              <v-row class="flex justify-space-between">
                <v-switch
                  v-model="autoRefresh"
                  :label="`Auto Refresh (${autoRefreshIntervalInSec}s)`"
                  @click="toggleAutoRefresh()"
                ></v-switch>

                <v-btn 
                  class="ma-1"
                  @click="downloadExcel"
                >
                  <v-icon>mdi-download</v-icon> DOWNLOAD CSV
                </v-btn>
              </v-row>
              <v-expansion-panels class="transparent">
                <v-expansion-panel class="transparent">
                  <v-expansion-panel-header class="transparent">
                    Advanced Filter
                  </v-expansion-panel-header>
                  <v-expansion-panel-content class="transparent">
                    <v-row dense>
                      <v-col>
                        <v-combobox
                          v-model="smOrderIdFilter"
                          label="SM Order Id"
                          multiple
                          small-chips
                          :deletable-chips="clearable"
                          :clearable="clearable"
                          outlined
                          :readonly="orderIdReadOnly"
                        />
                      </v-col>
                      <v-col>
                        <v-text-field
                          label="Created At - Start Time"
                          type="datetime-local"
                          v-model="smOrderLogCreatedAtStartTimeFilter"
                          step="2"
                          clearable
                          outlined
                        />
                      </v-col>
                      <v-col>
                        <v-text-field
                          label="Created At - End Time"
                          type="datetime-local"
                          v-model="smOrderLogCreatedAtEndTimeFilter"
                          step="2"
                          clearable
                          outlined
                        />
                      </v-col>
                    </v-row>
                  </v-expansion-panel-content>
                </v-expansion-panel>
              </v-expansion-panels>
              <v-spacer></v-spacer>
              <v-row>
                <v-col cols="4" sm="3">
                  <v-select
                    v-model="perPage"
                    :items="pageSizes"
                    label="Items per Page"
                    @change="handlePageSizeChange"
                  ></v-select>
                </v-col>
                <v-col cols="12" sm="9">
                  <v-pagination
                    v-model="currentPage"
                    :length="totalPages"
                    total-visible="7"
                    next-icon="mdi-menu-right"
                    prev-icon="mdi-menu-left"
                    @input="handlePageChange"
                    dark
                  ></v-pagination>
                </v-col>
              </v-row>
            </template>
            <template v-slot:items="{ item }">
              {{ item }}
            </template>
            <template v-slot:[`item.smOrderId`]="{ item, value }">
              <v-tooltip right close-delay="200" :ref="`smOrder${value}`">
                <template v-slot:activator="{ on, attrs }">
                  <td v-bind="attrs" v-on="on">
                    {{ value }}
                  </td>
                </template>
                <pre
                  style="pointer-events: initial;"
                  @mouseenter="$refs[`smOrder${value}`].runDelay('open')"
                  @mouseleave="$refs[`smOrder${value}`].runDelay('close')"
                  v-for="(val, key) in dynamicDestructure(
                    item.snapshot,
                    smOrderTooltipField
                  )"
                  :key="key"
                  >{{ `${key.padEnd(smOrderTooltipMaxLength)} : ${val}` }}</pre
                >
              </v-tooltip>
            </template>
          </v-data-table>
        </v-card>
      </v-tab-item>
    </v-tabs-items>
  </v-container>
</template>

<script>
import * as moment from "moment";
import { SmLogsAPI } from "../api/logs";
import { convertStringToLocal } from "../helper/common";
import { SmOrderStatus } from "../helper/enums";

export default {
  name: "SMOrderLog",

  data() {
    return {
      oldQuery: null,
      autoRefresh: false,
      autoRefreshTimer: null,
      autoRefreshIntervalInSec: 10,

      tab: null,
      refreshLoading: false,

      smOrderIdFilter: [],
      totalPages: 3,
      currentPage: 1,
      perPage: 15,
      pageSizes: [15, 30, 50, 100],
      maxPageSize: 100,

      smOrderLogCreatedAtStartTimeFilter: null,
      smOrderLogCreatedAtEndTimeFilter: null,

      orderIdReadOnly: false,
      clearable: true,
      smOrderLogs: [],
      hasError: false,
      isSuccess: false,
      errorMessage: null,
      smOrderTooltipField: [
        "orderNo",
        "targetedStack",
        "targetedZoneGroup",
        "priorityTier",
        "requestedAt",
        "requestedBy",
        "dispatchedAt",
        "startPickingAt",
        "pickedAt",
        "completedAt",
        "createdAt",
        "updatedAt",
        "deletedAt",
        "lastErroredAt",
        "errorReason",
      ],
      headers: [
        { text: "ID", value: "id", groupable: false },
        {
          text: "LOG CREATE AT",
          value: "createdAt",
          groupable: false,
          sortable: false,
        },
        { text: "SM ORDER ID", value: "smOrderId", groupable: false },
        {
          text: "STORAGE",
          value: "snapshot.storage",
          groupable: false,
        },
        {
          text: "TYPE",
          value: "snapshot.type",
          groupable: false,
        },
        {
          text: "PICK UP",
          value: "snapshot.pickupNode.threeDimWithZone",
          groupable: false,
        },
        {
          text: "DROP OFF",
          value: "snapshot.dropoffNode.threeDimWithZone",
          groupable: false,
        },
        {
          text: "STATUS",
          value: "snapshot.status",
          groupable: false,
        },
      ],

      smOrderLogData: {
        exportFields: {
          ID: "id",
          "LOG CREATED AT": "createdAt",
          "SM ORDER ID": "smOrderId",
          STATUS: "snapshot.status",
          "PRIORITY TIER": "snapshot.priorityTier",
          "SM ORDER NO": "snapshot.orderNo",
          "COMPLETED AT": "snapshot.completedAt",
          STORAGE: "snapshot.storage",
          TYPE: "snapshot.type",

          "PICK UP": "snapshot.pickupNode.threeDimWithZone",
          "DROP OFF": "snapshot.dropoffNode.threeDimWithZone",
          "CHAIN NO": "snapshot.chainNo",
          "CREATED AT": "snapshot.createdAt",
          "UPDATED AT": "snapshot.updatedAt",
          "DELETED AT": "snapshot.deletedAt",
          "IS TRANSFER": "snapshot.isTransfer",
          "REQUESTED AT": "snapshot.requestedAt",
          "REQUESTED BY": "snapshot.requestedBy",
          "BIN STACK RANK": "snapshot.binStationRank",
          "TARGETED STACK": "snapshot.targetedStack",
          "TARGETED ZONEGROUP": "snapshot.targetedZoneGroup",
          "DISPATCHED AT": "snapshot.dispatchedAt",
          "START PICKING AT": "snapshot.startPickingAt",
          "PICKED AT": "snapshot.pickedAt",
          "CREATION REASON": "snapshot.creationRank",
          "RECOVERY TYPE": "snapshot.recoveryType",
          "ERROR REASON": "snapshot.errorReason",
          "LAST ERROR AT": "snapshot.lastErroredAt",
        },
        result: [],
      },
    };
  },
  beforeMount() {
    this.reloadComponent()
  },
  beforeDestroy() {
    clearInterval(this.autoRefreshTimer);
  },

  methods: {
    async reloadComponent() {
      const params = this.$route.params

      if(params.smOrderId) {
        this.smOrderIdFilter.push(this.$route.params.smOrderId)
        this.orderIdReadOnly = true
        this.clearable = false
      } else {
        this.smOrderIdFilter = []
        this.orderIdReadOnly = false
        this.clearable = true
      }

      await this.getSmOrderInfo();
    },
    async downloadExcel() {
      await SmLogsAPI.exportSmOrderLogsExcel({
          smOrderIds: this.smOrderIdFilter,
          createdAtStart:
              this.smOrderLogCreatedAtStartTimeFilter ?? undefined,
          createdAtEnd: this.smOrderLogCreatedAtEndTimeFilter ?? undefined,
      }).then(response => {
        // Create a temporary link element to trigger the download
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement("a");
        link.href = url;

        const startDate = this.smOrderLogCreatedAtStartTimeFilter
            ? moment(this.smOrderLogCreatedAtStartTimeFilter).format("YYYYMMDD") + "_"
            : ""
        const endDate = this.smOrderLogCreatedAtEndTimeFilter
          ? moment(this.smOrderLogCreatedAtEndTimeFilter).format("YYYYMMDD") + "_"
          : ""
        const filename = `${startDate}${endDate}smOrderLogs.xlsx`
        link.setAttribute("download", filename); // Specify the file name
        document.body.appendChild(link);
        link.click();

        // Clean up
        window.URL.revokeObjectURL(url);
        link.remove();

      })
    },
    toggleAutoRefresh() {
      if (this.autoRefresh) {
        if (this.autoRefreshTimer) clearInterval(this.autoRefreshTimer);

        this.autoRefreshTimer = setInterval(() => {
          this.getSmOrderInfo(true, false);
        }, this.autoRefreshIntervalInSec * 1000);
      } else {
        clearInterval(this.autoRefreshTimer);
      }
    },

    handlePageChange(value) {
      this.currentPage = value;
      this.getSmOrderInfo();
    },

    handlePageSizeChange(value) {
      this.perPage = value;
      this.getSmOrderInfo();
    },

    dynamicDestructure(objData, propertiesArr) {
      if (!objData) return {};
      if (propertiesArr.length > 0) {
        return propertiesArr.reduce(
          (acc, cur) => ({ ...acc, [cur]: objData[cur] }),
          {}
        );
      }
      return objData;
    },

    smOrderTooltipMaxLength() {
      return Math.max(...this.smOrderTooltipField.map((x) => x.length));
    },

    async getSmOrderInfo(hardRefresh = false, reRunOldQuery = false) {
      try {
        if (hardRefresh) {
          this.currentPage = 1;
        }
        this.refreshLoading = true;
        let params = null;

        if (reRunOldQuery && this.oldQuery) {
          params = this.oldQuery;
        } else {
          params = {
            perPage: this.perPage,
            pageNo: this.currentPage,
            smOrderId: this.smOrderIdFilter,
            createdAtStart:
              this.smOrderLogCreatedAtStartTimeFilter ?? undefined,
            createdAtEnd: this.smOrderLogCreatedAtEndTimeFilter ?? undefined,
          };
        }
        await SmLogsAPI.getSmOrderLogs(params)
          .then((res) => {
            let result = res.data.data;

            if (!result) return null;
            this.totalPages = res.data.pagination.totalPages;
            this.totalItem = res.data.pagination.totalRecords;
            result.forEach((order) => {
              if (order.snapshot.pickupNode) {
                order.snapshot.pickupNode.threeDimWithZone =
                  order.snapshot.pickupNode.threeDim +
                  "(" +
                  order.snapshot.pickupNode.zoneGroup +
                  ")";
              }
              if (order.snapshot.dropoffNode) {
                order.snapshot.dropoffNode.threeDimWithZone =
                  order.snapshot.dropoffNode.threeDim +
                  "(" +
                  order.snapshot.dropoffNode.zoneGroup +
                  ")";
              }
            });

            this.smOrderLogs = result.map(r => ({
              ...r,
              createdAt: r.createdAt ? convertStringToLocal(r.createdAt, true) : undefined,
            }));
            this.smOrderLogData["result"] = result;

            this.oldQuery = params;
          })
          .catch((err) => {
            console.error(err)

            this.errorMessage = err.response.data.error;
            this.hasError = true;
          });
      } catch (e) {
        this.$awn.alert(`Order Page Get Data Exception - ${e.message}`);
      }

      this.refreshLoading = false;
    },

    rowColor(item) {
      if (!item) return "grey--text text--darken-1";
      switch (item.snapshot.status) {
        case SmOrderStatus.Available:
          return "yellow--text text--accent-4";
        case SmOrderStatus.Dispatched:
          return "cyan--text text--accent-3";
        case SmOrderStatus.Picking:
          return "light-blue--text text--lighten-1";
        case SmOrderStatus.Picked:
          return "light-green--text text--accent-3";
        case SmOrderStatus.Completed:
          return "grey--text text--darken-1";
        case SmOrderStatus.Skipped:
          return "grey--text text--darken-1";
        case SmOrderStatus.Error:
          return "red--text text--accent-3";
        default:
          return "";
      }
    },
  },
  watch: {
    "$route.params": "reloadComponent",
    smOrderIdFilter() {
      this.getSmOrderInfo(true, false);
    },
    smOrderLogCreatedAtStartTimeFilter() {
      this.getSmOrderInfo(true, false);
    },
    smOrderLogCreatedAtEndTimeFilter() {
      this.getSmOrderInfo(true, false);
    },
    
  }
};
</script>

<style>
/* @import '~@fortawesome/fontawesome-free/css/fontawesome.min.css';
        @import '~@fortawesome/fontawesome-free/css/solid.min.css'; */
@import "../assets/Stars.css";
@import "../assets/Background.css";

.content {
  margin: 50px;
  margin-top: 25px;
};

</style>
