<template>
  <v-container>
    <v-alert :value="hasError" transition="fade" type="error">{{
      errorMessage
    }}</v-alert>
    <v-alert :value="isSuccess" transition="fade" type="success"
      >Successfully Updated <br />
      <!-- <p>Auto return remaining time: {{ countdown }}s</p> -->
    </v-alert>
    <h2>Storage</h2>

    <v-divider></v-divider>

    <v-form class="transparent">
      <v-container class="mt-5 transparent">
        <v-row class="transparent">
          <v-col md="3" class="transparent"> Status: </v-col>
          <v-col class="transparent">
            <v-select
              :items="storageStatusList"
              label="Status"
              v-model="storage.status"
              outlined
            ></v-select>
          </v-col>
        </v-row>

        <v-row>
          <v-col md="3"> Coordinate: </v-col>
          <v-col>
            <v-text-field
              label="x-coord"
              v-model.number="storage.node.x"
              dense
              outlined
              filled
              rounded
              class="shrink"
            >
            </v-text-field>
          </v-col>
          <v-col>
            <v-text-field
              label="y-coord"
              v-model.number="storage.node.y"
              dense
              outlined
              filled
              rounded
              class="shrink"
            >
            </v-text-field>
          </v-col>
          <v-col>
            <v-text-field
              label="z-coord"
              v-model.number="storage.node.z"
              dense
              outlined
              filled
              rounded
              class="shrink"
            >
            </v-text-field>
          </v-col>
          <v-col>
            <v-text-field
              label="zone group"
              v-model="storage.node.zoneGroup"
              dense
              outlined
              filled
              rounded
              class="shrink"
            >
            </v-text-field>
          </v-col>
        </v-row>

        <v-row>
          <v-col md="3"> Last Movement: </v-col>
          <v-col>
            <v-select
              :items="storageLastMovement"
              label="Last Movement"
              v-model="storage.lastMovement"
              outlined
            ></v-select>
          </v-col>
        </v-row>

        <v-row>
          <v-col md="3">Station</v-col>
          <v-col>
            <v-text-field
              v-model.number="storage.station"
              dense
              outlined
              filled
              rounded
              class="shrink"
            ></v-text-field>
          </v-col>
        </v-row>

        <v-row>
          <v-col md="3">Tags</v-col>
          <v-col>
            <v-combobox
              v-model="storage.tags"
              label="Tags"
              multiple
              small-chips
              deletable-chips
              outlined
              @input="convertTagToUppercase"
            />
          </v-col>
        </v-row>

        <!-- <v-row>
            <v-col md="3"> ReservationNo: </v-col>
            <v-col>
              <v-text-field
                v-model="storage.reservationNo"
                dense
                outlined
                filled
                rounded
                class="shrink"
                disabled
              ></v-text-field>
            </v-col>
          </v-row> -->

        <v-row>
          <v-col md="3">Advanced Order</v-col>
          <v-col>
            <v-combobox
              v-model="storage.advancedOrders"
              label="read-only"
              multiple
              small-chips
              deletable-chips
              outlined
              disabled
            />
          </v-col>
        </v-row>

        <v-row>
          <v-col>
            <v-card-actions class="justify-center">
              <v-btn @click="updateStorage()">Update</v-btn>
            </v-card-actions>
          </v-col>
        </v-row>
      </v-container>
    </v-form>
  </v-container>

  <!-- Add advanced order table view -->
</template>

<script>
import { SmStorageAPI } from "../api/storages";
import { StorageLastMovement, StorageStatus } from "../helper/enums";

export default {
  name: "Storage",

  data() {
    return {
      storage: null,
      hasError: false,
      isSuccess: false,
      errorMessage: null,

      storageStatusList: Object.values(StorageStatus),
      storageLastMovement: Object.values(StorageLastMovement),

      timeoutDuration: 3000,
      countdown: 0,
    };
  },

  methods: {
    convertTagToUppercase(value) {
      if (value && Array.isArray(value)) {
        this.storage.tags = value.map((v) => v.toUpperCase());
      }
    },
    async getStorage() {
      const id = this.$route.params.id;

      if (!id) return;

      await SmStorageAPI.getStorage(id)
        .then((res) => {
          let { data } = res.data;

          if (!data) return null;

          this.storage = data;

          if (this.storage.node == null) {
            this.storage.node = {
              x: null,
              y: null,
              z: null,
              zoneGroup: null,
            };
          }

          this.hasError = false;
        })
        .catch((err) => {
          this.errorMessage = err.response.data.errors;
          this.hasError = true;
        })
        .finally(() => {
          this.scrollToTop();
        });
    },
    async updateStorage() {
      const id = this.$route.params.id;

      await SmStorageAPI.updateStorage(id, {
        code: parseInt(id),
        status: this.storage.status,
        lastMovement: this.storage.lastMovement,
        station: this.storage.station ? parseInt(this.storage.station) : null,
        node:
          this.storage.node &&
          this.storage.node.x != null &&
          this.storage.node.y != null &&
          this.storage.node.z != null &&
          this.storage.node.zoneGroup
            ? {
                x: this.storage.node.x,
                y: this.storage.node.y,
                z: this.storage.node.z,
                zoneGroup: this.storage.node.zoneGroup,
              }
            : null,
        tags: this.storage.tags,
      })
        .then((res) => {
          let { data } = res.data;

          if (!data) return null;

          this.storage = data;

          if (this.storage.node == null) {
            this.storage.node = {
              x: null,
              y: null,
              z: null,
              zoneGroup: null,
            };
          }

          this.isSuccess = true;
          this.hasError = false;
          setTimeout(() => {
            this.isSuccess = false;
          }, 5000);
        })
        .catch((err) => {
          this.errorMessage = err.response.data.errors;
          this.hasError = true;
        })
        .finally(() => {
          this.scrollToTop();
        });

      // this.startCountdown();
      // setTimeout(() => {
      //   this.$router.go(-1)
      // }, this.timeoutDuration)
    },
    scrollToTop() {
      window.scrollTo({
        top: 0,
        behavior: "smooth", // Smooth scrolling animation
      });
    },
    startCountdown() {
      // Set the initial countdown value based on the timeout duration
      this.countdown = this.timeoutDuration / 1000; // Convert milliseconds to seconds

      // Create an interval to update the countdown every second
      this.countdownInterval = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown--; // Decrement the countdown value
        } else {
          clearInterval(this.countdownInterval); // Clear the interval when countdown reaches 0
          this.hasError = false; // Hide the error message
        }
      }, 1000); // Update the countdown every second (1000 milliseconds)
    },
  },

  beforeMount() {
    this.getStorage();
  },
};
</script>
