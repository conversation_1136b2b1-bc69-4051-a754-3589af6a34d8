<template>
  <div class="cc-card" @click="pullDrawer">

    <p class="cc-title">{{ data.orderNo }}</p>

    <v-row class="cc-info">
      <v-col class="d-inline-flex align-center">
        <v-icon>mdi-file-cabinet </v-icon
        ><span class="ml-1">{{ data.storages.length }}</span>
      </v-col>
      <v-col class="d-inline-flex align-center">
        <v-icon>mdi-format-list-checks</v-icon
        ><span class="ml-1">{{ data.optimised }}</span>
      </v-col>
      <v-col class="d-inline-flex align-center">
        <v-icon>mdi-medal-outline</v-icon
        ><span class="ml-1">{{ data.rank ? data.rank : 0 }}</span>
      </v-col>
    </v-row>
  </div>
</template>

<script>
export default {
  name: "CustomCard",
  props: ["data"],
  methods: {
    pullDrawer() {
      this.$emit("pull-drawer", !this.data.draw);
      this.$emit("storage", this.data);
    },
  },
};
</script>

<style>
.cc-title {
  margin: 5px 0px 1rem;
  font-weight: bold;
  font-size: 1.2rem;
}
.cc-card {
  margin: 0.5rem 4px 0.5rem;
  padding: 0.5rem 0.75rem;
  text-align: left;
  border: 1px solid #dbdbdb;
  border-radius: 5px;
  background-color: white;
  min-width: 200px;
  font-size: 0.8em;
  color: #000000;
  box-shadow: -2px 3px 4px 0px rgba(100, 100, 100, 0.5);
}

.cc-card h3 {
  margin: 0px;
}

.cc-info {
  justify-content: space-evenly;
  padding: 0.1%;
}
.cc-btn {
  background-color: #5cdb95;
  border: none;
  color: white;
  border-radius: 5px;
}
</style>
