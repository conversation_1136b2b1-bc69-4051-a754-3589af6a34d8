<template>
  <v-container>
    <div class="dark-cloud" />
    <div class="stars" />
    <div class="stars2" />
    <div class="stars3" />

    <v-tabs dark :background-color="'transparent'" class="pa-4" v-model="tab">
      <v-tab href="#smorder">
        SM Orders
      </v-tab>
      <v-tab href="#advancedOrders">Advanced Orders</v-tab>
      <v-tab
        @click="Object.keys(dryRuns).length === 0 && getDryRuns()"
        href="#dryrun"
      >
        SM Dry Run
      </v-tab>
    </v-tabs>

    <v-tabs-items class="transparent" v-model="tab">
      <v-tab-item value="smorder">
        <v-card flat class="pa-4 white--text transparent">
          <div style="position:fixed; top:10%; right:3%; z-index:999;">
            <v-btn
              large
              @click="getData()"
              :loading="refreshLoading"
              dark
              style="z-index:999;"
            >
              Refresh
            </v-btn>
          </div>

          <v-data-table
            v-model="selected"
            :headers="headers"
            :items="Object.values(smOrders)"
            :items-per-page="1000"
            :item-class="rowColor"
            :footer-props="{ 'items-per-page-options': [15, 30, 50, 100, -1] }"
            multi-sort
            group-by="type"
            sort-by="id"
            show-group-by
            dark
            dense
            class="transparent"
          >
            <template v-slot:top>
              <v-row class="px-5 mt-2 purple--text text--accent-1">
                <v-col>
                  <span
                    >RETRIEVE : <b>{{ numberOfJobs.R }}</b></span
                  >
                </v-col>
                <v-col>
                  <span
                    >INTERNAL : <b>{{ numberOfJobs.I }}</b></span
                  >
                </v-col>
                <v-col>
                  <span
                    >PUTAWAY : <b>{{ numberOfJobs.P }}</b></span
                  >
                </v-col>
                <v-col><span /></v-col>
                <v-col><span /></v-col>
                <v-divider vertical />
                <v-col />
              </v-row>
              <v-row><v-divider /></v-row>
              <v-row class="px-5 mb-2 indigo--text text--accent-1">
                <v-col>
                  <span
                    >AVAILABLE : <b>{{ numberOfJobs.Available }}</b></span
                  >
                </v-col>
                <v-col>
                  <span
                    >DISPATCHED : <b>{{ numberOfJobs.Dispatched }}</b></span
                  >
                </v-col>
                <v-col>
                  <span
                    >PICKING : <b>{{ numberOfJobs.Picking }}</b></span
                  >
                </v-col>
                <v-col>
                  <span
                    >PICKED : <b>{{ numberOfJobs.Picked }}</b></span
                  >
                </v-col>
                <v-col>
                  <span
                    >ERROR : <b>{{ numberOfJobs.Error }}</b></span
                  >
                </v-col>
                <v-divider vertical />
                <v-col class="deep-purple--text text--accent-2 mt-n9">
                  <h1>
                    TOTAL : <b>{{ Object.keys(smOrders).length }}</b>
                  </h1>
                </v-col>
              </v-row>

              <v-row>
                <v-col>
                  <v-switch
                    v-model="autoRefresh"
                    :label="`Auto Refresh (${autoRefreshIntervalInSec}s)`"
                    @click="toggleAutoRefresh()"
                  />
                </v-col>
                <v-col>
                  <v-select
                    v-model.number="dispatchZoneGroupName"
                    dense
                    outlined
                    label="Zone Group"
                    class="mt-3"
                    :items="zoneGroups"
                    item-text="name"
                    item-value="id"
                  />
                </v-col>
                <v-col>
                  <v-btn
                    style="margin-right: 5px"
                    medium
                    @click="dispatch()"
                    light
                    :loading="dispatchLoading"
                    class="mt-3"
                  >
                    Trigger Dispatcher
                    <v-icon>mdi-send-circle-outline</v-icon>
                  </v-btn>
                </v-col>
              </v-row>

              <v-expansion-panels class="transparent">
                <v-expansion-panel class="transparent">
                  <v-expansion-panel-header class="transparent">
                    Advanced Filter
                  </v-expansion-panel-header>
                  <v-expansion-panel-content class="transparent">
                    <v-row class="transparent">
                      <v-col>
                        <v-autocomplete
                          v-model="smOrderStatusFilter"
                          :items="smOrderStatusOptions"
                          label="Status"
                          multiple
                          small-chips
                          deletable-chips
                          outlined
                        />
                      </v-col>
                      <v-col md="1">
                        <v-checkbox
                          v-model="smOrderIncludeDeletedFilter"
                          label="Include Deleted"
                          class="mt-n1"
                        />
                      </v-col>
                      <v-col md="1">
                        <v-checkbox
                          v-model="smOrderIncludeCompletedFilter"
                          label="Include Completed"
                          class="mt-n1"
                        />
                      </v-col>
                    </v-row>
                    <v-row dense>
                      <v-col>
                        <v-combobox
                          v-model="smOrderStorageCodeFilter"
                          label="Storage Code"
                          multiple
                          small-chips
                          deletable-chips
                          clearable
                          outlined
                        />
                      </v-col>
                      <v-col>
                        <v-combobox
                          v-model="smOrderOrderNoFilter"
                          label="Order No"
                          multiple
                          small-chips
                          deletable-chips
                          clearable
                          outlined
                        />
                      </v-col>
                    </v-row>
                    <v-row dense>
                      <v-col>
                        <v-combobox
                          v-model="smOrderTypeFilter"
                          :items="smOrderTypeOptions"
                          label="Type"
                          multiple
                          small-chips
                          deletable-chips
                          clearable
                          outlined
                        />
                      </v-col>
                      <v-col md="1">
                        <v-text-field
                          v-model.number="smOrderPerPage"
                          type="number"
                          label="Per Page"
                          :min="smOrderPerPageMin"
                          :max="smOrderPerPageMax"
                          outlined
                          @input="onSmOrderPerPageInput"
                        ></v-text-field>
                      </v-col>
                      <v-col md="1">
                        <v-text-field
                          v-model.number="smOrderPageNo"
                          type="number"
                          label="Page No."
                          :min="smOrderPageNoMin"
                          outlined
                          @input="onSmOrderPageNoInput"
                        ></v-text-field>
                      </v-col>
                    </v-row>
                    <v-row dense>
                      <v-col>
                        <v-text-field
                          label="Created At - Start Time"
                          type="datetime-local"
                          v-model="smOrderCreatedAtStartTimeFilter"
                          step="2"
                          clearable
                          outlined
                        />
                      </v-col>
                      <v-col>
                        <v-text-field
                          label="Created At - End Time"
                          type="datetime-local"
                          v-model="smOrderCreatedAtEndTimeFilter"
                          step="2"
                          clearable
                          outlined
                        />
                      </v-col>
                    </v-row>
                    <v-expand-transition>
                      <v-row v-show="smOrderIncludeDeletedFilter" dense>
                        <v-col>
                          <v-text-field
                            class="theme--dark "
                            label="Deleted - Start Time"
                            type="datetime-local"
                            v-model="smOrderDeletedAtStartTimeFilter"
                            step="2"
                            clearable
                            outlined
                            icon="mdi-calendar-account-outline"
                          />
                        </v-col>
                        <v-col>
                          <v-text-field
                            class="theme--dark "
                            label="Deleted - End Time"
                            type="datetime-local"
                            v-model="smOrderDeletedAtEndTimeFilter"
                            step="2"
                            clearable
                            outlined
                          />
                        </v-col>
                      </v-row>
                    </v-expand-transition>
                  </v-expansion-panel-content>
                </v-expansion-panel>
              </v-expansion-panels>

              <v-row class="px-5 mt-2 purple--text text--accent-1">
                <v-col>
                  <v-dialog v-model="updateOrderDialog" max-width="600px">
                    <v-card>
                      <v-card-title
                        >Update SM Order {{ updateOrderId }}</v-card-title
                      >

                      <v-card-text>
                        <v-row>
                          <v-col cols="12" md="6">
                            <v-select
                              v-model="updateOrderStatus"
                              :items="smOrderStatusOptions"
                              label="Status"
                              disabled
                            ></v-select>
                          </v-col>
                          <v-col cols="12" md="6">
                            <v-select
                              v-model="updateOrderPriorityTier"
                              :items="smOrderPriorityTierOptions"
                              label="Priority Tier"
                            ></v-select>
                          </v-col>
                        </v-row>

                        <v-row>
                          <v-col cols="12" md="3">
                            <v-text-field
                              v-model.number="updateOrderDropoffNode.x"
                              type="number"
                              label="Dropoff X"
                              disabled
                            ></v-text-field>
                          </v-col>
                          <v-col cols="12" md="3">
                            <v-text-field
                              v-model.number="updateOrderDropoffNode.y"
                              type="number"
                              label="Dropoff Y"
                              disabled
                            ></v-text-field>
                          </v-col>
                          <v-col cols="12" md="3">
                            <v-text-field
                              v-model.number="updateOrderDropoffNode.z"
                              type="number"
                              label="Dropoff Z"
                              disabled
                            ></v-text-field>
                          </v-col>
                          <v-col cols="12" md="3">
                            <v-select
                              v-model.number="updateOrderDropoffNode.zoneGroup"
                              :items="zoneGroups"
                              item-text="name"
                              item-value="name"
                              label="Dropoff Zone"
                              disabled
                            ></v-select>
                          </v-col>
                        </v-row>
                      </v-card-text>

                      <v-card-actions>
                        <v-btn color="red" text @click="forceDeleteOrder">
                          Force Delete
                        </v-btn>
                        <v-spacer />
                        <v-btn color="green" text @click="updateOrder">
                          Update
                        </v-btn>
                        <v-btn text @click="updateOrderDialog = false">
                          Close
                        </v-btn>
                      </v-card-actions>
                    </v-card>
                  </v-dialog>
                </v-col>
              </v-row>
            </template>

            <template v-slot:[`item.id`]="{ item, value }">
              <v-tooltip right close-delay="200" :ref="`smOrder${value}`">
                <template v-slot:activator="{ on, attrs }">
                  <td v-bind="attrs" v-on="on">
                    {{ value }}
                  </td>
                </template>
                <pre
                  style="pointer-events: initial;"
                  @mouseenter="$refs[`smOrder${value}`].runDelay('open')"
                  @mouseleave="$refs[`smOrder${value}`].runDelay('close')"
                  v-for="(val, key) in dynamicDestructure(
                    item,
                    smOrderTooltipField
                  )"
                  :key="key"
                  >{{ `${key.padEnd(smOrderTooltipMaxLength)} : ${val}` }}</pre
                >
              </v-tooltip>
            </template>
            <template v-slot:[`item.pickupNode`]="{ item, value }">
              <v-tooltip right>
                <template v-slot:activator="{ on, attrs }">
                  <td
                    v-bind="attrs"
                    v-on="on"
                    :class="
                      item.isMissingPickupNode
                        ? rowColor(undefined)
                        : rowColor(smOrders[item.id])
                    "
                  >
                    {{ nodeDescription(value) }}
                  </td>
                </template>
                <pre
                  v-for="(val, key) in dynamicDestructure(
                    value,
                    nodeTooltipField
                  )"
                  :key="key"
                  >{{ `${key.padEnd(nodeTooltipMaxLength)} : ${val}` }}</pre
                >
              </v-tooltip>
            </template>
            <template v-slot:[`item.dropoffNode`]="{ value }">
              <v-tooltip right>
                <template v-slot:activator="{ on, attrs }">
                  <td v-bind="attrs" v-on="on">
                    {{ nodeDescription(value) }}
                  </td>
                </template>
                <pre
                  v-for="(val, key) in dynamicDestructure(
                    value,
                    nodeTooltipField
                  )"
                  :key="key"
                  >{{ `${key.padEnd(nodeTooltipMaxLength)} : ${val}` }}</pre
                >
              </v-tooltip>
            </template>
            <template v-slot:[`item.storage`]="{ value }">
              <v-tooltip right>
                <template v-slot:activator="{ on, attrs }">
                  <td v-bind="attrs" v-on="on">
                    {{ value.code }}
                  </td>
                </template>
                <pre
                  v-for="(val, key) in dynamicDestructure(
                    value,
                    storageTooltipField
                  )"
                  :key="key"
                  >{{ `${key.padEnd(storageTooltipMaxLength)} : ${val}` }}</pre
                >
              </v-tooltip>
            </template>
            <template v-slot:[`item.nonExecutable`]="{ value }">
              <v-tooltip right>
                <template v-slot:activator="{ on, attrs }">
                  <v-icon v-bind="attrs" v-on="on">
                    {{ value ? "mdi-alert-outline" : "" }}
                  </v-icon>
                </template>
                <pre>{{ value }}</pre>
              </v-tooltip>
            </template>
            <template v-slot:[`item.controls`]="props">
              <v-icon dark @click="openUpdateOrderDialog(props.item)"
                >mdi-update
              </v-icon>
              <v-icon dark @click="infoOrder(props.item.id)"
                >mdi-information-outline
              </v-icon>
              <v-icon dark @click="cancelOrder(props.item)">
                mdi-close-circle-outline
              </v-icon>
            </template>
          </v-data-table>
        </v-card>
      </v-tab-item>

      <v-tab-item value="dryrun">
        <v-card flat class="pa-4 white--text transparent">
          <v-data-table
            :headers="dryRunHeaders"
            :items="Object.values(dryRuns)"
            :items-per-page="-1"
            :item-class="dryRunRowColor"
            dark
            dense
            class="transparent"
          >
            <template v-slot:top>
              <v-row class="px-5 mt-2 purple--text text--accent-1">
                <v-col>
                  <v-btn @click="openDryRunCreationDialog"
                    >Create Dry Run</v-btn
                  >
                  <v-dialog v-model="createDryRunDialog" max-width="600px">
                    <v-card>
                      <v-card-title>Create Dry Run</v-card-title>

                      <v-card-text>
                        <v-row>
                          <v-col cols="12" md="4">
                            <v-select
                              v-model="newDryRunSelectedMode"
                              :items="dryRunModeOptions"
                              label="Mode"
                            ></v-select>
                          </v-col>
                          <v-col cols="12" md="4">
                            <v-select
                              v-model="newDryRunSelectedStation"
                              :items="smStations"
                              item-text="id"
                              item-value="id"
                              label="Stations"
                              multiple
                            ></v-select>
                          </v-col>
                          <v-col cols="12" md="4">
                            <v-select
                              v-model="newDryRunSelectedZone"
                              :items="zoneGroups"
                              item-text="name"
                              item-value="name"
                              label="Zone Groups"
                              multiple
                            ></v-select>
                          </v-col>
                        </v-row>

                        <v-row>
                          <v-col cols="12" md="4">
                            <v-text-field
                              v-model.number="newDryRunQuantity"
                              type="number"
                              label="Quantity"
                              min="1"
                            ></v-text-field>
                          </v-col>
                          <v-col cols="12" md="4">
                            <v-text-field
                              v-model.number="newDryRunMinLayer"
                              type="number"
                              label="Min Layer"
                              min="1"
                            ></v-text-field>
                          </v-col>
                          <v-col cols="12" md="4">
                            <v-text-field
                              v-model.number="newDryRunMaxLayer"
                              type="number"
                              label="Max Layer"
                              max="10"
                            ></v-text-field>
                          </v-col>
                        </v-row>

                        <v-row>
                          <v-col cols="12" md="4">
                            <v-checkbox
                              v-model="newDryRunAllowCrossZg"
                              label="Allow Cross Zone Groups"
                            ></v-checkbox>
                          </v-col>
                          <v-col cols="12" md="4">
                            <v-checkbox
                              v-model="newDryRunEnableAutoStore"
                              label="Enable Auto Store"
                            ></v-checkbox>
                          </v-col>
                          <v-col cols="12" md="4">
                            <v-checkbox
                              v-model="newDryRunUseRandomDest"
                              label="Use Random Dest"
                            ></v-checkbox>
                          </v-col>
                        </v-row>
                      </v-card-text>

                      <v-card-actions>
                        <v-spacer />
                        <v-btn color="green" text @click="createNewDryRun">
                          Create
                        </v-btn>
                        <v-btn text @click="createDryRunDialog = false">
                          Close
                        </v-btn>
                      </v-card-actions>
                    </v-card>
                  </v-dialog>
                </v-col>
              </v-row>
            </template>

            <template v-slot:[`item.allowCrossZoneGroup`]="{ item }">
              <v-icon v-if="item.allowCrossZoneGroup">mdi-check</v-icon>
            </template>
            <template v-slot:[`item.enableAutoStore`]="{ item }">
              <v-icon v-if="item.enableAutoStore">mdi-check</v-icon>
            </template>
            <template v-slot:[`item.useRandomDest`]="{ item }">
              <v-icon v-if="item.useRandomDest">mdi-check</v-icon>
            </template>
            <template v-slot:[`item.controls`]="{ item }">
              <v-icon v-if="item.pausedAt" dark @click="resumeDryRun(item)">
                mdi-play-circle-outline
              </v-icon>
              <v-icon v-else dark @click="pauseDryRun(item)">
                mdi-pause-circle-outline
              </v-icon>
              <v-icon dark @click="stopDryRun(item)">
                mdi-close-circle-outline
              </v-icon>
            </template>
          </v-data-table>
        </v-card>
      </v-tab-item>

      <v-tab-item value="advancedOrders">
        <KanbanAdvancedOrder></KanbanAdvancedOrder>
      </v-tab-item>
    </v-tabs-items>
  </v-container>
</template>

<script>
import { mapActions, mapState } from "vuex";
import { SmDryRunAPI } from "../api/dry-runs";
import { SettingAPI } from "../api/settings";
import { SmOperationAPI } from "../api/sm-operation";
import { SmOrderAPI } from "../api/sm-orders";
import { convertStringToLocal } from "../helper/common";
import {
  Route,
  SmOrderPriorityTier,
  SmOrderStatus,
  SmOrderType,
} from "../helper/enums";
import KanbanAdvancedOrder from "./KanbanAdvancedOrder.vue";
export default {
  name: "Order",
  components: { KanbanAdvancedOrder },
  data() {
    return {
      tab: null,
      oldQuery: null,

      isDualStation: false,

      refreshLoading: false,
      autoRefresh: false,
      autoRefreshIntervalInSec: 10,
      autoRefreshTimer: null,

      dispatchLoading: false,
      dispatchZoneGroupName: null,

      selected: [],

      smOrderTooltipField: [
        "orderNo",
        "targetedStack",
        "targetedZoneGroup",
        "priorityTier",
        "requestedAt",
        "requestedBy",
        "dispatchedAt",
        "startPickingAt",
        "pickedAt",
        "completedAt",
        "createdAt",
        "updatedAt",
        "deletedAt",
        "lastErroredAt",
        "errorReason",
        "errorParent",
      ],
      nodeTooltipField: [
        "id",
        "type",
        "zoneGroup",
        "storageCap",
        "hardwareIndex",
      ],
      storageTooltipField: [
        "code",
        "tags",
        "status",
        "coordinate",
        "zoneGroup",
        "station",
        "lastMovement",
      ],

      smOrderStatusOptions: Object.values(SmOrderStatus),
      smOrderStatusFilter: [
        SmOrderStatus.Available,
        SmOrderStatus.Dispatched,
        SmOrderStatus.Picking,
        SmOrderStatus.Picked,
        SmOrderStatus.Error,
      ],
      smOrderStorageCodeFilter: [],
      smOrderOrderNoFilter: [],
      smOrderTypeOptions: Object.values(SmOrderType),
      smOrderTypeFilter: [
        SmOrderType.Internal,
        SmOrderType.Putaway,
        SmOrderType.Retrieving,
      ],
      smOrderPriorityTierOptions: Object.values(SmOrderPriorityTier),
      smOrderCreatedAtStartTimeFilter: null,
      smOrderCreatedAtEndTimeFilter: null,
      smOrderIncludeDeletedFilter: false,
      smOrderIncludeCompletedFilter: false,
      smOrderDeletedAtStartTimeFilter: null,
      smOrderDeletedAtEndTimeFilter: null,
      smOrderPerPageMin: -1,
      smOrderPerPageMax: 1000,
      smOrderPerPage: 1000,
      smOrderPageNoMin: 1,
      smOrderPageNo: 1,

      smOrders: {},

      headers: [
        { text: "NO", value: "index", groupable: false },
        { text: "ID", value: "id", groupable: false },
        { text: "TYPE", value: "type" },
        { text: "STATUS", value: "status", groupable: false },
        {
          text: "STORAGE",
          value: "storage",
          groupable: false,
          sort: (a, b) => (a && b ? a.code - b.code : 0),
        },
        { text: "CREATION REASON", value: "creationReason", groupable: false },
        {
          text: "PICKUP",
          value: "pickupNode",
          groupable: false,
          sort: (a, b) =>
            a && b
              ? a.x - b.x || a.y - b.y || a.z - b.z
              : !a && !b
              ? 0
              : !a
              ? 1
              : -1,
        },
        { text: "LAYER", value: "layer", groupable: false },
        {
          text: "DROPOFF",
          value: "dropoffNode",
          groupable: false,
          sort: (a, b) =>
            a && b
              ? a.x - b.x || a.y - b.y || a.z - b.z
              : !a && !b
              ? 0
              : !a
              ? 1
              : -1,
        },
        {
          text: "PRIORITY",
          value: "priorityTier",
          groupable: false,
        },
        {
          text: "NON EXEC",
          value: "nonExecutable",
          groupable: false,
          sortable: false,
        },
        {
          text: "ACTION",
          value: "controls",
          groupable: false,
          sortable: false,
        },
      ],

      updateOrderDialog: false,
      updateOrderId: null,
      updateOrderStatus: null,
      updateOrderPriorityTier: null,
      updateOrderDropoffNode: {
        x: null,
        y: null,
        z: null,
        zoneGroup: null,
      },

      dryRuns: {},
      dryRunModeOptions: ["CONTINUOUS", "SINGLE_ROUND"],
      newDryRunSelectedMode: null,
      newDryRunSelectedStation: [],
      newDryRunSelectedZone: [],
      newDryRunQuantity: 1,
      newDryRunMinLayer: 1,
      newDryRunMaxLayer: 1,
      newDryRunAllowCrossZg: false,
      newDryRunEnableAutoStore: false,
      newDryRunUseRandomDest: false,
      createDryRunDialog: false,
      dryRunHeaders: [
        { text: "NO", value: "index", groupable: false, sortable: false },
        { text: "ID", value: "id", groupable: false, sortable: false },
        { text: "TYPE", value: "type", groupable: false, sortable: false },
        { text: "MODE", value: "mode", groupable: false, sortable: false },
        {
          text: "STATIONS",
          value: "stations",
          groupable: false,
          sortable: false,
        },
        {
          text: "ZONE",
          value: "pickFromZoneGroups",
          groupable: false,
          sortable: false,
        },
        { text: "QTY", value: "qty", groupable: false, sortable: false },
        {
          text: "MIN LAYER",
          value: "minLayer",
          groupable: false,
          sortable: false,
        },
        {
          text: "MAX LAYER",
          value: "maxLayer",
          groupable: false,
          sortable: false,
        },
        {
          text: "CROSS ZONE",
          value: "allowCrossZoneGroup",
          groupable: false,
          sortable: false,
        },
        {
          text: "AUTO STORE BIN",
          value: "enableAutoStore",
          groupable: false,
          sortable: false,
        },
        {
          text: "RANDOM DEST",
          value: "useRandomDest",
          groupable: false,
          sortable: false,
        },
        {
          text: "ACTION",
          value: "controls",
          groupable: false,
          sortable: false,
        },
      ],
    };
  },

  methods: {
    onSmOrderPerPageInput(input) {
      const value = parseInt(input);
      if (Number.isNaN(value)) {
        this.smOrderPerPage = this.smOrderPerPageMax;
      } else if (value < this.smOrderPerPageMin) {
        this.smOrderPerPage = this.smOrderPerPageMin;
      } else if (value > this.smOrderPerPageMax) {
        this.smOrderPerPage = this.smOrderPerPageMax;
      } else {
        this.smOrderPerPage = value;
      }
    },

    onSmOrderPageNoInput(input) {
      const value = parseInt(input);
      if (Number.isNaN(value) || value < this.smOrderPageNoMin) {
        this.smOrderPageNo = this.smOrderPageNoMin;
      } else {
        this.smOrderPageNo = value;
      }
    },

    toggleAutoRefresh() {
      if (this.autoRefresh) {
        if (this.autoRefreshTimer) clearInterval(this.autoRefreshTimer);

        this.autoRefreshTimer = setInterval(() => {
          this.getData(true);
        }, this.autoRefreshIntervalInSec * 1000);
      } else {
        clearInterval(this.autoRefreshTimer);
      }
    },

    dynamicDestructure(objData, propertiesArr) {
      if (!objData) return {};
      if (propertiesArr.length > 0) {
        return propertiesArr.reduce(
          (acc, cur) => ({ ...acc, [cur]: objData[cur] }),
          {}
        );
      }
      return objData;
    },

    async dispatch() {
      const timeout = new Promise((resolve) => setTimeout(resolve, 1000));
      try {
        this.dispatchLoading = true;
        if (this.dispatchZoneGroupName) {
          await SmOperationAPI.triggerDispatcher(this.dispatchZoneGroupName);
        }
      } catch (e) {
        this.$awn.alert("Something went wrong, please try again later");
      } finally {
        await timeout;
        this.dispatchLoading = false;
      }
    },

    async getData(reRunOldQuery = false) {
      try {
        this.refreshLoading = true;

        const timeout = new Promise((resolve) => setTimeout(resolve, 1000));

        let params = null;
        if (reRunOldQuery) {
          params = this.oldQuery;
        } else {
          params = {
            perPage: this.smOrderPerPage ?? 1000,
            pageNo: this.smOrderPageNo ?? 1,
            status: this.smOrderStatusFilter,
            storages: this.smOrderStorageCodeFilter,
            includeStorageLayer: true,
            type: this.smOrderTypeFilter,
            orderNo: this.smOrderOrderNoFilter,
            excludeCompletedAt: this.smOrderIncludeCompletedFilter
              ? false
              : true,
            createdAtStart: this.smOrderCreatedAtStartTimeFilter ?? undefined,
            createdAtEnd: this.smOrderCreatedAtEndTimeFilter ?? undefined,
            deletedAtStart: this.smOrderIncludeDeletedFilter
              ? this.smOrderDeletedAtStartTimeFilter ?? undefined
              : undefined,
            deletedAtEnd: this.smOrderIncludeDeletedFilter
              ? this.smOrderDeletedAtEndTimeFilter ?? undefined
              : undefined,
          };
        }

        const nonExecutableOrdersMap = new Map();
        await SmOperationAPI.getNonExec()
          .then((res) => {
            for (const nonExecOrdersArray of Object.values(res.data.data)) {
              for (const obj of nonExecOrdersArray) {
                nonExecutableOrdersMap.set(parseInt(obj.orderId), obj.reason);
              }
            }
          })
          .catch((err) => {
            this.$awn.alert(`Get non executable orders failed. ${err.message}`);
          });

        this.smOrders = await SmOrderAPI.getOrders(params).then((orderRes) => {
          const smOrdersObj = {};
          orderRes.data.data.forEach((smOrder, index) => {
            //adding index
            smOrder.index = index + 1;

            //convert utc to local datetime
            smOrder.requestedAt = smOrder.requestedAt
              ? convertStringToLocal(smOrder.requestedAt, true, true)
              : null;
            smOrder.dispatchedAt = smOrder.dispatchedAt
              ? convertStringToLocal(smOrder.dispatchedAt, true, true)
              : null;
            smOrder.startPickingAt = smOrder.startPickingAt
              ? convertStringToLocal(smOrder.startPickingAt, true, true)
              : null;
            smOrder.pickedAt = smOrder.pickedAt
              ? convertStringToLocal(smOrder.pickedAt, true, true)
              : null;
            smOrder.completedAt = smOrder.completedAt
              ? convertStringToLocal(smOrder.completedAt, true, true)
              : null;
            smOrder.lastErroredAt = smOrder.lastErroredAt
              ? convertStringToLocal(smOrder.lastErroredAt, true, true)
              : null;
            smOrder.createdAt = smOrder.createdAt
              ? convertStringToLocal(smOrder.createdAt, true, true)
              : null;
            smOrder.updatedAt = smOrder.updatedAt
              ? convertStringToLocal(smOrder.updatedAt, true, true)
              : null;
            smOrder.deletedAt = smOrder.deletedAt
              ? convertStringToLocal(smOrder.deletedAt, true, true)
              : null;
            smOrder.lastMovedAt = smOrder.lastMovedAt
              ? convertStringToLocal(smOrder.lastMovedAt, true, true)
              : null;

            if (smOrder.storage) {
              const node = smOrder.storage.node;
              if (node) {
                smOrder.storage["coordinate"] = `${node.x},${node.y},${node.z}`;

                smOrder.storage["zoneGroup"] = node.zoneGroup;

                if (!smOrder.pickupNode) {
                  smOrder.pickupNode = node;
                  smOrder.isMissingPickupNode = true;
                }
              }
            }

            if (nonExecutableOrdersMap.has(smOrder.id)) {
              smOrder.nonExecutable = nonExecutableOrdersMap.get(smOrder.id);
            }

            smOrdersObj[smOrder.id] = smOrder;
          });
          this.oldQuery = params;

          return smOrdersObj;
        });

        await timeout;
      } catch (e) {
        this.$awn.alert(`Order Page Get Data Exception - ${e.message}`);
      }
      this.refreshLoading = false;
    },

    async getDryRuns() {
      try {
        this.dryRuns = await SmDryRunAPI.getDryRun({
          perPage: -1,
          orderBy: "createdAt",
        }).then((dryRunRes) => {
          const dryRunsObj = {};
          dryRunRes.data.data.forEach((dryRun, index) => {
            //adding index
            dryRun.index = index + 1;

            dryRunsObj[dryRun.id] = dryRun;
          });

          return dryRunsObj;
        });
      } catch (e) {
        this.$awn.alert(`Dry Run Get Data Exception - ${e.message}`);
      }
    },

    rowColor(item) {
      if (!item) return "grey--text text--darken-1";
      switch (item.status) {
        case SmOrderStatus.Available:
          return "yellow--text text--accent-4";
        case SmOrderStatus.Dispatched:
          return "cyan--text text--accent-3";
        case SmOrderStatus.Picking:
          return "light-blue--text text--lighten-1";
        case SmOrderStatus.Picked:
          return "light-green--text text--accent-3";
        case SmOrderStatus.Completed:
          return "grey--text text--darken-1";
        case SmOrderStatus.Skipped:
          return "grey--text text--darken-1";
        case SmOrderStatus.Error:
          return "red--text text--accent-3";
        default:
          return "";
      }
    },

    dryRunRowColor(item) {
      if (!item || item.pausedAt) return "grey--text text--darken-1";
      return "light-green--text text--accent-3";
    },

    async checkDualStation() {
      const getDualStationMap = await SettingAPI.getByKey("DualStationMap");
      this.isDualStation =
        getDualStationMap != null
          ? Object.keys(getDualStationMap)?.length > 0
            ? true
            : false
          : false;
    },

    nodeDescription(node) {
      if (node) {
        const threeDCoor = [node.x, node.y, node.z].join(",");
        const zoneGroupConversion = node.zoneGroup === "LEFT" ? "A" : "B";

        if (node.station) {
          return `${threeDCoor} ( ${
            this.isDualStation ? `${zoneGroupConversion} /` : ""
          } ${node.zoneGroup}  - ST ${node.station} )`;
        } else {
          return `${threeDCoor} ( ${
            this.isDualStation ? `${zoneGroupConversion} /` : ""
          } ${node.zoneGroup} )`;
        }
      }
      return null;
    },

    async infoOrder(smOrderId) {
      this.$router.push(Route.SmOrderLogs + `/${smOrderId}`);
    },

    async cancelOrder(item) {
      const { id, storage } = item;

      const confirmation = confirm(
        `Are you sure to cancel Order ${id} for Storage ${storage.code}?`
      );

      if (!confirmation) return;

      await SmOrderAPI.deleteOrder(id)
        .then(async (res) => {
          let failed = res.data.data;

          if (!failed || failed.length === 0) {
            this.$awn.info(`Order ${id} removed successfully.`);
          } else {
            this.$awn.alert(
              `Order ${id} partially succeed, error order: `,
              failed
            );
          }

          await this.getData();
        })
        .catch((err) => {
          this.$awn.alert(`Order ${id} remove failed. ${err.message}`);
        });
    },

    openUpdateOrderDialog(smOrder) {
      this.updateOrderId = smOrder.id;
      this.updateOrderStatus = smOrder.status;
      this.updateOrderPriorityTier = smOrder.priorityTier;
      this.updateOrderDropoffNode = smOrder.dropoffNode
        ? {
            x: smOrder.dropoffNode.x,
            y: smOrder.dropoffNode.y,
            z: smOrder.dropoffNode.z,
            zoneGroup: smOrder.dropoffNode.zoneGroup,
          }
        : {
            x: null,
            y: null,
            z: null,
            zoneGroup: null,
          };
      this.updateOrderDialog = true;
    },

    async updateOrder() {
      const existingSmOrder = this.smOrders[this.updateOrderId];
      const data = {
        status:
          this.updateOrderStatus !== existingSmOrder?.status
            ? this.updateOrderStatus
            : undefined,
        priorityTier:
          this.updateOrderPriorityTier !== existingSmOrder?.priorityTier
            ? this.updateOrderPriorityTier
            : undefined,
        dropoffNode:
          Number.isInteger(this.updateOrderDropoffNode.x) &&
          Number.isInteger(this.updateOrderDropoffNode.y) &&
          Number.isInteger(this.updateOrderDropoffNode.z) &&
          this.updateOrderDropoffNode.zoneGroup &&
          (this.updateOrderDropoffNode.x !== existingSmOrder?.dropoffNode?.x ||
            this.updateOrderDropoffNode.y !== existingSmOrder?.dropoffNode?.y ||
            this.updateOrderDropoffNode.z !== existingSmOrder?.dropoffNode?.z ||
            this.updateOrderDropoffNode.zoneGroup !==
              existingSmOrder.dropoffNode.zoneGroup)
            ? this.updateOrderDropoffNode
            : undefined,
      };

      const confirmation = confirm(
        `Are you sure to update Order ${this.updateOrderId}? If update dropoff,
        please make sure it is valid and sync with TC, all responsible on user.`
      );

      if (!confirmation) return;

      if (
        data.status === undefined &&
        data.priorityTier === undefined &&
        data.dropoffNode === undefined
      ) {
        this.updateOrderDialog = false;
        return;
      }

      await SmOrderAPI.updateOrder(this.updateOrderId, data)
        .then(async () => {
          this.$awn.info(`Order ${this.updateOrderId} updated successfully.`);
          this.updateOrderDialog = false;
          await this.getData();
        })
        .catch((err) => {
          this.$awn.alert(
            `Order ${this.updateOrderId} update failed. ${err.message}`
          );
        });
    },

    async forceDeleteOrder() {
      const confirmation = confirm(
        `Are you sure to force delete Order ${this.updateOrderId}?`
      );

      if (!confirmation) return;

      await SmOrderAPI.deleteOrder(this.updateOrderId, { silent: true })
        .then(async () => {
          this.$awn.info(
            `Order ${this.updateOrderId} force deleted successfully.`
          );
          this.updateOrderDialog = false;
          await this.getData();
        })
        .catch((err) => {
          this.$awn.alert(
            `Order ${this.updateOrderId} force delete failed. ${err.message}`
          );
        });
    },

    openDryRunCreationDialog() {
      this.newDryRunSelectedMode = this.dryRunModeOptions[0];
      this.newDryRunSelectedStation = [];
      this.newDryRunSelectedZone = [this.zoneGroups[0].name];
      this.newDryRunQuantity = 1;
      this.newDryRunMinLayer = 1;
      this.newDryRunMaxLayer = 1;
      this.newDryRunAllowCrossZg = false;
      this.newDryRunEnableAutoStore = false;
      this.newDryRunUseRandomDest = false;
      this.createDryRunDialog = true;
    },

    async createNewDryRun() {
      const data = {
        mode: this.newDryRunSelectedMode,
        stations: this.newDryRunSelectedStation,
        pickFromZoneGroups: this.newDryRunSelectedZone,
        qty: this.newDryRunQuantity,
        minLayer: this.newDryRunMinLayer,
        maxLayer: this.newDryRunMaxLayer,
        allowCrossZoneGroup: this.newDryRunAllowCrossZg,
        enableAutoStore: this.newDryRunEnableAutoStore,
        useRandomDest: this.newDryRunUseRandomDest,
      };
      await SmDryRunAPI.createDryRun(data)
        .then(async (res) => {
          const dryRun = res.data.data;
          dryRun.index = Object.keys(this.dryRuns).length + 1;
          this.$set(this.dryRuns, dryRun.id, dryRun);
          this.$awn.info(`Dry Run ${dryRun.id} created.`);
        })
        .catch((err) => {
          this.$awn.alert(`Dry Run creation failed. ${err.message}`);
        });
    },

    async pauseDryRun(item) {
      const { id } = item;

      const data = { id, isPaused: true };
      await SmDryRunAPI.updateDryRun(id, data)
        .then(async () => {
          this.$awn.info(`Dry Run ${id} paused.`);
          item.pausedAt = convertStringToLocal(new Date(), true, true);
        })
        .catch((err) => {
          this.$awn.alert(`Dry Run ${id} pause failed. ${err.message}`);
        });
    },

    async resumeDryRun(item) {
      const { id } = item;

      const data = { id, isPaused: false };
      await SmDryRunAPI.updateDryRun(id, data)
        .then(async () => {
          this.$awn.info(`Dry Run ${id} resumed.`);
          item.pausedAt = null;
        })
        .catch((err) => {
          this.$awn.alert(`Dry Run ${id} resume failed. ${err.message}`);
        });
    },

    async stopDryRun(item) {
      const { id } = item;

      const confirmation = confirm(
        `Are you sure to stop Dry Run ${id}? You cannot revert this action!`
      );

      if (!confirmation) return;

      const data = { id, isStopped: true };
      await SmDryRunAPI.updateDryRun(id, data)
        .then(async () => {
          this.$awn.info(`Dry Run ${id} stopped.`);
          this.$delete(this.dryRuns, id);
        })
        .catch((err) => {
          this.$awn.alert(`Dry Run ${id} stop failed. ${err.message}`);
        });
    },

    ...mapActions(["getZoneGroups", "getSmStations"]),
  },
  // beforeMounted(){

  // },
  mounted() {
    this.checkDualStation();
    this.getData(false);
    this.getZoneGroups().then((res) => {
      this.dispatchZoneGroupName = res[0]?.name;
    });
    this.getSmStations();
  },

  beforeDestroy() {
    clearInterval(this.autoRefreshTimer);
  },

  computed: {
    numberOfJobs() {
      let data = {
        R: 0,
        I: 0,
        P: 0,
        S: 0,
        E: 0,

        Available: 0,
        Dispatched: 0,
        Picking: 0,
        Picked: 0,
        Error: 0,
      };
      Object.values(this.smOrders).forEach((item) => {
        switch (item.type) {
          case SmOrderType.Retrieving:
            data.R += 1;
            break;
          case SmOrderType.Internal:
            data.I += 1;
            break;
          case SmOrderType.Putaway:
            data.P += 1;
            break;
          default:
            break;
        }
        switch (item.status) {
          case SmOrderStatus.Available:
            data.Available += 1;
            break;
          case SmOrderStatus.Dispatched:
            data.Dispatched += 1;
            break;
          case SmOrderStatus.Picking:
            data.Picking += 1;
            break;
          case SmOrderStatus.Picked:
            data.Picked += 1;
            break;
          case SmOrderStatus.Error:
            data.Error += 1;
            break;
          default:
            break;
        }
      });
      return data;
    },

    smOrderTooltipMaxLength() {
      return Math.max(...this.smOrderTooltipField.map((x) => x.length));
    },

    nodeTooltipMaxLength() {
      return Math.max(...this.nodeTooltipField.map((x) => x.length));
    },

    storageTooltipMaxLength() {
      return Math.max(...this.storageTooltipField.map((x) => x.length));
    },

    ...mapState(["zoneGroups", "smStations"]),
  },

  watch: {
    // smOrderIncludeCompletedFilter() {
    //   this.getData(false);
    // },
    // smOrderStatusFilter() {
    //   this.getData(false);
    // },
    // smOrderStorageCodeFilter() {
    //   this.getData(false);
    // },
    // smOrderOrderNoFilter() {
    //   this.getData(false);
    // },
    // smOrderTypeFilter() {
    //   this.getData(false);
    // },
    // smOrderCreatedAtStartTimeFilter() {
    //   this.getData(false);
    // },
    // smOrderCreatedAtEndTimeFilter() {
    //   this.getData(false);
    // },
    // smOrderDeletedAtStartTimeFilter() {
    //   this.getData(false);
    // },
    // smOrderDeletedAtEndTimeFilter() {
    //   this.getData(false);
    // },
  },
};
</script>

<style>
/* @import '~@fortawesome/fontawesome-free/css/fontawesome.min.css';
    @import '~@fortawesome/fontawesome-free/css/solid.min.css'; */
@import "../assets/Stars.css";
@import "../assets/Background.css";
.content {
  margin: 50px;
  margin-top: 25px;
}
</style>
