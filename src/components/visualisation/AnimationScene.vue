<template>
  <div>
    <v-card v-if="isTouchDevice" dark>
      <v-row align="center">
        <v-col cols="2"></v-col>
        <v-col cols="8">
          <DirectionButtons @click="panDirection" />
        </v-col>
        <v-col cols="2"></v-col>
      </v-row>
    </v-card>
    
    <v-card>
      <v-card-text>
        <div style="overflow: auto" ref="scene"></div>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
import * as THREE from "three";
import gsap from "gsap";
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls";

import { initializeGrid } from "../../api/grid.js";
import { SkycarStatus, AdgStatus } from "../../helper/enums.js";
import { getGridStatus } from "../../helper/common.js";
import DirectionButtons from "./DirectionButtons";

export default {
  name: "ThreeScene",
  props: {
    sceneSize: Object,
    currentZone: String,
    nodes: Array,
    adgMessage: Array,
    smoothAnimate: Boolean,
    skycarProfile: Object,
    coordType: Boolean,
    pathType: String,
  },
  components: {
    DirectionButtons,
  },
  data() {
    return {
      gridObject: {
        gridName: "C",
        gridMinX: 0,
        gridMaxX: 0,
        gridMinY: 0,
        gridMaxY: 0,
        gridMinZ: 0,
        gridMaxZ: 0,
      },
      gridObstacle: {},
      gridSkycar: {},
      gridObtained: false,
      skycarPosition: Array(),
      winchPosition: Array(),
      threeJsSkycar: Array(),
      threeJsSkycarText: Array(),
      currentAnimation: Object(),
      previousUpdate: Object(),
      skycarValues: Array(),

      sceneScreen: null,
      camera: null,
      controls: null,
      renderer: null,
      looping: true,
      animationLoop: null,

      isTouchDevice: null,

      excludeDrawObstacleType : ["PARKING"]
    };
  },
  mounted(){
    this.isTouchDevice = "ontouchstart" in window;
  },
  methods: {
    getGridStatus,
    async sortSkycar(){
      this.skycarValues = Object.values(this.gridSkycar)
        .filter(item => item && item.sid)
        .map(item => item.sid)
        .filter((value, index, self) => self.indexOf(value) === index)
        .sort((a, b) => a - b);
    },
    async initialization() {
      await this.getGridData();
      await this.sortSkycar()

      // Create Three.js scene
      if (this.sceneScreen === null){
        await this.sceneInitialization();
      }
      
      // Reset the controls to initial position
      const pan = new THREE.Vector3(0, 0, 0);
      this.controls.target.copy(pan);

      const halfXAxis = -((this.gridObject.gridMaxX - this.gridObject.gridMinX) / 2 + this.gridObject.gridMinX);
      this.camera.position.set(halfXAxis, 9, this.gridObject.gridMinY - 6);
      const panVector = new THREE.Vector3(halfXAxis, 0, 0);
      this.controls.target.add(panVector);

      await this.addMaterials()
      this.updateScene();
    },
    async disposeScene(){
      // Remove everything in the container
      const container = this.$refs.scene;
      while (container.firstChild) {
        container.removeChild(container.firstChild);
      }

      this.renderer.dispose()
      this.looping = false
    },
    async getGridData() {
      const gridData = await initializeGrid(this.currentZone);
      if (gridData) {
        this.gridObject = gridData.gridObject;
        this.gridObstacle = gridData.gridObstacle;
        this.gridSkycar = gridData.gridSkycar;
        this.gridObtained = true
      }
    },
    updateScene() {
      // Update camera aspect ratio
      this.camera.aspect = this.sceneSize.x / this.sceneSize.y;
      this.camera.updateProjectionMatrix();

      // Update renderer size
      this.renderer.setSize(this.sceneSize.x, this.sceneSize.y + 15);
    },
    async sceneInitialization() {
      // Create a scene
      this.sceneScreen = new THREE.Scene();
      this.sceneScreen.background = new THREE.Color("skyblue");

      // Create a camera
      this.camera = new THREE.PerspectiveCamera(
        60,
        this.sceneSize.x / (this.sceneSize.y + 15),
        0.1,
        1000
      );
      
      // Create a renderer
      this.renderer = new THREE.WebGLRenderer({
        antialias: false, 
        alpha: false, 
        powerPreference: "low-power",
        precision: "mediump",
        stencil: false, 
      });

      // Append renderer to the DOM
      this.$refs.scene.appendChild(this.renderer.domElement);

      // Add controls to pan around with mouse
      this.controls = new OrbitControls(this.camera, this.renderer.domElement);

      // Animation loop
      const loop = () => {
        if (this.looping){
          if (this.animationLoop) {
            cancelAnimationFrame(this.animationLoop);
          }
          this.animationLoop = requestAnimationFrame(() => loop());
  
          // Mouse control update
          this.controls.update();
  
          if (this.smoothAnimate){
            for (let sid = 0; sid < this.skycarPosition.length; sid++) {
              if (this.skycarPosition[sid]) {                
                if (this.threeJsSkycar[sid]["dualWinch"]){
                  this.removeWinch(sid, true, true)
                  this.removeWinch(sid, true, false)
                } else {
                  this.removeWinch(sid, false)
                }

                this.threeJsSkycar[sid]["skycar"].position.set(
                  this.skycarPosition[sid].x, 
                  this.skycarPosition[sid].y, 
                  this.skycarPosition[sid].z
                );
  
                this.threeJsSkycarText[sid].position.set(
                  this.skycarPosition[sid].x - 0.1, 
                  this.skycarPosition[sid].y + 0.6, 
                  this.skycarPosition[sid].z
                );
              }

              if (this.winchPosition[sid]){
                for (let direction in this.winchPosition[sid]) {
                  if (this.winchPosition[sid][direction]){
                    if (this.threeJsSkycar[sid]["dualWinch"]){
                      if (direction.includes("A")){
                        this.createDualWinch(
                          sid, 
                          parseFloat(-this.winchPosition[sid][direction].x), 
                          parseFloat(this.winchPosition[sid][direction].z),
                          parseFloat(-this.winchPosition[sid][direction].y),
                          false
                        )
                      } 
                      
                      if (direction.includes("B")) {
                        this.createDualWinch(
                          sid, 
                          parseFloat(-this.winchPosition[sid][direction].x), 
                          parseFloat(this.winchPosition[sid][direction].z),
                          parseFloat(-this.winchPosition[sid][direction].y),
                          true
                        )
                      }
                    } else {
                      this.createSingleWinch(
                        sid,
                        parseFloat(-this.winchPosition[sid][direction].x), 
                        parseFloat(this.winchPosition[sid][direction].z),
                        parseFloat(-this.winchPosition[sid][direction].y)
                      )
                    }
                  }
                }
              }
            }
          }
  
          // Render the scene
          this.renderer.render(this.sceneScreen, this.camera);
        }
      }

      // Start the animation loop
      loop();
    },
    async addMaterials(){
      // Add a cube to the scene
      this.createCube();

      // Add the skycars
      this.generateMultipleSkycars();

      // Add the paths
      if (this.pathType === "NONE"){
        await this.updatePath();
      }

      // Add the axis
      if (this.gridObtained && this.coordType){
        this.addAxisLabels()
      }
    },
    async removeAxisLabels(){
      this.sceneScreen.traverse(function (object) {
        if (object instanceof THREE.Sprite) {
          object.geometry.dispose();
          object.material.dispose();
        }
      });

      const spritesToRemove = this.sceneScreen.children.filter(
        (child) => child instanceof THREE.Sprite && child.name === ""
      );

      spritesToRemove.forEach((sprite) => {
        sprite.geometry.dispose();
        sprite.material.dispose();
        this.sceneScreen.remove(sprite);
      });
    },
    async resetScene(){
      // Dispose of all objects in the scene
      this.sceneScreen.traverse(function (object) {
        if (object instanceof THREE.Mesh) {
          object.geometry.dispose();
          object.material.dispose();
        }
      });

      // Remove all objects from the scene
      while (this.sceneScreen.children.length > 0) {
        this.sceneScreen.remove(this.sceneScreen.children[0]);
      }

      this.gridObject = {
        gridName: "C",
        gridMinX: 0,
        gridMaxX: 0,
        gridMinY: 0,
        gridMaxY: 0,
        gridMinZ: 0,
        gridMaxZ: 0,
      }
      this.gridObstacle = {}
      this.gridSkycar = {}
      this.gridObtained = false
      
      this.skycarPosition = Array()
      this.winchPosition = Array()
      this.threeJsSkycar = Array()
      this.threeJsSkycarText = Array()
      this.currentAnimation = Object()
      this.previousUpdate = Object()
      this.skycarValues = Array()
    },
    async moveSkycar() {
      if (this.adgMessage.length > 0) {
        for (const message of this.adgMessage){
          if (!message.processing){
            continue
          }
          
          const msg = message.msg.split(",");
          const targetPosition = { x: -msg[7], y: -msg[9], z: msg[8] };
          const sid = msg[1]
          const direction = msg[5]

          if (this.currentAnimation[sid] && this.currentAnimation[sid][direction]){
            if (
              targetPosition.x === this.currentAnimation[sid][direction].x &&
              targetPosition.y === this.currentAnimation[sid][direction].y &&
              targetPosition.z === this.currentAnimation[sid][direction].z 
            ) {
              continue
            }
          }

          const axis = msg[6]
          let timeNeeded
          let totalSteps

          if (direction === "F" || direction === "B"){
            if (axis === "x"){
              totalSteps = Math.abs(parseInt(msg[7]) - Math.abs(this.skycarPosition[sid].x))
            } else {
              totalSteps = Math.abs(parseInt(msg[8]) - Math.abs(this.skycarPosition[sid].z))
            }
            timeNeeded = this.skycarProfile[axis][direction][parseInt(totalSteps)]

            this.gsapMoveAnimation(timeNeeded, sid, targetPosition, direction)
          } else {
            timeNeeded = this.skycarProfile[msg[11].toLowerCase()][direction][parseInt(msg[9])] / 2

            if (!this.winchPosition[sid]) {
              this.winchPosition[sid] = Object()            
            }

            this.winchPosition[sid][direction] = { x: parseInt(-msg[7]), y: 0, z: parseInt(msg[8]) }

            const upPosition = { x: parseInt(-msg[7]), y: 0, z: parseInt(msg[8]) }
            this.gsapWinchAnimation(timeNeeded, sid, targetPosition, direction, upPosition)
          }

          if (!this.currentAnimation[sid]) {
            this.currentAnimation[sid] = Object()
          }
          this.currentAnimation[sid][direction] = targetPosition
        }
      }
    },
    gsapMoveAnimation(timeNeeded, sid, targetPosition, direction){
      gsap.to(this.skycarPosition[sid], {
        duration: timeNeeded - 0.7,
        ...targetPosition,
        ease: "expo.inout",
        onComplete: () => {
          this.currentAnimation[sid][direction] = null
        }
      });
    },
    gsapWinchAnimation(timeNeeded, sid, targetPosition, direction, upPosition){
      const tween1 = gsap.to(this.winchPosition[sid][direction], {
        duration: timeNeeded - 0.1,
        ...targetPosition,
        ease: "power2.out",
        onComplete: () => {
          gsap.killTweensOf(tween1)
          
          setTimeout(() => {
            gsap.to(this.winchPosition[sid][direction], {
              duration: timeNeeded - 0.1,
              ...upPosition,
              ease: "power2.out",
              onComplete: () => {
                this.winchPosition[sid][direction] = null
                this.currentAnimation[sid][direction] = null
              }
            });
          }, 500)
        },
      });
    },
    async updateSkycar() {
      const completedNodes = this.getCompletedNodes();
      for (const nodes of completedNodes.values()) {
        if (nodes) {
          for (let key in nodes) {
            let node = nodes[key]
            if (!node){
              continue
            }

            if (key === "last"){
              this.updateSkycarPosition(node.skycar, -node.end[0], -0.1, node.end[1]);
            } 

            if (key === "previous"){
              const previousNode = nodes["previous"]
              if (previousNode.action === "F" || previousNode.action === "B"){
                continue
              }
            }

            if (this.previousUpdate[node.skycar] && this.previousUpdate[node.skycar][node.action]){
              if (
                node.end[0] === this.previousUpdate[node.skycar][node.action].x &&
                node.end[2] === this.previousUpdate[node.skycar][node.action].y &&
                node.end[1] === this.previousUpdate[node.skycar][node.action].z
              ) {
                if (node.action !== "F" && node.action !== "B"){
                  if (this.threeJsSkycar[node.skycar]["dualWinch"]){
                    if (node.action.includes("A")){
                      this.removeWinch(
                        node.skycar,
                        true,
                        false
                      )
                    } else if (node.action.includes("B")) {
                      this.removeWinch(
                        node.skycar,
                        true,
                        true
                      )
                    }
                  } else {
                    this.removeWinch(node.skycar, false)
                  }
                }
                this.previousUpdate[node.skycar][node.action] = null
              }
            }
          }
        }
      }

      const enqueuedNodes = this.getEnqueuedNodes()
      for (const nodes of enqueuedNodes.values()) {
        if (nodes) {
          for (let key in nodes) {
            let node = nodes[key]
            if (!node){
              continue
            }

            if (key === "second"){
              if (node.action === "F" || node.action === "B"){
                continue
              } else {
                const previousNode = nodes["first"]
                if (previousNode.action === "F" || previousNode.action === "B"){
                  continue
                }
              }
            }

            if (node.action !== "F" && node.action !== "B"){
              if (this.previousUpdate[node.skycar] && this.previousUpdate[node.skycar][node.action]){
                if (
                  node.end[0] === this.previousUpdate[node.skycar][node.action].x &&
                  node.end[2] === this.previousUpdate[node.skycar][node.action].y &&
                  node.end[1] === this.previousUpdate[node.skycar][node.action].z
                ) {
                  continue
                }
              }
              
              setTimeout(() => {
                if (this.threeJsSkycar[node.skycar]["dualWinch"]){
                  if (node.action.includes("A")){
                    this.createDualWinch(
                      node.skycar, 
                      parseFloat(node.end[0]), 
                      parseFloat(node.end[1]), 
                      parseFloat(node.end[2]),
                      false
                    )
                  } else if (node.action.includes("B")) {
                    this.createDualWinch(
                      node.skycar, 
                      parseFloat(node.end[0]), 
                      parseFloat(node.end[1]), 
                      parseFloat(node.end[2]),
                      true
                    )
                  }
                } else {
                  this.createSingleWinch(
                    node.skycar, 
                    parseFloat(node.end[0]), 
                    parseFloat(node.end[1]), 
                    parseFloat(node.end[2])
                  )
                }
                this.updateSkycarPosition(node.skycar, -node.end[0], -0.1, node.end[1]);
    
                if (!this.previousUpdate[node.skycar]) {
                  this.previousUpdate[node.skycar] = Object()
                }
                
                this.previousUpdate[node.skycar][node.action] = { x: node.end[0], y: node.end[2], z: node.end[1] }
              }, 1500)
            } else {
              this.updateSkycarPosition(node.skycar, -node.start[0], node.start[2] - 0.1, node.start[1]);
            }
          }
        }
      }
    },
    updateSkycarPosition(sid, x, y, z) {
      this.threeJsSkycar[sid]["skycar"].position.set(x, y, z);
      this.threeJsSkycarText[sid].position.set(x - 0.1, y + 0.6, z);

      this.skycarPosition[sid] = { x: x, y: y, z };
    },
    getCompletedNodes() {
      const completedNodes = new Map();

      this.nodes
        .filter(node => node.status === AdgStatus.COMPLETED)
        .forEach(node => {
          if (!completedNodes.has(node.skycar)) {
            completedNodes.set(node.skycar, { last: node, previous: null });
          } else {
            const skycarNodes = completedNodes.get(node.skycar);
            if (skycarNodes.previous === null || node.time > skycarNodes.previous.time) {
              skycarNodes.previous = node;
            }
            if (node.time > skycarNodes.last.time) {
              skycarNodes.previous = skycarNodes.last;
              skycarNodes.last = node;
            }
          }
        });

      return completedNodes;
    },
    getEnqueuedNodes() {
      const enqueuedNodes = new Map();

      this.nodes
        .filter(node => node.status === AdgStatus.ENQUEUED)
        .forEach(node => {
          if (!enqueuedNodes.has(node.skycar)) {
            enqueuedNodes.set(node.skycar, { first: node, second: null });
          } else {
            const skycarNodes = enqueuedNodes.get(node.skycar);
            if (skycarNodes.second === null || node.time < skycarNodes.second.time) {
              skycarNodes.second = node;
            }
            if (node.time < skycarNodes.first.time) {
              skycarNodes.second = skycarNodes.first;
              skycarNodes.first = node;
            }
          }
        });

      return enqueuedNodes;
    },
    async updatePath() {
      for (const node of this.nodes) {
        if (
          (this.pathType === "BOTH" && (node.status === AdgStatus.ENQUEUED || node.status === AdgStatus.STAGED)) ||
          (this.pathType === "STAGED ONLY" && node.status === AdgStatus.STAGED) ||
          (this.pathType === "ENQUEUED ONLY" && node.status === AdgStatus.ENQUEUED)
        ) {
          let startParams;

          if (node.action === "F" || node.action === "B") {
            startParams = [node.start[0], node.start[1], node.start[2], node.start[3]];
            await this.createThickLine(startParams, node.action, node.skycar, node.status);
          } else {
            for (let i = 0; i < Math.abs(parseInt(node.start[2])); i++) {
              startParams = [node.start[0], node.start[1], i, node.start[3]];
              await this.createThickLine(startParams, node.action, node.skycar, node.status);
            }
          }
        }
      }
    },
    async removeAllPaths() {
      if (this.sceneScreen.children) {
        for (let i = this.sceneScreen.children.length - 1; i >= 0; i--) {
          const child = this.sceneScreen.children[i];
          if (child instanceof THREE.Mesh && child.name === "") {
            child.geometry.dispose()
            child.material.dispose()
            this.sceneScreen.remove(child);
          }
        }
      }
    },
    generateMultipleSkycars() {
      var here = this;
      Object.keys(this.gridSkycar).forEach(function(key) {
        let sid = here.gridSkycar[key].sid;
        let status = here.gridSkycar[key].status;
        let dualWinch = "BOTH" in here.gridSkycar[key].assignedStorage ? false : true;
        var [x, z] = key.split(",");
        const result = here.createSkycar(sid, -x, 0, z, status);
        if (result){
          here.threeJsSkycar[sid] = {
            "skycar": result[0],
            "dualWinch": dualWinch
          }
          here.threeJsSkycarText[sid] = result[1]
        }
      });
    },
    skycarStatusColor(status){
      let color = null;
      switch (status) {
        case SkycarStatus.AVAILABLE:
          color = "lightgreen";
          break;
        case SkycarStatus.MAINTENANCE:
          color = "blue";
          break;
        case SkycarStatus.ERROR:
          color = "red";
          break;
        case SkycarStatus.DOCKED:
          color = "yellow";
          break;
        default:
          color = "gray";
          break;
      }

      return color
    },
    updateSkycarStatus(sid, status){
      let color = this.skycarStatusColor(status);
      const newMaterial = new THREE.MeshBasicMaterial({ color: color });

      this.threeJsSkycar[sid]["skycar"].material.dispose();
      this.threeJsSkycar[sid]["skycar"].material = newMaterial
    },
    createSkycar(sid, xPos, yPos, zPos, status) {
      let color = this.skycarStatusColor(status);

      if (status === SkycarStatus.MAINTENANCE){
        const obstacleObject = this.gridObstacle[`${-xPos},${zPos}`];
        if (!(obstacleObject && obstacleObject.sid && obstacleObject.sid.includes(sid))) {
          return 0;
        }
      }

      const boxGeometry = new THREE.BoxGeometry(0.9, 0.8, 0.9); 
      const boxMaterial = new THREE.MeshBasicMaterial({ color: color });
      const box = new THREE.Mesh(boxGeometry, boxMaterial.clone());
      box.name = "SC " + sid
      this.sceneScreen.add(box);

      this.skycarPosition[sid] = {
        x: xPos,
        y: yPos - 0.1,
        z: zPos,
      };

      const label = this.createText(
        box.name, 
        this.skycarPosition[sid].x - 0.1,
        this.skycarPosition[sid].y + 0.6, 
        this.skycarPosition[sid].z
      );
      label.name = `SC ${sid}`
      this.sceneScreen.add(label);

      box.position.set(
        this.skycarPosition[sid].x,
        this.skycarPosition[sid].y,
        this.skycarPosition[sid].z
      );
      return [box, label];
    },
    addAxisLabels() {
      const labelX = this.createText(
        "X", 
        -((this.gridObject.gridMaxX - this.gridObject.gridMinX) / 2 + this.gridObject.gridMinX) - 0.5, 
        this.gridObject.gridMinZ, 
        this.gridObject.gridMinY - 1.5
      );
      this.xLabel();
      this.sceneScreen.add(labelX);
      
      const labelY = this.createText(
        "Y", 
        -this.gridObject.gridMinX + 1, 
        this.gridObject.gridMinZ, 
        (this.gridObject.gridMaxY - this.gridObject.gridMinY) / 2 + this.gridObject.gridMinY - 0.3
      );
      this.yLabel();
      this.sceneScreen.add(labelY);
      
      const labelZ = this.createText(
        "Z", 
        -this.gridObject.gridMinX + 1, 
        -((this.gridObject.gridMaxZ - this.gridObject.gridMinZ + 1) / 2 + this.gridObject.gridMinZ), 
        this.gridObject.gridMinY - 1
      );
      this.zLabel();
      this.sceneScreen.add(labelZ);
    },
    xLabel() {
      for (let i = this.gridObject.gridMinX; i <= this.gridObject.gridMaxX; i++) {
        const label = this.createText(
          `${i}`, 
          -i - 0.5, 
          this.gridObject.gridMinZ, 
          this.gridObject.gridMinY - 1
        );
        this.sceneScreen.add(label);
      }
    },
    yLabel() {
      for (let i = this.gridObject.gridMinY; i <= this.gridObject.gridMaxY; i++) {
        const label = this.createText(
          `${i}`,
          -this.gridObject.gridMinX + 0.5, 
          this.gridObject.gridMinZ, 
          i - 0.3
        );
        this.sceneScreen.add(label);
      }
    },
    zLabel() {
      for (let i = this.gridObject.gridMinZ + 1; i <= this.gridObject.gridMaxZ; i++) {
        const label = this.createText(
          `${i}`, 
          -this.gridObject.gridMinX + 0.5, 
          this.gridObject.gridMinZ + 1 * (-i), 
          this.gridObject.gridMinY - 0.75
        );
        this.sceneScreen.add(label);
      }
    },
    createText(text, x, y, z) {
      const labelCanvas = document.createElement("canvas");
      const context = labelCanvas.getContext("2d");
      context.font = "Bold 100px Arial";
      context.fillStyle = "black";
      context.fillText(text, 0, 100);

      const texture = new THREE.CanvasTexture(labelCanvas);
      const material = new THREE.SpriteMaterial({ map: texture });
      const sprite = new THREE.Sprite(material);

      sprite.position.set(x, y, z);
      sprite.scale.set(1, 0.5, 1);

      return sprite;
    },
    createCube() {
      const geometry = new THREE.BoxGeometry(1, 1, 1);
      var geo = new THREE.EdgesGeometry(geometry);
      var material = new THREE.LineBasicMaterial({
        color: "blue",
        transparent: true,
        opacity: 0.3,
      });

      // Add the entire grid box
      for (var x = this.gridObject.gridMinX; x <= this.gridObject.gridMaxX; x++) {
        for (var y = this.gridObject.gridMinZ + 1; y <= this.gridObject.gridMaxZ; y++) {
          for (var z = this.gridObject.gridMinY; z <= this.gridObject.gridMaxY; z++) {
            const isBorder = x === this.gridObject.gridMinX || x === this.gridObject.gridMaxX ||
            z === this.gridObject.gridMinY || z === this.gridObject.gridMaxY ||
            y === this.gridObject.gridMinZ + 1 || y === this.gridObject.gridMaxZ;

            if (isBorder) {
              const smallBox = new THREE.LineSegments(geo, material);
              smallBox.position.set(-x, -y, z);
              this.sceneScreen.add(smallBox);
            }

            const isObstacle = `${x},${z}` in this.gridObstacle;
            if (isObstacle) {
              if (y !== 1) {
                continue;
              } 
              var obstacle = this.gridObstacle[`${x},${z}`];
              this.addObstacle(x, z, obstacle.type[0]);
            }
          }
        }
      }
    },
    createDualWinch(sid, x, y, z, leftPosition){
      this.removeWinch(sid, true, leftPosition)

      // Vertical Lines
      this.createWinch(sid, x, y, z, false, false, false, false, true, false, leftPosition)
      this.createWinch(sid, x, y, z, false, true, false, false, true, false, leftPosition)
      this.createWinch(sid, x, y, z, true, false, false, false, true, false, leftPosition)
      this.createWinch(sid, x, y, z, true, true, false, false, true, false, leftPosition)

      // Horizontal Lines
      // Long
      this.createWinch(sid, x, y, z, false, false, false, true, true, false, leftPosition)
      this.createWinch(sid, x, y, z, false, true, false, true, true, false, leftPosition)

      // Short
      this.createWinch(sid, x, y, z, false, false, true, false, true, true, leftPosition)
      this.createWinch(sid, x, y, z, true, false, true, false, true, true, leftPosition)
    },
    createWinch(sid, x, y, z, left, front, xRotation, zRotation, dual, small, leftDualPosition = true){
      const zValue = dual ? (xRotation ? 0.35 : (zRotation ? 0.8 : z)) : (xRotation || zRotation) ? 0.8 : z
      const color = dual ? (leftDualPosition ? "gray" : 0x36454F) : "gray"

      const geometry = new THREE.CylinderGeometry(0.03, 0.03, zValue, 32);
      const lineMaterial = new THREE.MeshBasicMaterial({ color: color });
      const line = new THREE.Mesh(geometry, lineMaterial);

      const xOffset = zRotation ? 0 : (left ? -0.4 : 0.4)
      const yOffset = xRotation ? ( 
        small ? (
            leftDualPosition ? -0.22 : 0.22
          ) : 0
        ) : (
        dual ? (
          front ? (
            leftDualPosition ? -0.05 : 0.05 
          ) : (
            leftDualPosition ? -0.4 : 0.4 
          )
        ) : (
          front ? -0.4 : 0.4
        )
      )
      const zOffset = (xRotation || zRotation) ? -0.5 : ((z - 1) / 2)

      if (xRotation){
        line.rotation.x = Math.PI / 2
      }

      if (zRotation){
        line.rotation.z = Math.PI / 2
      }
      
      line.position.set(-x + xOffset, -z + zOffset, y + yOffset);
      if (dual){
        line.name = leftDualPosition ? `${sid} A Winch ${x},${y},${z}` : `${sid} B Winch ${x},${y},${z}`
      } else {
        line.name = `${sid}, Winch ${x},${y},${z}`
      }
      this.sceneScreen.add(line);
    },
    createSingleWinch(sid, x, y, z){
      this.removeWinch(sid, false)

      // Vertical Line
      this.createWinch(sid, x, y, z, false, false, false, false, false, false)
      this.createWinch(sid, x, y, z, false, true, false, false, false, false)
      this.createWinch(sid, x, y, z, true, false, false, false, false, false)
      this.createWinch(sid, x, y, z, true, true, false, false, false, false)

      // Horizontal Line
      this.createWinch(sid, x, y, z, true, false, true, false, false, false)
      this.createWinch(sid, x, y, z, false, false, true, false, false, false)
      this.createWinch(sid, x, y, z, false, true, false, true, false, false)
      this.createWinch(sid, x, y, z, false, false, false, true, false, false)
    },
    removeWinch(sid, dual, leftDualPosition = true) {
      let winchName
      if (dual){
        winchName = leftDualPosition ? `${sid} A` : `${sid} B`
      } else {
        winchName = `${sid}`;
      }
      const winchesToRemove = this.sceneScreen.children.filter(object => object.name.startsWith(winchName));

      winchesToRemove.forEach(winchToRemove => {
        winchToRemove.geometry.dispose();
        winchToRemove.material.dispose();
        this.sceneScreen.remove(winchToRemove);
      });
    },
    addObstacle(x, z, obstacleType){
      if (this.excludeDrawObstacleType.includes(obstacleType)){return}

      const geometry = new THREE.BoxGeometry(1, 0.6, 1);
      var gridStatus = getGridStatus(obstacleType)

      var gridMaterial = new THREE.MeshBasicMaterial({
        color: gridStatus["color"],
        opacity: 0.9,
        transparent: true
      });
      var boxMesh = new THREE.Mesh(geometry, gridMaterial.clone());
      boxMesh.name = `Obstacle ${x},${z}`
      boxMesh.position.set(-x, -0.2, z);
      this.sceneScreen.add(boxMesh);
    },
    removeObstacle(x, z) {
      const obstacleName = `Obstacle ${x},${z}`;
      const obstacleToRemove = this.sceneScreen.getObjectByName(obstacleName);

      if (obstacleToRemove) {
          obstacleToRemove.geometry.dispose();
          obstacleToRemove.material.dispose();
          this.sceneScreen.remove(obstacleToRemove);
      } 
    },
    async brightenColor(color){
      const brightenedColor = new THREE.Color(color);
      brightenedColor.multiplyScalar(2);
      return brightenedColor;
    },
    async createThickLine(positions, direction = null, sid, status) {
      const colorPallete = [
        "#3498db", "#2c3e50", "#2ecc71", "#008080", "#c1d82f",
        "#f1c40f", "#e67e22", "#e74c3c", "#e91e63", "#9b59b6",
        "#673ab7", "#3f51b5", "#00bcd4", "#add8e6", "#90ee90",
        "#ffc107", "#ff5722", "#795548", "#808080", "#d3d3d3"
      ]

      const index = this.skycarValues.indexOf(sid);
      let color = index === -1 ? "gray" : colorPallete[index];
      if (status === AdgStatus.STAGED){
        color = await this.brightenColor(color);
      }

      const lineGeometry = new THREE.CylinderGeometry(0.1, 0.1, 1, 32);
      const lineMaterial = new THREE.MeshBasicMaterial({ color: color });
      const line = new THREE.Mesh(lineGeometry, lineMaterial);

      const setLinePosition = (offsetX, offsetY, offsetZ) => {
        line.position.set(-positions[0] + offsetX, -positions[2] + offsetY, positions[1] + offsetZ);
      };

      const setLineRotation = (axis, direction) => {
        if (axis === "x") {
          line.rotation.x = Math.PI / 2;
          line.rotation.z = Math.PI / 2;
          setLinePosition(direction === "F" ? -0.5 : 0.5, -0.2, 0);
        } else {
          line.rotation.x = Math.PI / 2;
          setLinePosition(0, -0.2, direction === "F" ? 0.5 : -0.5);
        }
      };

      switch (direction) {
        case "F":
          setLineRotation(positions[3], direction);
          break;
        case "B":
          setLineRotation(positions[3], direction);
          break;
        default:
          setLinePosition(0, -0.7, 0);
          break;
      }
      this.sceneScreen.add(line);
    },
    panDirection(direction) {
      const panSpeed = 1;

      const pan = new THREE.Vector3();
      switch (direction) {
        case "left":
          pan.set(panSpeed, 0, 0);
          break;
        case "right":
          pan.set(-panSpeed, 0, 0);
          break;
        case "up":
          pan.set(0, 0, panSpeed);
          break;
        case "down":
          pan.set(0, 0, -panSpeed);
          break;
      }

      this.camera.position.add(pan);
      this.controls.target.add(pan);
    },
  },
};
</script>
