<template>
  <div>
    <v-row>
      <v-col v-for="(direction, index) in directions" :key="index" cols="4">
        <v-btn v-if="direction" style="min-width: 80px;" @click="panDirection(direction)">{{ direction }}</v-btn>
        <v-col v-else></v-col>
      </v-col>
    </v-row>
  </div>
</template>

<script>
export default {
  data() {
    return {
      directions: ["", "up", "", "left", "down", "right"]
    };
  },
  methods: {
    panDirection(direction) {
      this.$emit("click", direction);
    },
  },
};
</script>
