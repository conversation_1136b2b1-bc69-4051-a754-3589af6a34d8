<template>
  <v-card>
    <v-card-text>
      <div style="overflow: auto" ref="networkGraph"></div>
      <div class="tooltip"></div>
    </v-card-text>
  </v-card>
</template>

<script>
import * as d3 from "d3";

import { AdgStatus } from "../../helper/enums.js";

export default {
  name: "AdgNetworkGraph",
  props: {
    graphSize: Object, 
    adgs: Object, 
  },
  data() {
    return {
      circleRadius: 15, 
      legendColors: {
        Staged: this.getNodeColor(AdgStatus.STAGED, false),
        Enqueued: this.getNodeColor(AdgStatus.ENQUEUED, false),
        Completed: this.getNodeColor(AdgStatus.COMPLETED, false),
      },
      edgeLegendColors: "black",
      removePopupKeys: ["x", "y", "rowTitle"],

      idToIndex: null,
      computedNodes: Array(),
      focusedSkycarIds: Array(),

      maxX: null,
      maxY: null,
    };
  },
  mounted() {
    const container = this.$refs.networkGraph
    container.addEventListener("wheel", this.handleHorizontalScroll, { passive: false });
  },
  beforeDestroy() {
    const container = this.$refs.networkGraph
    container.removeEventListener("wheel", this.handleHorizontalScroll);
  },
  methods: {
    async initialization(){
      await this.preprocessNodes()
      await this.createSVGContainer()
      await this.buildNetworkGraph()
      await this.createLegend();
    },
    async disposeGraph(){
      // Remove everything in the container
      const container = this.$refs.networkGraph;
      while (container.firstChild) {
        container.removeChild(container.firstChild);
      }
    },
    async preprocessNodes(){
      if (this.adgs.nodes.length > 0) {
        let maxX = 0
        let maxY = 0
        const skycarValues = Array.from(new Set(this.adgs.nodes.map(node => node.skycar))).sort((a, b) => a - b);
        const skycarGroupIndices = {};

        this.computedNodes = this.adgs.nodes.map((node) => {
          const skycarIndex = skycarValues.indexOf(node.skycar);
          const index = (skycarGroupIndices[skycarIndex] || 0) + 1;
          skycarGroupIndices[skycarIndex] = index;

          const x = (index - 1) * 75 + 50;
          const y = skycarIndex * 75 + 30;
          if (maxX < x){
            maxX = x
          }
          if (maxY < y + 120){
            maxY = y + 120
          }
          if ((index - 1) === 0) {
            const rowTitle = "SC " + node.skycar
            return { ...node, x, y, rowTitle };
          }
          return { ...node, x, y };
        });
        this.maxX = maxX
        this.maxY = maxY
        this.idToIndex = Object.fromEntries(
          this.adgs.nodes.map((node, index) => [`Skycar${node.skycar}-${node.time}`, index])
        );
      }
    },
    calculateAdjustedCoordinate(d, axis) {
      const sourceNode = this.computedNodes[this.idToIndex[d.source]];
      const targetNode = this.computedNodes[this.idToIndex[d.target]];
      const angle = Math.atan2(targetNode.y - sourceNode.y, targetNode.x - sourceNode.x);
      return axis === "x" 
        ? targetNode.x - (this.circleRadius * Math.cos(angle)) 
        : targetNode.y - (this.circleRadius * Math.sin(angle));
    },
    async updateNetwork() {
      await this.preprocessNodes()
      await this.updateGraph()
      await this.buildNetworkGraph()
    },
    async buildNetworkGraph(){
      // Create links
      if (this.adgs.links.length > 0 && this.computedNodes.length > 0){
        d3.select(this.$refs.networkGraph)
          .select("svg")
          .selectAll(".graph-elements")
          .remove();
              
        const svg = d3.select(this.$refs.networkGraph).select("svg");
        
        svg.selectAll("line.network-link")
          .data(this.adgs.links)
          .enter().append("line")
          .attr("class", "graph-elements network-link")
          .attr("stroke", d => (
            this.focusedSkycarIds.length === 0 || 
            this.focusedSkycarIds.includes(this.computedNodes[this.idToIndex[d.source]].skycar)
          ) ? "black" : "gray")
          .attr("stroke-width", d => (
            this.focusedSkycarIds.length === 0 || 
            this.focusedSkycarIds.includes(this.computedNodes[this.idToIndex[d.source]].skycar)
          ) ? 3 : 1)
          .attr("marker-end", d => (
            this.focusedSkycarIds.length === 0 || 
            this.focusedSkycarIds.includes(this.computedNodes[this.idToIndex[d.source]].skycar)
          ) ? "url(#blackarrowhead)" : "url(#grayarrowhead)")
          .attr("x1", d => this.computedNodes[this.idToIndex[d.source]].x)
          .attr("y1", d => this.computedNodes[this.idToIndex[d.source]].y)
          .attr("x2", d => this.calculateAdjustedCoordinate(d, "x"))
          .attr("y2", d => this.calculateAdjustedCoordinate(d, "y"))
          .on("click", (event, d) => this.edgesClicked(d));
          
        // Create nodes
        const nodeGroup = svg
        .selectAll("g.node-group")
        .data(this.computedNodes)
        .enter()
        .append("g")
        .attr("class", "graph-elements node-group")
        .on("click", (event, d) => this.nodesClicked(d))
        .on("mouseenter", (event, d) => this.mouseEntered(event, d))
        .on("mouseleave", (event) => this.mouseLeaved(event));
        
        nodeGroup
        .append("circle")
        .attr("class", "graph-elements")
        .attr("r", this.circleRadius)
        .attr("fill", d => this.getNodeColor(d.status, false))
        .attr("stroke", d => this.getNodeColor(d.status, true)) 
        .attr("stroke-width", d => (
          this.focusedSkycarIds.length === 0 || this.focusedSkycarIds.includes(d.skycar)
        ) ? 2 : 0)
        .attr("opacity", d => (
          this.focusedSkycarIds.length === 0 || this.focusedSkycarIds.includes(d.skycar)
        ) ? 1 : 0.3);
        
        nodeGroup
        .append("text")
        .attr("class", "graph-elements")
        .text(d => d.action)
        .attr("dx", d => (
          d.action.length === 1
          ) ? -6 : -13)
        .attr("dy", 7)
        .attr("font-size", 20)
        .style("font-weight", "bold");

        // Display skycar number
        nodeGroup
          .filter(d => d.rowTitle)
          .append("text")
          .attr("class", "skycar-number")
          .text(d => d.rowTitle)
          .attr("dx", -45)
          .attr("dy", 5)
          .attr("font-size", 12)
          .style("font-weight", "bold");
        
        nodeGroup
          .attr("transform", d => `translate(${d.x},${d.y})`);
      }
    },
    async createSVGContainer(){
      // Create SVG container
      const svg = d3.select(this.$refs.networkGraph)
        .append("svg")
        .attr("width", this.maxX > this.graphSize.x ? this.maxX + 50 : this.graphSize.x)
        .attr("height", this.maxY > this.graphSize.y ? this.maxY + 30 : this.graphSize.y)
        .style("background-color", "lightblue");

      // Create links with arrowheads
      const defs = svg.append("defs");

      defs.append("marker")
        .attr("id", "grayarrowhead")
        .attr("refX", 8)
        .attr("refY", 3)
        .attr("markerWidth", 10)
        .attr("markerHeight", 10)
        .attr("orient", "auto")
        .append("path")
        .attr("d", "M0,0 L0,6 L9,3 Z")
        .attr("fill", "gray");

        defs.append("marker")
          .attr("id", "blackarrowhead")
          .attr("refX", 8)
          .attr("refY", 3)
          .attr("markerWidth", 10)
          .attr("markerHeight", 10)
          .attr("orient", "auto")
          .append("path")
          .attr("d", "M0,0 L0,6 L9,3 Z")
          .attr("fill", "black");
    },
    async createLegend() {
      const svg = d3.select(this.$refs.networkGraph).select("svg");
      const svgWidth = 10
      const svgHeight = +svg.attr("height");
      
      // Legend for Nodes
      const nodeLegend = svg.append("g")
        .attr("class", "node-legend")
        .attr("transform", `translate(${svgWidth},${svgHeight - 80})`);

      const nodeLegendItems = nodeLegend.selectAll(".legend-item")
        .data(Object.entries(this.legendColors))
        .enter().append("g")
        .attr("class", "legend-item")
        .attr("transform", (d, i) => `translate(0, ${i * (this.circleRadius + 10)})`);

      nodeLegendItems.append("circle")
        .attr("cx", 10)
        .attr("cy", 10)
        .attr("r", this.circleRadius - 5)
        .attr("fill", d => d[1]);

      nodeLegendItems.append("text")
        .attr("x", 30)
        .attr("y", 15)
        .text(d => d[0])
        .attr("font-size", 12);

      // Legend for Edges
      const edgeLegend = svg.append("g")
        .attr("class", "edge-legend")
        .attr("transform", `translate(${svgWidth},${svgHeight - 105})`);

      edgeLegend.append("path")
        .attr("d", "M0,0 L10,5 L0,10 L2,5 Z")
        .attr("fill", "black")
        .attr("transform", "translate(15, 5)");

      edgeLegend.append("line")
        .attr("x1", 0)
        .attr("y1", 10)
        .attr("x2", 20)
        .attr("y2", 10)
        .attr("stroke", "black")
        .attr("stroke-width", 2);

      edgeLegend.append("text")
        .attr("x", 40)
        .attr("y", 15)
        .text("Edges")
        .attr("font-size", 12);
    },
    getNodeColor(status, darken) {
      const colorMap = {
        Staged: "gray",
        Enqueued: "orange",
        Completed: "green",
      };
      if (darken){
        return d3.color(colorMap[status]).darker(1)
      } else {
        return d3.color(colorMap[status]).brighter(0.5)
      }
    },
    toggleSkycarFocus(sid){
      if (this.focusedSkycarIds.includes(sid)) {
        this.focusedSkycarIds = this.focusedSkycarIds.filter(item => item !== sid);
      } else {
        this.focusedSkycarIds.push(sid);
      }
    },
    nodesClicked(nodes) {
      this.toggleSkycarFocus(nodes.skycar)
      this.buildNetworkGraph();
    },
    edgesClicked(edges) {
      const sid= this.computedNodes[this.idToIndex[edges.source]].skycar
      this.toggleSkycarFocus(sid)
      this.buildNetworkGraph();
    },
    mouseEntered(event, d) {
      d3.select(event.currentTarget)
        .select("circle")
        .attr("r", this.circleRadius + 4);

      this.showTooltip(event, d);
    },
    mouseLeaved(event) {
      d3.select(event.currentTarget)
        .select("circle")
        .attr("r", this.circleRadius);

      this.hideTooltip();
    },
    handleHorizontalScroll(event) {
      if (this.$refs.networkGraph.scrollWidth > this.$refs.networkGraph.clientWidth) {
        event.preventDefault();
        this.$refs.networkGraph.scrollLeft -= event.wheelDelta;
      }
    },
    showTooltip(event, node) {
      const tooltip = d3.select(".tooltip");

      tooltip.html(""); // Clear existing content
      for (const property in node) {
        if (!this.removePopupKeys.includes(property)) {
          tooltip.append("small").html(`${property}: ${node[property]}</br>`);
        }
      }

      const graphContainer = d3.select(this.$refs.networkGraph).select("svg");
      const graphPosition = graphContainer.node().getBoundingClientRect();
      
      let leftPosition = event.clientX - graphPosition.left - this.$refs.networkGraph.scrollLeft - 200;
      let topPosition = event.clientY - graphPosition.top;

      tooltip.style("display", "block")
        .style("left", leftPosition + "px")
        .style("top", topPosition + "px")
        .style("border", "1px solid black");
    },
    hideTooltip() {
      d3.select(".tooltip")
        .style("display", "none")
        .style("border", "0")
    },
    async updateGraph(){
      // Update Graph Size
      const svg = d3.select(this.$refs.networkGraph).select("svg");

      svg.attr("width", this.maxX > this.graphSize.x ? this.maxX + 50 : this.graphSize.x)
        .attr("height", this.maxY > this.graphSize.y ? this.maxY + 30 : this.graphSize.y)

      const svgWidth = 10
      const svgHeight = +svg.attr("height");  
      svg.select(".node-legend")
        .attr("transform", `translate(${svgWidth},${svgHeight - 80})`);

      svg.select(".edge-legend")
        .attr("transform", `translate(${svgWidth},${svgHeight - 105})`);      
    },
    async resetGraph() {
      // Remove container and its content
      d3.select(this.$refs.networkGraph).selectAll("*").remove();

      // Clear computed data
      this.computedNodes = [];
      this.idToIndex = null;
      this.focusedSkycarIds = [];

      // Reset maxX and maxY
      this.maxX = null;
      this.maxY = null;
    },
  }
};
</script>

<style scoped>
.tooltip {
  position: absolute;
  background-color: lightblue;
  z-index: 1;
}
</style>
