<template>
    <span>
        <v-dialog
            v-model="dialogBool"
            max-width="1200"
        >
            <v-card>
                <v-col>
                    <v-data-table
                        :headers="headers"
                        :items="jobItems"
                    >
                        <template v-slot:top>
                            <v-toolbar flat>
                                <v-toolbar-title>Skycars still Charging</v-toolbar-title>
                                <v-spacer></v-spacer>
                                <v-btn
                                    color="primary"
                                    dark
                                    class="mb-2"
                                    @click="btnUndockAllSkycar()"
                                >
                                    Undock All
                                </v-btn>
                            </v-toolbar>
                        </template>

                        <template v-slot:[`item.job_status`]="{ item }">
                            <v-chip
                                class = "white--text"
                                :color="getStatusColor(item.job_status)"
                                >
                                {{ item.job_status }}
                            </v-chip>
                        </template>

                        <template v-slot:[`item.skycar_sent_undock`]="{ item }">
                            <v-chip
                                class = "white--text"
                                :color="getStatusColor(item.skycar_sent_undock)"
                                >
                                {{ item.skycar_sent_undock }}
                            </v-chip>
                        </template>

                        <template v-slot:[`item.actions`]="{ item }">
                            <v-tooltip bottom>
                                <template v-slot:activator="{ on: tooltip }">
                                    <v-icon
                                        v-on="{ ...tooltip }"
                                        @click="btnUndockSkycar(item.job_id)"
                                    >
                                        mdi-connection
                                    </v-icon>
                                </template>
                                <span>Undock Skycar {{ item.skycar_id }}</span>
                            </v-tooltip>    
                        </template>

                    </v-data-table>
                    <v-card-actions>
                        <ProgressCircular :doneSync="doneSync"/>
                        <v-btn
                            color="green darken-1"
                            text
                            @click="btnRefresh()"
                            :disabled="!doneSync"
                        >Refresh
                        </v-btn>
                        <v-btn
                            color="green darken-1"
                            text
                            @click="closeDialog()"
                        >Close
                        </v-btn>
                    </v-card-actions>
                </v-col>
            </v-card>
        </v-dialog>
        <v-dialog v-model="apiResponse" max-width="600">
            <v-card light v-bind:value="{ apiResponseTitle, apiResponseDesc }">
                <v-card-title>{{ apiResponseTitle }}</v-card-title>
                <v-card-text>{{ apiResponseDesc }}</v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="grey" text @click="apiResponse = false">OK</v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </span>
</template>

<script>
import { getCMHost, getRequestHeader, useRefreshToken } from "../../../helper/common"
import ProgressCircular from "../../shared/ProgressCircular.vue";
import { routeCM } from "../../../helper/enums";
export default {
    components: {
        ProgressCircular
    },
    props: {
        showNotification: {
            type: Function
        },
        cube: {
            type: String
        }
    },
    data: () => ({
        dialogBool: false,
        apiResponse: false,
        apiResponseTitle: "",
        apiResponseDesc: "",
        doneSync: true,
        processStatus: "",
        headers: [
            { text: "Charging Job ID", value: "job_id" },
            { text: "Status", value: "job_status" },
            { text: "Station", value: "station_id" },
            { text: "Skycar", value: "skycar_id" },
            { text: "Battery Percentage", value: "skycar_percent" },
            { text: "Undock Sent Recently?", value: "skycar_sent_undock" },
            { text: "Job Created At", value: "created_at" },
            { text: "Actions", value: "actions", sortable: false }
        ],
        jobItems: [],
    }),
    methods: {
        getStatusColor(status) {
            console.log('getStatusColor: ' + status)
            if (status == "AVAILABLE" || status == false) {
                return "green";
            } else if (status == "COMPLETE" || status == "CANCELED" || status == "INVALID") {
                return "grey";
            } else if (status == "ERRORED" || status == true) {
                return "red";
            } else {
                return "orange";
            }
        },  
        async openDialog() {
            await this.btnRefresh()
            this.dialogBool = true
        },
        closeDialog() {
            this.dialogBool = false
        },
        async btnRefresh() {
            try {
                this.doneSync = false
                let url = new URL(getCMHost() + routeCM.CHECK_CHARGING_SKYCARS)

                let res = await fetch(url, {
                    method: "GET",
                    headers: getRequestHeader()
                })
                let json = await res.json()
                if (json.code === 401) {
                    return useRefreshToken(this, this.btnRefresh)
                }
                if (!json.status) {
                    this.jobItems = json.data
                    this.processStatus = json.process_status
                }
            } catch (error) {
                this.showNotification(false, error)
            } finally {
                setTimeout(() => {
                    this.doneSync = true
                }, 500)
            }
        },
        async btnUndockSkycar(jobId) {
            let url = new URL(getCMHost() + routeCM.UNDOCK_SKYCAR)
            await fetch(url, {
                method: "POST",
                headers: getRequestHeader(),
                body: JSON.stringify({ 'job_id': jobId })
            }).then(async response => {
                let json = await response.json()
                if (json.code === 401) {
                    return useRefreshToken(this, this.btnUndockSkycar, jobId)
                }
                if (!json.status) {
                    this.apiResponseTitle = "Sent Undock Successfully"
                    this.apiResponseDesc = json.msg
                    this.apiResponse = true

                    // delay a bit before refreshing
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    this.btnRefresh()
                }
            }).catch(error => {
                this.showNotification(false, error)
            })
        },
        async btnUndockAllSkycar() {
            let url = new URL(getCMHost() + routeCM.UNDOCK_ALL_SKYCARS)
            await fetch(url, {
                method: "POST",
                headers: getRequestHeader(),
            }).then(async response => {
                let json = await response.json()
                if (json.code === 401) {
                    return useRefreshToken(this, this.btnUndockAllSkycar)
                }
                if (!json.status) {
                    this.apiResponseTitle = "Sent Undock All Successfully"
                    this.apiResponseDesc = json.msg
                    this.apiResponse = true

                    // delay a bit before refreshing
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    this.btnRefresh()
                }
            }).catch(error => {
                this.showNotification(false, error)
            })
        }
    }
}
</script>
