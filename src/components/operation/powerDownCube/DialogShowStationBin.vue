<template>
  <v-dialog v-model="dialogBool" width="800"> 
    <v-card>
      <v-toolbar
        color="red"
        dark
      >
        <v-toolbar-title>Bins not stored</v-toolbar-title>
      </v-toolbar>

      <v-card-title>
        <div>
          Bins are still remain at the stations are not stored inside the cube!
        </div>
      </v-card-title>
      <v-divider />
      <v-card-text>
        <v-list 
          v-for="(item, index) in stationBins" 
          :key="index"
        >
          <v-list-item style="height: 0px">
            <div>
              Station ID {{ index }}: Bin {{ item }} are still in this station
            </div>
          </v-list-item>
        </v-list>
      </v-card-text>
      <v-col>
        <v-alert 
          border="top" 
          color="red" 
          dark
        >
          <v-checkbox 
            v-model="checkbox" 
            :label="getText()" 
            color="white"
          />
        </v-alert>
      </v-col>
      <v-card-actions>
        <v-spacer />
        <ProgressCircular :doneSync="doneSync"/>
        <v-btn
          color="green darken-1"
          text
          @click="btnConfirm()"
          :disabled="disableConfirm()"
        >
          Confirm
        </v-btn>
        <v-btn
          color="green darken-1"
          text
          @click="closeDialog()"
        >
          Close
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
import ProgressCircular from "../../shared/ProgressCircular.vue";

export default {
  components: {
    ProgressCircular
  },
  data: () => ({
    dialogBool: false,
    checkbox: false,
    doneSync: true,
    stationBins: Object(),
  }),
  methods: {
    btnConfirm(){
      this.$emit("forcePowerDown")
    },
    async openDialog(value) {
      await this.getData(value)
      this.dialogBool = true
    },
    async getData(value){
      this.stationBins = value
    },
    closeDialog() {
      this.dialogBool = false;
      this.doneSync = true
      this.stationBins = Object();
    },
    getText() {
      return `Bins are still remain in station ${Object.keys(this.stationBins)}. Are you sure you
        want to continue this action? Power Down Cube with bins remained in stations.`
    },
    disableConfirm() {
      if (!this.doneSync) {
        return true
      }
      if (!this.checkbox) {
        return true
      }
      return false
    },
  }
};
</script>
