<template>
    <v-dialog
        v-model="dialogBool"
        width="600"
    >
        <v-card>
            <v-toolbar
                color="primary"
                dark
            >
                <v-toolbar-title>Remove Obstacle</v-toolbar-title>
            </v-toolbar>
            <v-col>
                <v-card-text>
                    I have verified that coordinate ({{ coord }}) is cleared, and I would like to remove its obstacle.
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <ProgressCircular :doneSync="doneSync"/>
                    <v-btn
                        color="green darken-1"
                        text
                        @click="btnConfirm()"
                        :disabled="!doneSync"
                    >Confirm
                    </v-btn>
                    <v-btn
                        color="green darken-1"
                        text
                        @click="closeDialog()"
                    >Close
                    </v-btn>
                </v-card-actions>
            </v-col>
        </v-card>
    </v-dialog>
</template>

<script>
import { getHost, getRequestHeader, useRefreshToken } from "../../../helper/common";
import { SkycarError } from "../../../helper/enums";
import ProgressCircular from "../../shared/ProgressCircular.vue";
export default {
    components: {
        ProgressCircular
    },
    props: {
        btnRefresh: {
            type: Function
        },
        showNotification: {
            type: Function
        },
        cube: {
            type: String
        }
    },
    data: () => ({
        dialogBool: false,
        doneSync: true,
        coord: null
    }),
    methods: {
        async openDialog(coord) {
            this.coord = coord
            this.dialogBool = true
        },
        closeDialog() {
            this.dialogBool = false
        },
        async btnConfirm() {
            try {
                this.doneSync = false
                let url = getHost(this.cube) + SkycarError.addRemoveObs

                let req = await fetch(url, {
                    method: "DELETE",
                    body: JSON.stringify({
                        // eslint-disable-next-line camelcase
                        two_d: [this.coord]
                    }),
                    headers: getRequestHeader()
                })
                let res = JSON.parse(await req.text())
                if (res.code === 401){ // If access token is unauthorized
                    // use refresh token to get new access token from auth server
                    return useRefreshToken(this, this.btnConfirm)
                }
                if (res.status) {
                    await this.btnRefresh()
                    this.closeDialog()
                    this.showNotification(true, `Obstacle is removed at coordinate (${this.coord}).`)
                } else {
                    this.showNotification(false, res.message)
                }
            } catch (error) {
                this.showNotification(false, error)
            } finally {
                setTimeout(() => {
                    this.doneSync = true
                }, 500)
            }
        }
    }
}
</script>
