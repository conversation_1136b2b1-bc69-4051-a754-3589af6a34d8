<template>
    <v-dialog
        v-model="dialogBool"
        width="600"
    >
        <v-card>
            <v-toolbar
                color="primary"
                dark
            >
                <v-toolbar-title>Remove Coordinate</v-toolbar-title>
            </v-toolbar>
            <v-col>
                <v-card-text>
                    I would like to remove coordinate ({{ coord }}) from prefer parking coordinates.
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <ProgressCircular :doneSync="doneSync"/>
                    <v-btn
                        color="green darken-1"
                        text
                        :disabled="!doneSync"
                        @click="btnConfirm()"
                    >Confirm
                    </v-btn>
                    <v-btn
                        color="green darken-1"
                        text
                        @click="closeDialog()"
                    >Close
                    </v-btn>
                </v-card-actions>
            </v-col>
        </v-card>
    </v-dialog>
</template>

<script>
import { getHost, getRequestHeader, useRefreshToken } from "../../../helper/common";
import { RouteOperation } from "../../../helper/enums";
import ProgressCircular from "../../shared/ProgressCircular.vue";
export default {
    components: {
        ProgressCircular
    },
    props: {
        btnRefresh: {
            type: Function
        },
        showNotification: {
            type: Function
        },
        cube: {
            type: String
        }
    },
    data: () => ({
        dialogBool: false,
        doneSync: true,
        coord: null
    }),
    methods: {
        async openDialog(coord) {
            this.coord = coord
            this.dialogBool = true
        },
        closeDialog() {
            this.dialogBool = false
        },
        async btnConfirm() {
            try {
                this.doneSync = false
                let url = getHost(this.cube) + RouteOperation.PARKING_PREFER_COORD

                let req = await fetch(url, {
                    method: "PUT",
                    body: JSON.stringify({
                        coord: this.coord,
                        action: 0
                    }),
                    headers: getRequestHeader()
                })
                let res = JSON.parse(await req.text())
                if (res.code === 401){ // If access token is unauthorized
                    // use refresh token to get new access token from auth server
                    return useRefreshToken(this, this.btnConfirm)
                }
                if (res.status) {
                    await this.btnRefresh()
                    this.closeDialog()
                    this.showNotification(true, `Removed coordinate (${this.coord}) from prefer parking slot.`)
                } else {
                    this.showNotification(false, res.message)
                }
            } catch (error) {
                this.showNotification(false, error)
            } finally {
                setTimeout(() => {
                    this.doneSync = true
                }, 500)
            }
        }
    }
}
</script>
