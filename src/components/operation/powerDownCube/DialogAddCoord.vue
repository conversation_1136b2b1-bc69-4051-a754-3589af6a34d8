<template>
  <v-dialog 
    v-model="dialogBool" 
    width="600"
  >
    <v-card>
      <v-toolbar 
        color="primary" 
        dark
      >
        <v-toolbar-title>Add Coordinate</v-toolbar-title>
      </v-toolbar>
      <v-col>
        <v-form v-model="form">
          <v-row>
            <v-col
              cols="12" 
              sm="6" 
            >
              <v-text-field
                v-model="coordX"
                label="Coordinate X"
                rounded
                filled
                type="number"
                :rules="[(v) => v !== null && v !== undefined && v !== '' || 'Required']"
              >
                <template #prepend-inner>
                  <v-icon 
                    @click="openGrid()" 
                    style="margin-right: 5px;"
                  >
                    mdi-grid
                  </v-icon>
                </template>
              </v-text-field>
            </v-col>
            <v-col
              cols="12" 
              sm="6" 
            >
              <v-text-field
                v-model="coordY"
                label="Coordinate Y"
                rounded
                filled
                type="number"
                :rules="[(v) => v !== null && v !== undefined && v !== '' || 'Required']"
              >
                <template #prepend-inner>
                  <v-icon 
                    @click="openGrid()" 
                    style="margin-right: 5px;"
                  >
                    mdi-grid
                  </v-icon>
                </template>
              </v-text-field>
            </v-col>
          </v-row>
          <v-card-actions>
            <v-spacer />
            <ProgressCircular :doneSync="doneSync" />
            <v-btn
              color="green darken-1"
              text
              @click="btnConfirm()"
              :disabled="!form || !doneSync"
            >
              Confirm
            </v-btn>
            <v-btn 
              color="green darken-1" 
              text 
              @click="closeDialog()"
            >
              Close
            </v-btn>
          </v-card-actions>

          <DialogCoordinateSelection 
            ref="dialogCoordinateSelection" 
            @update-coord="updateCoord"
          />
        </v-form>
      </v-col>
    </v-card>
  </v-dialog>
</template>

<script>
import {
  getHost,
  getRequestHeader,
  useRefreshToken,
} from "../../../helper/common";
import { RouteOperation } from "../../../helper/enums";
import ProgressCircular from "../../shared/ProgressCircular.vue";
import DialogCoordinateSelection from "../../dialogs/DialogCoordinateSelection";

export default {
  components: {
    ProgressCircular,
    DialogCoordinateSelection,
  },
  data: () => ({
    dialogBool: false,
    doneSync: true,
    coordX: null,
    coordY: null,
    form: null,
  }),
  props: {
    btnRefresh: {
      type: Function,
    },
    showNotification: {
      type: Function,
    },
    cube: {
      type: String,
    },
  },
  methods: {
    async openDialog() {
      this.dialogBool = true;
      this.coordX = null;
      this.coordY = null;
    },
    closeDialog() {
      this.dialogBool = false;
    },
    openGrid(){
      this.$refs.dialogCoordinateSelection.openDialog(this.cube)
    },
    updateCoord(selectedCells){
      if (selectedCells.length > 0) {
        this.coordX = String(selectedCells[0].x);
        this.coordY = String(selectedCells[0].y);
      }
    },
    async btnConfirm() {
      try {
        this.doneSync = false;
        let url = getHost(this.cube) + RouteOperation.PARKING_PREFER_COORD;
        let coord = `${this.coordX},${this.coordY}`;

        let req = await fetch(url, {
          method: "PUT",
          body: JSON.stringify({
            coord: coord,
            action: 1,
          }),
          headers: getRequestHeader(),
        });
        let res = JSON.parse(await req.text());
        if (res.code === 401) {
          // If access token is unauthorized
          // use refresh token to get new access token from auth server
          return useRefreshToken(this, this.btnConfirm);
        }
        if (res.status) {
          await this.btnRefresh();
          this.closeDialog();
          this.showNotification(
            true,
            `Added coordinate (${coord}) as prefer parking slot.`
          );
        } else {
          this.showNotification(false, res.message);
        }
      } catch (error) {
        this.showNotification(false, error);
      } finally {
        setTimeout(() => {
          this.doneSync = true;
        }, 500);
      }
    },
  },
};
</script>
