<template>
    <span>
        <v-dialog
            v-model="dialogBool"
            max-width="800"
        >
            <v-card>
                <v-col>
                    <v-data-table
                        :headers="headers"
                        :items="result"
                    >
                        <template v-slot:top>
                            <v-toolbar flat>
                                <v-toolbar-title>Parking Prefer Coordinate</v-toolbar-title>
                                <v-spacer></v-spacer>
                                <v-btn
                                    color="primary"
                                    dark
                                    class="mb-2"
                                    @click="btnAddCoord()"
                                >
                                    Add Coordinate
                                </v-btn>
                            </v-toolbar>
                        </template>

                        <template v-slot:[`item.coord`]="{ item }">
                            <v-chip
                                color="green"
                                dark
                            >
                                {{ item.coord }}
                            </v-chip>
                        </template>

                        <template v-slot:[`item.is_blocked`]="{ item }">
                            <v-chip 
                                :color="getStatus(item.is_blocked)[0]" 
                                dark
                            >
                                {{ getStatus(item.is_blocked)[1] }}
                            </v-chip>
                        </template>

                        <template v-slot:[`item.actions`]="{ item }">
                            <v-icon
                                @click="btnRemoveCoord(item.coord)"
                            >
                                mdi-delete
                            </v-icon>
                            <v-icon
                                v-if="item.is_blocked"
                                @click="btnClearObstacle(item.coord)"
                            >
                                mdi-pencil
                            </v-icon>
                        </template>

                    </v-data-table>
                    <v-card-actions>
                        <v-card-text>{{ text }}</v-card-text>
                        <v-spacer></v-spacer>
                        <ProgressCircular :doneSync="doneSync"/>
                        <v-btn
                            color="green darken-1"
                            text
                            @click="btnRefresh()"
                            :disabled="!doneSync"
                        >Refresh
                        </v-btn>
                        <v-btn
                            color="green darken-1"
                            text
                            @click="closeDialog()"
                        >Close
                        </v-btn>
                    </v-card-actions>
                </v-col>
            </v-card>
        </v-dialog>
        <DialogAddCoord
            ref="dialogAddCoord" 
            :btnRefresh="btnRefresh" 
            :showNotification="showNotification"
            :cube="cube"
        />
        <DialogClearObstacle 
            ref="dialogClearObstacle" 
            :btnRefresh="btnRefresh" 
            :showNotification="showNotification"
            :cube="cube"
        />
        <DialogRemoveCoord 
            ref="dialogRemoveCoord" 
            :btnRefresh="btnRefresh" 
            :showNotification="showNotification"
            :cube="cube"
        />
    </span>
</template>

<script>
import { getHost, getStatus, getRequestHeader, useRefreshToken } from "../../../helper/common"
import { RouteOperation } from "../../../helper/enums";
import ProgressCircular from "../../shared/ProgressCircular.vue";
import DialogAddCoord from "./DialogAddCoord.vue";
import DialogClearObstacle from "./DialogClearObstacle.vue";
import DialogRemoveCoord from "./DialogRemoveCoord.vue";
export default {
    components: {
        DialogAddCoord,
        DialogClearObstacle,
        DialogRemoveCoord,
        ProgressCircular
    },
    props: {
        showNotification: {
            type: Function
        },
        cube: {
            type: String
        }
    },
    data: () => ({
        getStatus,
        dialogBool: false,
        doneSync: true,
        headers: [
            { text: "Coordinate", value: "coord" },
            { text: "Has Obstacle", value: "is_blocked" },
            { text: "Action", value: "actions" }
        ],
        result: [],
        text: null
    }),
    methods: {
        async openDialog() {
            this.result = []
            this.text = null
            await this.btnRefresh()
            this.dialogBool = true
        },
        closeDialog() {
            this.dialogBool = false
        },
        async btnRefresh() {
            try {
                this.doneSync = false
                let url = getHost(this.cube) + RouteOperation.PARKING_PREFER_COORD

                let req = await fetch(url, {
                    method: "GET",
                    headers: getRequestHeader()
                })
                let res = JSON.parse(await req.text())
                if (res.code === 401){ // If access token is unauthorized
                    // use refresh token to get new access token from auth server
                    return useRefreshToken(this, this.btnRefresh)
                }
                if (res.status) {
                    this.result = res.model.slots
                    this.text = this.updateText(res.model.num_skycars)
                } else {
                    this.showNotification(false, res.message)
                }
            } catch (error) {
                this.showNotification(false, error)
            } finally {
                setTimeout(() => {
                    this.doneSync = true
                }, 500)
            }
        },
        btnClearObstacle(coord) {
            this.$refs.dialogClearObstacle.openDialog(coord)
        },
        btnRemoveCoord(coord) {
            this.$refs.dialogRemoveCoord.openDialog(coord)
        },
        btnAddCoord() {
            this.$refs.dialogAddCoord.openDialog()
        },
        updateText(numSkycars) {
            let numSlot = this.result.filter(slot => !slot.is_blocked).length
            return `Skycars to be parked: ${numSkycars}, Parking slot available: ${numSlot}.`
        }
    }
}
</script>
