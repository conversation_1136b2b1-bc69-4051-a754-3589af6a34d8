<template>
    <v-card
        :color="cardColor"
        elevation="5"
    >
        <v-card-title>
            <v-row>
                {{ cardTitle }}
                <v-spacer></v-spacer>
                <v-btn
                    @click="btnUndock()"
                    icon
                >
                    <v-icon class="pulse-icon">mdi-connection</v-icon>
                </v-btn>
                <v-btn
                    @click="btnConfig()"
                    icon
                >
                    <v-icon class="pulse-icon">mdi-cog</v-icon>
                </v-btn>
            </v-row>
        </v-card-title>
        <v-card-text>
            <v-row>
                <v-spacer></v-spacer>
                <ProgressCircular 
                    class="mt-2"
                    :doneSync="doneSync"
                    color="white"
                />
                <v-btn
                    class="mx-1"
                    rounded
                    @click="btnPowerDownCube(false)"
                    :disabled="!doneSync"
                >
                    Confirm
                </v-btn>
            </v-row>
        </v-card-text>
        <v-progress-linear
            v-if="stepper[stepper.length-1].status==Status.A"
            indeterminate
            color="blue darken-2"
        ></v-progress-linear>
        <v-stepper
            dark
            alt-labels
        >
            <v-stepper-header>
                <template
                    v-for="(step, index) in stepper"
                    >
                    <v-stepper-step
                        :key="step.title"
                        :step="index + 1"
                        :complete="step.status==Status.C"
                        :rules="[() => step.status != Status.E]"
                    >
                        <small>{{step.title}}</small>
                    </v-stepper-step>
                    <v-divider 
                        v-if="index < stepper.length - 1"
                        :key="step.index"
                    ></v-divider>
                </template>
            </v-stepper-header>
        </v-stepper>
        <DialogPreferCoord
            :showNotification="showNotification"
            :cube="cube"
            ref="dialogPreferCoord"
        />
        <DialogUndock
            :showNotification="showNotification"
            ref="dialogUndock"
        />
        <DialogShowStationBin
            @forcePowerDown="forcePowerDown"
            ref="dialogShowStationBin"
        />
    </v-card>
</template>

<script>
import { flagStageStatus, Status } from "../util"
import { getRequestHeader, getHost, getCMHost, useRefreshToken } from "../../../helper/common"
import { RouteOperation, routeCM } from "../../../helper/enums"
import DialogPreferCoord from "./DialogPreferCoord.vue"
import DialogShowStationBin from "./DialogShowStationBin.vue"
import DialogUndock from "./DialogUndock.vue"
import ProgressCircular from "../../shared/ProgressCircular.vue"

export default {
    components: {
        DialogPreferCoord,
        DialogShowStationBin,
        DialogUndock,
        ProgressCircular
    },
    props: {
        cube: {
            type: String
        },
        showNotification: {
            type: Function
        },
        clearNotification: {
            type: Function
        }
    },
    data: () => ({
        Status,
        doneSync: true,
        cardColor: "yellow",
        cardTitle: "End of Cube: Power Down Cube",
        stepper: [
            {
                title: "Finishing Job",
                status: Status.C
            },
            {
                title: "Parking",
                status: Status.C
            },
            {
                title: "Completed",
                status: Status.C
            }
        ]
    }),
    methods: {
        async getChargingSkycars() {
            try {
                let url = new URL(getCMHost() + routeCM.CHECK_CHARGING_SKYCARS)
                let res = await fetch(url, {
                    method: "GET",
                    headers: getRequestHeader(),
                })
                let json = await res.json()
                if (json.code === 401){ // If access token is unauthorized
                    // use refresh token to get new access token from auth server
                    return useRefreshToken(this, this.getChargingSkycars)
                }
                if (json.process_status) {
                    flagStageStatus(this.stepper, json.process_status, 0)
                }
            } catch (error) {
                alert(error)
            }
        },

        async btnPowerDownCube(bypass) {
            this.doneSync = false
            this.clearNotification()
            try {
                flagStageStatus(this.stepper, Status.A)
                await this.getChargingSkycars()

                let res = await fetch(`${getHost(this.cube)}${RouteOperation.CUBE}`, {
                    method: "POST",
                    headers: getRequestHeader(),
                    body: JSON.stringify({ start: false, bypass: bypass })
                })
                let json = await res.json()
                if (json.code === 401){ // If access token is unauthorized
                    // use refresh token to get new access token from auth server
                    return useRefreshToken(this, this.btnPowerDownCube, bypass)
                }
                if (!json.status) {
                    if (json.model.no_slot) {
                        this.$refs.dialogPreferCoord.openDialog()
                    }
                    if (json.model.rejection) {
                        this.$refs.dialogShowStationBin.openDialog(json.model.rejection)
                    }
                    flagStageStatus(this.stepper, Status.E)
                    this.showNotification(false, json.message)
                }
            } catch (error) {
                alert(error)
            } finally {
                setTimeout(() => {
                    this.doneSync = true
                }, 500)
            }
        },
        async forcePowerDown(){
            await this.btnPowerDownCube(true)
            this.$refs.dialogShowStationBin.closeDialog()
        },
        btnConfig() {
            this.$refs.dialogPreferCoord.openDialog()
        },
        btnUndock() {
            this.$refs.dialogUndock.openDialog()
        },
        fetchStatus(item) {
            if (item.status == Status.A) {
                flagStageStatus(this.stepper, Status.C)
            } else {
                this.flagStatus(Status.A)
                for (let stage = 0; stage < item.stage; stage++) {
                    this.flagStatus(Status.C, stage)
                }
            }
        },
        flagStatus(item) {
            if (item.status) {
                flagStageStatus(this.stepper, Status.C, item.stage)
                if (item.stage == null) {
                    this.showNotification(true, "Success to Power Down Cube")
                }
            } else {
                for (let stage = item.stage; stage < this.stepper.length; stage++) {
                    flagStageStatus(this.stepper, Status.E, stage)
                }
                if (item.no_slot) {
                    this.$refs.dialogPreferCoord.openDialog()
                }
                this.showNotification(false, item.message)
            }
        }
    }
}
</script>
