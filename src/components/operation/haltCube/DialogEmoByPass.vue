<template>
    <v-dialog
        v-model="dialogBool"
        width="600"
    >
        <v-card dark>
            <v-card-title>By Pass EMO button</v-card-title>
            <v-checkbox
                v-model="confirm"
                class="mx-3"
                label="I would like to by pass Emergency Stop Button to resume operation."
            ></v-checkbox>
            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn
                    text
                    @click="btnByPassEMO(false) && closeDialog()"
                    :disabled="!confirm"
                >
                    Confirm
                </v-btn>
                <v-btn
                    text
                    @click="closeDialog"
                >
                    Close
                </v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script>
export default {
    props: {
        btnByPassEMO: {
            type: Function
        }
    },
    data: () => ({
        dialogBool: false,
        confirm: false
    }),
    methods: {
        openDialog() {
            this.confirm = false
            this.dialogBool = true
        },
        closeDialog() {
            this.dialogBool = false
        }
    }
}
</script>
