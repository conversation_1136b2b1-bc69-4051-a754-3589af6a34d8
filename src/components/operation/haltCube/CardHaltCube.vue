<template>
    <v-card color="red" elevation="5">
        <v-card-title>Halt Cube: Emergency Stop
            <v-row>
                <v-spacer></v-spacer>
                <v-btn @click="btnConfig()" icon>
                    <v-icon class="pulse-icon">mdi-cog</v-icon>
                </v-btn>
            </v-row>
        </v-card-title>

        <v-card-text>
            <v-row>
                <v-spacer></v-spacer>
                <ProgressCircular class="mt-2" :doneSync="doneSync" color="white" />
                <v-btn v-if="status == Status.A" rounded @click="btnHaltCube(true)" :disabled="!doneSync">
                    Activate
                </v-btn>
                <v-btn v-else rounded @click="openDialog" :disabled="!doneSync">
                    Deactivate
                </v-btn>
            </v-row>
        </v-card-text>
        <v-progress-linear v-if="status == Status.P" indeterminate color="blue darken-2"></v-progress-linear>
        <v-stepper dark alt-labels>
            <v-stepper-header>
                <v-stepper-step step="1" :complete="status != Status.P">
                    <small>Halting Cube</small>
                </v-stepper-step>
                <v-divider></v-divider>
                <v-stepper-step step="2" :complete="status != Status.P">
                    <small>Completed</small>
                </v-stepper-step>
            </v-stepper-header>
        </v-stepper>
        <DialogHaltCube :btnHaltCube="btnHaltCube" ref="dialogHaltCube" />

        <DialogEmoByPass :btnByPassEMO="btnByPassEMO" ref="dialogEmoByPass" />
    </v-card>
</template>

<script>
import { getRequestHeader, getHost, useRefreshToken } from "../../../helper/common"
import { RouteOperation } from "../../../helper/enums"
import { Status } from "../util"
import DialogHaltCube from "./DialogHaltCube.vue"
import DialogEmoByPass from "./DialogEmoByPass.vue"
import ProgressCircular from "../../shared/ProgressCircular.vue"
export default {
    props: {
        showNotification: {
            type: Function
        },
        clearNotification: {
            type: Function
        },
        cube: {
            type: String
        }
    },
    components: {
        DialogHaltCube,
        DialogEmoByPass,
        ProgressCircular
    },
    data: () => ({
        Status,
        doneSync: true,
        status: Status.A
    }),
    methods: {
        async btnHaltCube(activate) {
            this.doneSync = false
            this.clearNotification()
            try {
                var prevStatus = this.status
                this.status = Status.P
                let res = await fetch(`${getHost(this.cube)}${RouteOperation.HALT_CUBE}`, {
                    method: "POST",
                    body: JSON.stringify({ activate: activate }),
                    headers: getRequestHeader()
                })
                let json = await res.json()
                if (json.code === 401) { // If access token is unauthorized
                    // use refresh token to get new access token from auth server 
                    return useRefreshToken(this, this.btnHaltCube, activate)
                }
                if (json.status) {
                    if (activate) {
                        if (json.model.is_completed) {
                            this.status = Status.C
                            this.showNotification(true, "Halt cube is activated")
                        } else {
                            this.showNotification(true, "Halt cube is activating")
                        }
                    } else {
                        this.status = Status.A
                        this.showNotification(true, "Halt cube is deactivated")
                    }
                } else {
                    this.status = prevStatus
                    this.showNotification(false, json.model.message)
                }
            } catch (error) {
                alert(error)
            } finally {
                setTimeout(() => {
                    this.doneSync = true
                }, 500)
            }
        },
        fetchStatus(data) {
            this.status = data.status
        },
        updateStatus(status) {
            if (!status) {
                this.status = Status.A
            } else if (this.status != Status.P) {
                this.status = Status.C
            }
        },
        flagStatus(item) {
            if (item.status) {
                this.status = Status.C
                this.showNotification(true, "Halt cube is activated")
            }
        },
        openDialog() {
            this.$refs.dialogHaltCube.openDialog()
        },
        btnConfig() {
            this.$refs.dialogEmoByPass.openDialog()
        },
        async btnByPassEMO() {
            try {
                let res = await fetch(`${getHost(this.cube)}${RouteOperation.BY_PASS_CUBE_EMO}`, {
                    method: "POST",
                    body: JSON.stringify({ bypass: true }),
                    headers: getRequestHeader()
                })
                let json = await res.json()

                if (json.code === 401) {
                    return useRefreshToken(this, this.btnByPassEMO)
                }

                if (json.code == 200) { this.showNotification(true, json.message); return }
                this.showNotification(false, json.message)
            } catch (error) {
                alert(error)
            }

        }
    }
}
</script>

