<template>
  <v-card 
    :color="cardColor" 
    elevation="5"
  >
    <v-card-title>
      {{ cardTitle }}
      <v-chip
        v-if="qrEnabled"
        class="ml-2"
        small
        color="success"
        text-color="white"
      >
        <v-icon small left>mdi-qrcode</v-icon>
        QR Rail is activated...
      </v-chip>
    </v-card-title>
    <v-card-text>
      <v-row>
        <v-spacer />
        <ProgressCircular 
          class="mt-2" 
          :doneSync="doneSync" 
          color="white" 
        />
        <v-btn
          class="mx-1"
          rounded
          @click="btnToggleQR(true)"
          :disabled="!doneSync || qrEnabled"
          color="success"
        >
          Enable QR
        </v-btn>

        <v-btn 
          class="mx-1" 
          rounded 
          @click="btnToggleQR(false)"
          :disabled="!doneSync || !qrEnabled"
          color="error"
        >
          Disable QR
        </v-btn>
      </v-row>
    </v-card-text>
    <v-stepper 
      dark 
      alt-labels
    >
      <v-stepper-header>
        <template>
          <v-stepper-step
            key="QR Status"
            :step="0"
            :complete="qrEnabled"
          >
            <small>{{ qrEnabled ? 'QR Enabled' : 'QR Disabled' }}</small>
          </v-stepper-step>
        </template>
      </v-stepper-header>
    </v-stepper>

  </v-card>
</template>

<script>

import {
  getHost,
  useRefreshToken,
} from "../../../helper/common";
import { routeMatrix } from "../../../helper/enums";
import ProgressCircular from "../../shared/ProgressCircular.vue";
let httpRequest = require("../../../helper/http_request.js");

export default {
  components: {
    ProgressCircular
  },
  props: {
    cube: {
      type: String,
    },
    showNotification: {
      type: Function,
    },
    clearNotification: {
      type: Function,
    },
  },
  data: () => ({
    doneSync: true, // Controls button enabled/disabled state
    cardColor: "blue",
    cardTitle: "Rail QR Enroll",
    qrEnabled: false
  }),
  async created() {
    this.doneSync = false;
    await this.getQRStatus();
    this.doneSync = true;
  },
  methods: {
    async getQRStatus() {
      try {
        let res = await httpRequest.axiosRequest(
          "GET",
          getHost(this.cube),
          routeMatrix.gridQr
        );
        
        if (res.status === 401) {
          // If access token is unauthorized
          // use refresh token to get new access token from auth server
          return useRefreshToken(this, this.getQRStatus);
        }
        
        if (res.data && res.data.status && res.data.model) {
          this.qrEnabled = res.data.model.status || false;
        } else {
          console.warn('Unexpected QR status response:', res.data);
        }
      } catch (error) {
        console.error('Failed to fetch QR status:', error);
        // Keep default values on error
      }
    },
    async btnToggleQR(enable) {
      this.doneSync = false;
      this.clearNotification();
      
      try {
        const route = `${routeMatrix.gridQr}?toogle=${enable}`;
        
        let res = await httpRequest.axiosRequest(
          "POST",
          getHost(this.cube),
          route,
          {}
        );
        
        if (res.status === 401) {
          // If access token is unauthorized
          // use refresh token to get new access token from auth server
          this.doneSync = true; // Reset before recursive call
          return useRefreshToken(this, () => this.btnToggleQR(enable));
        }
        
        if (res.status >= 400) {
          // Handle 400 and 500 errors - refresh status to get actual server state
          this.showNotification(false, res.data?.message || `Failed to toggle QR (${res.status})`);
          await this.getQRStatus(); // Refresh to get actual status
        } else if (res.data && res.data.status) {
          this.qrEnabled = enable;
          this.showNotification(true, `QR ${enable ? 'enabled' : 'disabled'} successfully`);
        } else {
          this.qrEnabled = !enable;
          this.showNotification(false, res.data?.message || 'Failed to toggle QR');
          await this.getQRStatus(); // Refresh to get actual status
        }
      } catch (error) {
        console.error('QR toggle error:', error);
        this.showNotification(false, error.message || 'Failed to toggle QR');
        await this.getQRStatus(); // Refresh to get actual status
      } finally {
        // Reset doneSync to true to stop progress bar and re-enable buttons
        this.doneSync = true;
      }
    },
    fetchStatus(item) {
      // This method can be used to update status from parent component
      // Handle both direct status and model.status structure
      if (item && typeof item === 'object') {
        this.qrEnabled = item.model ? item.model.status : (item.status || false);
      } else {
        this.qrEnabled = item || false;
      }
    },
    flagStatus(item) {
      if (item && typeof item === 'object') {
        // Handle both direct status and model.status structure
        const qrStatus = item.model ? item.model.status : item.status;
        if (qrStatus !== undefined) {
          this.qrEnabled = qrStatus;
          this.showNotification(true, `QR ${this.qrEnabled ? 'enabled' : 'disabled'}`);
        } else {
          this.showNotification(false, item.message || 'Failed to update QR status');
        }
      } else {
        this.showNotification(false, 'Invalid QR status data');
      }
    },
  },
};
</script> 