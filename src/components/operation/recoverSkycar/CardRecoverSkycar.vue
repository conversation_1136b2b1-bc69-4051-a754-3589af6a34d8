<template>
    <v-card
        color="lime"
        elevation="5"
    >
        <v-card-title>Recover Skycar</v-card-title>
        <v-card-text>
            <v-row>
                <v-spacer></v-spacer>
                <ProgressCircular 
                    class="mt-2"
                    :doneSync="doneSync"
                    color="white"
                />
                <v-btn
                    rounded
                    @click="btnShowSkycar"
                    :disabled="!doneSync"
                >
                    Confirm
                </v-btn>
            </v-row>
        </v-card-text>
        <v-progress-linear
            v-if="!doneSync"
            indeterminate
            color="blue darken-2"
        ></v-progress-linear>
        <v-stepper
            dark
            alt-labels
        >
            <v-stepper-header>
                <v-stepper-step
                    step="1"
                    :complete="doneSync"
                >
                    <small>Fetching</small>
                </v-stepper-step>
                <v-divider></v-divider>
                <v-stepper-step
                    step="2"
                    :complete="doneSync"
                >
                    <small>Completed</small>
                </v-stepper-step>
            </v-stepper-header>
        </v-stepper>
        <DialogRecoverSkycar 
            :showNotification="showNotification"
            :clearNotification="clearNotification"
            :cube="cube"
            ref="dialogRecoverSkycar"
        />
    </v-card>
</template>

<script>
import axios from "axios"
import { getRequestHeader, getHost, useRefreshToken } from "../../../helper/common"
import { SkycarError } from "../../../helper/enums"
import DialogRecoverSkycar from "./DialogRecoverSkycar.vue"
import ProgressCircular from "../../shared/ProgressCircular.vue"
export default {
    components: {
        DialogRecoverSkycar,
        ProgressCircular
    },
    props: {
        showNotification: {
            type: Function
        },
        clearNotification: {
            type: Function
        },
        cube: {
            type: String
        }
    },
    data: () => ({
        doneSync: true
    }),
    methods: {
        async btnShowSkycar() {
            this.clearNotification()
            if (await this.updateErrorSkycar()) {
                this.$refs.dialogRecoverSkycar.openDialog()
            } else {
                this.showNotification(false, "No skycar with error status is found")
            }
        },
        async updateErrorSkycar() {
            this.doneSync = false
            try {
                let res = await axios.get(
                    `${getHost(this.cube)}${SkycarError.recoverError}`, 
                    { headers:getRequestHeader() }
                )
                let skycars = res.data.data.sid_list
                if (skycars.length > 0) {
                    this.$refs.dialogRecoverSkycar.updateSkycars(skycars)
                    return true
                } else {
                    return false
                }
            } catch (error) {
                if (error.response.status === 401){ // If access token is unauthorized
                    // use refresh token to get new access token from auth server
                    return useRefreshToken(this, this.updateErrorSkycar)
                }
                alert(error)
                return false
            } finally {
                setTimeout(() => {
                    this.doneSync = true
                }, 500)
            }
        }
    }
}
</script>
