<template>
        <v-dialog
            v-model="dialogBool"
        >
            <v-card dark>
                <v-card-title>Error Skycar</v-card-title>
                    <v-simple-table>
                        <template v-slot:default>
                            <thead>
                                <tr>
                                    <th class="text-center">Skycar</th>
                                    <th class="text-left">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr
                                    v-for="skycar in skycars"
                                    :key="skycar"
                                >
                                    <td class="text-center">{{ skycar }}</td>
                                    <td>
                                        <v-btn
                                            class="mx-2"
                                            color="green"
                                            @click="btnRecoverSkycar(SkycarRecovery.REVIVE, skycar)"
                                        >
                                            Revive
                                        </v-btn>
                                        <v-btn
                                            class="mx-2"
                                            color="orange"
                                            @click="btnRecoverSkycar(SkycarRecovery.AUTO_CHARGE_OUT, skycar)"
                                        >
                                            Auto Charge Out
                                        </v-btn>
                                        <v-btn
                                            class="mx-2"
                                            color="lime"
                                            @click="btnRecoverSkycar(SkycarRecovery.INSPECT, skycar)"
                                        >
                                            Inspect
                                        </v-btn>
                                    </td>
                                </tr>
                            </tbody>
                        </template>
                    </v-simple-table>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn
                        text
                        @click="closeDialog"
                    >Close</v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
</template>

<script>
import { getRequestHeader, getHost, useRefreshToken } from "../../../helper/common"
import { RouteOperation, SkycarRecovery } from "../../../helper/enums"
export default {
    props: {
        showNotification: {
            type: Function
        },
        clearNotification: {
            type: Function
        },
        cube: {
            type: String
        }
    },
    data: () => ({
        SkycarRecovery,
        dialogBool: false,
        skycars: []
    }),
    methods: {
        openDialog() {
            this.dialogBool = true
        },
        closeDialog() {
            this.dialogBool = false
        },
        updateSkycars(skycars) {
            this.skycars = skycars
        },
        async btnRecoverSkycar(mode, skycar) {
            try {
                this.clearNotification()
                let res = await fetch(`${getHost(this.cube)}${RouteOperation.SKYCAR_ACTION}`, {
                    method: "POST",
                    body: JSON.stringify({
                        mode: mode, 
                        sid: skycar, 
                        rmc: false 
                    }),
                    headers: getRequestHeader()
                })
                let json = await res.json()
                if (json.code === 401){ // If access token is unauthorized
                    // use refresh token to get new access token from auth server
                    return useRefreshToken(this, this.btnRecoverSkycar, mode, skycar)
                }
                if (json.status) {
                    this.closeDialog()
                } else {
                    this.showNotification(false, json.message)
                }
            } catch (error) {
                alert(error)
            }
        }
    }
}
</script>
