<template>
    <span>
        <v-card
            color="pink"
            elevation="5"
        >
            <v-card-title>Why cube is idle?</v-card-title>
            <v-card-text>
                <v-row>
                    <v-spacer></v-spacer>
                    <v-btn
                        :disabled="!doneSync"
                        rounded
                        @click="btnCheck()"
                    >
                        Check
                    </v-btn>
                </v-row>
            </v-card-text>
            <v-progress-linear
                v-if="!doneSync"
                indeterminate
                color="blue darken-2"
            ></v-progress-linear>
            <v-stepper
                dark
                alt-labels
            >
                <v-stepper-header>
                    <v-stepper-step
                        step="1"
                        :complete="doneSync"
                    >
                        <small>Checking</small>
                    </v-stepper-step>
                    <v-divider></v-divider>
                    <v-stepper-step
                        step="2"
                        :complete="doneSync"
                    >
                        <small>Completed</small>
                    </v-stepper-step>
                </v-stepper-header>
            </v-stepper>
        </v-card>
        <DialogHealthCheck
            :showNotification="showNotification"
            :cube="cube"
            ref="dialogHealthCheck"
        />
    </span>
</template>

<script>
import DialogHealthCheck from './DialogHealthCheck.vue'

export default {
    components: {
        DialogHealthCheck
    },
    props: {
        showNotification: {
            type: Function
        },
        cube: {
            type: String
        }
    },
    data: () => ({
        doneSync: true
    }),
    methods: {
        async btnCheck() {
            try {
                this.doneSync = false
                await this.$refs.dialogHealthCheck.btnCheck()
            } finally {
                this.doneSync = true
            }
        },
    }
}
</script>