<template>
    <v-list-item v-if="!getValid | !showInvalid">
        <IconValid :valid="getValid"/>
        <v-list-item-content>
            <v-list-item-title>Errored Planning</v-list-item-title>
            <v-list-item-subtitle>{{ getSubtitle() }}</v-list-item-subtitle>
        </v-list-item-content>
    </v-list-item>
</template>

<script>
import IconValid from './IconValid.vue'
import { getColor, getIcon } from './utils'
export default {
    components: {
        IconValid
    },
    props: {
        erroredPlanning: {
            type: String
        },
        lastPlan: {
            type: String
        },
        showInvalid: {
            type: Boolean
        }
    },
    created() {
        
    },
    data: () => ({

    }),
    methods: {
        getColor,
        getIcon,
        getSubtitle() {
            if (this.getValid) {
                return `No error from planning since ${this.lastPlan}.`
            } else {
                return this.erroredPlanning
            }
        }
    },
    computed: {
        getValid() {
            return !this.erroredPlanning
        }
    },
    watch: {
        
    }
}
</script>
