<template>
    <v-list-item v-if="!getValid | !showInvalid">
        <IconValid :valid="getValid"/>
        <v-list-item-content>
            <v-list-item-title>Enqueue Status</v-list-item-title>
            <v-list-item-subtitle>{{ getSubtitle() }}</v-list-item-subtitle>
        </v-list-item-content>
    </v-list-item>
</template>

<script>
import IconValid from "./IconValid.vue"
import { getColor, getIcon } from "./utils"
export default {
    components: {
        IconValid
    },
    props: {
        enqueueStatus: {
            type: String
        },
        showInvalid: {
            type: Boolean
        }
    },
    created() {
        
    },
    data: () => ({

    }),
    methods: {
        getColor,
        getIcon,
        getSubtitle() {
            if (this.getValid) {
                return "No error from enqueue."
            } else {
                return this.enqueueStatus
            }
        }
    },
    computed: {
        getValid() {
            return !this.enqueueStatus
        }
    },
    watch: {
        
    }
}
</script>
