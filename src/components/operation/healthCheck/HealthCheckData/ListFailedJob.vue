<template>
    <v-list-group
        v-if="!getValid | !showInvalid"
        no-action
    >
        <v-icon
            slot="prependIcon"
            :color="getColor(getValid)"
        >{{ getIcon(getValid) }}
        </v-icon>
        <template v-slot:activator>
            <v-list-item-content>
                <v-list-item-title>Inexecutable Jobs</v-list-item-title>
                <v-list-item-subtitle>{{ getSubtitle() }}</v-list-item-subtitle>
            </v-list-item-content>
        </template>
        <v-list-item
            v-for="job in failedJobs"
            :key="job"
        >
            <v-chip
                color="red"
                dark
            >
                {{ job }}
            </v-chip>
        </v-list-item>
        <v-list-item v-if="getValid"> {{ getSubtitle() }}</v-list-item>
    </v-list-group>
</template>

<script>
import { getColor, getIcon } from './utils'
export default {
    components: {
        
    },
    props: {
        failedJobs: {
            type: Array
        },
        showInvalid: {
            type: Boolean
        }
    },
    created() {
        
    },
    data: () => ({

    }),
    methods: {
        getColor,
        getIcon,
        getSubtitle() {
            if (this.getValid) {
                return `No inexecutable job is found.`
            } else {
                return `${this.failedJobs.length} inexecutable jobs are found.`
            }
        }
    },
    computed: {
        getValid() {
            return this.failedJobs.length == 0
        }
    },
    watch: {
        
    }
}
</script>
