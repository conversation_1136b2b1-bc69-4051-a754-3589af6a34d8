<template>
    <v-list-group
        v-if="!getValid | !showInvalid"
        no-action
    >
        <v-icon
            slot="prependIcon"
            :color="getColor(getValid)"
        >{{ getIcon(getValid) }}
        </v-icon>
        <template v-slot:activator>
            <v-list-item-content>
                <v-list-item-title>Skycar in Obstacle</v-list-item-title>
                <v-list-item-subtitle>{{ getSubtitle() }}</v-list-item-subtitle>
            </v-list-item-content>
        </template>
        <v-list-item
            v-for="(coord, skycar) in skycarInObstacle"
            :key="skycar"
        >
            <v-chip
                color="red"
                dark
            >
                Skycar {{ skycar }} in Obstacle {{ coord }}.
            </v-chip>
        </v-list-item>
        <v-list-item v-if="getValid"> {{ getSubtitle() }}</v-list-item>
    </v-list-group>
</template>

<script>
import { getColor, getIcon } from "./utils"
export default {
    props: {
        skycarInObstacle: {
            type: Object
        },
        showInvalid: {
            type: Boolean
        }
    },
    methods: {
        getColor,
        getIcon,
        getSubtitle() {
            if (this.getValid) {
                return "No skycar in obstacle."
            } else {
                return `${this.getLength} skycars in obstacle.`
            }
        }
    },
    computed: {
        getValid() {
            return this.getLength == 0
        },
        getLength() {
            return Object.keys(this.skycarInObstacle).length
        }
    }
}
</script>
