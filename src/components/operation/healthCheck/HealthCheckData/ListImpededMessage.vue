<template>
    <v-list-group
        v-if="!getValid | !showInvalid"
        no-action
    >
        <v-icon
            slot="prependIcon"
            :color="getColor(getValid)"
        >{{ getIcon(getValid) }}
        </v-icon>
        <template v-slot:activator>
            <v-list-item-content>
                <v-list-item-title>Impeded Messages</v-list-item-title>
                <v-list-item-subtitle>{{ getSubtitle() }}</v-list-item-subtitle>
            </v-list-item-content>
        </template>
        <v-row class="ml-2">
            <v-col
                v-for="(message, index) in impededMessages"
                :key="index"
                cols="12"
                md="4"
                sm="6"
                lg="4"
            >
                <v-card>
                    <v-card-title>{{ message.title }}</v-card-title>
                    <v-card-subtitle>{{ message.text }}</v-card-subtitle>
                    <v-card-text>
                        <v-col>
                            <v-row
                                v-for="(value, key) in showBody(message)"
                                :key="key"
                            >
                                <span style="font-size: 15px;">
                                    <strong>{{ key }}</strong>: {{ value }}
                                </span>
                            </v-row>
                        </v-col>
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>
        <v-list-item v-if="getValid"> {{ getSubtitle() }}</v-list-item>
    </v-list-group>
</template>

<script>
import { getColor, getIcon } from "./utils"
export default {
    components: {
        
    },
    props: {
        impededMessages: {
            type: Array
        },
        showInvalid: {
            type: Boolean
        }
    },
    created() {
        
    },
    data: () => ({

    }),
    methods: {
        getColor,
        getIcon,
        getSubtitle() {
            if (this.getValid) {
                return "No impeded message is found."
            } else {
                return "Impeded messages are found."
            }
        },
        showBody(message) {
            return Object.entries(message).reduce((acc, [key, value]) => {
                if (key != "text" && key != "title") {
                    acc[key] = value
                }
                return acc
            }, {})
        }
    },
    computed: {
        getValid() {
            return this.impededMessages.length == 0
        }
    },
    watch: {
        
    }
}
</script>
