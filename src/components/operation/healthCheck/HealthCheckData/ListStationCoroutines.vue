<template>
  <v-list-group
    v-if="!getValid | !showInvalid"
    no-action
  >
    <v-icon
      slot="prependIcon"
      :color="getColor(getValid)"
    >
      {{ getIcon(getValid) }}
    </v-icon>
    <template v-slot:activator>
      <v-list-item-content>
        <v-list-item-title>Station Coroutines</v-list-item-title>
        <v-list-item-subtitle>{{ getSubtitle() }}</v-list-item-subtitle>
      </v-list-item-content>
    </template>
    <v-list-item v-if="!getValid">
      <v-chip
        v-for="station in stationCoroutines"
        :key="station"
        class="mr-1"
        color="red"
        dark
      >
        {{ station }}
      </v-chip>
    </v-list-item>
    <v-list-item v-else> 
      {{ getSubtitle() }}
    </v-list-item>
  </v-list-group>
</template>

<script>
import { getColor, getIcon } from "./utils"
export default {
    props: {
        stationCoroutines: {
            type: Array
        },
        showInvalid: {
            type: Boolean
        }
    },
    methods: {
        getColor,
        getIcon,
        getSubtitle() {
            if (this.getValid) {
                return "All coroutines are alive."
            } else {
                return `${this.stationCoroutines.length} dead coroutines are found.`
            }
        }
    },
    computed: {
        getValid() {
            return this.stationCoroutines.length == 0
        }
    }
}
</script>
