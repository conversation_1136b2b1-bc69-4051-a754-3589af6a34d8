<template>
    <v-list-item v-if="!status | !showInvalid">
        <IconValid :valid="status"/>
        <v-list-item-content>
            <v-list-item-title>Cycle Status</v-list-item-title>
            <v-list-item-subtitle>{{ getSubtitle() }}</v-list-item-subtitle>
        </v-list-item-content>
    </v-list-item>
</template>

<script>
import IconValid from "./IconValid.vue";
export default {
    components: {
        IconValid
    },
    props: {
        status: {
            type: Boolean
        },
        updatedAt: {
            type: String
        },
        reason: {
            type: String
        },
        username: {
            type: String
        },
        showInvalid: {
            type: Boolean
        }
    },
    methods: {
        getSubtitle() {
            if (this.status) {
                return `TC was started at ${this.updatedAt} by ${this.username}.`
            } else {
                return `TC was stopped at ${this.updatedAt} due to ${this.reason} by ${this.username}.`
            }
        }
    }
}
</script>
