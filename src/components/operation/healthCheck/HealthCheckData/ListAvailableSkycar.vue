<template>
  <v-list-group
    v-if="!getValid | !showInvalid"
    no-action
  >
    <v-icon
      slot="prependIcon"
      :color="getColor(getValid)"
    >
      {{ getIcon(getValid) }}
    </v-icon>
    <template v-slot:activator>
      <v-list-item-content>
        <v-list-item-title>Available Skycars</v-list-item-title>
        <v-list-item-subtitle>{{ getSubtitle() }}</v-list-item-subtitle>
      </v-list-item-content>
    </template>
    <v-list-item v-if="getValid">
      <v-chip
        v-for="skycar in availableSkycars"
        :key="skycar"
        class="mr-1"
        color="green"
        dark
      >
        Skycar {{ skycar }}
      </v-chip>
    </v-list-item>
    <v-list-item v-if="!getValid">
      {{ getSubtitle() }}
    </v-list-item>
  </v-list-group>
</template>

<script>
import { getColor, getIcon } from "./utils";
export default {
  components: { },
  props: {
    availableSkycars: {
      default: () => Array(),
      type: Array
    },
    showInvalid: {
      type: Boolean,
    },
  },
  created() {},
  data: () => ({}),
  methods: {
    getIcon,
    getColor,
    getSubtitle() {
      if (this.getValid) {
        return `${this.availableSkycars.length} available skycars are found.`;
      } else {
        return "No available skycar is found.";
      }
    },
  },
  computed: {
    getValid() {
      return this.availableSkycars.length > 0;
    },
  },
  watch: {},
};
</script>
