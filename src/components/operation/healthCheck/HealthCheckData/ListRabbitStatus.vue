<template>
  <v-list-group 
      v-if="!getValid || !showInvalid"  
      no-action
  >
      <v-icon
          slot="prependIcon"
          :color="getColor(getValid)"
      >{{ getIcon(getValid) }}
      </v-icon>
      <template v-slot:activator>
          <v-list-item-content>
              <v-list-item-title>RabbitMQ Status</v-list-item-title>
              <v-list-item-subtitle>{{ getSubtitle() }}</v-list-item-subtitle>
          </v-list-item-content>
      </template>
      <v-list-item v-if="!getValid">
          <v-chip
              class="mr-1"
              color="red"
              dark
          >
              {{ status.message }}
          </v-chip>
      </v-list-item>
      <v-list-item v-else>
          <v-list-item-content>
              <v-list-item-subtitle>
                  <div>Host: {{ status.details.host }}:{{ status.details.port }}</div>
              </v-list-item-subtitle>
          </v-list-item-content>
      </v-list-item>
  </v-list-group>
</template>

<script>
import { getColor, getIcon } from "./utils"

export default {
  props: {
      status: {
          type: Object,
          required: true
      },
      showInvalid: {
          type: Boolean,
          required: true
      }
  },
  methods: {
      getColor,
      getIcon,
      getSubtitle() {
          if (this.getValid) {
              return "RabbitMQ service is running."
          } else {
              return this.status.message || "RabbitMQ service status unknown"
          }
      }
  },
  computed: {
      getValid() {
          return this.status && this.status.status === 'healthy'
      }
  }
}
</script>