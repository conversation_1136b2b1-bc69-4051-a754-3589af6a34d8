<template>
    <v-list-item v-if="!getValid | !showInvalid">
        <IconValid :valid="getValid"/>
        <v-list-item-content>
            <v-list-item-title>Main Thread Status</v-list-item-title>
            <v-list-item-subtitle>{{ getSubtitle() }}</v-list-item-subtitle>
        </v-list-item-content>
    </v-list-item>
</template>

<script>
import IconValid from "./IconValid.vue";
export default {
    components: {
        IconValid
    },
    props: {
        mainThreadStatus: {
            type: Boolean
        },
        showInvalid: {
            type: Boolean
        }
    },
    created() {
        
    },
    data: () => ({

    }),
    methods: {
        getSubtitle() {
            if (this.getValid) {
                return "Main thread is alive."
            } else {
                return "Please validate TC status and restart TC."
            }
        }
    },
    computed: {
        getValid() {
            return this.mainThreadStatus == true
        },
    },
    watch: {
        
    }
}
</script>
