<template>
    <v-list-group
        v-if="!getValid | !showInvalid"
        no-action
    >
        <v-icon
            slot="prependIcon"
            :color="getColor(getValid)"
        >{{ getIcon(getValid) }}
        </v-icon>
        <template v-slot:activator>
            <v-list-item-content>
                <v-list-item-title>Errored Skycars</v-list-item-title>
                <v-list-item-subtitle>{{ getSubtitle() }}</v-list-item-subtitle>
            </v-list-item-content>
        </template>
        <v-list-item v-if="!getValid">
            <v-chip
                v-for="skycar in erroredSkycars"
                :key="skycar"
                class="mr-1"
                color="red"
                dark
            >
                Skycar {{ skycar }}
            </v-chip>
        </v-list-item>
        <v-list-item v-else> {{ getSubtitle() }}</v-list-item>
    </v-list-group>
</template>

<script>
import { getColor, getIcon } from './utils'
export default {
    components: {
        
    },
    props: {
        erroredSkycars: {
            type: Array
        },
        showInvalid: {
            type: Boolean
        }
    },
    created() {
        
    },
    data: () => ({

    }),
    methods: {
        getColor,
        getIcon,
        getSubtitle() {
            if (this.getValid) {
                return `No errored skycar is found.`
            } else {
                return `${this.erroredSkycars.length} errored skycars are found.`
            }
        }
    },
    computed: {
        getValid() {
            return this.erroredSkycars.length == 0
        }
    },
    watch: {
        
    }
}
</script>
