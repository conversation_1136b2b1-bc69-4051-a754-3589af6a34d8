<template>
    <v-list-group
        v-if="!getValid | !showInvalid"
        no-action
    >
        <v-icon
            slot="prependIcon"
            :color="getColor(getValid)"
        >{{ getIcon(getValid) }}
        </v-icon>
        <template v-slot:activator>
            <v-list-item-content>
                <v-list-item-title>Unpaired Skycars</v-list-item-title>
                <v-list-item-subtitle>{{ getSubtitle() }}</v-list-item-subtitle>
            </v-list-item-content>
        </template>
        <v-list-item v-if="!getValid">
            <v-chip
                v-for="skycar in unpairedSkycars"
                :key="skycar"
                class="mr-1"
                color="red"
                dark
            >
                Skycar {{ skycar }}
            </v-chip>
        </v-list-item>
        <v-list-item v-if="getValid"> {{ getSubtitle() }}</v-list-item>
    </v-list-group>
</template>

<script>
import { getColor, getIcon } from './utils'
export default {
    components: {
        
    },
    props: {
        unpairedSkycars: {
            type: Array
        },
        showInvalid: {
            type: Boolean
        }
    },
    created() {
        
    },
    data: () => ({

    }),
    methods: {
        getIcon,
        getColor,
        getSubtitle() {
            if (this.getValid) {
                return `No unpaired skycar is found.`
            } else {
                return `${this.unpairedSkycars.length} unpaired skycars are found.`
            }
        }
    },
    computed: {
        getValid() {
            return this.unpairedSkycars.length == 0
        }
    },
    watch: {
        
    }
}
</script>
