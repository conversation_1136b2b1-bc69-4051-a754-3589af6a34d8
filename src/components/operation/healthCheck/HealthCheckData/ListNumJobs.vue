<template>
    <v-list-item v-if="!getValid | !showInvalid">
        <IconValid :valid="getValid"/>
        <v-list-item-content>
            <v-list-item-title>Job Status</v-list-item-title>
            <v-list-item-subtitle>{{ getSubtitle() }}</v-list-item-subtitle>
        </v-list-item-content>
    </v-list-item>
</template>

<script>
import IconValid from './IconValid.vue';
export default {
    components: {
        IconValid
    },
    props: {
        numJobs: {
            type: Number
        },
        showInvalid: {
            type: Boolean
        }
    },
    created() {
        
    },
    data: () => ({

    }),
    methods: {
        getSubtitle() {
            if (this.getValid) {
                return `TC currently has ${this.numJobs} jobs.`
            } else {
                return `TC currently has no job.`
            }
        }
    },
    computed: {
        getValid() {
            return this.numJobs != 0
        },
    },
    watch: {
        
    }
}
</script>
