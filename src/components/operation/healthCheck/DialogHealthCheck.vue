<template>
  <v-dialog
    v-model="dialogBool"
  >
    <v-card>
      <v-toolbar
        color="pink"
        dark
      >
        <v-toolbar-title>Health Check</v-toolbar-title>
      </v-toolbar>
      <v-col>
        <v-list>
          <ListCyclestatus
            :status="cycleStop.status"
            :updated-at="cycleStop.updatedAt"
            :reason="cycleStop.reason"
            :username="cycleStop.username"
            :show-invalid="showInvalid"
          />
          <ListNumJobs 
            :num-jobs="numJobs"
            :show-invalid="showInvalid"
          />
          <ListErrorSkycar 
            :errored-skycars="erroredSkycars"
            :show-invalid="showInvalid"
          />
          <ListErrorPlanning
            :errored-planning="erroredPlanning"
            :last-plan="lastPlan"
            :show-invalid="showInvalid"
          />
          <ListErrorEnqueue
            :enqueue-status="enqueueStatus"
            :show-invalid="showInvalid"
          />
          <ListImpededMessage
            :impeded-messages="impededMessages"
            :show-invalid="showInvalid"
          />
          <ListFailedJob
            :failed-jobs="failedJobs"
            :show-invalid="showInvalid"
          />
          <ListUnpairedSkycar
            :unpaired-skycars="unpairedSkycars"
            :show-invalid="showInvalid"
          />
          <ListAvailableSkycar
            :available-skycars="availableSkycars"
            :show-invalid="showInvalid"
          />
          <ListSkycarInObstacle
            :skycar-in-obstacle="skycarInObstacle"
            :show-invalid="showInvalid"
          />
          <ListSkycarCoroutines
            :skycar-coroutines="skycarCoroutines"
            :show-invalid="showInvalid"
          />
          <ListStationCoroutines
            :station-coroutines="stationCoroutines"
            :show-invalid="showInvalid"
          />
          <ListMainThreadStatus
            :main-thread-status="mainThreadStatus"
            :show-invalid="showInvalid"
          />
          <ListRabbitStatus  
            :status="rabbitStatus"
            :show-invalid="showInvalid"
          />
          <ListBTStatus
            :status="tcbtStatus"
            :show-invalid="showInvalid"
          />
          <ListSMStatus
            :status="smStatus"
            :show-invalid="showInvalid"
          />
          <ListCMStatus
            :status="cmStatus"
            :show-invalid="showInvalid"
          />
        </v-list>

        <v-card-actions>
          <v-checkbox
            v-model="showInvalid"
            label="Show Invalid Only."
          />
          <v-spacer />
          <ProgressCircular :done-sync="doneSync" />
          <v-btn
            color="green darken-1"
            text
            @click="btnCheck()"
            :disabled="!doneSync"
          >
            Refresh
          </v-btn>
          <v-btn
            color="green darken-1"
            text
            @click="closeDialog()"
          >
            Close
          </v-btn>
        </v-card-actions>
      </v-col>
    </v-card>
  </v-dialog>
</template>

<script>
import ListCyclestatus from "./HealthCheckData/ListCyclestatus.vue"
import ListErrorPlanning from "./HealthCheckData/ListErrorPlanning.vue"
import ListErrorSkycar from "./HealthCheckData/ListErrorSkycar.vue"
import ListFailedJob from "./HealthCheckData/ListFailedJob.vue"
import ListImpededMessage from "./HealthCheckData/ListImpededMessage.vue"
import ListNumJobs from "./HealthCheckData/ListNumJobs.vue"
import ListUnpairedSkycar from "./HealthCheckData/ListUnpairedSkycar.vue"
import ListAvailableSkycar from "./HealthCheckData/ListAvailableSkycar.vue"
import ListSkycarInObstacle from "./HealthCheckData/ListSkycarInObstacle.vue"
import ListSkycarCoroutines from "./HealthCheckData/ListSkycarCoroutines.vue"
import ListStationCoroutines from "./HealthCheckData/ListStationCoroutines.vue"
import ListMainThreadStatus from "./HealthCheckData/ListMainThreadStatus.vue"
import ListErrorEnqueue from "./HealthCheckData/ListErrorEnqueue.vue"
import ProgressCircular from "../../shared/ProgressCircular.vue"
import ListRabbitStatus from "./HealthCheckData/ListRabbitStatus.vue"
import ListBTStatus from "./HealthCheckData/ListBTStatus.vue"
import ListSMStatus from "./HealthCheckData/ListSMStatus.vue"
import ListCMStatus from "./HealthCheckData/ListCMStatus.vue"
import { convertStringToLocal, getHost, getRequestHeader, useRefreshToken } from "../../../helper/common"
import { RouteOperation, RouteStatus } from "../../../helper/enums"
import axios from "axios"
export default {
    components: {
    ListCyclestatus,
    ListErrorEnqueue,
    ListErrorPlanning,
    ListErrorSkycar,
    ListFailedJob,
    ListImpededMessage,
    ListNumJobs,
    ListUnpairedSkycar,
    ListAvailableSkycar,
    ListSkycarCoroutines,
    ListSkycarInObstacle,
    ListStationCoroutines,
    ListMainThreadStatus,
    ProgressCircular,
    ListRabbitStatus,
    ListBTStatus,
    ListSMStatus,
    ListCMStatus
},
    props: {
        showNotification: {
            type: Function
        },
        cube: {
            type: String
        }
    },
    data: () => ({
        dialogBool: false,
        showInvalid: false,
        doneSync: true,
        cycleStop: {
            status: null,
            updatedAt: null,
            reason: null,
            username: null
        },
        numJobs: null,
        erroredSkycars: null,
        erroredPlanning: null,
        enqueueStatus: null,
        lastPlan: null,
        impededMessages: null,
        failedJobs: null,
        unpairedSkycars: null,
        availableSkycars: null,
        skycarCoroutines: null,
        stationCoroutines: null,
        skycarInObstacle: null,
        mainThreadStatus: null,
        rabbitStatus: {
        status: 'unknown',
        message: 'Not checked',
        details: {}
        },
        tcbtStatus: {
          status:'unknown',
          message: 'Not checked',
          details: {}
        },
        smStatus: {
          status: 'unknown',
          message: 'Not checked',
          details: {}
        },
        cmStatus: {
          status: 'unknown',
          message: 'Not checked',
          details: {}
        }
    }),
    methods: {
        closeDialog() {
            this.dialogBool = false
        },
        getModel(model) {
            this.cycleStop = {
                status: model.cycle_stop.status,
                updatedAt: convertStringToLocal(model.cycle_stop.updated_at, true),
                reason: model.cycle_stop.reason,
                username: model.cycle_stop.username
            }
            this.numJobs = model.num_of_jobs
            this.erroredSkycars = model.error_skycars
            this.erroredPlanning = model.error_in_planning
            this.enqueueStatus = model.enqueue_error
            this.lastPlan = convertStringToLocal(model.last_plan, true)
            this.impededMessages = model.impeded_messages
            this.failedJobs = model.failed_job_rule
            this.unpairedSkycars = model.unpaired_sids
            this.availableSkycars = model.available_sids
            this.skycarCoroutines = model.skycar_coroutines
            this.stationCoroutines = model.station_coroutines
            this.skycarInObstacle = model.skycar_on_obstacle
            this.mainThreadStatus = model.main_thread_status
        },
        async btnCheck() {
          try {
              this.doneSync = false
              const healthCheckUrl = getHost(this.cube) + RouteOperation.HEALTH_CHECK
              const statusUrl = getHost(this.cube) + RouteStatus.ALL

              const [healthRes, statusRes] = await Promise.all([
                  axios.get(healthCheckUrl, { headers: getRequestHeader() }),
                  axios.get(statusUrl).catch(err => ({
                      data: {
                          services: {
                              rabbitmq: {
                                  status: 'unhealthy',
                                  message: 'Failed to fetch status',
                                  details: { error: err.message }
                              },
                              'tc-bt': {
                                  status: 'unhealthy',
                                  message: 'Failed to fetch status',
                                  details: { error: err.message }
                              },
                              'sm': {
                                  status: 'unhealthy',
                                  message: 'Failed to fetch status',
                                  details: { error: err.message }
                              },
                              'cm': {
                                  status: 'unhealthy',
                                  message: 'Failed to fetch status',
                                  details: { error: err.message }
                              }
                          }
                      }
                  }))
              ])

              if (healthRes.data.status) {
                  this.getModel(healthRes.data.model)
                  
                  // Update services status from the single API response
                  const services = statusRes.data?.services || {}
                  
                  this.rabbitStatus = services.rabbitmq || {
                      status: 'unknown',
                      message: 'Status not available',
                      details: {}
                  }
                  
                  this.tcbtStatus = services['tc-bt'] || {
                      status: 'unknown',
                      message: 'Status not available',
                      details: {}
                  }
                  
                  this.smStatus = services['sm'] || {
                      status: 'unknown',
                      message: 'Status not available',
                      details: {}
                  }

                  this.cmStatus = services['cm'] || {
                      status: 'unknown',
                      message: 'Status not available',
                      details: {}
                  }

                  this.dialogBool = true
                  this.showNotification(true, "Health Check is updated.")
              } else {
                  this.showNotification(false, healthRes.data.message)
                  this.closeDialog()
              }
          } catch (error) {
              if (error.response?.status === 401) {
                  return useRefreshToken(this, this.btnCheck)
              }
              this.showNotification(false, error)
              this.closeDialog()
          } finally {
              setTimeout(() => {
                  this.doneSync = true
              }, 500)
          }
      }
    }
  }

</script>
