<template>
  <v-app app>
    <v-col>
      <v-row>
        <v-container>
          <v-card>
            <v-container>
              <v-row>
                <v-col cols="2">
                  <v-select
                    v-model="cube"
                    label="Cube"
                    outlined
                    :items="getCube()"
                    @change="getOperationStatus"
                  />
                </v-col>
                <v-chip 
                  class="mt-3" 
                  :color="color" 
                  label 
                  outlined 
                  large
                >
                  {{ text }}
                </v-chip>
                <v-spacer />
                <v-btn
                  class="mx-2 mt-4"
                  @click="getOperationStatus"
                  color="primary"
                  :disabled="!doneSync"
                  right
                >
                  <v-progress-circular
                    v-if="!doneSync"
                    indeterminate
                    color="primary"
                    :size="15"
                    :width="1"
                  />
                  Refresh
                </v-btn>
              </v-row>
              <v-row>
                <v-col>
                  <CardInitiateCube
                    :cube="cube"
                    :showNotification="showNotification"
                    :clearNotification="clearNotification"
                    ref="cardInitiateCube"
                  />
                </v-col>
                <v-col>
                  <CardPowerDownCube
                    :cube="cube"
                    :showNotification="showNotification"
                    :clearNotification="clearNotification"
                    ref="cardPowerDownCube"
                  />
                </v-col>
                <v-col>
                  <CardReleaseBattery
                    :cube="cube"
                    :showNotification="showNotification"
                    :clearNotification="clearNotification"
                    ref="cardReleaseBattery"
                  />
                </v-col>
              </v-row>
              <v-row>
                <v-col>
                  <CardRecoverSkycar
                    :showNotification="showNotification"
                    :clearNotification="clearNotification"
                    :cube="cube"
                  />
                </v-col>
                <v-col>
                  <CardHaltCube
                    :showNotification="showNotification"
                    :clearNotification="clearNotification"
                    :cube="cube"
                    ref="cardHaltCube"
                  />
                </v-col>
                <v-col>
                  <CardHealthCheck
                    :showNotification="showNotification"
                    :cube="cube"
                  />
                </v-col>
              </v-row>
            </v-container>
          </v-card>
        </v-container>
      </v-row>
    </v-col>
    <SnackbarNotification ref="snackbarNotification" />
  </v-app>
</template>
<script>
import {
  convertStringToLocal,
  getCube,
  getHost,
  getMapping,
  useRefreshToken,
} from "../../helper/common.js";
import { RouteOperation, Websocket } from "../../helper/enums";
import { socket } from "../../App.vue";
import CardHaltCube from "./haltCube/CardHaltCube.vue";
import CardHealthCheck from "./healthCheck/CardHealthCheck.vue";
import CardInitiateCube from "./initiateCube/CardInitiateCube.vue";
import CardPowerDownCube from "./powerDownCube/CardPowerDownCube.vue";
import CardRecoverSkycar from "./recoverSkycar/CardRecoverSkycar.vue";
import CardReleaseBattery from "./releaseBattery/CardReleaseBattery.vue";
import SnackbarNotification from "../shared/SnackbarNotification.vue";
let httpRequest = require("../../helper/http_request.js");

export default {
  components: {
    CardHaltCube,
    CardInitiateCube,
    CardPowerDownCube,
    CardRecoverSkycar,
    CardReleaseBattery,
    CardHealthCheck,
    SnackbarNotification,
  },
  async created() {
    await this.getOperationStatus();
    this.getMessage();
  },
  data: () => ({
    getCube,
    cube: getCube()[0],
    doneSync: true,
    isStarted: null,
    reason: null,
    updatedAt: null,
    color: "grey",
    text: "TC IS INACTIVE.",
  }),
  methods: {
    async getOperationStatus() {
      this.doneSync = false;
      try {
        let res = await httpRequest.axiosRequest(
          "get",
          getHost(this.cube),
          RouteOperation.CUBE
        );

        if (res.status === 401) {
          // If access token is unauthorized
          // use refresh token to get new access token from auth server
          return useRefreshToken(this, this.getOperationStatus);
        }

        let json = res.data;
        if (json.status) {
          let data = json.data;
          this.$refs.cardInitiateCube.fetchStatus(
            data[OperationEvent.INITIATE_CUBE]
          );
          this.$refs.cardPowerDownCube.fetchStatus(
            data[OperationEvent.POWER_DOWN_CUBE]
          );
          this.$refs.cardReleaseBattery.fetchStatus(
            data[OperationEvent.RELEASE_BATTERY]
          );
          this.$refs.cardHaltCube.fetchStatus(data[OperationEvent.HALT_CUBE]);
          this.flagStatus(data[OperationEvent.CUBE_STATUS]);
          this.showNotification(true, "Refresh Success");
        } else {
          this.showNotification(false, json.message);
        }
      } catch (error) {
        this.flagInactive();
        alert(error);
      } finally {
        setTimeout(() => {
          this.doneSync = true;
        }, 500);
      }
    },
    showNotification(success, message) {
      this.$refs.snackbarNotification.showNotification(success, message);
    },
    clearNotification() {
      this.$refs.snackbarNotification.clearNotification();
    },
    getMessage() {
      let here = this;
      socket.on(Websocket.OPERATION, function(data) {
        let item = data.item;
        if (item.cube != getMapping(here.cube)) {
          return;
        }

        switch (item.event) {
          case OperationEvent.INITIATE_CUBE: {
            here.$refs.cardInitiateCube.flagStatus(item);
            break;
          }
          case OperationEvent.POWER_DOWN_CUBE: {
            here.$refs.cardPowerDownCube.flagStatus(item);
            break;
          }
          case OperationEvent.RELEASE_BATTERY:
            break;
          case OperationEvent.HALT_CUBE: {
            here.$refs.cardHaltCube.flagStatus(item);
            break;
          }
          case OperationEvent.CUBE_STATUS: {
            here.flagStatus(item);
            break;
          }
          case OperationEvent.EMO_HALT_CUBE:{
            console.log(`EMO_HALT_CUBE is ${item.status}`)
            break;
          }
          case OperationEvent.SW_HALT_CUBE:{
            here.$refs.cardHaltCube.updateStatus(item.status)
            break;
          }
        }
      });
    },
    flagStatus(item) {
      let reason = item.reason;
      let updatedAt = convertStringToLocal(item.updated_at, true);
      if (item.status) {
        this.text = `TC STARTED AT ${updatedAt} by ${item.username}.`;
        this.color = "success";
      } else {
        this.text = `TC STOPPED AT ${updatedAt} due to ${reason} by ${item.username}.`;
        this.color = "error";
      }
    },
    flagInactive() {
      this.text = "TC IS INACTIVE";
      this.color = "grey";
    },
  },
};

const OperationEvent = {
  INITIATE_CUBE: "INITIATE_CUBE",
  POWER_DOWN_CUBE: "POWER_DOWN_CUBE",
  RELEASE_BATTERY: "RELEASE_BATTERY",
  HALT_CUBE: "HALT_CUBE",
  CUBE_STATUS: "CUBE_STATUS",
  EMO_HALT_CUBE: "EMO_HALT_CUBE",
  SW_HALT_CUBE: "SW_HALT_CUBE",
};

export function closeMessage() {
  socket.off(Websocket.OPERATION);
}
</script>
