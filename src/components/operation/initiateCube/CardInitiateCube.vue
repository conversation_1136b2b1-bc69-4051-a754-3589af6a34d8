<template>
  <v-card 
    :color="cardColor" 
    elevation="5"
  >
    <v-card-title>
      <v-row>
        {{ cardTitle }}
        <v-spacer />
        <v-btn
          @click="btnEventLogDialog()"
          icon
        >
          <v-icon class="glow-icon">mdi-information</v-icon>
        </v-btn>
      </v-row>
    </v-card-title>
    <v-card-text>
      <v-row>
        <v-spacer />
        <ProgressCircular 
          class="mt-2" 
          :doneSync="doneSync" 
          color="white" 
        />
        <v-btn
          class="mx-1"
          rounded
          @click="btnStartCube()"
          :disabled="!doneSync"
        >
          Start
        </v-btn>

        <v-btn 
          class="mx-1" 
          rounded 
          @click="btnStopCube()"
        >
          Stop
        </v-btn>
      </v-row>
    </v-card-text>
    <v-progress-linear
      v-if="status == Status.A"
      indeterminate
      color="blue darken-2"
    />
    <v-stepper 
      dark 
      alt-labels
    >
      <v-stepper-header>
        <template>
          <v-stepper-step
            key="Synchronizing Status"
            :step="0"
            :complete="status == Status.C"
            :rules="[() => status != Status.E]"
          >
            <small>Pairing</small>
          </v-stepper-step>
          <v-divider />
          <v-stepper-step
            key="Completed"
            :step="1"
            :complete="status == Status.C"
            :rules="[() => status != Status.E]"
          >
            <small>Completed</small>
          </v-stepper-step>
        </template>
      </v-stepper-header>
    </v-stepper>
    <DialogStopCube 
      :show-notification="showNotification" 
      ref="dialogStopCube"
    />
    <DialogFailPairing 
      :show-notification="showNotification"
      ref="dialogFailPairing" 
    />
    <DialogEventLog
      :show-notification="showNotification"
      ref="dialogEventLog"
    />
  </v-card>
</template>

<script>
import { Status } from "../util";
import {
  getRequestHeader,
  getHost,
  useRefreshToken,
  getMapping,
} from "../../../helper/common";
import { Module, RouteOperation } from "../../../helper/enums";
import DialogStopCube from "./DialogStopCube.vue";
import ProgressCircular from "../../shared/ProgressCircular.vue";
import DialogFailPairing from "./CubeFailPairing/DialogFailPairing.vue"
import DialogEventLog from "../../dialogs/DialogEventLog.vue";

export default {
  components: {
    DialogStopCube,
    ProgressCircular,
    DialogFailPairing,
    DialogEventLog
  },
  props: {
    cube: {
      type: String,
    },
    showNotification: {
      type: Function,
    },
    clearNotification: {
      type: Function,
    },
  },
  data: () => ({
    Status,
    doneSync: true,
    cardColor: "green",
    cardTitle: "Initiate Cube",
    status: Status.C,
    failedPairing: null
  }),
  methods: {
    async btnStartCube() {
      this.doneSync = false;
      this.clearNotification();
      try {
        this.status = Status.A;
        let res = await fetch(`${getHost(this.cube)}${RouteOperation.CUBE}`, {
          method: "POST",
          headers: getRequestHeader(),
          body: JSON.stringify({ start: true, bypass: false }),
        });
        let json = await res.json();
        if (json.code === 401) {
          // If access token is unauthorized
          // use refresh token to get new access token from auth server
          return useRefreshToken(this, this.btnStartCube);
        }
        if (!json.status) {
          this.status = Status.E;
          this.showNotification(false, json.message);
          
          if (json.model.unconnected) {
            this.$refs.dialogFailPairing.fetchFails(json.model.unconnected)
            this.$refs.dialogFailPairing.openDialog(this.cube, 1)
          } else if (json.model.ota) {
            this.$refs.dialogFailPairing.fetchFails(json.model.ota)
            this.$refs.dialogFailPairing.openDialog(this.cube, 3)
          }
        }

      
      } catch (error) {
        alert(error);
      } finally {
        setTimeout(() => {
          this.doneSync = true;
        }, 500);
      }
    },
    btnStopCube() {
      this.$refs.dialogStopCube.openDialog(this.cube);
    },
    fetchStatus(item) {
      this.status = item.status == Status.A ? Status.C : Status.A;
    },
    flagStatus(item) {
      if (item.status) {
        this.status = Status.C;
        this.showNotification(true, "Success to Initiate Cube");
      } else {
        this.status = Status.E;
        this.showNotification(false, item.message);

        if (item.unpaired && (!this.$store.state.login || item.user === this.$store.state.user.username)) {
          this.$refs.dialogFailPairing.fetchFails(item.unpaired)
          this.$refs.dialogFailPairing.openDialog(this.cube, 2)
        }
      }
    },
    btnEventLogDialog() {
      this.$refs.dialogEventLog.openDialog(`${Module.CUBE}-${getMapping(this.cube)}`)
    },
  },
};
</script>
