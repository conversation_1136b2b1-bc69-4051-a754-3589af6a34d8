<template>
  <v-dialog 
    v-model="dialogBool" 
    width="800"
  >
    <v-card>
      <v-toolbar
        color="red"
        dark
      >
        <v-toolbar-title>{{ getHeader() }}</v-toolbar-title>
      </v-toolbar>
      <v-col>
        <v-card-title>
          <div>
            {{ getText() }}
          </div>  
          <div 
            v-if="removedPairing.length > 0"
            style="font-size: 15px"
          >
            {{ removedPairing.length }} action cancelled. 
            <a @click.prevent="btnUndo"> 
              Click here 
            </a> 
            to Undo All
          </div>
        </v-card-title>
        <v-divider />
        <v-card-text>
          <v-list-item style="height: 0px">
            <v-checkbox 
              v-model="checkboxAll"
              label="Select All"
              @change="selectAll"
            />
            <v-spacer />
            <v-card-actions>
              <v-btn
                color="black"
                class="white--text"
                small
                width="100"
                height="30"
                :disabled="disableAll()"
                @click="actionAll"
              >
                Action All
              </v-btn>
              <v-btn
                color="black"
                class="white--text"
                small
                width="100"
                height="30"
                :disabled="disableAll()"
                @click="cancelAll"
              >
                Cancel All
              </v-btn>
            </v-card-actions>
          </v-list-item>
          <v-divider />
          <v-list 
            v-for="(item, index) in failedPairing" 
            :key="index"
          >
            <v-list-item style="height: 0px">
              <v-checkbox 
                v-model="checkbox[item[0]]"
              />

              <div>
                Skycar ID {{ item[0] }}: The suggested coordinates for the skycar are 
                (x={{ item[1] }}, y={{ item[2] }})
              </div>
              <v-spacer />
              <v-card-actions>
                <v-btn
                  color="black"
                  class="white--text"
                  width="100"
                  @click="btnAction(item)"
                >
                  Action
                </v-btn>
                <v-btn
                  color="black"
                  class="white--text"
                  width="100"
                  @click="btnCancel(index)"
                >
                  Cancel
                </v-btn>
              </v-card-actions>
            </v-list-item>
          </v-list>
        </v-card-text>
        <v-card-actions style="height: 30px">
          <v-spacer />
          <v-btn
            color="green darken-1"
            text
            @click="closeDialog()"
          >
            Close
          </v-btn>
        </v-card-actions>
      </v-col>
    </v-card>
    <DialogTriggerAction
      :cube="cube"
      :triggerError="triggerError"
      ref="dialogTriggerAction"
    />
    <DialogTriggerAll
      :triggerError="triggerError"
      @update-checkbox="updateCheckBoxAll"
      ref="dialogTriggerAll"
    />
  </v-dialog>
</template>

<script>
import DialogTriggerAction from "./DialogTriggerAction"
import DialogTriggerAll from "./DialogTriggerAll"
import { getHost, useRefreshToken } from "../../../../helper/common"
import { RouteError } from "../../../../helper/enums"
let httpRequest = require("../../../../helper/http_request");

export default {
  components: {
    DialogTriggerAction,
    DialogTriggerAll
  },
  props: {
    showNotification: {
      type: Function
    }
  },
  data: () => ({
    dialogBool: false,
    failedPairing: Array(),
    removedPairing: Array(),
    checkbox: Array(),
    checkboxAll: false,
    cube: null,
    connection: null
  }),

  methods: {
    openDialog(cube, connectionValue) {
      this.connection = connectionValue
      this.checkbox = Array();
      this.checkboxAll =  false;
      this.dialogBool = true;
      this.cube = cube
    },
    fetchFails(value){
      this.failedPairing = value
    },
    updateCheckBoxAll(){
      this.checkboxAll = false
    },
    updateFails(index){
      const removeIndex = this.failedPairing.findIndex((element) => element[0] === index)
      this.failedPairing.splice(removeIndex, 1)
      this.checkbox[index] = false
      if (this.failedPairing.length == 0 && this.removedPairing.length == 0) {
        this.closeDialog()
      }
    },
    closeDialog() {
      this.dialogBool = false;
      this.checkbox = Array();
      this.checkboxAll = false;
      this.failedPairing = Array();
      this.removedPairing = Array();
      this.connection = null
    },
    getText(){
      switch (this.connection) {
        case 1:
          return `There are a total of ${ this.failedPairing.length } Skycars not connected
          when cube initialise.`
        case 2:
          return `There are a total of ${ this.failedPairing.length } Skycars failed to 
          pair when cube initialise.`
        case 3:
          return `There are a total of ${ this.failedPairing.length } Skycars undergoing OTA update 
          when cube initialise.`
      }
    },
    getHeader(){
      switch (this.connection) {
        case 1:
          return "Skycar Not Connected"
        case 2:
          return "Skycar Pairing Failed"
        case 3:
          return "Skycar undergoing OTA update"
      }
    },
    btnAction(item){
      this.$refs.dialogTriggerAction.openDialog(item)
    },
    btnCancel(index){
      this.removedPairing.push(this.failedPairing[index])
      this.checkbox[this.failedPairing[index][0]] = false
      this.failedPairing.splice(index, 1)
      if (this.failedPairing.length > 1 && this.checkboxAll){
        this.checkboxAll = false
      }
    },
    btnUndo(){
      var here = this
      this.removedPairing.forEach(function(pairs) {
        here.failedPairing.push(pairs)
      })
      this.failedPairing.sort((a, b) => a[0] - b[0]);
      this.removedPairing = Array()
      this.checkboxAll = false
    },
    disableAll() {
      if (this.checkbox.length === 0 || this.checkbox.every(value => !value)){
        return true
      }
      return false
    },
    cancelAll() {
      const skycarID = this.getSkycarID()

      var here = this
      skycarID.forEach(function(index) {
        const cancelIndex = here.failedPairing.findIndex((element) => element[0] === index)
        here.btnCancel(cancelIndex)
      })

      this.checkboxAll = false
    },
    getSkycarID(){
      return this.checkbox.reduce((acc, currentValue, currentIndex) => {
        if (currentValue === true) {
          acc.push(currentIndex);
        }
        return acc;
      }, []);
    },
    actionAll() {
      const skycarID = this.getSkycarID()
      this.$refs.dialogTriggerAll.openDialog(
        this.failedPairing.filter(row => skycarID.includes(row[0])),
        skycarID
      )
    },
    selectAll(){
      var here = this
      this.failedPairing.forEach(function(pairs) {
        here.checkbox[pairs[0]] = here.checkboxAll
      })
    },
    async triggerError(skycar, coordX, coordY, triggerAll){
      var requestBody = {
        sid: skycar,
        position: "",
        x: coordX,
        y: coordY,
        remark: "Unable to pair skycar during initiate cube."
      }

      let res = await httpRequest.axiosRequest(
        "post", 
        getHost(this.cube), 
        RouteError.MOCK_ERROR,
        requestBody
      )

      if (res.status === 200){
        if (res.data.status) {
          this.updateFails(skycar)
          if (!triggerAll){
            this.$refs.dialogTriggerAction.closeDialog()
          } 
        } else {
          this.showNotification(false, res.data.message)
        }
      } else if (res.status === 401){
        return useRefreshToken(this, this.triggerError, skycar, coordX, coordY)
      } else {
        this.showNotification(false, res.data.message)
      }
    }
  },
  watch: {
    checkbox(){
      var here = this
      var shouldBreak = false

      this.failedPairing.forEach(function(pairs) {
        if (!here.checkbox[pairs[0]]) {
          shouldBreak = true
          return true
        }
      })

      if (shouldBreak) {
        this.checkboxAll = false
        return 0
      }
      this.checkboxAll = true
    }
  }
};
</script>
