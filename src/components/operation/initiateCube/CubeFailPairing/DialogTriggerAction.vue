<template>
  <v-dialog
    v-model="dialogBool"
    width="600"
  >
    <v-card>
      <v-toolbar
        color="red"
        dark
      >
        <v-toolbar-title>Trigger Error for Skycar {{ skycar }}</v-toolbar-title>
      </v-toolbar>
      <v-col>
        <v-row>
          <v-col>
            <v-text-field
              v-model="skycar"
              label="Skycar ID"
              rounded
              filled
              readonly
            />
          </v-col>
          <v-col>
            <v-text-field
              v-model="storage"
              label="Storage"
              rounded
              filled
            />
          </v-col>
        </v-row>
        <v-row>
          <v-col 
            cols="12"
            sm="6" 
          >
            <v-text-field
              v-model="coordX"
              label="Coordinate X"
              rounded
              filled
              type="number"
            >
              <template #prepend-inner>
                <v-icon 
                  @click="openGrid()" 
                  style="margin-right: 5px;"
                >
                  mdi-grid
                </v-icon>
              </template>
            </v-text-field>
          </v-col>
          <v-col 
            cols="12"
            sm="6" 
          >
            <v-text-field
              v-model="coordY"
              label="Coordinate Y"
              rounded
              filled
              type="number"
            >
              <template #prepend-inner>
                <v-icon 
                  @click="openGrid()" 
                  style="margin-right: 5px;"
                >
                  mdi-grid
                </v-icon>
              </template>
            </v-text-field>
          </v-col>
        </v-row>
        <v-row>
          <v-col>
            <v-alert 
              border="top" 
              color="red" 
              dark
            >
              <v-checkbox 
                v-model="checkbox" 
                :label="getText()" 
                color="white"
              />
            </v-alert>
          </v-col>
        </v-row>
        <v-card-actions>
          <v-spacer />
          <ProgressCircular :doneSync="doneSync" />
          <v-btn
            color="green darken-1"
            text
            @click="btnConfirm()"
            :disabled="disableConfirm()"
          >
            Confirm
          </v-btn>
          <v-btn 
            color="green darken-1" 
            text 
            @click="closeDialog()"
          >
            Close
          </v-btn>
        </v-card-actions>
        <DialogCoordinateSelection 
          ref="dialogCoordinateSelection" 
          @update-coord="updateCoord"
        />
      </v-col>
    </v-card>
  </v-dialog>
</template>

<script>
import ProgressCircular from "../../../shared/ProgressCircular.vue"
import DialogCoordinateSelection from "../../../dialogs/DialogCoordinateSelection";

export default {
  components: {
    ProgressCircular,
    DialogCoordinateSelection
  },
  props: {
    cube: {
      type: String,
    },
    triggerError: {
      type: Function
    }
  },
  data: () => ({
    dialogBool: false,
    doneSync: true,
    skycar: null,
    coordX: null,
    coordY: null,
    storage: null,
    checkbox: false
  }),
  methods: {
    openGrid(){
      this.$refs.dialogCoordinateSelection.openDialog(this.cube)
    },
    updateCoord(selectedCells){
      if (selectedCells.length > 0) {
        this.coordX = String(selectedCells[0].x);
        this.coordY = String(selectedCells[0].y);
      }
    },
    openDialog(item) {
      this.checkbox = false
      this.skycar = item[0]
      this.coordX = item[1]
      this.coordY = item[2]
      this.dialogBool = true
    },
    getText() {
      return `Skycar ${this.skycar} is not responding. 
        I would like to trigger error on behalf of the skycar. 
        I am sure that the skycar will remain at (${this.coordX},${this.coordY}) 
        to avoid unsynchronized data between software and hardware.`
    },
    closeDialog() {
      this.dialogBool = false
      this.checkbox = false
    },
    disableConfirm() {
      if (!this.doneSync) {
        return true
      }
      if (this.coordX==="") {
        return true
      }
      if (this.coordY==="") {
        return true
      }
      if (!this.checkbox) {
        return true
      }
      return false
    },
    async btnConfirm(){
      this.doneSync = false

      await this.triggerError(this.skycar, this.coordX, this.coordY, false)
      
      this.doneSync = true
    }
  }
}
</script>
