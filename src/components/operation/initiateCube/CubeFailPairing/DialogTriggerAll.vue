<template>
  <v-dialog 
    v-model="dialogBool" 
    width="500"
  >
    <v-card>
      <v-toolbar 
        color="red" 
        dark
      >
        <v-toolbar-title>Trigger Error for Selected Skycars (Skycar {{ skycarsIDs.toString() }})</v-toolbar-title>
      </v-toolbar>
      <v-col>
        <v-row>
          <v-col>
            <v-alert 
              border="top" 
              color="red" 
              dark
            >
              <v-checkbox 
                v-model="checkbox" 
                :label="getText()" 
                color="white" 
              />
            </v-alert>
          </v-col>
        </v-row>
        <v-card-actions>
          <v-spacer />
          <ProgressCircular :doneSync="doneSync" />
          <v-btn
            color="green darken-1"
            text
            @click="btnConfirm()"
            :disabled="disableConfirm()"
          >
            Confirm
          </v-btn>
          <v-btn 
            color="green darken-1" 
            text 
            @click="closeDialog()"
          >
            Close
          </v-btn>
        </v-card-actions>
      </v-col>
    </v-card>
  </v-dialog>
</template>

<script>
import ProgressCircular from "../../../shared/ProgressCircular.vue"

export default {
  components: {
    ProgressCircular
  },
  props: {
    triggerError: {
      type: Function
    }
  },
  data: () => ({
    dialogBool: false,
    checkbox: false,
    skycars: Array(),
    skycarsIDs: Array(),
    doneSync: true
  }),
  methods: {
    openDialog(item , IDs) {
      this.dialogBool = true;
      this.skycars = item
      this.skycarsIDs = IDs
    },
    closeDialog() {
      this.dialogBool = false;
      this.checkbox = false;
    },
    btnConfirm() {
      this.doneSync = false
      var here = this
      
      this.skycars.forEach(async function(value){
        await here.triggerError(value[0], value[1], value[2], true)
      })

      this.closeDialog()
      this.$emit("update-checkbox")
      this.doneSync = true
    },
    disableConfirm() {
      if (!this.doneSync) {
        return true
      }
      if (!this.checkbox) {
        return true
      }
      return false
    },
    getCoords(){
      const lastTwoElements = this.skycars.map(subArray => subArray.slice(-2));

      return lastTwoElements.map(subArray => `(${subArray.join(", ")})`).join(", ");
    },
    getText() {
      return `Skycar ${this.skycarsIDs.toString()} 
      ${this.skycarsIDs.length === 1 ? "is" : "are"} 
      not responding. I would like to trigger error on behalf of the skycar. 
      I am sure that the skycar will remain at coordinates ${this.getCoords()} 
      respectively to avoid unsynchronized data between software and hardware.`
    }
  },
};
</script>
