<template>
    <v-dialog
        v-model="dialogBool"
        max-width="500"
    >
        <v-card>
            <v-toolbar
                dark
            >
                <v-toolbar-title>Stop TC</v-toolbar-title>
            </v-toolbar>
            <v-col>
                <v-form v-model="form">
                    <v-select
                        v-model="reason"
                        :items="reasons"
                        outlined
                        label="Reason"
                        :rules="[v => !!v || 'Required']"
                    ></v-select>
                    <v-card-actions>
                        <v-spacer></v-spacer>
                        <ProgressCircular :doneSync="doneSync"/>
                        <v-btn
                            color="green darken-1"
                            text
                            @click="btnConfirm()"
                            :disabled="!form || !doneSync"
                        >Confirm
                        </v-btn>
                        <v-btn
                            color="green darken-1"
                            text
                            @click="closeDialog()"
                        >Close
                        </v-btn>
                    </v-card-actions>
                </v-form>
            </v-col>
        </v-card>
    </v-dialog>
</template>

<script>
import { getRequestHeader, getHost, useRefreshToken } from "../../../helper/common"
import { CycleStop, RouteOperation } from "../../../helper/enums"
import ProgressCircular from "../../shared/ProgressCircular.vue"
export default {
    components: {
        ProgressCircular
    },
    props: {
        showNotification: {
            type: Function
        }
    },
    data: () => ({
        form: null,
        dialogBool: false,
        doneSync: true,
        cube: null,
        reason: null,
        reasons: [
            "Cube/Grid Operation",
            "Debug Software",
            "Debug Skycar",
            "Debug Charging Station",
            "Debug WorkStation"
        ]
    }),
    methods: {
        openDialog(cube) {
            this.cube = cube
            this.reason = null
            this.dialogBool = true
        },
        closeDialog() {
            this.dialogBool = false
        },
        async btnConfirm() {
            try {
                this.doneSync = false
                let url = getHost(this.cube) + RouteOperation.CYCLESTOP
                let req = await fetch(url, {
                    method: "POST",
                    body: JSON.stringify({
                        status: CycleStop.ENABLED,
                        reason: this.reason
                    }),
                    headers: getRequestHeader()
                })
                let res = JSON.parse(await req.text())
                if (res.code === 401){ // If access token is unauthorized
                    // use refresh token to get new access token from auth server
                    return useRefreshToken(this, this.btnConfirm)
                }
                if (res.status) {
                    this.showNotification(true, "TC is stopped successfully.")
                    this.closeDialog()
                } else {
                    this.showNotification(false, res.message)
                }
            } catch (error) {
                this.showNotification(false, error)
            } finally {
                setTimeout(() => {
                    this.doneSync = true
                }, 500)
            }
        }
    }
}
</script>
