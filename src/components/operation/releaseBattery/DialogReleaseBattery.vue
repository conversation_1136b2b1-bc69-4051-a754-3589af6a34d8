<template>
    <v-dialog
        v-model="dialogBool"
        width="600"
    >
        <v-card>
            <v-toolbar
                color="primary"
                dark
            >
                <v-toolbar-title>Release Battery</v-toolbar-title>
            </v-toolbar>
            <v-col>
                <v-card-text>
                    <v-list>
                        <v-list-item>1. I have confirmed that all the skycars are currently at their desired coordinates.</v-list-item>
                        <v-list-item>2. I agree that no skycar is allowed to be relocated after relasing battery.</v-list-item>
                    </v-list>
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <ProgressCircular :doneSync="doneSync"/>
                    <v-btn
                        color="green darken-1"
                        text
                        @click="btnConfirm()"
                        :disabled="!doneSync"
                    >Confirm
                    </v-btn>
                    <v-btn
                        color="green darken-1"
                        text
                        @click="closeDialog()"
                    >Close
                    </v-btn>
                </v-card-actions>
            </v-col>
        </v-card>
    </v-dialog>
</template>

<script>
import ProgressCircular from '../../shared/ProgressCircular.vue';
export default {
    components: {
        ProgressCircular
    },
    props: {
        btnReleaseBattery: {
            type: Function
        },
        showNotification: {
            type: Function
        }
    },
    data: () => ({
        dialogBool: false,
        doneSync: true
    }),
    methods: {
        async openDialog() {
            this.dialogBool = true
        },
        closeDialog() {
            this.dialogBool = false
        },
        async btnConfirm() {
            try {
                this.doneSync = false
                await this.btnReleaseBattery()
            } finally {
                setTimeout(() => {
                    this.doneSync = true
                }, 500)
            }
        }
    }
}
</script>
