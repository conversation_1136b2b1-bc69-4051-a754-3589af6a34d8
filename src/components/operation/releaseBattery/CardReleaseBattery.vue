<template>
    <v-card
        :color="cardColor"
        elevation="5"
    >
        <v-card-title>
            {{ cardTitle }}
        </v-card-title>
        <v-card-text>
            <v-row>
                <v-spacer></v-spacer>
                <v-btn
                    class="mx-1"
                    rounded
                    @click="btnOpenDialog()"
                >
                    Confirm
                </v-btn>
            </v-row>
        </v-card-text>
        <v-progress-linear
            v-if="stepper[stepper.length-1].status==Status.A"
            indeterminate
            color="blue darken-2"
        ></v-progress-linear>
        <v-stepper
            dark
            alt-labels
        >
            <v-stepper-header>
                <template
                    v-for="(step, index) in stepper"
                    >
                    <v-stepper-step
                        :key="step.title"
                        :step="index + 1"
                        :complete="step.status==Status.C"
                        :rules="[() => step.status != Status.E]"
                    >
                        <small>{{step.title}}</small>
                    </v-stepper-step>
                    <v-divider 
                        v-if="index < stepper.length - 1"
                        :key="step.index"
                    ></v-divider>
                </template>
            </v-stepper-header>
        </v-stepper>
        <DialogReleaseBattery 
            :btnReleaseBattery="btnReleaseBattery" 
            :showNotification="showNotification"
            ref="dialogReleaseBattery" 
        />
    </v-card>
</template>

<script>
import { flagStageStatus, Status } from "../util"
import { getRequestHeader, getHost, useRefreshToken } from "../../../helper/common"
import { RouteOperation } from "../../../helper/enums"
import DialogReleaseBattery from "./DialogReleaseBattery.vue"

export default {
    components: {
        DialogReleaseBattery
    },
    props: {
        cube: {
            type: String
        },
        showNotification: {
            type: Function
        },
        clearNotification: {
            type: Function
        }
    },
    data: () => ({
        Status,
        cardColor: "orange",
        cardTitle: "End of Cube: Release Battery",
        stepper: [
            {
                title: "Releasing",
                status: Status.C
            },
            {
                title: "Completed",
                status: Status.C
            }
        ]
    }),
    methods: {
        btnOpenDialog() {
            this.$refs.dialogReleaseBattery.openDialog()
        },
        async btnReleaseBattery() {
            flagStageStatus(this.stepper, Status.A)
            let res = await fetch(`${getHost(this.cube)}${RouteOperation.BATTERY}`, {
                method: "POST",
                headers: getRequestHeader()
            })
            let json = await res.json()
            if (json.code === 401){ // If access token is unauthorized
                    // use refresh token to get new access token from auth server
                    return useRefreshToken(this, this.btnReleaseBattery)
                }
            if (json.status) {
                this.showNotification(true, "Success to Release Battery")
            } else {
                this.showNotification(false, json.message)
            }
            flagStageStatus(this.stepper, Status.C)
        },
        fetchStatus(item) {
            if (item.status == Status.A) {
                flagStageStatus(this.stepper, Status.C)
            }
        }
    }
}
</script>
