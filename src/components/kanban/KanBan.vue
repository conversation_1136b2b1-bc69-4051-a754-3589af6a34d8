<template>
  <div class="vue-drag-n-drop">
    <div class="dd-result-group">
      <div
        v-for="(item, ind) in dropGroups"
        v-bind:key="ind"
        class="dd-drop-column"
      >
        <div class="dd-drop-column__head">
          <div class="dd-drop-column__title">
            <span>
              {{ item.displayName }}
            </span>
            <v-chip small class="ml-2">
              {{ item.children.length }}
            </v-chip>
          </div>
          <div class="dd-drop-column__subtitle">
            {{ item.description }}
          </div>
        </div>
        <div class="dd-drop-column__body">
          <Container
            class="dd-container"
            group-name="item.name"
            @drag-start="onDragStart(item.name, $event)"
            @drop="(e) => onCardDrop(item.name, e)"
            :get-child-payload="getCardPayload(item.name)"
            drag-class="dd-card-ghost"
            drop-class="dd-card-ghost-drop"
            :lock-axis="shouldLockXAxis(item.name)"
          >
            <Draggable v-for="(card, cid) in item.children" :key="cid">
              <slot name="dd-card" v-bind:cardData="card">
                <div class="card">
                  <p>
                    {{ card }}
                  </p>
                </div>
              </slot>
            </Draggable>
          </Container>
        </div>
      </div>
    </div>

    <div class="dd-drop-actions"></div>
  </div>
</template>

<script>
import { Container, Draggable } from "vue-smooth-dnd";
import _ from "lodash";
import RequiredProps from "./props.js";

export default {
  name: "Kanban",
  components: { Container, Draggable },
  props: RequiredProps,

  data: function() {
    return {
      items: [],
      dropGroups: [],
      draggableLockAxis: "y",
      originalDataRank: {},
    };
  },

  created() {
    if (this.inPlace) {
      //this.items = this.originalData;
      this.dropGroups = this.dropzones;
    } else {
      //this.items = _.cloneDeep(this.originalData);
      this.dropGroups = _.cloneDeep(this.dropzones);
    }
  },

  methods: {
    shouldLockXAxis(dropGroupName) {
      // Determine whether to lock the axis based on drop group and edit condition
      if (dropGroupName == "New" && !this.enableEdit) {
        return "y";
      }

      // Determine whether to lock the both axis
      return "x,y";
    },

    onDragStart(groupName, event) {
      const containerData = this.getDataForContainer(groupName);
      this.originalDataRank[groupName] = containerData.map((item) => item.rank);

      // Optionally, you can also access the original drag event
      console.log("Original Drag Event:", event);
    },

    getDataForContainer(groupName) {
      const group = this.dropGroups.find((group) => group.name === groupName);
      return group ? group.children : [];
    },

    /**
     * Even that runs when an item is dropped in the original list bucket.
     * @param {Object} dropResult Holds the value of what is dropped.
     * @public
     */
    onDrop(dropResult) {
      this.items = this.applyDrag(this.items, dropResult);
      this.$emit("dropInOriginalBucket", dropResult);
    },

    /**
     * Runs when the card is dropped in any of the drop buckets. Handles the dropping into new bucket and
     * removing from original bucket.
     * @param {String} columnId Holds the ID of the original bucket tot get the card.
     * @param {Object} dropResult Holds the drop result.
     */
    onCardDrop(columnId, dropResult) {
      if (columnId === "New") {
        const currentSequence = this.dropGroups.filter(
          (p) => p.name === columnId
        )[0];

        if (
          dropResult.removedIndex !== null ||
          dropResult.addedIndex !== null
        ) {
          if (dropResult.removedIndex !== null) {
            let found = this.dropGroups.filter((p) => p.name === columnId)[0];
            found.children.splice(dropResult.removedIndex, 1);
          }

          if (dropResult.addedIndex !== null) {
            let found = this.dropGroups.filter((p) => p.name === columnId)[0];
            found.children.splice(dropResult.addedIndex, 0, dropResult.payload);
          }
        }
        const startIdx = Math.min(
          dropResult.removedIndex,
          dropResult.addedIndex
        );
        const endIdx = Math.max(dropResult.removedIndex, dropResult.addedIndex);

        for (let i = startIdx; i <= endIdx; i++) {
          currentSequence.children[i].rank = this.originalDataRank[columnId][i];
        }
        this.$emit(
          "dropInDestinationBucket",
          startIdx,
          endIdx,
          currentSequence
        );
      }
    },

    /**
     * Gets the card payload
     * @param {String} Holds the ID.
     */
    getCardPayload(id) {
      let that = this;
      //console.log('Orgine?', that.dropGroups.filter(p => p.name === id)[0])
      return function(index) {
        let found = that.dropGroups.filter((p) => p.name === id)[0].children[
          index
        ];

        return found;
      };
    },

    /**
     * Same as card payload but this is only implemented in original list.
     * @public
     */
    getOriginalCardPayload() {
      let that = this;
      return function(index) {
        return that.items[index];
      };
    },

    /**
     * Applies the dragging result. It removes the item from original bucket and keeps it in new new list.
     * @param {Array} arr Holds the array.
     * @param {Object} dragResult Holds the drag information.
     * @returns the new corrected list.
     * @public
     */
    applyDrag(arr, dragResult) {
      const { removedIndex, addedIndex, payload } = dragResult;
      if (removedIndex === null && addedIndex === null) return arr;

      const result = [...arr];
      let itemToAdd = payload;

      if (removedIndex !== null) {
        itemToAdd = result.splice(removedIndex, 1)[0];
      }

      if (addedIndex !== null) {
        result.splice(addedIndex, 0, itemToAdd);
      }

      return result;
    },
  },
};
</script>

<style>
.dd-title {
  color: #ffffff;
}
.dd-drop-column {
  display: inline-block;
  vertical-align: top;
  width: 300px;
  margin-right: 1rem;
  padding-bottom: 5px;
  min-height: 5rem;
  white-space: normal;
  background-color: #eaeaeab8;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.12), 0 1px 1px rgba(0, 0, 0, 0.24);
  font: bold;
  border-radius: 5px;
}

.dd-drop-column__head {
  padding: 0.875rem 1rem 4px;
}
.dd-drop-column__title {
  font-weight: bold;
  display: flex;
  align-items: center;
}
.dd-drop-column__subtitle {
  font-size: 0.875rem;
  margin-top: 4px;
}

.dd-drop-column__body {
  height: 55vh;
  overflow-y: auto;
  padding: 0 0.5rem 5px;
}

.card {
  margin: 5px;
  width: 250px;
  background-color: white;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.12), 0 1px 1px rgba(0, 0, 0, 0.24);
  padding: 10px;
  color: #000000;
}

.dd-result-group {
  overflow: auto;
  white-space: nowrap;
  text-align: left;
}

.dd-first-group {
  overflow-y: auto;
  max-height: 200px;
}

.dd-first-group > .smooth-dnd-container {
  min-height: 100px;
  white-space: unset;
}

.dd-drop-actions {
  text-align: center;
  margin: 10px 0px;
}

.dd-drop-actions button {
  margin-right: 10px;
  padding: 10px;
  background-color: white;
  border-radius: 5px;
}

.dd-save {
  background: #5cdb95 !important;
  border: none;
  cursor: pointer;
}

.dd-cancel {
  border: none;
  cursor: pointer;
}
</style>
