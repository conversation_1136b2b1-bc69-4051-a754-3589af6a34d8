<template>
  <div class="transparent" style="min-height: 100px; max-height: 250px;">
    <v-layout class="transparent">
      <v-navigation-drawer
        v-model="drawerAppear"
        temporary
        right
        :width="1500"
        @input="handleDrawerInput"
        style="position: absolute; background-color: #1E1E1E; height:1000px"
      >
        <!-- Content here -->
        <template>
          <div style="background-color: #1E1E1E; color: white">
            <!-- header here -->
            <div style="padding: 2%; text-align:left; color: white">
              <h1>
                {{ advOrder.orderNo }}
              </h1>

              <v-row style="padding: 2px;" no-gutters>
                <p
                  style="padding: 0px 10px 0px 10px; 
                  color: white; 
                  border: 2px solid white; 
                  border-radius:15px;"
                >
                  {{ advOrder.overallStatus }}
                </p>
                <p style="padding-left: 15px;">
                  Received at {{ advOrder.createdAt }}
                </p>
              </v-row>
            </div>
            <hr />
            <!-- <div style="height: 100px;"></div> -->

            <v-row style="padding-block: 2%; text-align: center;">
              <v-col cols="3">
                <v-row style="display:flex; justify-content: center;">
                  <v-icon size="45" color="white">mdi-server</v-icon>
                  <div style="padding: 5%;"></div>
                  <h1 style="font-size: xxx-large;">
                    {{ advOrder.storages.length }}
                  </h1>
                </v-row>
                <div style="padding:1%"></div>
                <p>Storages</p>
              </v-col>
              <v-col cols="3" style="color:greenyellow">
                <v-row style="display:flex; justify-content: center;">
                  <v-icon size="45" color="green">mdi-thumb-up-outline</v-icon>
                  <div style="padding: 5%;"></div>
                  <h1 style="font-size: xxx-large;">
                    {{ advOrder.optimised }}
                  </h1>
                </v-row>
                <div style="padding:1%"></div>
                <p>Optmised</p>
              </v-col>
              <v-col cols="3" style="color:yellow">
                <v-row style="display:flex; justify-content: center;">
                  <v-icon size="45" color="yellow">mdi-tune-variant</v-icon>
                  <div style="padding: 5%;"></div>
                  <h1 style="font-size: xxx-large;">
                    {{ advOrder.optimising }}
                  </h1>
                </v-row>
                <div style="padding:1%"></div>
                <p>Optimising</p>
              </v-col>
              <v-col cols="3" style="color:lightskyblue">
                <v-row style="display:flex; justify-content: center;">
                  <v-icon size="45" color="blue" icon=""
                    >mdi-moon-waning-crescent</v-icon
                  >
                  <div style="padding: 5%;"></div>
                  <h1 style="font-size: xxx-large;">
                    {{ advOrder.pending }}
                  </h1>
                </v-row>
                <div style="padding:1%"></div>
                <p>Pending</p>
              </v-col>
            </v-row>
            <v-row
              style="padding-inline: 2%; text-align:left; justify-content: space-between;"
              no-gutters
            >
              <div>
                <p>
                  {{ selected.length }} rows selected. Total records:
                  {{ advOrder.storages.length }}
                </p>
              </div>
              <v-btn
                small
                dark
                color="red"
                outlined
                :disabled="isDeleteButtonDisabled"
                @click="deleteStorageFromAdvOrder(advOrder.orderNo)"
              >
                <v-icon start color="red">mdi-delete-outline</v-icon>
                Remove Selected Storage
              </v-btn>
            </v-row>
            <div style="padding: 1%"></div>
            <v-data-table
              v-model="selected"
              :headers="advancedHeaders"
              :items="Object.values(advOrder.storages)"
              :item-class="rowColor"
              item-key="index"
              :height="300"
              class="scrollAppearance"
              style="padding-top:2%; border: 1px solid white;"
              multi-sort
              dark
              dense
              show-select
            >
            </v-data-table>
          </div>
        </template>
      </v-navigation-drawer>
    </v-layout>
  </div>
</template>

<script>
// import StorageOrderStatus from '../../constants/advanced-order.enum'
import { AdvancedOrderAPI } from '../../api/advanced-order'

export default {
  name: 'StorageDrawer',
  props: ['advOrder', 'drawerAppear'],
  data() {
    const threeDSort = (a, b) => {
      const node1 = this.advOrder.node[a]
      const node2 = this.advOrder.node[b]
      if (node1 && node2) {
        return node1.x - node2.x || node1.y - node2.y || node1.z - node2.z
      }
      return 0
    }

    const storageCodeSort = (a, b) => {
      const storage1 = this.storages[a]
      const storage2 = this.storages[b]
      if (storage1 && storage2) {
        return storage1.storageCode - storage2.storageCode
      }
      return 0
    }

    return {
      drawer: null,
      rowsSelected: 0,
      selected: [],
      advancedHeaders: [
        { text: 'NO', value: 'index', groupable: false },
        { text: 'STATUS', value: 'statusGroup', groupable: false },
        {
          text: 'CODE',
          value: 'code',
          groupable: false,
          sort: storageCodeSort,
        },
        {
          text: 'COORDINATE',
          value: 'coor3D',
          groupable: false,
          sort: threeDSort,
        },
        {
          text: 'LAYER',
          value: 'layer',
          groupable: false,
        },
        {
          text: 'STATION',
          value: 'station',
          groupable: false,
        },
        {
          text: 'TAGS',
          value: 'tags',
          groupable: false,
        },

        {
          text: 'LAST OPTIMISED AT',
          value: 'lastOptimisedAt',
          groupable: false,
          sort: threeDSort,
        },

        {
          text: 'HAS RETRIEVING',
          value: 'hasRetrieving',
          groupable: false,
          sort: storageCodeSort,
        },
      ],
    }
  },
  computed: {
    isDeleteButtonDisabled() {
      return this.selected.length === 0
    },
  },
  watch: {
    drawerAppear(appear) {
      // Reset selected array when drawerAppear is false
      if (!appear) {
        this.selected = []
      }
    },
  },
  methods: {
    //Checkbox acts as Radio (temporary)
    handleCheckboxClick(clickedItem) {
      // Handle checkbox clicks to act like radio buttons
      if (this.selected === clickedItem) {
        // If the clicked item is already selected, deselect it
        this.selected = null
      } else {
        // If the clicked item is not in the selected array, select only that item
        this.selected = clickedItem
      }
    },

    handleDrawerInput(isDrawerOpen) {
      if (!isDrawerOpen) {
        this.$emit('drawer-closed', this.drawerAppear)
      }
    },

    rowColor(item) {
      if (!item) return 'grey--text text--darken-1'
      switch (item.statusGroup) {
        case 'Pending':
          return 'light-blue--text text--lighten-1'
        case 'Optimising':
          return 'yellow--text text--lighten-2'
        case 'Optimised':
          return 'light-green--text text--accent-2'
        case 'Retrieving':
          return 'green--text text--lighten-1'
      }
    },
    async deleteStorageFromAdvOrder(orderNo) {
      const remainStorages = this.advOrder.storage.
                                  filter((s) => !this.selected.some((y) => y.code === s.code)).
                                  map(s => {return {code: s.code}})
      try {
        if (
          confirm(
            `are you sure you want to delete storage ${this.selected.map(s=>s.code).join(
              ',',
            )} from order ${orderNo}?`,
          )
        ) {
          await AdvancedOrderAPI.deleteStorageFromAdvancedOrder(
            orderNo,
            remainStorages,
          )

          alert(
            `removed storageNo: ${this.selected.map(s=>s.code).join(
              ',',
            )} from advanced order: ${orderNo}`,
          )
        }

        this.selected = []

        //this.$emit("saving", true)
      } catch (ex) {
        alert(`something went wrong`)
        console.error(ex.message)
      } finally {
        await this.reloadData()
      }
    },
  },

}
</script>
<style scoped>
.v-data-table::v-deep .scrollAppearance {
  overflow-y: auto !important;
  scrollbar-width: thin !important;
  scrollbar-color: #cccccc #ffffff !important;
}

/* WebKit (Chrome, Safari) scrollbar styles */
@media screen and (max-width: 767px) {
  .scrollAppearance::-webkit-scrollbar {
    width: 8px; /* Adjust the width as needed */
  }

  .scrollAppearance::-webkit-scrollbar-track {
    background-color: #ffffff; /* Track color */
  }

  .scrollAppearance::-webkit-scrollbar-thumb {
    background-color: #cccccc; /* Thumb color */
  }
}
</style>
