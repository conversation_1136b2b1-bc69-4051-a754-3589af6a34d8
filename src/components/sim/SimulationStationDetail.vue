import LoginVue from '../Users/<USER>/Login.vue';
<template>
  <v-app>
    <div>
      <v-breadcrumbs :items="breadcrumbs"></v-breadcrumbs>
      <!-- Your Station Detail content here -->
    </div>
    <v-container>
      <v-card dark d-flex align-center>
        <v-card-title>Station Configuration</v-card-title>
        <v-card-text class="font-weight-medium">
          <v-row v-for="(value, key) in config" :key="key" align="center">
            <v-col cols="3">
              <v-label dark>{{ key }}</v-label>
            </v-col>
            <v-col cols="3">
              <v-text-field
                v-model.number="config[key]"
                type="number"
                :disabled="greyout[key]"
              ></v-text-field>
            </v-col>
          </v-row>
        </v-card-text>

        <v-card-actions class="ma-4">
          <v-row align="center">
            <v-col cols="6" v-if="showUpdate()">
              <v-btn block @click="updateStationConfig" color="primary"
                >Update</v-btn
              >
            </v-col>

            <v-col cols="6" align="right">
              <router-link :to="`/simulation`">Back</router-link>
            </v-col>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-container>
  </v-app>
</template>

<script>
const httpRequest = require("../../helper/http_request.js");

// import { getHost, getCube } from "../../helper/common";
import { RouteIsolation } from "../../helper/enums";
import { useRefreshToken } from "../../helper/common";

export default {
  created() {
    console.log(`Created for ${this.$route.params.id}`);
    this.url = this.$route.query.url;
    this.stationId = this.$route.query.station_id;
    this.getStationConfig();
  },
  computed() {},
  data() {
    return {
      url: null,
      greyout: {
        session_start_time: true,
        station_id: true,
        current_uph_job_counter: true,
      },
      config: {},
      breadcrumbs: [
        {
          text: "Simulation",
          disabled: false,
          href: "#/simulation", // Replace with your Simulation route path
        },
        {
          text: `Station ${this.$route.query.station_id}`, // Assuming the parameter is 'id'
          disabled: true,
        },
      ],

      showUpdate() {
        if (this.config && Object.keys(this.config).length > 0) {
          return true;
        }
        console.log("update false");
        return false;
      },
    };
  },

  methods: {
    async getStationConfig() {
      const queryParameters = `station_id=${this.stationId}`;
      const response = await httpRequest.axiosRequest(
        "GET",
        this.url,
        RouteIsolation.CONFIG_STATION,
        null,
        false,
        queryParameters
      );

      if (response != null) {
        if (response.data.code === 401) {
          return useRefreshToken(this, this.getStationConfig);
        } else if (response.data.code != 200) {
          this.$awn.alert(response.data.message);
          return;
        } else {
          this.config = response.data.data;
        }
      }
    },

    async updateStationConfig() {
      console.log(this.config);
      const response = await httpRequest.axiosRequest(
        "PUT",
        this.url,
        RouteIsolation.CONFIG_STATION,
        this.config
      );
      if (response != null) {
        if (response.data.code === 401) {
          return useRefreshToken(this, this.getStationConfig);
        } else if (response.data.code != 200) {
          this.$awn.alert(response.data.message);
          return;
        } else {
          this.$awn.info(response.data.message);
          this.config = response.data.data;
        }
      }

      this.getStationConfig();
    },
  },
};
</script>

<style>
.align-items-center {
  display: flex;
  align-items: center;
}
</style>
