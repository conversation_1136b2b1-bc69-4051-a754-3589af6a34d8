<template>
  <v-app app>
    <v-card-title>Preventive Maintenance</v-card-title>
    <v-container fluid>

      <v-btn-toggle rounded dark v-model="selectedHardwareType" mandatory class="d-flex flex-row my-4"
        background-color="grey darken-3">

        <v-btn v-for="(btn, index) in pmModel.hardwareTypeArrays" :key="index" :value="btn" class="flex-grow-1"
          height="100" active-class="btn">
          {{ btn }}
        </v-btn>

      </v-btn-toggle>




      <!--  tab header -->
      <v-tabs v-model="pmUI.tabSelected" grow centered dark background-color="grey darken-3">
        <v-tab v-for="(item, index) in pmUI.tabHeader" :key="index">
          {{ item }}
        </v-tab>
      </v-tabs>
      <v-tabs-items v-model="pmUI.tabSelected">

        <!-- tab install part -->
        <v-tab-item>
          <v-container>
            <v-banner color="white" icon="mdi-robot-industrial" outlined rounded single-line sticky>
              Update Installed Parts for <span style="font-weight:bold">{{ selectedHardwareType }}</span> Module.
              <!-- You selected module  <span style="font-weight:bold; color:red">{{ selectedHardwareType }}</span>, 
                please fill in hardware id and select parts from
                drop down list and provided an unique serial number. -->
            </v-banner>
            <v-row>
              <v-col cols="6">
                <v-text-field label="Hardware ID (e.g 1 for skycar 1)" placeholder="Number only"
                  v-model="pmModel.hardware_id" type="number" filled ></v-text-field>
    
              </v-col>

              <v-col cols="6">
                <v-combobox v-model="pmModel.comboSelect" :items="pmModel.comboItems" item-text="type" item-value="id"
                  label="Please select your part" 
                  chips placeholder="If no part available please contact support to enroll the parts.">
                  <template v-slot:selection="data">
                    <v-chip :key="JSON.stringify(data.item)" v-bind="data.attrs" :input-value="data.selected"
                      :disabled="data.disabled" @click:close="data.parent.selectItem(data.item)">
                      <v-avatar class="accent white--text" left v-text="data.item.type[0].toUpperCase()"></v-avatar>
                      {{ data.item.type }}-{{ data.item.model }}-{{
                        data.item.name
                      }}
                    </v-chip>
                  </template>

                  <template v-slot:item="data">
                    {{ data.item.type }}-{{ data.item.model }}
                  </template>
                </v-combobox>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="6">
                <v-text-field label="Serial No" placeholder="Serial number of the part" v-model="pmModel.serial_no"
                  type="text"></v-text-field>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="6">
                <v-btn dark rounded class="mx-5" @click="installOnClick()">
                  Confirm
                </v-btn></v-col>
              <v-col cols="6">
                <v-btn dark rounded class="mx-5" @click="clearInstallForm()">
                  Clear
                </v-btn></v-col>
            </v-row>
            <v-row><v-divider></v-divider></v-row>
          </v-container>
        </v-tab-item>

        <!-- tab view binding part -->
        <v-tab-item>
          <v-container>
            <v-banner color="white" icon="mdi-robot-industrial" outlined rounded single-line sticky>
              View Installed Parts for <span style="font-weight:bold">{{ selectedHardwareType }}</span> Module.
            </v-banner>

            <v-row>
              <v-col cols="9">
                <v-text-field label="Hardware ID (leave empty if want view all)" placeholder="Number only"
                  v-model="pmModel.hardware_id" type="number" filled
                  @input="pmModel.hardware_id = $event !== '' ? $event : null"></v-text-field>
              </v-col>
              <v-col cols="3" class="d-flex justify-end">
                <v-btn class="mt-3 mr-3" @click="btnClickViewParts()" color="green" dark>
                  <v-icon>mdi-restart</v-icon>
                  <span>Submit</span>
                </v-btn>

              </v-col>
            </v-row>
            <v-row>
              <v-col class="d-flex justify-end">
                <v-btn color="blue" dark> <download-csv :data="pmModel.rows" :fields="exportInUseDatabableFields"
                    :name="exportFileName">
                    <v-icon>mdi-download</v-icon> DOWNLOAD
                  </download-csv></v-btn>
              </v-col>


            </v-row>

            <v-data-table :headers="pmModel.inUseRecordHeaders" item-key="id" :items="pmModel.rows" :items-per-page="10"
              class="elevation-1 mt-4" dark sort-by="hardware_id">
              <!-- action button within datatable -->
              <template v-slot:[`item.action_uninstall`]="{ item }">
                <v-btn small class="mr-2" @click="viewSkycarPreventiveDialog(item)" light>
                  Action
                </v-btn>
              </template>

              <!-- hover tooltips for column motor_mileage  -->
              <template v-slot:[`item.motor_mileage`]="{ item }">
                <v-tooltip bottom>
                  <template v-slot:activator="{ on, attrs }">
                    <span v-bind="attrs" v-on="on">
                      {{ item.motor_mileage.toFixed(2) }}</span>
                  </template>
                  <v-card>
                    <v-container>
                      <v-row> X Axis Mileage : {{ item.x_mileage }}</v-row>
                      <v-row> Y Axis Mileage : {{ item.y_mileage }}</v-row>
                    </v-container>
                  </v-card>
                </v-tooltip>
              </template>

              <!-- hover tooltips for column name  -->
              <template v-slot:[`item.name`]="{ item }">
                <v-tooltip bottom>
                  <template v-slot:activator="{ on, attrs }">
                    <span v-bind="attrs" v-on="on"> {{ item.name }}</span>
                  </template>
                  <v-card>
                    <v-container>
                      <v-row v-for="key in itemKeys" :key="key">{{ key }}: {{ item[key] }}</v-row>

                      <v-row> Binding Id : {{ item.id }}</v-row>
                      <v-row> Part Id : {{ item.parts_id }}</v-row>
                      <v-row> Serial No : {{ item.serial_no }}</v-row>
                      <v-row> Model : {{ item.model }}</v-row>
                      <v-row> Name : {{ item.name }}</v-row>
                      <v-row> Max Qty : {{ item.max_qty }}</v-row>
                    </v-container>
                  </v-card>
                </v-tooltip>
              </template>
            </v-data-table>
          </v-container>
        </v-tab-item>

        <!-- tab view binding part history -->
        <v-tab-item>
          <v-container>
            <v-banner color="white" icon="mdi-robot-industrial" outlined rounded single-line sticky>
              View Historical Record of Installed Parts for <span style="font-weight:bold">{{ selectedHardwareType
              }}</span> Module.
            </v-banner>

            <v-row>
              <v-col cols="9">
                <v-text-field label="Hardware ID (leave empty if want view all)" placeholder="Number only"
                  v-model="pmModel.hardware_id" type="number" filled
                  @input="pmModel.hardware_id = $event !== '' ? $event : null"></v-text-field>
              </v-col>

              <v-col cols="3" class="d-flex justify-end">
                <v-btn class="mt-3 mr-3" @click="btnViewHistory()" color="green" dark>
                  <v-icon>mdi-restart</v-icon>
                  <span>Submit</span>
                </v-btn>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="d-flex justify-end">
                <v-btn class="mt-3" color="blue" dark> <download-csv :data="pmModel.historyRows"
                    :fields="exportHistoryDatatableFields" :name="exportFileName">
                    <v-icon>mdi-download</v-icon> DOWNLOAD
                  </download-csv></v-btn>
              </v-col>
            </v-row>


            <!-- Column Selection Feature  -->
            <v-select v-model="pmModel.selectedHistoryHeaders" :items="pmModel.defaultHistoryHeaders"
              label="Select Columns" multiple outlined return-object>
              <template v-slot:selection="{ item, index }">
                <v-chip v-if="index < pmModel.selectedColumnsDisplay">
                  <span>{{ item.text }}</span>
                </v-chip>
                <span v-if="index === pmModel.selectedColumnsDisplay" class="grey--text caption">(+{{
                  pmModel.selectedHistoryHeaders.length - pmModel.selectedColumnsDisplay }} others)</span>
              </template>
            </v-select>

            <v-data-table :headers="showHistoryHeaders" item-key="id" :items="pmModel.historyRows" :items-per-page="50"
              class="elevation-1 mt-4" dark sort-by="hardware_id">


              <!-- hover tooltips for column motor_mileage  -->
              <template v-slot:[`item.motor_mileage`]="{ item }">
                <v-tooltip bottom>
                  <template v-slot:activator="{ on, attrs }">
                    <span v-bind="attrs" v-on="on">
                      {{ item.motor_mileage.toFixed(2) }}</span>
                  </template>
                  <v-card>
                    <v-container>
                      <v-row> X Axis Mileage : {{ item.x_mileage }}</v-row>
                      <v-row> Y Axis Mileage : {{ item.y_mileage }}</v-row>
                    </v-container>
                  </v-card>
                </v-tooltip>
              </template>



            </v-data-table>
          </v-container>
        </v-tab-item>




      </v-tabs-items>



      <v-dialog v-if="pmModel.bolActionDialog" v-model="pmModel.bolActionDialog" max-width="800">
        <v-card>
          <v-card-text>
            <v-row>
              <v-col cols="12">
                <v-toolbar-title>Hardware ID :
                  {{ pmModel.selectedRow.hardware_id }}</v-toolbar-title></v-col>

            </v-row>

            <v-row>
              <v-col cols="6">
                Please choose a reason for uninstalling part from
                this hardware.
              </v-col>
              <v-col>
                <v-combobox v-model="selectedActionLabel" :items="items" chips clearable label="Your reason / action."
                  solo>
                  <template v-slot:selection="{ attrs, item, select, selected }">
                    <v-chip v-bind="attrs" :input-value="selected" close @click="select" @click:close="remove(item)">
                      <strong>{{ item }}</strong>&nbsp;
                      <!-- <span>(action)</span> -->
                    </v-chip>
                  </template>

                </v-combobox>
              </v-col>
            </v-row>

            <v-row>
              <v-col cols="12"> Module : {{ selectedHardwareType }}</v-col>
            </v-row>

            <v-row>
              <v-col cols="12"> Type : {{ pmModel.selectedRow.type }}</v-col>
            </v-row>

            <v-row>
              <v-col cols="12"> Serial Number: {{ pmModel.selectedRow.serial_no }}
              </v-col>
            </v-row>

            <v-row>
              <v-col cols="3">
                <v-btn class="mx-1" @click="btnClickUnbind()" :disabled="this.selectedActionLabel == null">
                  Confirm
                </v-btn>
              </v-col>
              <v-col cols="3">
                <v-btn @click="pmModel.bolActionDialog = false">
                  Close
                </v-btn>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-dialog>
    </v-container>
  </v-app>
</template>

<script>
import { RoutePM, ActionLabel, HardwareType, PreventiveMaintenanceActionType } from "../../helper/enums.js";
import { getRequestHeader } from "../../helper/common.js";
import axios from "axios";
import { AxiosHttpWithAwesomeAlert } from "../../helper/http_request.js";
import { debounce } from "lodash";

export default {
  name: "App",

  components: {},

  created() {
    // Initialization for select drop down
    this.fetchParts(HardwareType.SKYCAR);

    // Initialization for part binding history datatable
    const excludedHeaders = ["motor_mileage", "winching_mileage"];
    this.pmModel.filteredHistoryHeaders = this.pmModel.defaultHistoryHeaders
    .filter(header => !excludedHeaders.includes(header.value));
    this.pmModel.selectedHistoryHeaders = this.pmModel.filteredHistoryHeaders;

  },

  watch: {
    selectedHardwareType(newVal) {
      this.fetchParts(newVal);
    }

  },

  methods: {
    clearInstallForm() {
      this.pmModel.hardware_id = null
      this.pmModel.serial_no = null
    },
    remove(item) {
      this.selectedActionLabel.splice(this.selectedActionLabel.indexOf(item), 1)
    },
    toggleHistoryDatatableColumn(header) {
      header.showColumn = !header.showColumn;
    },

    installOnClick: debounce(function(){
      this.btnClickInstallConfirm()
    },2000)
  },


  data: () => ({
    // Setting
    decimalRPrecision: 2,
    maxLength : 10 ,

    // Initialize Data
    selectedHardwareType: "SKYCAR",
    items: Object.values(PreventiveMaintenanceActionType),
    selectedActionLabel: PreventiveMaintenanceActionType.UNINSTALL,

    pmUI: {
      tabSelected: null,
      tabHeader: ["Installation Update", "Current In Use Record", "Historical Record"]
    },
    pmModel: {
      // Part Binding Datatable Variable
      inUseRecordHeaders: [
        { text: "Hardware ID", value: "hardware_id" },
        { text: "Part Name", value: "name" },
        { text: "Part Type", value: "type" },
        { text: "Part Model", value: "model" },
        { text: "Serial No", value: "serial_no" },
        { text: "Installed Date", value: "created_at" },
        { text: "Installed By", value: "created_by" },
        { text: "Motor Mileage 'KM ", value: "motor_mileage" },
        { text: "Winching Mileage 'KM", value: "winching_mileage" },
        { text: "Action", value: "action_uninstall" }
      ],
      rows: [],

      // Part History Datatable Variable
      defaultHistoryHeaders: [
        { text: "Hardware ID", value: "parts_binding_history_hardware_id" },
        { text: "Hardware Type", value: "parts_hardware_type" },
        { text: "Part Name", value: "parts_name" },
        { text: "Part Type", value: "parts_type" },
        { text: "Part Model", value: "parts_model" },
        { text: "Serial No", value: "parts_binding_history_serial_no" },
        { text: "Action", value: "parts_binding_history_action_label" },
        { text: "Installed Date", value: "parts_binding_history_installed_at" },
        { text: "Uninstalled Date", value: "parts_binding_history_created_at" },
        { text: "Uninstalled By", value: "parts_binding_history_created_by" },
        { text: "Motor Mileage 'KM ", value: "motor_mileage" },
        { text: "Winching Mileage 'KM", value: "winching_mileage" }
      ],
      selectedHistoryHeaders: [],
      filteredHistoryHeaders: [],
      historyRows: [],
      selectedColumnsDisplay: 6,
      //
      btnValidation : true,
      bolActionDialog: false,
      bolShowInstallPart: false,
      bolShowCreatePart: false,
      hardware_id: null,
      selectedRow: null,
      comboSelect: null,
      comboItems: [],
      parts: {},
      hardwareTypeArrays: Object.values(HardwareType),
    },

    btnClickViewParts: async function () {
      let res = await this.httpRequestBindingParts();
      this.pmModel.rows = res.data;
    },
    btnViewHistory: async function () {
      let res = await this.httpRequestBindingPartsHistory();
      this.pmModel.historyRows = res.data;
    },

    btnClickInstallConfirm: async function () {
      if (this.pmModel.serial_no == null) {
        this.$awn.alert("Serial no not allow null.") // onReject
        return
      }

      if (this.pmModel.hardware_id == null) {
        this.$awn.alert("Hardware id not allow null.") // onReject
        return
      }

      if (this.pmModel.hardware_id.length > this.maxLength){
        this.$awn.alert(`Hardware id not more than ${this.maxLength} length.`) // onReject
        return
      }

      let payload = {
        hardware_id: parseInt(this.pmModel.hardware_id),
        parts_id: this.pmModel.comboSelect.id,
        serial_no: this.pmModel.serial_no,
        action_label: ActionLabel.INSTALL,
        hardware_type: this.selectedHardwareType
      };

      var tcBt = `${process.env.VUE_APP_TC_BT}${RoutePM.CONTROLLER_BINDING}`;
      let promise = axios.post(tcBt, payload, { headers:getRequestHeader() });
      this.$awn.async(
        promise,
        (res) => this.$awn.success(`${res.data.message}`), // onSuccess 
        (res) => this.$awn.alert(`${res.response.data.message}`) // onReject
      );

      this.clearInstallForm();
    },

    btnClickUnbind: async function () {
      var actionLabel = this.selectedActionLabel
      var tcBt = `${process.env.VUE_APP_TC_BT}${RoutePM.CONTROLLER_BINDING}`;

      var payload = {
        hardware_id: this.pmModel.selectedRow.hardware_id,
        serial_no: this.pmModel.selectedRow.serial_no,
        action_label: actionLabel
      };

      let promise = axios.delete(tcBt, { data: payload }, { headers:getRequestHeader() });
      this.$awn.async(
        promise,
        (res) => this.$awn.success(`${res.data.message}`), // onSuccess
        (res) => this.$awn.alert(`${res.response.data.message}`) // onReject
      );

      // disable dialog + refresh
      this.pmModel.bolActionDialog = false;
      await new Promise((resolve) => setTimeout(resolve, 1000)); // delay 1 sec
      this.btnClickViewParts();
    },
    httpRequestBindingParts: async function () {

      const baseRoute = `${process.env.VUE_APP_TC_BT}${RoutePM.CONTROLLER_BINDING_HARDWARE}`;
      const hardwareParam = this.pmModel.hardware_id === null ?
        `hardware_type=${this.selectedHardwareType}` :
        `hardware_id=${this.pmModel.hardware_id}&&hardware_type=${this.selectedHardwareType}`;
      const tcBt = `${baseRoute}?${hardwareParam}`;

      let promise = axios.get(tcBt, {
        headers: getRequestHeader()
      })
      const responsePromise = AxiosHttpWithAwesomeAlert(this.$awn, promise,false);

      // Return the responsePromise to the parent caller
      return responsePromise;
    },

    httpRequestBindingPartsHistory: async function () {
      const baseRoute = `${process.env.VUE_APP_TC_BT}${RoutePM.CONTROLLER_BINDING_HARDWARE_HISTORY}`;
      const hardwareParam = this.pmModel.hardware_id === null ?
        `hardware_type=${this.selectedHardwareType}` :
        `hardware_id=${this.pmModel.hardware_id}&&hardware_type=${this.selectedHardwareType}`;
      const tcBt = `${baseRoute}?${hardwareParam}`;

      let promise = axios.get(tcBt, {
        headers: getRequestHeader()
      })
      const responsePromise = AxiosHttpWithAwesomeAlert(this.$awn, promise,false);

      // Return the responsePromise to the parent caller
      return responsePromise;

    },
    httpRequestParts: async function (hardwareType) {
      var tcBt = `${process.env.VUE_APP_TC_BT}${RoutePM.CONTROLLER_PARTS}?hardware_type=${hardwareType}`;
      var requestOptions = {
        method: "GET",
        headers: getRequestHeader()
      };
      try {
        let res = await fetch(tcBt, requestOptions);
        return res.json();
      } catch (error) {
        return {
          data: [],
          error: error
        };
      }
    },
    httpBind: async function (payload) {
      var tcBt = `${process.env.VUE_APP_TC_BT}${RoutePM.CONTROLLER_BINDING}`;
      var requestOptions = {
        method: "POST",
        body: JSON.stringify(payload),
        headers: getRequestHeader()
      };
      try {
        let res = await fetch(tcBt, requestOptions);
        console.log(res);
        return res.json();
      } catch (error) {
        return {
          data: [],
          error: error
        };
      }
    },

    partUpdate: function (data) {
      alert(data);
    },

    fetchParts: async function (hardwareType) {
      let res = await this.httpRequestParts(hardwareType);
  
      if (res.data.length > 0){
        this.pmModel.comboItems = res.data
        this.pmModel.comboSelect = res.data[0];
      }
    
    },

    viewSkycarPreventiveDialog: async function (currentRow) {
      this.pmModel.selectedRow = currentRow;
      this.pmModel.bolActionDialog = true;
    }
  }),

  computed: {
    showHistoryHeaders() {
      return this.pmModel.defaultHistoryHeaders.filter(s => this.pmModel.selectedHistoryHeaders.includes(s));
    },
    exportFileName() {
      let date = Date.now().toString()
      let tabSelected = this.pmUI.tabHeader[this.pmUI.tabSelected]
      return `${tabSelected}_${date}.csv`
    },
    exportHistoryDatatableFields() {
      return this.pmModel.defaultHistoryHeaders.map(header => header.value)
    },
    exportInUseDatabableFields() {
      return this.pmModel.inUseRecordHeaders.map(header => header.value)
    },
    itemKeys() {
      if (this.item) {
        return Object.keys(this.item)
      } else {
        return []
      }

    }

  }
};
</script>

<style scoped>
.d-flex {
  display: flex;
}

.flex-wrap {
  flex-wrap: wrap;
}

.justify-center {
  justify-content: center;
}
</style>