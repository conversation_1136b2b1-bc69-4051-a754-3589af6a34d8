<template>
  <v-container>
    <v-row>
      <v-card>
        <v-card-title class="black white--text">
          <v-row>
            <v-col cols="1" class="text-start">
              {{ zoneGroupName }}
            </v-col>
            <v-col cols="2" class="text-center">
              <v-tooltip top>
                <template v-slot:activator="{ on }">
                  <v-btn
                    dark
                    icon
                    v-on="on"
                    @click="gen3dCube()"
                    :loading="loading3d"
                  >
                    <v-icon color="gray">mdi-cube</v-icon>
                  </v-btn>
                </template>
                3D
              </v-tooltip>
              <v-tooltip top>
                <template v-slot:activator="{ on }">
                  <v-btn
                    dark
                    icon
                    v-on="on"
                    @click="gen3dCube(true)"
                    :loading="loading3dsm"
                  >
                    <v-icon color="gray">mdi-cube</v-icon>
                  </v-btn>
                </template>
                3D Storage Movement
              </v-tooltip>
            </v-col>
            <v-col cols="3" class="text-center">
              <v-tooltip top>
                <template v-slot:activator="{ on }">
                  <v-btn
                    dark
                    icon
                    v-on="on"
                    @click="
                      heatMap !== 'EMPTY' ? (heatMap = 'EMPTY') : (heatMap = '')
                    "
                  >
                    <v-icon :color="pickRestrictColor">mdi-cube</v-icon>
                  </v-btn>
                </template>
                Empty Bin
              </v-tooltip>
              <v-tooltip top>
                <template v-slot:activator="{ on }">
                  <v-btn
                    dark
                    icon
                    v-on="on"
                    @click="
                      heatMap !== 'ADVANCED'
                        ? (heatMap = 'ADVANCED')
                        : (heatMap = '')
                    "
                  >
                    <v-icon :color="pickRestrictColor">mdi-cube</v-icon>
                  </v-btn>
                </template>
                Advanced Bin
              </v-tooltip>
            </v-col>
            <v-col class="text-end">
              <v-tooltip top>
                <template v-slot:activator="{ on }">
                  <v-btn
                    dark
                    icon
                    v-on="on"
                    @click="restrictHandler('pick', 'add')"
                  >
                    <v-icon :color="pickRestrictColor"
                      >mdi-package-variant-closed-plus</v-icon
                    >
                  </v-btn>
                </template>
                Add Pick Restrict
              </v-tooltip>
              <v-tooltip top>
                <template v-slot:activator="{ on }">
                  <v-btn
                    dark
                    icon
                    v-on="on"
                    @click="restrictHandler('pick', 'delete')"
                  >
                    <v-icon :color="pickRestrictColor"
                      >mdi-package-variant-closed-minus</v-icon
                    >
                  </v-btn>
                </template>
                Delete Pick Restrict
              </v-tooltip>
              <v-tooltip top>
                <template v-slot:activator="{ on }">
                  <v-btn
                    dark
                    icon
                    v-on="on"
                    @click="restrictHandler('drop', 'add')"
                  >
                    <v-icon :color="dropRestrictColor"
                      >mdi-package-variant-closed-plus</v-icon
                    >
                  </v-btn>
                </template>
                Add Drop Restrict
              </v-tooltip>
              <v-tooltip top>
                <template v-slot:activator="{ on }">
                  <v-btn
                    dark
                    icon
                    v-on="on"
                    @click="restrictHandler('drop', 'delete')"
                  >
                    <v-icon :color="dropRestrictColor"
                      >mdi-package-variant-closed-minus</v-icon
                    >
                  </v-btn>
                </template>
                Delete Drop Restrict
              </v-tooltip>
              <v-tooltip top>
                <template v-slot:activator="{ on }">
                  <v-btn
                    dark
                    icon
                    v-on="on"
                    @click="getCubeData"
                    :loading="refreshLoading"
                  >
                    <v-icon>mdi-refresh</v-icon>
                  </v-btn>
                </template>
                Refresh
              </v-tooltip>
            </v-col>
          </v-row>
        </v-card-title>
        <v-card class="pa-2" style="overflow:auto">
          <v-layout v-for="(row, idx) in gridAxis" :key="idx">
            <div v-for="(col, idx) in row" :key="idx">
              <div v-if="col.type === 'EMPTY_CARD'">
                <v-card flat>
                  <v-card
                    :height="stackBoxHeight"
                    :width="stackBoxWidth"
                    flat
                  ></v-card>
                </v-card>
              </div>
              <div v-else-if="col.type === 'AXIS_CARD'">
                <v-card outlined flat :style="{ borderColor: 'transparent' }">
                  <v-card
                    :height="stackBoxHeight"
                    :width="stackBoxWidth"
                    :class="stackBoxTextClass"
                    flat
                  >
                    {{ col.value }}
                  </v-card>
                </v-card>
              </div>
              <div v-else>
                <v-card outlined rounded="md">
                  <v-tooltip right>
                    <template v-slot:activator="{ on, attrs }">
                      <v-hover v-slot="{ hover }">
                        <v-card
                          :height="stackBoxHeight"
                          :width="stackBoxWidth"
                          v-bind="attrs"
                          v-on="on"
                          :class="[
                            stackBoxTextClass,
                            {
                              shimmer:
                                storageMovementsStack.has(
                                  stackKey(col.x, col.y)
                                ) &&
                                storageMovementsStack.get(
                                  stackKey(col.x, col.y)
                                ).isLast,
                            },
                          ]"
                          :style="{
                            background: getStackColor(col.x, col.y),
                            border: storageMovementsStack.has(
                              stackKey(col.x, col.y)
                            )
                              ? storageMovementsStack.get(
                                  stackKey(col.x, col.y)
                                ).isLast
                                ? ''
                                : '4px solid orange'
                              : selectedStack.has(stackKey(col.x, col.y))
                              ? '2px solid blue'
                              : '',
                            transition: 'background-color 0.3s ease',
                          }"
                          @mousedown="startSelection(col.x, col.y, $event)"
                          @mousemove="updateSelection(col.x, col.y)"
                          @mouseup="endSelection(col.x, col.y, $event)"
                        >
                          <font
                            v-if="
                              !storageMovementsStack.has(stackKey(col.x, col.y))
                            "
                            class="transition-ease-in-out"
                            :style="{
                              opacity:
                                hover || isNaN(getStackText(col.x, col.y))
                                  ? 1
                                  : 0,
                            }"
                          >
                            {{ getStackText(col.x, col.y) }}
                          </font>
                          <font v-else>{{
                            storageMovementsStack
                              .get(stackKey(col.x, col.y))
                              .order.join()
                          }}</font>
                        </v-card>
                      </v-hover>
                    </template>
                    <pre>Stack: <b>{{ stackKey(col.x, col.y) }}</b></pre>
                    <pre
                      v-for="info in getStackStorageInfo(col.x, col.y)"
                      :key="info"
                      >{{ info }}</pre
                    >
                  </v-tooltip>
                </v-card>
              </div>
            </div>
          </v-layout>
        </v-card>
      </v-card>
    </v-row>
  </v-container>
</template>

<script>
import { SmObstaclesAPI } from "../../api/obstacles";
import { SmOperationAPI } from "../../api/sm-operation";
import { SmNodeType } from "../../helper/enums";
import { getBTUrl } from "../../helper/common";
import axios from "axios";

export default {
  name: "StackComponent",

  props: {
    zoneGroupName: String,
    fromX: Number,
    toX: Number,
    fromY: Number,
    toY: Number,
    fromZ: Number,
    toZ: Number,
    originPosition: String,
    stackBoxHeight: Number,
    stackBoxWidth: Number,
    stackBoxTextClass: String,
    stackColor: Object,
    obstacleColor: String,
    pickRestrictColor: String,
    dropRestrictColor: String,
    gatewayInColor: String,
    gatewayOutColor: String,
    gatewayColor: String,
    storageMovements: Array,
  },

  data() {
    return {
      heatMap: "",
      refreshLoading: false,
      selectedStack: new Set(),
      selectStartX: null,
      selectStartY: null,
      isSelecting: false,
      obstacles: {},
      stacks: {},
      storageMap: new Map(),
      storageMovementsStack: new Map(),
      loading3d: false,
      loading3dsm: false,
    };
  },

  methods: {
    async getCubeData() {
      this.refreshLoading = true;
      this.selectedStack = new Set();

      try {
        const obstacleApi = SmObstaclesAPI.getObstacles({
          zoneGroups: [this.zoneGroupName],
          perPage: -1,
        }).then((res) => {
          const data = res.data.data;

          if (data && data.length > 0) {
            const obstacles = {};
            for (const obs of data) {
              obstacles[obs.twoDim] = {
                isSkycarAccessible: obs.isSkycarAccessible,
                canPick: obs.canPick,
                canDrop: obs.canDrop,
              };
            }
            this.obstacles = obstacles;
          }
        });

        const stackApi = SmOperationAPI.getStackStats(this.zoneGroupName).then(
          (res) => {
            const data = res.data.data;

            if (data) {
              this.stacks = data;
              for (const [twoDim, val] of Object.entries(this.stacks)) {
                for (const s of val.storages) {
                  const { x, y } = this.stackXy(twoDim);
                  this.storageMap.set(s.storageCode, { ...s, twoDim, x, y });
                }
              }
            }
          }
        );

        await Promise.all([obstacleApi, stackApi]);
      } catch (e) {
        alert(`Overview Page Get Cube Data Exception - ${e.message}`);
      }
      this.refreshLoading = false;
    },

    stackKey(x, y) {
      return `${x},${y}`;
    },

    stackXy(twoDim) {
      const xy = twoDim.split(",");
      return { x: parseInt(xy[0]), y: parseInt(xy[1]) };
    },

    getStackColor(x, y) {
      const colors = [];
      const stackKey = this.stackKey(x, y);

      const obs = this.obstacles[stackKey];
      if (obs) {
        if (!obs.isSkycarAccessible) colors.push(this.obstacleColor);
        if (!obs.canPick) colors.push(this.pickRestrictColor);
        if (!obs.canDrop) colors.push(this.dropRestrictColor);
      }

      const stack = this.stacks[stackKey];
      if (stack) {
        switch (stack.type) {
          case SmNodeType.GatewayIn:
            colors.push(this.gatewayInColor);
            break;
          case SmNodeType.GatewayOut:
            colors.push(this.gatewayOutColor);
            break;
          case SmNodeType.Gateway:
            colors.push(this.gatewayColor);
            break;
          case SmNodeType.Storage:
            switch (this.heatMap) {
              case "ADVANCED":
                colors.push(
                  this.stackColor[
                    stack.storages.filter((s) => s.advancedOrders.length > 0)
                      .length
                  ]
                );
                break;
              case "EMPTY":
                colors.push(
                  this.stackColor[
                    stack.storages.filter(
                      (s) =>
                        s.tags.length === 0 || s.tags.some((t) => t === "EMPTY")
                    ).length
                  ]
                );
                break;
              default:
                colors.push(this.stackColor[stack.storages.length]);
                break;
            }
            break;
          default:
            break;
        }
      }
      return colors.length > 1
        ? `linear-gradient(135deg, ${colors.join(",")})`
        : colors[0];
    },

    getStackText(x, y) {
      const stackKey = this.stackKey(x, y);
      const stack = this.stacks[stackKey];
      if (stack) {
        if (stack) {
          switch (stack.type) {
            case SmNodeType.GatewayIn:
              return `${stack.station}-P`;
            case SmNodeType.GatewayOut:
              return `${stack.station}-D`;
            case SmNodeType.Gateway:
              return `${stack.station}-DP`;
            case SmNodeType.Storage:
              return stack.storages.length;
            default:
              break;
          }
        }
      }
      return "";
    },

    getStackStorageInfo(x, y) {
      const stackKey = this.stackKey(x, y);
      const stack = this.stacks[stackKey];
      if (stack) {
        const storages = stack.storages ?? [];
        if (stack.type === SmNodeType.Storage) {
          return storages.map((storage, idx) => {
            const number = `${idx + 1}.`.padEnd(4, " ");
            const zLevel = `z:${storage.z}`.padEnd(5, " ");
            const storageCode = `- ${storage.storageCode}`.padEnd(7, " ");
            const tagOrAdvOrder =
              this.heatMap === "ADVANCED"
                ? `- ${storage.advancedOrders.join()}`
                : `- ${storage.tags.join()}`;
            return number + zLevel + storageCode + tagOrAdvOrder;
          });
        } else if (
          [
            SmNodeType.GatewayOut,
            SmNodeType.GatewayIn,
            SmNodeType.Gateway,
          ].includes(stack.type)
        ) {
          return storages.map((storage, idx) => {
            const number = `${idx + 1}.`.padEnd(4, " ");
            const codeAndLastMovement = `${storage.storageCode} - ${storage.lastMovement}`;
            const tagOrAdvOrder =
              this.heatMap === "ADVANCED"
                ? ` - ${storage.advancedOrders.join()}`
                : ` - ${storage.tags.join()}`;
            return number + codeAndLastMovement + tagOrAdvOrder;
          });
        }
      }
      return [];
    },

    resetSelection() {
      this.selectStartX = null;
      this.selectStartY = null;
      this.isSelecting = false;
      this.selectedStack = new Set();
    },

    startSelection(x, y) {
      if (
        this.selectedStack.size === 1 &&
        this.selectedStack.has(this.stackKey(x, y))
      ) {
        this.resetSelection();
        return;
      }
      this.resetSelection();
      this.isSelecting = true;
      document.body.style.userSelect = "none"; // Disable text selection
      this.selectStartX = x;
      this.selectStartY = y;
      this.selectedStack.add(this.stackKey(x, y));
    },

    updateSelection(x, y) {
      if (!this.isSelecting) return;

      const selected = new Set();

      for (
        let i = Math.min(this.selectStartX, x);
        i <= Math.max(this.selectStartX, x);
        i++
      ) {
        for (
          let j = Math.min(this.selectStartY, y);
          j <= Math.max(this.selectStartY, y);
          j++
        ) {
          selected.add(this.stackKey(i, j));
        }
      }
      this.selectedStack = selected;
    },

    endSelection() {
      this.isSelecting = false;
      document.body.style.userSelect = ""; // Re-enable text selection
    },

    async restrictHandler(type, action) {
      try {
        const targetStack = [...this.selectedStack];
        const canPick = type === "pick" ? action !== "add" : undefined;
        const canDrop = type === "drop" ? action !== "add" : undefined;

        await SmObstaclesAPI.updateRestrictions({
          stacks: targetStack.map((stackKey) => {
            const [x, y] = stackKey.split(",");
            return { x: +x, y: +y };
          }),
          zoneGroup: this.zoneGroupName,
          canPick,
          canDrop,
        }).then(() => {
          for (const stack of targetStack) {
            if (!this.obstacles[stack]) {
              this.obstacles[stack] = {
                isSkycarAccessible: true,
                canPick: true,
                canDrop: true,
              };
            }

            if (canPick !== undefined) {
              this.obstacles[stack].canPick = canPick;
            }

            if (canDrop !== undefined) {
              this.obstacles[stack].canDrop = canDrop;
            }
          }
        });
        this.$forceUpdate();
      } catch (e) {
        alert(`Update Obstacle Exception - ${e.message}`);
      }
    },

    async gen3dCube(storageMovementMode = false) {
      try {
        if (storageMovementMode && this.storageMovements.length === 0) {
          alert("Missing storage movements");
          return;
        }

        const storages = storageMovementMode
          ? this.storageMovements.map((s) => {
              return {
                code: s.code,
                x: s.x,
                y: s.y,
                z: s.z,
                info: s.info,
                type: "",
                /* eslint-disable */
                job_type: s.jobType,
                /* eslint-enable */
              };
            })
          : Array.from(this.storageMap.values()).map((s) => {
              return {
                code: s.storageCode,
                x: s.x,
                y: s.y,
                z: s.z,
                info: "",
                type:
                  s.advancedOrders.length > 0
                    ? "ADVANCED"
                    : s.tags.length === 0 || s.tags.includes("EMPTY")
                    ? "EMPTY"
                    : "",
                /* eslint-disable */
                job_type: "",
                /* eslint-enable */
              };
            });
        if (storageMovementMode) {
          this.loading3dsm = true;
        } else {
          this.loading3d = true;
        }
        const response = await axios.post(getBTUrl() + "/cube-plot", {
          /* eslint-disable */
          x_start: this.fromX,
          x_end: this.toX,
          y_start: this.fromY,
          y_end: this.toY,
          z_start: this.fromZ + 1,
          z_end: this.toZ,
          storage_movement_mode: storageMovementMode,
          storages,
          /* eslint-enable */
        });
        if (storageMovementMode) {
          this.loading3dsm = false;
        } else {
          this.loading3d = false;
        }
        // Open a new tab
        const newTab = window.open("", "_blank");

        if (newTab) {
          newTab.document.open();
          newTab.document.write(response.data.data); // Insert the HTML
          newTab.document.close();
        } else {
          alert("Popup blocked! Please allow popups for this site.");
        }
      } catch (e) {
        if (storageMovementMode) {
          this.loading3dsm = false;
        } else {
          this.loading3d = false;
        }
        alert(`Overview Page Get Storage Movements Exception - ${e.message}`);
      }
    },
  },

  computed: {
    gridAxis() {
      const result = [];
      const xRange = Array.from(
        { length: this.toX - this.fromX + 1 },
        (_, i) => this.fromX + i
      );
      const yRange = Array.from(
        { length: this.toY - this.fromY + 1 },
        (_, i) => this.fromY + i
      );
      if (this.originPosition === "TOP LEFT") {
        const yAxisRow = [];
        yAxisRow.push({ type: "EMPTY_CARD" });
        for (const y of yRange) {
          yAxisRow.push({ type: "AXIS_CARD", value: y });
        }
        result.push(yAxisRow);
        for (const x of xRange) {
          const stackRow = [];
          stackRow.push({ type: "AXIS_CARD", value: x });
          for (const y of yRange) {
            stackRow.push({ type: "STACK_CARD", x, y });
          }
          result.push(stackRow);
        }
      } else if (this.originPosition === "BOTTOM LEFT") {
        const reverseYRange = yRange.reverse();
        for (const y of reverseYRange) {
          const stackRow = [];
          stackRow.push({ type: "AXIS_CARD", value: y });
          for (const x of xRange) {
            stackRow.push({ type: "STACK_CARD", x, y });
          }
          result.push(stackRow);
        }
        const xAxisRow = [];
        xAxisRow.push({ type: "EMPTY_CARD" });
        for (const x of xRange) {
          xAxisRow.push({ type: "AXIS_CARD", value: x });
        }
        result.push(xAxisRow);
      } else if (this.originPosition === "TOP RIGHT") {
        const xAxisRow = [];
        const reverseXRange = xRange.reverse();
        for (const x of reverseXRange) {
          xAxisRow.push({ type: "AXIS_CARD", value: x });
        }
        xAxisRow.push({ type: "EMPTY_CARD" });
        result.push(xAxisRow);
        for (const y of yRange) {
          const stackRow = [];
          for (const x of reverseXRange) {
            stackRow.push({ type: "STACK_CARD", x, y });
          }
          stackRow.push({ type: "AXIS_CARD", value: y });
          result.push(stackRow);
        }
      } else if (this.originPosition === "BOTTOM RIGHT") {
        const reverseXRange = xRange.reverse();
        const reverseYRange = yRange.reverse();
        for (const x of reverseXRange) {
          const stackRow = [];
          for (const y of reverseYRange) {
            stackRow.push({ type: "STACK_CARD", x, y });
          }
          stackRow.push({ type: "AXIS_CARD", value: x });
          result.push(stackRow);
        }
        const yAxisRow = [];
        for (const y of reverseYRange) {
          yAxisRow.push({ type: "AXIS_CARD", value: y });
        }
        yAxisRow.push({ type: "EMPTY_CARD" });
        result.push(yAxisRow);
      }
      return result;
    },
  },

  watch: {
    storageMovements: {
      deep: true,
      handler(newVal) {
        const newMap = new Map();
        for (const [idx, val] of newVal.entries()) {
          newMap.set(val.twoDim, {
            order: newMap.has(val.twoDim)
              ? [...newMap.get(val.twoDim).order, idx + 1]
              : [idx + 1],
            isPending: !val.completedAt,
            isLast: idx + 1 === newVal.length,
          });
        }
        this.storageMovementsStack = newMap;
      },
    },
  },

  beforeMount() {
    this.getCubeData();
  },
};
</script>

<style scoped>
.shimmer {
  border: 4px solid transparent;
  animation: shimmer-border 1.5s infinite linear;
}

@keyframes shimmer-border {
  0% {
    border-color: rgba(255, 153, 0, 0.747);
  }
  50% {
    border-color: rgb(255, 81, 0);
  }
  100% {
    border-color: rgba(255, 153, 0, 0.747);
  }
}
</style>
