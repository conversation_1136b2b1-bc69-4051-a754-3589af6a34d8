<template>
  <v-app>
    <v-container fluid>
      <v-card dark>
        <v-col>
          <v-row>
            <!-- Zone dropdown  -->
            <v-select
              v-model="currentZone"
              :items="zones"
              @change="shell.sid = Array(), modelSkycar.skycar = Array(), syncSkycar(), getMdOut()"
              class="ma-2"
              prepend-icon="mdi-cube"
            />
  
            <!-- Gateway btn -->
            <v-btn
              @click="syncGW()"
              dark
              class="ma-2"
              :color="modelGW.btnColor"
            >
              <span>{{ modelGW.btnText }}</span>
            </v-btn>
            <v-btn
              @click="btnAddSkycar"
              dark
              class="ma-2"
              color="green"
            >
              Add Skycar
            </v-btn>
  
            <!-- Sync btn -->
            <v-btn
              @click="syncSkycar()"
              color="green"
              dark
              class="ma-2"
              :disabled="!doneSync"
            >
              Refresh
            </v-btn>
          </v-row>
        </v-col>
        <v-progress-linear
          v-if="!doneSync"
          color="green"
          indeterminate
        />
      </v-card>
  
      <!-- Skycars List Datatable -->
      <v-data-table
        v-model="modelSkycar.dtSelected"
        :headers="headers"
        item-key="skycar_id"
        :items="modelSkycar.skycar"
        :items-per-page="15"
        group-by="status"
        class="elevation-1"
        dark
        sort-by="skycar_id"
      >
        <!-- cell filtering -->
        <template v-slot:[`item.skycar_id`]="{ item }">
          <span :class="getSidColor(item.mode)">{{ item.skycar_id }}</span>
        </template>

        <template v-slot:[`item.connect`]="{ item }">
          <v-chip
            :color="getColor(item.connect)"
            dark
          >
            {{ getStatus(item.connect) }}
          </v-chip>
        </template>
  
        <template v-slot:[`item.pair`]="{ item }">
          <v-chip
            :color="getColor(item.pair)"
            dark
          >
            {{ getStatus(item.pair) }}
          </v-chip>
        </template>
  
        <template v-slot:[`item.winch`]="{ item }">
          <v-chip
            v-for="[position, winch] in Object.entries(item.winch).sort()"
            :key="position"
            :color="getWinchColor(winch.is_active, winch.assign_storage_code , winch.storage_no)"
            class="ml-1 fixed-width-chip"
            dark
          >
            <v-tooltip bottom>
              <template v-slot:activator="{ on, attrs }">
                <span
                  v-bind="attrs"
                  v-on="on"
                >
                  {{ position[0] }} {{ winch.storage_no ? winch.storage_no : "EMPTY" }}
                </span>
              </template>
              <span> {{ winch.assign_storage_code }} </span>
            </v-tooltip>
          </v-chip>
        </template>
  
        <template v-slot:[`item.action`]="{ item }">
          <v-btn
            small
            class="mr-2"
            light
            @click="btnActionDialog(item)"
          >
            Action
          </v-btn>
          <v-btn
            small
            class="mr-2"
            light
            @click="btnEventLogDialog(item)"
          >
            Event Log
          </v-btn>
        </template>
      </v-data-table>
    </v-container>
  
  
    <!-- MCU Skycar Shell Mode -->
    <v-toolbar 
      color="black"
      dark 
      flat
    >
      <template v-slot:extension>
        <v-tabs
          v-model="tab"
          align-with-title
        >
          <v-tabs-slider />
          <v-tab
            v-for="tab in tabItems"
            :key="tab"
          >
            {{ tab }}
          </v-tab>
        </v-tabs>
      </template>
    </v-toolbar>
    <v-tabs-items v-model="tab">
      <!-- Shell Mode -->
      <v-tab-item>
        <v-card
          dark
          color="black"
        >
          <!-- Skycar Selection -->
          <v-col>
            <v-row class="mt-12">
              <v-col>
                <v-btn
                  v-model="shell['all']"
                  color="orange"
                  rounded
                  class="ma-1"
                  @click="btnUpdateSid('all')"
                >
                  <v-icon v-if="shell['all']">
                    mdi-sticker-check-outline
                  </v-icon>
                  <v-icon v-else>
                    mdi-sticker-remove-outline
                  </v-icon>
                  <span>All</span>
                </v-btn>
                <v-btn
                  v-for="sid in getSkycarID()"
                  :key="sid"
                  rounded
                  :color="shell['sid'].includes(sid) ? 'green' : 'red'"
                  class="ma-1"
                  @click="btnUpdateSid(sid)"
                >
                  <v-icon v-if="shell['sid'].includes(sid)">
                    mdi-sticker-check-outline
                  </v-icon>
                  <v-icon v-else>
                    mdi-sticker-remove-outline
                  </v-icon>
                  <span>SC {{ sid }}</span>
                </v-btn>
              </v-col>
            </v-row>
          </v-col>
          <!-- Tabs -->
          <v-tabs
            v-model="shell['tab']"
            align-with-title
          >
            <v-tabs-slider />
            <v-tab
              v-for="shell_item in shell['item']"
              :key="shell_item"
            >
              {{ shell_item }}
            </v-tab>
          </v-tabs>
        </v-card>
        <v-card
          dark
        >
          <v-tabs-items
            v-model="shell['tab']"
            dark
          >
            <!-- Status -->
            <v-tab-item>
              <v-row
                v-for="status in shell['status']"
                :key="status['title']"
              >
                <v-col>
                  <pre class="mx-2">{{ status['title'] }} Status</pre>
                  <span
                    v-for="data in status['data']"
                    :key="data['title']"
                  >
                    <v-tooltip bottom>
                      <template v-slot:activator="{ on, attrs }">
                        <v-btn
                          width="130"
                          v-bind="attrs"
                          v-on="on"
                          class="ma-2"
                          :color="data['color']"
                          dark
                          rounded
                          @click="btnSendShell({sid: shell['sid'], action: data['action'], value: data['data']})"
                        >
                          <v-icon class="mx-1">{{ data['icon'] }}</v-icon>
                          {{ data['title'] }}
                        </v-btn>
                      </template>
                      <span>{{ data['data'] }}</span>
                    </v-tooltip>
                  </span>
                </v-col>
              </v-row>
            </v-tab-item>
            <!-- Setting -->
            <v-tab-item>
              <!-- Set Skycar ID -->
              <pre class="mx-2">Set Skycar ID</pre>
              <v-row>
                <v-col
                  cols="4"
                  class="mx-2"
                >
                  <v-select
                    v-model="shell['new_sid']"
                    label="ID"
                    :items="getSkycarID()"
                    rounded
                    filled
                  />
                </v-col>
                <v-col class="mx-2">
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        v-bind="attrs"
                        v-on="on"
                        dark
                        color="green"
                        rounded
                        @click="btnSendShell({sid: shell['sid'], action: '1', value: 'skycab system write skycarid ' + shell['new_sid']})"
                        class="ma-1"
                        :disabled="shell['sid'].length != 1 || shell['sid'] == shell['new_sid'] || !shell['new_sid']"
                      >
                        <v-icon>mdi-check</v-icon>
                        Confirm
                      </v-btn>
                    </template>
                    skycab system write skycarid {{ shell['new_sid'] }}
                  </v-tooltip>
                </v-col>
              </v-row>
              <v-divider />
              <!-- Set Skycar Position -->
              <pre class="mx-2">Set Skycar Position</pre>
              <v-row>
                <v-col
                  cols="2"
                  class="mx-2"  
                >
                  <v-text-field
                    v-model="shell['coord_x']"
                    filled
                    type="number"
                    label="X"
                    rounded
                  />
                </v-col>
                <v-col cols="2">
                  <v-text-field
                    v-model="shell['coord_y']"
                    type="number"
                    label="Y"
                    rounded
                    filled
                  />
                </v-col>
                <v-col>
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        v-on="on"
                        v-bind="attrs"
                        color="green"
                        rounded
                        @click="btnSendShell({sid: shell['sid'], action: '1', value: 'skycab system set position ' + shell['coord_x'] + ' ' + shell['coord_y']})"
                        class="ma-1"
                        dark
                        :disabled="shell['sid'].length != 1 || !shell['coord_x'] || !shell['coord_y']"
                      >
                        <v-icon>mdi-check</v-icon>
                        Confirm
                      </v-btn>
                    </template>
                    skycab system set position {{ shell['coord_x'] }} {{ shell['coord_y'] }}
                  </v-tooltip>
                </v-col>
              </v-row>
              <v-divider />
              <!-- Set Skycar Pitch -->
              <pre class="mx-2">Set Skycar Pitch</pre>
              <v-row>
                <v-col
                  cols="2"
                  class="mx-2"
                  v-for="key in Object.keys(shell['pitch'])"
                  :key="key"
                >
                  <v-text-field
                    v-model="shell['pitch'][key]['value']"
                    type="number"
                    :label="shell['pitch'][key]['title']"
                    rounded
                    filled
                  />
                </v-col>
                <v-col>
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        v-on="on"
                        v-bind="attrs"
                        color="green"
                        rounded
                        @click="btnSendShell({sid: shell['sid'], action: '1', value: `skycab system set pitch ${shell['pitch']['x_f']['value']} ${shell['pitch']['x_b']['value']} ${shell['pitch']['y_f']['value']} ${shell['pitch']['y_b']['value']}`})"
                        class="ma-1"
                        dark
                        :disabled="shell['sid'].length != 1 || !shell['pitch']['x_f']['value'] || !shell['pitch']['x_b']['value'] || !shell['pitch']['y_f']['value'] || !shell['pitch']['y_b']['value']"
                      >
                        <v-icon>mdi-check</v-icon>
                        Confirm
                      </v-btn>
                    </template>
                    skycab system set pitch {{ shell['pitch']['x_f']['value'] }} {{ shell['pitch']['x_b']['value'] }} {{ shell['pitch']['y_f']['value'] }} {{ shell['pitch']['y_b']['value'] }}
                  </v-tooltip>
                </v-col>
              </v-row>
              <v-divider />
              <!-- Set Skycar Pulse -->
              <pre class="mx-2">{{ shell.pulse.title }}</pre>
              <v-row>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-select
                    v-model="shell.pulse.direction.value"
                    :items="shell.pulse.direction.option"
                    rounded
                    filled
                    :label="shell.pulse.direction.title"
                  />
                </v-col>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-select
                    v-model="shell.pulse.increment.value"
                    :items="shell.pulse.increment.option"
                    rounded
                    filled
                    :label="shell.pulse.increment.title"
                  />
                </v-col>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-text-field
                    v-model="shell.pulse.jog.value"
                    rounded
                    filled
                    :label="shell.pulse.jog.title"
                    type="number"
                  />
                </v-col>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-text-field
                    v-model="shell.pulse.no_jog.value"
                    rounded
                    filled
                    :label="shell.pulse.no_jog.title"
                    type="number"
                  />
                </v-col>
                <v-col>
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        v-on="on"
                        v-bind="attrs"
                        color="green"
                        rounded
                        @click="btnSendShell({sid: shell.sid, action: shell.pulse.action, value: `${shell.pulse.value} ${shell.pulse.direction.value} ${shell.pulse.increment.value} ${shell.pulse.jog.value} ${shell.pulse.no_jog.value}`})"
                        class="ma-1"
                        dark
                        :disabled="!shell.pulse.jog.value || !shell.pulse.no_jog.value"
                      >
                        <v-icon>{{ shell.pulse.icon }}</v-icon>
                        Confirm
                      </v-btn>
                    </template>
                    {{ shell.pulse.value }} {{ shell.pulse.direction.value }} {{ shell.pulse.increment.value }} {{ shell.pulse.jog.value }} {{ shell.pulse.no_jog.value }}
                  </v-tooltip>
                </v-col>
              </v-row>
              <v-divider />
              <!-- Set Heartbeat Status -->
              <pre class="mx-2">Set Heartbeat Status</pre>
              <v-row>
                <v-col>
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        v-bind="attrs"
                        v-on="on"
                        class="ma-1"
                        color="green"
                        @click="btnSendShell({sid: shell['sid'], action: '1', value: 'skycab xbee start heartbeat'})"
                        rounded
                      >
                        <v-icon>mdi-heart-outline</v-icon>
                        Start             
                      </v-btn>
                    </template>
                    skycab xbee start heartbeat
                  </v-tooltip>
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        v-on="on"
                        v-bind="attrs"
                        class="ma-1"
                        color="orange"
                        @click="btnSendShell({sid: shell['sid'], action: '1', value: 'skycab xbee stop heartbeat'})"
                        rounded
                      >
                        <v-icon>mdi-heart-off-outline</v-icon>
                        Stop
                      </v-btn>
                    </template>
                    skycab xbee stop heartbeat
                  </v-tooltip>
                </v-col>
              </v-row>
              <!-- Set Xbee Loop Back -->
              <pre class="mx-2">{{ shell.loop_back.title }}</pre>
              <v-row>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-text-field
                    v-model="shell.loop_back.no_loop.value"
                    rounded
                    filled
                    :label="shell.loop_back.no_loop.title"
                    type="number"
                  />
                </v-col>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-text-field
                    v-model="shell.loop_back.delay.value"
                    rounded
                    filled
                    :label="shell.loop_back.delay.title"
                    type="number"
                  />
                </v-col>
                <v-col>
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        v-on="on"
                        v-bind="attrs"
                        color="green"
                        rounded
                        @click="btnSendShell({sid: shell.sid, action: shell.loop_back.action, value: `${shell.loop_back.value_start} ${shell.loop_back.no_loop.value} ${shell.loop_back.delay.value}`})"
                        class="ma-1"
                        dark
                        :disabled="!shell.loop_back.no_loop.value || !shell.loop_back.delay.value"
                      >
                        <v-icon>{{ shell.loop_back.icon }}</v-icon>
                        Start
                      </v-btn>
                    </template>
                    {{ shell.loop_back.value_start }} {{ shell.loop_back.no_loop.value }} {{ shell.loop_back.delay.value }}
                  </v-tooltip>
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        v-on="on"
                        v-bind="attrs"
                        color="red"
                        rounded
                        @click="btnSendShell({sid: shell.sid, action: shell.loop_back.action, value: shell.loop_back.value_stop})"
                        class="ma-1"
                        dark
                      >
                        <v-icon>{{ shell.loop_back.icon }}</v-icon>
                        Stop
                      </v-btn>
                    </template>
                    {{ shell.loop_back.value_stop }}
                  </v-tooltip>
                </v-col>
              </v-row>
              <v-divider />
              <!-- Set Xbee Mac Address -->
              <pre class="mx-2">{{ shell.mac_address.title }}</pre>
              <v-row>
                <v-col
                  cols="2"
                  class="mx-2"
                  v-for="key in Object.keys(shell.mac_address.input)"
                  :key="key"
                >
                  <v-text-field
                    v-model="shell.mac_address.input[key].value"
                    :label="shell.mac_address.input[key].title"
                    rounded
                    filled
                  />
                </v-col>
                <v-col>
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        v-on="on"
                        v-bind="attrs"
                        color="green"
                        rounded
                        @click="btnSendShell({sid: shell.sid, action: shell.mac_address.action, value: `${shell.mac_address.value} ${shell.mac_address.input.id_1.value} ${shell.mac_address.input.id_2.value} ${shell.mac_address.input.id_3.value} ${shell.mac_address.input.id_4.value}`})"
                        class="ma-1"
                        dark
                        :disabled="!shell.mac_address.input.id_1.value || !shell.mac_address.input.id_2.value || !shell.mac_address.input.id_3.value || !shell.mac_address.input.id_4.value"
                      >
                        <v-icon>{{ shell.mac_address.icon }}</v-icon>
                        Confirm
                      </v-btn>
                    </template>
                    {{ shell.mac_address.value }} {{ shell.mac_address.input.id_1.value }} {{ shell.mac_address.input.id_2.value }} {{ shell.mac_address.input.id_3.value }} {{ shell.mac_address.input.id_4.value }}
                  </v-tooltip>
                </v-col>
              </v-row>
              <v-divider />
              <!-- Set Time -->
              <pre class="mx-2">{{ shell.set_time.title }}</pre>
              <v-row>
                <v-col
                  cols="2"
                  class="mx-2"
                  v-for="key in Object.keys(shell.set_time.input)"
                  :key="key"
                >
                  <v-text-field
                    v-model="shell.set_time.input[key].value"
                    type="number"
                    :label="shell.set_time.input[key].title"
                    rounded
                    filled
                  />
                </v-col>
                <v-col>
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        v-on="on"
                        v-bind="attrs"
                        color="green"
                        rounded
                        @click="btnSendShell({sid: shell.sid, action: shell.set_time.action, value: `${shell.set_time.value} ${shell.set_time.input.day.value} ${shell.set_time.input.month.value} ${shell.set_time.input.year.value} ${shell.set_time.input.hour.value} ${shell.set_time.input.minute.value} ${shell.set_time.input.second.value}`})"
                        class="ma-1"
                        dark
                      >
                        <v-icon>{{ shell.set_time.icon }}</v-icon>
                        {{ shell.set_time.header }}
                      </v-btn>
                    </template>
                    {{ shell.set_time.value }} {{ shell.set_time.input.day.value }} {{ shell.set_time.input.month.value }} {{ shell.set_time.input.year.value }} {{ shell.set_time.input.hour.value }} {{ shell.set_time.input.minute.value }} {{ shell.set_time.input.second.value }}
                  </v-tooltip>
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        v-on="on"
                        v-bind="attrs"
                        color="orange"
                        rounded
                        @click="btnSendShell({sid: shell.sid, action: shell.get_time.action, value: shell.get_time.value})"
                        class="ma-1"
                        dark
                      >
                        <v-icon>{{ shell.get_time.icon }}</v-icon>
                        {{ shell.get_time.header }}
                      </v-btn>
                    </template>
                    {{ shell.get_time.value }}
                  </v-tooltip>
                </v-col>
              </v-row>
              <v-divider />
            </v-tab-item>
            <!-- Control -->
            <v-tab-item>
              <v-row>
                <!-- Move -->
                <v-col class="mx-2">
                  <h2 class="ma-2">
                    Move
                  </h2>
                  <!-- Axis -->
                  <v-row>
                    <v-col>
                      <pre>Axis</pre>
                      <span
                        v-for="item in [
                          {title: 'axis-X', data: 'x', text: 'Vertical', icon: 'mdi-arrow-expand-vertical', text: 'Move Vertically'},
                          {title: 'axis-Y', data: 'y', text: 'Horizontal', icon: 'mdi-arrow-expand-horizontal', text: 'Move Horizontally'},
                        ]" 
                        :key="item['title']"
                      >
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <v-btn
                              v-on="on"
                              v-bind="attrs"
                              v-model="shell['value_axis']"
                              rounded
                              :color="shell['value_axis'] == item['data'] ? 'orange' : 'black'"
                              @click="shell['value_axis'] = item['data']"
                              class="mx-1"
                              width="130"
                            >
                              <v-icon>{{ item['icon'] }}</v-icon>
                              <span>{{ item['title'] }}</span>
                            </v-btn>
                          </template>
                          <span>{{ item['text'] }}</span>
                        </v-tooltip>
                      </span>
                    </v-col>
                  </v-row>
                  <!-- Direction -->
                  <v-row>
                    <v-col>
                      <pre>Direction</pre>
                      <span
                        v-for="item in [
                          {title: 'Forward', data: 'f', text: 'Move Forward', icon: 'mdi-arrow-expand-up'},
                          {title: 'Backward', data: 'b', text: 'Move Backward', icon: 'mdi-arrow-expand-down'},
                        ]" 
                        :key="item['title']"
                      >
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <v-btn
                              v-on="on"
                              v-bind="attrs"
                              v-model="shell['value_direction']"
                              rounded
                              :color="shell['value_direction'] == item['data'] ? 'orange' : 'black'"
                              @click="shell['value_direction'] = item['data']"
                              class="ma-1"
                              width="130"
                            >
                              <v-icon>{{ item['icon'] }}</v-icon>
                              {{ item['title'] }}
                            </v-btn>
                          </template>
                          <span>Move {{ item['text'] }}</span>
                        </v-tooltip>
                      </span>
                    </v-col>
                  </v-row>
                  <!-- Quantity -->
                  <v-row>
                    <v-col>
                      <pre>Quantity</pre>
                      <v-text-field
                        v-model="shell['value_quantity']"
                        type="number"
                        solo
                        light
                        rounded
                      />
                    </v-col>
                    <v-col>
                      <!-- Plus -->
                      <v-row class="mt-3">
                        <v-btn
                          @click="shell['value_quantity'] = parseInt(shell['value_quantity']) + 1"
                          rounded
                          class="ma-1"
                          color="orange"
                          small
                        >
                          <v-icon>mdi-plus</v-icon>
                        </v-btn>
                      </v-row>
                      <!-- Minus -->
                      <v-row>
                        <v-btn
                          @click="shell['value_quantity'] -= 1"
                          rounded
                          class="ma-1"
                          :disabled="shell['value_quantity'] <= 1"
                          small
                          light
                        >
                          <v-icon>mdi-minus</v-icon>
                        </v-btn>
                      </v-row>
                    </v-col>
                  </v-row>
                  <!-- Confirm -->
                  <v-row>
                    <v-col>
                      <v-tooltip bottom>
                        <template v-slot:activator="{ on, attrs }">
                          <v-btn
                            v-bind="attrs"
                            v-on="on"
                            color="green"
                            rounded
                            @click="btnSendShell({sid: shell['sid'], action: '1', value: 'skycab mcube ' + shell['value_axis'] + ' ' + shell['value_direction'] + ' ' + shell['value_quantity']})"
                            class="ma-2"
                            :disabled="shell['value_quantity'] < 1"
                          >
                            <v-icon>mdi-check</v-icon>
                            Confirm
                          </v-btn>
                        </template>
                        skycab mcube {{ shell['value_axis'] }} {{ shell['value_direction'] }} {{ shell['value_quantity'] }}
                      </v-tooltip>
                    </v-col>
                  </v-row>
                </v-col>
                <v-divider vertical />
                <!-- Winch -->
                <v-col class="mx-2">
                  <h2 class="mx-2">
                    Winch
                  </h2>
                  <!-- Direction -->
                  <v-row>
                    <v-col>
                      <pre>Direction</pre>
                      <span
                        v-for="item in [
                          {title: 'Up', data: 'u', icon: 'mdi-arrow-expand-up', text: 'Winch Up'},
                          {title: 'Down', data: 'd', icon: 'mdi-arrow-expand-down', text: 'Winch Down'},
                          {title: 'Pick', data: 'pick', icon: 'mdi-lock-outline', text: 'Pick'},
                          {title: 'Drop', data: 'drop', icon: 'mdi-lock-open-outline', text: 'Drop'},
                        ]" 
                        :key="item['title']"
                      >
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <v-btn
                              v-on="on"
                              v-bind="attrs"
                              v-model="shell['value_z_type']"
                              rounded
                              :color="shell['value_z_type'] == item['data'] ? 'orange' : 'black'"
                              @click="shell['value_z_type'] = item['data']"
                              class="ma-1"
                              width="130"
                            >
                              <v-icon>{{ item['icon'] }}</v-icon>
                              <span>{{ item['title'] }}</span>
                            </v-btn>
                          </template>
                          <span>{{ item['text'] }}</span>
                        </v-tooltip>
                      </span>
                    </v-col>
                  </v-row>
                  <!-- Quantity -->
                  <v-row>
                    <v-col>
                      <pre>Quantity</pre>
                      <v-text-field
                        v-model="shell['value_z_quantity']"
                        type="number"
                        label="Value"
                        solo
                        light
                        rounded
                      />
                    </v-col>
                    <v-col>
                      <!-- Plus -->
                      <v-row class="mt-3">
                        <v-btn
                          @click="shell['value_z_quantity'] = parseInt(shell['value_z_quantity']) + 1"
                          rounded
                          class="ma-1"
                          color="orange"
                          small
                          :disabled="shell['value_z_quantity'] >= 14"
                        >
                          <v-icon>mdi-plus</v-icon>
                        </v-btn>
                      </v-row>
                      <!-- Minus -->
                      <v-row>
                        <v-btn
                          @click="shell['value_z_quantity'] -= 1"
                          rounded
                          class="ma-1"
                          :disabled="shell['value_z_quantity'] <= 1"
                          small
                          light
                        >
                          <v-icon>mdi-minus</v-icon>
                        </v-btn>
                      </v-row>
                    </v-col>
                  </v-row>
                  <!-- Weight -->
                  <v-row>
                    <v-col>
                      <pre>Weight</pre>
                      <v-text-field
                        v-model="shell['value_z_weight']"
                        solo
                        light
                        type="number"
                        label="Weight"
                        rounded
                      />
                    </v-col>
                    <v-col>
                      <!-- Plus -->
                      <v-row class="mt-3">
                        <v-btn
                          @click="shell['value_z_weight'] = parseInt(shell['value_z_weight']) + 1"
                          rounded
                          class="ma-1"
                          color="orange"
                          small
                        >
                          <v-icon>mdi-plus</v-icon>
                        </v-btn>
                      </v-row>
                      <!-- Minus -->
                      <v-row>
                        <v-btn
                          @click="shell['value_z_weight'] -= 1"
                          rounded
                          class="ma-1"
                          :disabled="shell['value_z_weight'] <= 1"
                          small
                          light
                        >
                          <v-icon>mdi-minus</v-icon>
                        </v-btn>
                      </v-row>
                    </v-col>
                  </v-row>
                  <!-- Destination -->
                  <v-row>
                    <v-col>
                      <pre>Destination</pre>
                      <span
                        v-for="item in [
                          {title: 'STL', data: 'stl', icon: 'mdi-arrow-bottom-left-thin-circle-outline', text: 'STL'},
                          {title: 'STH', data: 'sth', icon: 'mdi-arrow-top-right-thin-circle-outline', text: 'STH'},
                          {title: 'QCL', data: 'qcl', icon: 'mdi-arrow-bottom-left-thin-circle-outline', text: 'QCL'},
                          {title: 'QCH', data: 'qch', icon: 'mdi-arrow-top-right-thin-circle-outline', text: 'QCH'},
                          {title: 'CB', data: 'cb', icon: 'mdi-cube', text: 'CB'},
                        ]" 
                        :key="item['title']"
                      >
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <v-btn
                              v-on="on"
                              v-bind="attrs"
                              v-model="shell['value_z_des']"
                              rounded
                              :color="shell['value_z_des'] == item['data'] ? 'orange' : 'black'"
                              @click="shell['value_z_des'] = item['data']"
                              class="ma-1"
                              width="100"
                            >
                              <v-icon>{{ item['icon'] }}</v-icon>
                              <span>{{ item['title'] }}</span>
                            </v-btn>
                          </template>
                          <span>{{ item['text'] }}</span>
                        </v-tooltip>
                      </span>
                    </v-col>
                  </v-row>
                  <!-- Confirm -->
                  <v-row>
                    <v-col>
                      <v-tooltip bottom>
                        <template v-slot:activator="{ on, attrs }">
                          <v-btn
                            v-on="on"
                            v-bind="attrs"
                            color="green"
                            rounded
                            @click="(shell.value_z_type == 'u' || shell.value_z_type == 'd') ? btnSendShell({sid: shell['sid'], action: '1', value: 'skycab winch z ' + shell['value_z_type'] + ' ' + shell['value_z_quantity'] + ' ' + shell['value_z_weight'] + ' ' + shell['value_z_des']}) : btnSendShell({sid: shell['sid'], action: '1', value: 'skycab ' + shell['value_z_type'] + ' ' + shell['value_z_quantity'] + ' ' + shell['value_z_weight'] + ' ' + shell['value_z_des']})"
                            class="ma-1"
                            :disabled="15 > shell['value_z_quantity'] < 1 || shell['value_z_weight'] < 1"
                          >
                            <v-icon>mdi-check</v-icon>
                            Confirm
                          </v-btn>
                        </template>
                        <span v-if="shell.value_z_type == 'u' || shell.value_z_type == 'd'">
                          skycab winch z {{ shell['value_z_type'] }} {{ shell['value_z_quantity'] }} {{ shell['value_z_weight'] }} {{ shell['value_z_des'] }}
                        </span>
                        <span
                          v-else
                        >
                          skycab {{ shell['value_z_type'] }} {{ shell['value_z_quantity'] }} {{ shell['value_z_weight'] }} {{ shell['value_z_des'] }}          
                        </span>
                      </v-tooltip>
                    </v-col>
                  </v-row>
                </v-col>
                <v-divider vertical />
                <!-- Status -->
                <v-col class="mx-2">
                  <h2 class="mx-2">
                    Status
                  </h2>
                  <!-- Wex -->
                  <v-row>
                    <v-col>
                      <pre>Wex</pre>
                      <span
                        v-for="item in [
                          {title: 'Wex X', data: 'skycab wex x', icon: 'mdi-arrow-expand-horizontal', color: 'green'},
                          {title: 'Wex Y', data: 'skycab wex y', icon: 'mdi-arrow-expand-vertical', color: 'orange'},
                          {title: 'Wex XY', data: 'skycab wex xy', icon: 'mdi-arrow-expand-all', color: 'lime'},
                        ]"
                        :key="item['title']"
                      >
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <v-btn
                              v-bind="attrs"
                              v-on="on"
                              class="ma-1"
                              :color="item['color']"
                              @click="btnSendShell({sid: shell['sid'], action: '1', value: item['data']})"
                              rounded
                              width="130"
                            >
                              <v-icon>{{ item['icon'] }}</v-icon>
                              <span>{{ item['title'] }}</span>              
                            </v-btn>
                          </template>
                          <span>{{ item['data'] }}</span>
                        </v-tooltip>
                      </span>
                    </v-col>
                  </v-row>
                  <!-- Gripper -->
                  <v-row>
                    <v-col>
                      <pre>Gripper</pre>
                      <span
                        v-for="item in [
                          {title: 'Attach', data: 'skycab gripper o', icon: 'mdi-lock-outline', color: 'green'},
                          {title: 'Release', data: 'skycab gripper c', icon: 'mdi-lock-open-outline', color: 'orange'},
                        ]" 
                        :key="item['title']"
                      >
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <v-btn
                              width="130"
                              v-on="on"
                              v-bind="attrs"
                              class="ma-1"
                              :color="item['color']"
                              @click="btnSendShell({sid: shell['sid'], action: '1', value: item['data']})"
                              rounded
                            >
                              <v-icon>{{ item['icon'] }}</v-icon>
                              <span>{{ item['title'] }}</span>
                            </v-btn>
                          </template>
                          <span>{{ item['data'] }}</span>
                        </v-tooltip>
                      </span>
                    </v-col>
                  </v-row>
                  <!-- Bin -->
                  <v-row>
                    <v-col>
                      <pre>Bin</pre>
                      <v-row>
                        <v-col>
                          <v-text-field
                            v-model="shell['bin_x']"
                            solo
                            type="number"
                            label="X"
                            light
                            rounded
                          >
                            <template #prepend-inner>
                              <v-icon 
                                @click="openGrid()" 
                                style="margin-right: 5px;"
                              >
                                mdi-grid
                              </v-icon>
                            </template>
                          </v-text-field>
                        </v-col>
                        <v-col>
                          <v-text-field
                            v-model="shell['bin_y']"
                            solo
                            type="number"
                            label="Y"
                            light
                            rounded
                          >
                            <template #prepend-inner>
                              <v-icon 
                                @click="openGrid()" 
                                style="margin-right: 5px;"
                              >
                                mdi-grid
                              </v-icon>
                            </template>
                          </v-text-field>
                        </v-col>
                        <v-col>
                          <v-tooltip bottom>
                            <template v-slot:activator="{ on, attrs }">
                              <v-btn
                                v-on="on"
                                v-bind="attrs"
                                color="green"
                                rounded
                                @click="btnCheckBin(null, shell['bin_x'], shell['bin_y'])"
                                class="ma-1"
                                dark
                                :disabled="shell['bin_x'] < 0 || shell['bin_y'] < 0"
                              >
                                <v-icon>mdi-check</v-icon>
                                Confirm
                              </v-btn>
                            </template>
                            Check bins at current coordinates
                          </v-tooltip>
                        </v-col>
                      </v-row>
                      <v-row>
                        <v-col>
                          <v-text-field
                            v-model="shell['bin_code']"
                            solo
                            type="number"
                            label="Bin Code"
                            light
                            rounded
                          />
                        </v-col>
                        <v-col>
                          <v-tooltip bottom>
                            <template v-slot:activator="{ on, attrs }">
                              <v-btn
                                v-on="on"
                                v-bind="attrs"
                                color="green"
                                rounded
                                @click="btnCheckBin(shell['bin_code'], null, null)"
                                class="ma-1"
                                dark
                                :disabled="shell['bin_code'] < 0"
                              >
                                <v-icon>mdi-check</v-icon>
                                Confirm
                              </v-btn>
                            </template>
                            Check the current coordinates of bin
                          </v-tooltip>
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
              <v-divider />
              <v-row>
                <!-- Charge In -->
                <v-col class="mx-2">
                  <h2 class="mx-2">
                    {{ shell.auto_charge_in.title }}
                  </h2>
                  <v-row>
                    <v-col
                      cols="2"
                      class="mx-2"
                    >
                      <v-select
                        v-model="shell.auto_charge_in.axis.value"
                        :items="shell.auto_charge_in.axis.option"
                        rounded
                        filled
                        :label="shell.auto_charge_in.axis.title"
                      />
                    </v-col>
                    <v-col
                      cols="2"
                      class="mx-2"
                    >
                      <v-select
                        v-model="shell.auto_charge_in.direction.value"
                        :items="shell.auto_charge_in.direction.option"
                        rounded
                        filled
                        :label="shell.auto_charge_in.direction.title"
                      />
                    </v-col>
                    <v-col
                      cols="2"
                      class="mx-2"
                    >
                      <v-text-field
                        v-model="shell.auto_charge_in.quantity.value"
                        rounded
                        filled
                        :label="shell.auto_charge_in.quantity.title"
                        type="number"
                      />
                    </v-col>
                    <v-col>
                      <v-tooltip bottom>
                        <template v-slot:activator="{ on, attrs }">
                          <v-btn
                            v-on="on"
                            v-bind="attrs"
                            color="green"
                            rounded
                            @click="btnSendShell({sid: shell.sid, action: shell.auto_charge_in.action, value: `${shell.auto_charge_in.value} ${shell.auto_charge_in.axis.value} ${shell.auto_charge_in.direction.value} ${shell.auto_charge_in.quantity.value}`})"
                            class="ma-1"
                            dark
                            :disabled="!shell.auto_charge_in.quantity.value"
                          >
                            <v-icon>{{ shell.auto_charge_in.icon }}</v-icon>
                            Confirm
                          </v-btn>
                        </template>
                        {{ shell.auto_charge_in.value }} {{ shell.auto_charge_in.axis.value }} {{ shell.auto_charge_in.direction.value }} {{ shell.auto_charge_in.quantity.value }}
                      </v-tooltip>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-tab-item>
            <!-- Custom -->
            <v-tab-item>
              <v-col>
                <h2 class="ma-2">
                  Custom Command
                </h2>
                <v-row>
                  <v-col>
                    <v-text-field
                      ref="shell_value"
                      solo
                      rounded
                      light
                      label="Type here..."
                      v-model="shell['value']"
                      clearable
                      @keydown.enter="btnSendShell({sid: shell['sid'], action: '1', value: shell['value']}), shell['focus'] = true"
                      @focus="$event.target.select()"
                    />
                  </v-col>
                  <v-col cols="2">
                    <v-btn
                      @click="btnSendShell({sid: shell['sid'], action: '1', value: shell['value']}), shell['focus'] = true"
                      class="ma-2"
                      color="green"
                      rounded
                      :disabled="!shell['value']"
                    >
                      <v-icon>mdi-check</v-icon>
                      Confirm
                    </v-btn>
                  </v-col>
                </v-row>
              </v-col>
              <v-divider />
              <v-col>
                <h2 class="ma-2 text red--text">
                  <v-icon
                    class="mx-1"
                    color="red"  
                  >
                    mdi-alert
                  </v-icon>
                  Forced Command
                </h2>
                <v-row>
                  <!-- Move -->
                  <v-col class="mx-2 red--text">
                    <h3>Forced Move</h3>
                    <!-- Axis -->
                    <v-row>
                      <v-col>
                        <pre>Axis</pre>
                        <span
                          v-for="item in [
                            {title: 'axis-X', data: 'x', text: 'Vertical', icon: 'mdi-arrow-expand-vertical', text: 'Move Vertically'},
                            {title: 'axis-Y', data: 'y', text: 'Horizontal', icon: 'mdi-arrow-expand-horizontal', text: 'Move Horizontally'},
                          ]" 
                          :key="item['title']"
                        >
                          <v-tooltip bottom>
                            <template v-slot:activator="{ on, attrs }">
                              <v-btn
                                v-on="on"
                                v-bind="attrs"
                                v-model="shell.force_axis"
                                rounded
                                :color="shell.force_axis == item['data'] ? 'orange' : 'black'"
                                @click="shell.force_axis = item['data']"
                                class="mx-1"
                                width="130"
                              >
                                <v-icon>{{ item['icon'] }}</v-icon>
                                <span>{{ item['title'] }}</span>
                              </v-btn>
                            </template>
                            <span>{{ item['text'] }}</span>
                          </v-tooltip>
                        </span>
                      </v-col>
                    </v-row>
                    <!-- Direction -->
                    <v-row>
                      <v-col>
                        <pre>Direction</pre>
                        <span
                          v-for="item in [
                            {title: 'Forward', data: 'f', text: 'Move Forward', icon: 'mdi-arrow-expand-up'},
                            {title: 'Backward', data: 'b', text: 'Move Backward', icon: 'mdi-arrow-expand-down'},
                          ]" 
                          :key="item['title']"
                        >
                          <v-tooltip bottom>
                            <template v-slot:activator="{ on, attrs }">
                              <v-btn
                                v-on="on"
                                v-bind="attrs"
                                v-model="shell.force_direction"
                                rounded
                                :color="shell.force_direction == item['data'] ? 'orange' : 'black'"
                                @click="shell.force_direction = item['data']"
                                class="ma-1"
                                width="130"
                              >
                                <v-icon>{{ item['icon'] }}</v-icon>
                                {{ item['title'] }}
                              </v-btn>
                            </template>
                            <span>Move {{ item['text'] }}</span>
                          </v-tooltip>
                        </span>
                      </v-col>
                    </v-row>
                    <!-- Quantity -->
                    <v-row>
                      <v-col>
                        <pre>Quantity</pre>
                        <v-text-field
                          v-model="shell.force_quantity"
                          type="number"
                          solo
                          light
                          rounded
                        />
                      </v-col>
                      <v-col>
                        <!-- Plus -->
                        <v-row class="mt-3">
                          <v-btn
                            @click="shell.force_quantity = parseInt(shell.force_quantity) + 1"
                            rounded
                            class="ma-1"
                            color="orange"
                            small
                          >
                            <v-icon>mdi-plus</v-icon>
                          </v-btn>
                        </v-row>
                        <!-- Minus -->
                        <v-row>
                          <v-btn
                            @click="shell.force_quantity -= 1"
                            rounded
                            class="ma-1"
                            :disabled="shell.force_quantity <= 1"
                            small
                            light
                          >
                            <v-icon>mdi-minus</v-icon>
                          </v-btn>
                        </v-row>
                      </v-col>
                    </v-row>
                    <!-- Confirm -->
                    <v-row>
                      <v-col>
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <v-btn
                              v-bind="attrs"
                              v-on="on"
                              color="green"
                              rounded
                              @click="btnSendShell({sid: shell.sid, action: '1', value: `skycab actuator gyems fmove ${shell.force_axis} ${shell.force_direction} ${shell.force_quantity}`})"
                              class="ma-2"
                              :disabled="shell.force_quantity < 1"
                            >
                              <v-icon>mdi-check</v-icon>
                              Confirm
                            </v-btn>
                          </template>
                          skycab actuator gyems fmove {{ shell.force_axis }} {{ shell.force_direction }} {{ shell.force_quantity }}
                        </v-tooltip>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-divider vertical />
                  <v-col class="mx-2 red--text">
                    <!-- Wex -->
                    <h3>Forced Wex</h3>
                    <v-row>
                      <v-col>
                        <span
                          v-for="item in [
                            {title: 'Wex X', data: 'skycab actuator wex x', icon: 'mdi-arrow-expand-horizontal', color: 'green'},
                            {title: 'Wex Y', data: 'skycab actuator wex y', icon: 'mdi-arrow-expand-vertical', color: 'orange'},
                            {title: 'Wex XY', data: 'skycab actuator wex xy', icon: 'mdi-arrow-expand-all', color: 'lime'},
                          ]"
                          :key="item.title"
                        >
                          <v-tooltip bottom>
                            <template v-slot:activator="{ on, attrs }">
                              <v-btn
                                v-bind="attrs"
                                v-on="on"
                                class="ma-1"
                                :color="item.color"
                                @click="btnSendShell({sid: shell.sid, action: '1', value: item.data})"
                                rounded
                                width="130"
                              >
                                <v-icon>{{ item.icon }}</v-icon>
                                <span>{{ item.title }}</span>              
                              </v-btn>
                            </template>
                            <span>{{ item.data }}</span>
                          </v-tooltip>
                        </span>
                      </v-col>
                    </v-row>
                    <!-- Winch -->
                    <h3>Forced Winch</h3>
                    <v-row>
                      <v-col>
                        <span
                          v-for="item in [
                            {title: 'Up', data: 'skycab actuator moons dir u', icon: 'mdi-arrow-expand-up', color: 'green'},
                            {title: 'Down', data: 'skycab actuator moons dir d', icon: 'mdi-arrow-expand-down', color: 'orange'},
                            {title: 'Stop', data: 'skycab actuator moons dir n', icon: 'mdi-pause-octagon-outline', color: 'red'},
                          ]" 
                          :key="item.title"
                        >
                          <v-tooltip bottom>
                            <template v-slot:activator="{ on, attrs }">
                              <v-btn
                                width="130"
                                v-on="on"
                                v-bind="attrs"
                                class="ma-1"
                                :color="item.color"
                                @click="btnSendShell({sid: shell.sid, action: '1', value: item.data})"
                                rounded
                              >
                                <v-icon>{{ item.icon }}</v-icon>
                                <span>{{ item.title }}</span>
                              </v-btn>
                            </template>
                            <span>{{ item.data }}</span>
                          </v-tooltip>
                        </span>
                      </v-col>
                    </v-row>
                    <!-- Gripper -->
                    <h3>Forced Gripper</h3>
                    <v-row>
                      <v-col>
                        <span
                          v-for="item in [
                            {title: 'Attach', data: 'skycab actuator gripper o', icon: 'mdi-lock-outline', color: 'green'},
                            {title: 'Release', data: 'skycab actuator gripper c', icon: 'mdi-lock-open-outline', color: 'orange'},
                          ]" 
                          :key="item.title"
                        >
                          <v-tooltip bottom>
                            <template v-slot:activator="{ on, attrs }">
                              <v-btn
                                width="130"
                                v-on="on"
                                v-bind="attrs"
                                class="ma-1"
                                :color="item.color"
                                @click="btnSendShell({sid: shell.sid, action: '1', value: item.data})"
                                rounded
                              >
                                <v-icon>{{ item.icon }}</v-icon>
                                <span>{{ item.title }}</span>
                              </v-btn>
                            </template>
                            <span>{{ item.data }}</span>
                          </v-tooltip>
                        </span>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
                <v-divider />
                <!-- Force Jog -->
                <v-row>
                  <v-col class="mx-2">
                    <h2 class="ma-2 text red--text">
                      <v-icon
                        class="mx-1"
                        color="red"  
                      >
                        mdi-alert
                      </v-icon>
                      {{ shell.force_jog.title }}
                    </h2>
                    <v-row>
                      <v-col
                        cols="2"
                        class="mx-2"
                      >
                        <v-select
                          v-model="shell.force_jog.axis.value"
                          :items="shell.force_jog.axis.option"
                          rounded
                          filled
                          :label="shell.force_jog.axis.title"
                        />
                      </v-col>
                      <v-col
                        cols="2"
                        class="mx-2"
                      >
                        <v-select
                          v-model="shell.force_jog.direction.value"
                          :items="shell.force_jog.direction.option"
                          rounded
                          filled
                          :label="shell.force_jog.direction.title"
                        />
                      </v-col>
                      <v-col
                        cols="2"
                        class="mx-2"
                      >
                        <v-text-field
                          v-model="shell.force_jog.speed.value"
                          rounded
                          filled
                          :label="shell.force_jog.speed.title"
                          type="number"
                        />
                      </v-col>
                      <v-col>
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <v-btn
                              v-on="on"
                              v-bind="attrs"
                              color="green"
                              rounded
                              @click="btnSendShell({sid: shell.sid, action: shell.force_jog.action, value: `${shell.force_jog.value} ${shell.force_jog.axis.value} ${shell.force_jog.direction.value} ${shell.force_jog.speed.value}`})"
                              class="ma-1"
                              dark
                              :disabled="!shell.force_jog.speed.value"
                            >
                              <v-icon>{{ shell.force_jog.icon }}</v-icon>
                              Confirm
                            </v-btn>
                          </template>
                          {{ shell.force_jog.value }} {{ shell.force_jog.axis.value }} {{ shell.force_jog.direction.value }} {{ shell.force_jog.speed.value }}
                        </v-tooltip>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
                <v-divider />
                <!-- Force Pairing -->
                <v-row>
                  <v-col class="mx-2">
                    <h2 class="ma-2 text red--text">
                      <v-icon
                        class="mx-1"
                        color="red"  
                      >
                        mdi-alert
                      </v-icon>
                      {{ shell.force_pairing.title }}
                    </h2>
                    <v-row>
                      <v-col
                        cols="2"
                        class="mx-2"
                      >
                        <v-select
                          v-model="shell.force_pairing.axis.value"
                          :items="shell.force_pairing.axis.option"
                          rounded
                          filled
                          :label="shell.force_pairing.axis.title"
                        />
                      </v-col>
                      <v-col
                        cols="2"
                        class="mx-2"
                      >
                        <v-text-field
                          v-model="shell.force_pairing.bin_value.value"
                          rounded
                          filled
                          :label="shell.force_pairing.bin_value.title"
                          type="number"
                        />
                      </v-col>
                      <v-col>
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <v-btn
                              v-on="on"
                              v-bind="attrs"
                              color="green"
                              rounded
                              @click="btnSendShell({sid: shell.sid, action: shell.force_pairing.action, value: `${shell.force_pairing.value} ${shell.force_pairing.axis.value} ${shell.force_pairing.bin_value.value}`})"
                              class="ma-1"
                              dark
                              :disabled="!shell.force_pairing.bin_value.value"
                            >
                              <v-icon>{{ shell.force_pairing.icon }}</v-icon>
                              Confirm
                            </v-btn>
                          </template>
                          {{ shell.force_pairing.value }} {{ shell.force_pairing.axis.value }} {{ shell.force_pairing.bin_value.value }}
                        </v-tooltip>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
                <v-divider />
                <!-- By pass -->
                <v-row>
                  <v-col class="mx-2">
                    <h2 class="ma-2 text red--text">
                      <v-icon
                        class="mx-1"
                        color="red"  
                      >
                        mdi-alert
                      </v-icon>
                      Bypass
                    </h2>
                    <v-row>
                      <v-col>
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <v-btn
                              v-on="on"
                              v-bind="attrs"
                              :color="shell.bypass_job_flag.color"
                              rounded
                              @click="btnSendShell({sid: shell.sid, action: shell.bypass_job_flag.action, value: shell.bypass_job_flag.value})"
                              class="ma-1"
                              dark
                            >
                              <v-icon>{{ shell.bypass_job_flag.icon }}</v-icon>
                              {{ shell.bypass_job_flag.title }}
                            </v-btn>
                          </template>
                          {{ shell.bypass_job_flag.value }}
                        </v-tooltip>
                      </v-col>
                      <v-col>
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <v-btn
                              v-on="on"
                              v-bind="attrs"
                              :color="shell.bypass_hardware.color"
                              rounded
                              @click="btnSendShell({sid: shell.sid, action: shell.bypass_hardware.action, value: shell.bypass_hardware.value})"
                              class="ma-1"
                              dark
                            >
                              <v-icon>{{ shell.bypass_hardware.icon }}</v-icon>
                              {{ shell.bypass_hardware.title }}
                            </v-btn>
                          </template>
                          {{ shell.bypass_hardware.value }}
                        </v-tooltip>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-col>
            </v-tab-item>
            <!-- Recovery -->
            <v-tab-item>
              <v-card>
                <v-card-title>Recovery Procedure</v-card-title>
                <v-card-text>
                  <li>When Cube is idle, using "CONNECT SKYCAR" button to connect the skycar. "ACK" should be received when the connection is establised.</li>
                  <li>Using "HOME" button to physically recover the skycar. "Job Done" should be received when the process is finished.</li>
                  <li>Using "PAIR" button to synchronize the status of the skycar. A pop-up message should be received in response of the status message.</li>
                  <li>Go to "Error Handling Tab" to recover the skycar.</li>
                </v-card-text>
              </v-card>
              <v-row>
                <v-col
                  v-for="item in shell.recovery"
                  :key="item.title"
                >
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        width="200"
                        v-bind="attrs"
                        v-on="on"
                        class="ma-2"
                        :color="item.color"
                        dark
                        rounded
                        @click="btnSendShell({sid: shell.sid, action: item.action, value: item.data})"
                      >
                        <v-icon class="mx-1">
                          {{ item.icon }}
                        </v-icon>
                        {{ item.title }}
                      </v-btn>
                    </template>
                    <span>{{ item.data }}</span>
                  </v-tooltip>
                </v-col>
              </v-row>
            </v-tab-item>
            <!-- Burn Test -->
            <v-tab-item>
              <!-- Encoder -->
              <pre class="mx-2">{{ burn_test.encoder.title }}</pre>
              <v-row>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-select
                    v-model="burn_test.encoder.input.mode.value"
                    :items="burn_test.encoder.input.mode.option"
                    rounded
                    filled
                    :label="burn_test.encoder.input.mode.title"
                  />
                </v-col>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-select
                    v-model="burn_test.encoder.input.axis.value"
                    :items="burn_test.encoder.input.axis.option"
                    rounded
                    filled
                    :label="burn_test.encoder.input.axis.title"
                  />
                </v-col>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        width="130"
                        v-bind="attrs"
                        v-on="on"
                        class="ma-2"
                        color="green"
                        dark
                        rounded
                        @click="btnSendShell({sid: shell.sid, action: burn_test.encoder.action, value: `${burn_test.encoder.value[burn_test.encoder.input.mode.value]} ${burn_test.encoder.input.axis.value}`})"
                      >
                        Start
                      </v-btn>
                    </template>
                    <span>{{ burn_test.encoder.value[burn_test.encoder.input.mode.value] }} {{ burn_test.encoder.input.axis.value }}</span>
                  </v-tooltip>
                </v-col>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        width="130"
                        v-bind="attrs"
                        v-on="on"
                        class="ma-2"
                        color="red"
                        dark
                        rounded
                        @click="btnSendShell({sid: shell.sid, action: burn_test.encoder.action, value: burn_test.encoder.value_stop})"
                      >
                        Stop
                      </v-btn>
                    </template>
                    <span>{{ burn_test.encoder.value_stop }}</span>
                  </v-tooltip>
                </v-col>
              </v-row>
              <v-divider />
              <!-- Gyems Position -->
              <pre class="mx-2">{{ burn_test.gyems.position.title }}</pre>
              <v-row>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-select
                    v-model="burn_test.gyems.position.input.axis.value"
                    :items="burn_test.gyems.position.input.axis.option"
                    rounded
                    filled
                    :label="burn_test.gyems.position.input.axis.title"
                  />
                </v-col>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-select
                    v-model="burn_test.gyems.position.input.direction.value"
                    :items="burn_test.gyems.position.input.direction.option"
                    rounded
                    filled
                    :label="burn_test.gyems.position.input.direction.title"
                  />
                </v-col>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-text-field
                    v-model="burn_test.gyems.position.input.position_1.value"
                    rounded
                    filled
                    :label="burn_test.gyems.position.input.position_1.title"
                  />
                </v-col>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-text-field
                    v-model="burn_test.gyems.position.input.position_2.value"
                    rounded
                    filled
                    :label="burn_test.gyems.position.input.position_2.title"
                  />
                </v-col>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        width="130"
                        v-bind="attrs"
                        v-on="on"
                        class="ma-2"
                        color="green"
                        dark
                        rounded
                        :disabled="!burn_test.gyems.position.input.position_1.value || !burn_test.gyems.position.input.position_2.value"
                        @click="btnSendShell({sid: shell.sid, action: burn_test.gyems.position.action, value: `${burn_test.gyems.position.value} ${burn_test.gyems.position.input.axis.value} ${burn_test.gyems.position.input.direction.value} ${burn_test.gyems.position.input.position_1.value} ${burn_test.gyems.position.input.position_2.value}`})"
                      >
                        Confirm
                      </v-btn>
                    </template>
                    <span>{{ burn_test.gyems.position.value }} {{ burn_test.gyems.position.input.axis.value }} {{ burn_test.gyems.position.input.direction.value }} {{ burn_test.gyems.position.input.position_1.value }} {{ burn_test.gyems.position.input.position_2.value }}</span>
                  </v-tooltip>
                </v-col>
              </v-row>
              <v-divider />
              <!-- Gyems Speed -->
              <pre class="mx-2">{{ burn_test.gyems.speed.title }}</pre>
              <v-row>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-select
                    v-model="burn_test.gyems.speed.input.axis.value"
                    :items="burn_test.gyems.speed.input.axis.option"
                    rounded
                    filled
                    :label="burn_test.gyems.speed.input.axis.title"
                  />
                </v-col>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-text-field
                    v-model="burn_test.gyems.speed.input.speed.value"
                    rounded
                    filled
                    :label="burn_test.gyems.speed.input.speed.title"
                  />
                </v-col>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        width="130"
                        v-bind="attrs"
                        v-on="on"
                        class="ma-2"
                        color="green"
                        dark
                        rounded
                        :disabled="!burn_test.gyems.speed.input.speed.value"
                        @click="btnSendShell({
                          sid: shell.sid, 
                          action: burn_test.gyems.speed.action, 
                          value: `${burn_test.gyems.speed.value} ${burn_test.gyems.speed.input.axis.value} ${burn_test.gyems.speed.input.speed.value}`
                        })"
                      >
                        Confirm
                      </v-btn>
                    </template>
                    <span>{{ burn_test.gyems.speed.value }} {{ burn_test.gyems.speed.input.axis.value }} {{ burn_test.gyems.speed.input.speed.value }}</span>
                  </v-tooltip>
                </v-col>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        width="130"
                        v-bind="attrs"
                        v-on="on"
                        class="ma-2"
                        color="orange"
                        dark
                        rounded
                        @click="btnSendShell({
                          sid: shell.sid, 
                          action: burn_test.gyems.speed.action, 
                          value: burn_test.gyems.speed.value_stop
                        })"
                      >
                        Stop
                      </v-btn>
                    </template>
                    <span>{{ burn_test.gyems.speed.value_stop }}</span>
                  </v-tooltip>
                </v-col>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        width="130"
                        v-bind="attrs"
                        v-on="on"
                        class="ma-2"
                        color="red"
                        dark
                        rounded
                        @click="btnSendShell({
                          sid: shell.sid, 
                          action: burn_test.gyems.speed.action, 
                          value: burn_test.gyems.speed.value_off
                        })"
                      >
                        Off
                      </v-btn>
                    </template>
                    <span>{{ burn_test.gyems.speed.value_off }}</span>
                  </v-tooltip>
                </v-col>
              </v-row>
              <v-divider />
              <!-- Calibration -->
              <pre class="mx-2">{{ burn_test.gyems.calibrate.title }}</pre>
              <v-row>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-select
                    v-model="burn_test.gyems.calibrate.input.mode.value"
                    :items="burn_test.gyems.calibrate.input.mode.option"
                    rounded
                    filled
                    :label="burn_test.gyems.calibrate.input.mode.title"
                  />
                </v-col>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-select
                    v-model="burn_test.gyems.calibrate.input.axis.value"
                    :items="burn_test.gyems.calibrate.input.axis.option"
                    rounded
                    filled
                    :label="burn_test.gyems.calibrate.input.axis.title"
                  />
                </v-col>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-select
                    v-model="burn_test.gyems.calibrate.input.direction.value"
                    :items="burn_test.gyems.calibrate.input.direction.option"
                    rounded
                    filled
                    :label="burn_test.gyems.calibrate.input.direction.title"
                  />
                </v-col>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        width="130"
                        v-bind="attrs"
                        v-on="on"
                        class="ma-2"
                        color="green"
                        dark
                        rounded
                        @click="btnSendShell({
                          sid: shell.sid, 
                          action: burn_test.gyems.calibrate.action, 
                          value: `${burn_test.gyems.calibrate.value} ${burn_test.gyems.calibrate.input.mode.value} ${burn_test.gyems.calibrate.input.axis.value} ${burn_test.gyems.calibrate.input.direction.value}`
                        })"
                      >
                        Confirm
                      </v-btn>
                    </template>
                    <span>{{ burn_test.gyems.calibrate.value }} {{ burn_test.gyems.calibrate.input.mode.value }} {{ burn_test.gyems.calibrate.input.axis.value }} {{ burn_test.gyems.calibrate.input.direction.value }}</span>
                  </v-tooltip>
                </v-col>
              </v-row>
              <v-divider />
              <!-- Pick Drop -->
              <pre class="mx-2">{{ burn_test.winching.winch.title }}</pre>
              <v-row>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-text-field
                    v-model="burn_test.winching.winch.input.count.value"
                    rounded
                    filled
                    :label="burn_test.winching.winch.input.count.title"
                  />
                </v-col>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-text-field
                    v-model="burn_test.winching.winch.input.level.value"
                    rounded
                    filled
                    :label="burn_test.winching.winch.input.level.title"
                  />
                </v-col>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-text-field
                    v-model="burn_test.winching.winch.input.weight.value"
                    rounded
                    filled
                    :label="burn_test.winching.winch.input.weight.title"
                  />
                </v-col>
              </v-row>
              <v-row>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-select
                    v-model="burn_test.winching.winch.input.type.value"
                    :items="burn_test.winching.winch.input.type.option"
                    rounded
                    filled
                    :label="burn_test.winching.winch.input.type.title"
                  />
                </v-col>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-select
                    v-model="burn_test.winching.winch.input.winch_type.value"
                    :items="burn_test.winching.winch.input.winch_type.option"
                    rounded
                    filled
                    :label="burn_test.winching.winch.input.winch_type.title"
                  />
                </v-col>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        width="130"
                        v-bind="attrs"
                        v-on="on"
                        class="ma-2"
                        color="green"
                        dark
                        rounded
                        :disabled="!burn_test.winching.winch.input.count.value || !burn_test.winching.winch.input.level.value || !burn_test.winching.winch.input.weight.value"
                        @click="btnSendShell({
                          sid: shell.sid, 
                          action: burn_test.winching.winch.action, 
                          value: `${burn_test.winching.winch.value} ${burn_test.winching.winch.input.type.value} ${burn_test.winching.winch.input.count.value} ${burn_test.winching.winch.input.level.value} ${burn_test.winching.winch.input.weight.value} ${burn_test.winching.winch.input.winch_type.value}`
                        })"
                      >
                        Confirm
                      </v-btn>
                    </template>
                    <span>{{ burn_test.winching.winch.value }} {{ burn_test.winching.winch.input.type.value }} {{ burn_test.winching.winch.input.count.value }} {{ burn_test.winching.winch.input.level.value }} {{ burn_test.winching.winch.input.weight.value }} {{ burn_test.winching.winch.input.winch_type.value }}</span>
                  </v-tooltip>
                </v-col>
              </v-row>
              <v-divider />
              <!-- Winch Calibrate -->
              <pre class="mx-2">{{ burn_test.winching.calibrate.title }}</pre>
              <v-row>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-select
                    v-model="burn_test.winching.calibrate.input.mode.value"
                    :items="burn_test.winching.calibrate.input.mode.option"
                    rounded
                    filled
                    :label="burn_test.winching.calibrate.input.mode.title"
                  />
                </v-col>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-text-field
                    v-model="burn_test.winching.calibrate.input.level.value"
                    rounded
                    filled
                    :label="burn_test.winching.calibrate.input.level.title"
                  />
                </v-col>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-select
                    v-model="burn_test.winching.calibrate.input.dest_type.value"
                    :items="burn_test.winching.calibrate.input.dest_type.option"
                    rounded
                    filled
                    :label="burn_test.winching.calibrate.input.dest_type.title"
                  />
                </v-col>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        width="130"
                        v-bind="attrs"
                        v-on="on"
                        class="ma-2"
                        color="green"
                        dark
                        rounded
                        :disabled="!burn_test.winching.calibrate.input.level.value"
                        @click="btnSendShell({
                          sid: shell.sid, 
                          action: burn_test.winching.calibrate.action, 
                          value: `${burn_test.winching.calibrate.value} ${burn_test.winching.calibrate.input.mode.value} ${burn_test.winching.calibrate.input.level.value} ${burn_test.winching.calibrate.input.dest_type.value}`
                        })"
                      >
                        Confirm
                      </v-btn>
                    </template>
                    <span>{{ burn_test.winching.calibrate.value }} {{ burn_test.winching.calibrate.input.mode.value }} {{ burn_test.winching.calibrate.input.level.value }} {{ burn_test.winching.calibrate.input.dest_type.value }}</span>
                  </v-tooltip>
                </v-col>
              </v-row>
              <v-divider />
              <!-- RSSI -->
              <pre class="mx-2">{{ burn_test.rssi.title }}</pre>
              <v-row>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-text-field
                    v-model="burn_test.rssi.input.repeat.value"
                    rounded
                    filled
                    :label="burn_test.rssi.input.repeat.title"
                    type="number"
                  />
                </v-col>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-text-field
                    v-model="burn_test.rssi.input.delay.value"
                    rounded
                    filled
                    :label="burn_test.rssi.input.delay.title"
                    type="number"
                  />
                </v-col>
                <v-col
                  cols="2"
                  class="mx-2"
                >
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        width="130"
                        v-bind="attrs"
                        v-on="on"
                        class="ma-2"
                        color="green"
                        dark
                        rounded
                        :disabled="!burn_test.rssi.input.repeat.value"
                        @click="btnSendShell({
                          sid: shell.sid, 
                          action: burn_test.rssi.action, 
                          value: burn_test.rssi.value
                        }, burn_test.rssi.input.repeat.value, burn_test.rssi.input.delay.value)"
                      >
                        Confirm
                      </v-btn>
                    </template>
                    <span>{{ burn_test.rssi.value }}</span>
                  </v-tooltip>
                </v-col>
              </v-row>
              <v-divider />
            </v-tab-item>
          </v-tabs-items>
        </v-card>
        <!-- All -->
        <v-dialog
          v-if="shell['boolAll']"
          v-model="shell['boolAll']"
          max-width="800"
          @keydown.enter="shell['boolAll']=false, btnSendAll(command)"
        >
          <v-card>
            <v-toolbar
              dark
              :color="getSubmitStatus(shell['txtAll'].status)"
            >
              <v-toolbar-title>Status: {{ shell['txtAll'].status }}</v-toolbar-title>
            </v-toolbar>
            <v-progress-linear
              v-if="shell['txtAll'].status == 'Pending'"
              indeterminate
              color="green"
            />
            <v-card-text class="pt-6">
              <pre class="text-wrap">{{ shell['txtAll'].reason }}</pre>
              <v-row>
                <v-col cols="3">
                  <v-row
                    v-for="sid in shell['sid']"
                    :key="sid"
                  >
                    <v-chip
                      color="green"
                      dark
                      class="ma-1"
                    >
                      <v-icon class="mx-1">
                        mdi-car
                      </v-icon>
                      <pre>SC {{ sid }}</pre>
                    </v-chip>
                  </v-row>
                </v-col>
                <v-col>
                  <v-row
                    v-for="data in shell['txtAll'].data"
                    :key="data"
                  >
                    <v-chip
                      color="orange"
                      dark
                      class="ma-1"
                    >
                      <v-icon class="mx-1">
                        mdi-message
                      </v-icon>
                      <pre>{{ data }}</pre> 
                    </v-chip>
                  </v-row>
                </v-col>
              </v-row>
            </v-card-text>
            <v-card-actions>
              <v-spacer />
              <v-btn
                color="green darken-1"
                text
                @click="shell['boolAll'] = false, btnSendAll(command)"
              >
                Yes
              </v-btn>
              <v-btn
                color="green darken-1"
                text
                @click="shell['boolAll'] = false"
              >
                No
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>
        <!-- Check Bin -->
        <v-dialog
          v-if="shell['boolBin']"
          v-model="shell['boolBin']"
          max-width="500"
          @keydown.enter="shell['boolBin']=false"
        >
          <v-card>
            <v-toolbar
              dark
              :color="getSubmitStatus(shell['txtBin'].status)"
            >
              <v-toolbar-title>Status: {{ shell['txtBin'].status }}</v-toolbar-title>
            </v-toolbar>
            <v-progress-linear
              v-if="shell['txtBin'].status == 'Pending'"
              indeterminate
              color="green"
            />
            <v-card-text class="pt-6">
              <span v-if="shell['txtBin'].data == 0">
                <pre class="text-wrap">{{ shell.txtBin.reason }}</pre>
              </span>
              <span v-else>
                <v-row
                  v-for="data in shell['txtBin'].data"
                  :key="data[0]"
                >
                  <v-chip
                    color="green"
                    dark
                    class="ma-1"
                  >
                    <v-icon class="mx-1">mdi-cube</v-icon>
                    <pre>{{ data[0] }}</pre> 
                  </v-chip>
                  <v-chip
                    color="orange"
                    dark
                    class="ma-1"
                    width="150"
                  >
                    <v-icon class="mx-1">mdi-map-marker</v-icon>
                    <pre>{{ data[1] }}</pre> 
                  </v-chip>
                </v-row>
              </span>
            </v-card-text>
            <v-card-actions>
              <v-spacer />
              <v-btn
                color="green darken-1"
                text
                @click="shell['boolBin'] = false"
              >
                Close
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>
      </v-tab-item>
      <!-- Feedback -->
      <v-dialog
        v-if="boolEng"
        v-model="boolEng"
        max-width="800"
        @keydown.enter="boolEng=false"
      >
        <v-card>
          <v-toolbar
            dark
            :color="getSubmitStatus(txtEng.status)"
          >
            <v-toolbar-title>Status: {{ txtEng.status }}</v-toolbar-title>
          </v-toolbar>
          <v-progress-linear
            v-if="txtEng.status == 'Pending'"
            indeterminate
            color="green"
          />
          <v-card-text class="pt-6">
            <pre class="text-wrap">{{ txtEng.reason }}</pre>
            <v-row>
              <v-col>
                <v-row>
                  <v-chip
                    color="green"
                    dark
                    class="ma-1"
                    v-for="sid in shell['sid']"
                    :key="sid"
                  >
                    <v-icon class="mx-1">
                      mdi-car
                    </v-icon>
                    <pre>SC {{ sid }}</pre>
                  </v-chip>
                </v-row>
              </v-col>
              <v-col>
                <span v-if="txtEng.data.length != 0">
                  <v-row
                    v-for="data in txtEng.data"
                    :key="data"
                  >
                    <v-chip
                      color="orange"
                      dark
                      class="ma-1"
                    >
                      <v-icon class="mx-1">mdi-message</v-icon>
                      <pre>{{ data }}</pre> 
                    </v-chip>
                  </v-row>
                </span>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-spacer />
            <v-btn
              color="green darken-1"
              text
              @click="boolEng = false"
            >
              Close
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
      <!-- Error Mode -->
      <v-tab-item>
        <v-toolbar
          dark 
          color="black"
        >
          <v-row>
            <v-spacer />
            <v-menu
              v-model="modelErrorSkycar.bolmenu"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-y
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  class="ma-1"
                  v-model="modelErrorSkycar.dtfromto"
                  label="Choose dates"
                  prepend-icon="mdi-calendar"
                  readonly
                  v-bind="attrs"
                  v-on="on"
                >
                  {{ modelErrorSkycar.dtfromto }}
                </v-text-field>
              </template>
              <v-date-picker
                range
                v-model="modelErrorSkycar.dtfromto"
                @input="modelErrorSkycar.bolmenu = false"
              />
            </v-menu>
  
              
            <v-btn
              class="ma-1"
              @click="btnGetErrorSkycar()"
              color="green"
            >
              <v-icon>mdi-restart</v-icon>
              <span>Refresh</span>
            </v-btn>
            <v-btn
              class="ma-1"
              color="blue"
            >
              <download-csv 
                :data="error.result"
                :fields="modelErrorSkycar.exportFields"
                :name="SkycarErrorExportFileName"
              >
                <v-icon>mdi-download</v-icon> DOWNLOAD CSV      
              </download-csv>
            </v-btn>
          </v-row>
        </v-toolbar>
        <v-data-table
          :headers="error['headers']"
          :items="error['result']"
          :items-per-page="15"
          group-by="zone"
          class="elevation-1"
          dark
          item-class="yellow--text text--accent-4"
        >
          <!-- Local Time -->
          <template v-slot:[`item.time`]="{ item }">
            {{ item.dt_local }}
          </template>

          <template v-slot:[`item.recovered_time`]="{ item }">
            {{ item.rt_local }}
          </template>
  
          <!-- Error Message -->
          <template v-slot:[`item.error_msg`]="{ item }">
            <v-tooltip bottom>
              <template v-slot:activator="{ on, attrs }">
                <span
                  v-bind="attrs"
                  v-on="on"
                >{{ item.error_msg }}</span>
              </template>
              <v-card
                dark
                min-width="500"
              >
                <v-card-title>Error Message</v-card-title>
                <v-simple-table 
                  fixed-header
                >
                  <template v-slot:default>
                    <thead>
                      <tr>
                        <th class="text-left">
                          Title
                        </th>
                        <th class="text-left">
                          Data
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        v-for="detail in item.msg_detail"
                        :key="detail.title"
                      >
                        <td>{{ detail.title }}</td>
                        <td>{{ detail.data }}</td>
                      </tr>
                    </tbody>
                  </template>
                </v-simple-table>
              </v-card>
            </v-tooltip>
          </template>
  
          <!-- Error Name -->
          <template v-slot:[`item.error_name`]="{ item }">
            <v-tooltip bottom>
              <template v-slot:activator="{ on, attrs }">
                <v-icon
                  v-bind="attrs"
                  v-on="on"
                  @click="btnGetErrorDetail(item.error_detail)"
                  class="mx-1"
                >
                  mdi-information
                </v-icon>
              </template>
              <span>Show Error Detail</span>
            </v-tooltip>
            <span>{{ item.error_name }}</span>
          </template>
            
          <!-- Action Button -->
          <template v-slot:[`item.error_action_remark`]="{ item }">
            <v-btn
              small
              class="mr-2"
              light
              @click="btnSkycarErrorDialog(item,'remark')"
            >
              Action
            </v-btn>
          </template>
  
          <!-- Warning Log Button -->
          <template v-slot:[`item.warning_log`]="{ item }">
            <v-btn
              small
              class="mr-2"
              light
              @click="btnSkycarErrorDialog(item,'warning')"
            >
              Warning Log
            </v-btn>
          </template>
        </v-data-table>
  
        <!-- Action For Error Skycar Detail -->
        <DialogSkycarErrorAction 
          :error="error" 
          :model-error-skycar="modelErrorSkycar" 
          :target-a-p-i="RouteSkycar.ERROR_SKYCAR"
          :target-host="getTcHost()" 
          :callable-http="AxiosHttpWithAwesomeAlert"
        />
         
        
  
        <!-- Action For Error Skycar Warning Logs -->
        <DialogSkycarErrorWarningLogs 
          :error="error" 
          :model-error-skycar="modelErrorSkycar"
        />
     
  
  
        <!-- Detail -->
        <v-dialog
          v-if="error['boolDetail']"
          v-model="error['boolDetail']"
          max-width="500"
          @keydown.enter="error['boolDetail']=false"
        >
          <v-card>
            <v-toolbar
              dark
              color="green"
            >
              <v-toolbar-title>Error Detail</v-toolbar-title>
            </v-toolbar>
            <v-card-text class="pt-6">
              <pre class="text-wrap">{{ error['txtDetail'].reason }}</pre>
              <v-row>
                <v-col>
                  <span v-if="error['txtDetail'].length != 0">
                    <v-row
                      v-for="data in error['txtDetail']"
                      :key="data"
                    >
                      <pre class="text-wrap">{{ data }}</pre> 
                    </v-row>
                  </span>
                </v-col>
              </v-row>
            </v-card-text>
            <v-card-actions>
              <v-spacer />
              <v-btn
                color="green darken-1"
                text
                @click="error['boolDetail'] = false"
              >
                Close
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>
        <!-- Refresh -->
        <v-dialog
          v-if="error['boolRefresh']"
          v-model="error['boolRefresh']"
          max-width="500"
          @keydown.enter="error['boolRefresh']=false"
        >
          <v-card>
            <v-toolbar
              dark
              :color="getSubmitStatus(error['txtRefresh'].status)"
            >
              <v-toolbar-title>Status: {{ error['txtRefresh'].status }}</v-toolbar-title>
            </v-toolbar>
            <v-progress-linear
              v-if="error['txtRefresh'].status == 'Pending'"
              indeterminate
              color="green"
            />
            <v-card-text class="pt-6">
              <pre class="text-wrap">{{ error['txtRefresh'].message }}</pre>
              <v-row>
                <v-col>
                  <span v-if="error['txtRefresh'].data.length != 0">
                    <v-row
                      v-for="data in error['txtRefresh'].data"
                      :key="data"
                    >
                      <v-chip
                        color="orange"
                        dark
                        class="ma-1"
                      >
                        <v-icon class="mx-1">mdi-message</v-icon>
                        <pre>{{ data }}</pre> 
                      </v-chip>
                    </v-row>
                  </span>
                </v-col>
              </v-row>
            </v-card-text>
            <v-card-actions>
              <v-spacer />
              <v-btn
                color="green darken-1"
                text
                @click="error['boolRefresh'] = false"
              >
                Close
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>
      </v-tab-item>
      <!-- Debug Mode -->
      <v-tab-item>
        <v-card dark>
          <v-card-text>
            <v-row>
              <!-- TC Log -->
              <v-col>
                <v-card-title>TC Log</v-card-title>
                <FormSkycarMessages :get-card-message="getCardMessage"/>
              </v-col>
              <v-divider vertical />
              <!-- TC ADG -->
              <v-col>
                <v-card-title>TC Adg</v-card-title>
                <FormAdgTracking :cube-options="zones" />
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
        <v-dialog
          v-model="debug['boolSpecific']"
          @keydown.enter="debug['boolSpecific']=false"
        >
          <v-card>
            <v-card-title>Result</v-card-title>
            <v-col class="ma-1">
              <v-data-table
                :headers="debug['headSpecific']"
                :items="debug['resSpecific']"
                :items-per-page="-1"
                :sort-by="['date']"
                dark
                dense
                :item-class="getMessageColor"
                hide-default-footer
              />
            </v-col>
            <v-row>
              <v-spacer />
              <v-btn
                color="green darken-1"
                text
                @click="debug['boolSpecific']=false"
                class="ma-2"
              >
                Close
              </v-btn>
            </v-row>
          </v-card>
        </v-dialog>
      </v-tab-item>
      <!-- Utility Mode -->
      <v-tab-item>
        <v-toolbar
          dark 
          color="black"
        >
          <v-row>
            <v-menu
              v-model="utilitySkycar.bolmenu"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-y
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  class="ma-1"
                  v-model="utilitySkycar.dtfromto"
                  label="Choose dates"
                  prepend-icon="mdi-calendar"
                  readonly
                  v-bind="attrs"
                  v-on="on"
                />
              </template>
              <v-date-picker
                range
                v-model="utilitySkycar.dtfromto"
                @input="utilitySkycar.bolmenu = false"
              />
            </v-menu>

            <v-btn
              class="ma-1"
              @click="btnGetCubeData()"
              color="green"
            >
              <v-icon>mdi-restart</v-icon>
              <span>Refresh</span>
            </v-btn>
            <v-btn
              class="ma-1"
              color="blue"
            >
              <download-csv 
                :data="utilitySkycar.result"
                :fields="utilitySkycar.exportFields"
                :name="CubeDataExportFileName"
              >
                <v-icon>mdi-download</v-icon> DOWNLOAD CSV      
              </download-csv>
            </v-btn>
          </v-row>
        </v-toolbar>

        <v-data-table
          :headers="utilitySkycar['headers']"
          :items="utilitySkycar['result']"
          :items-per-page="15"
          class="elevation-1"
          dark
          item-class="yellow--text text--accent-4"
        >
          <!-- Number of Error Message -->
          <template v-slot:[`item.number_of_errors`]="{ item }">
            <v-tooltip bottom>
              <template v-slot:activator="{ on, attrs }">
                <span
                  v-bind="attrs"
                  v-on="on"
                >{{ getTotalNumberOfErrors(item.number_of_errors) }}</span>
              </template>
              <v-card
                dark
                min-width="500"
              >
                <v-card-title>Error Skycars</v-card-title>
                <v-simple-table 
                  fixed-header
                >
                  <template v-slot:default>
                    <thead>
                      <tr>
                        <th class="text-left">
                          Skycar ID
                        </th>
                        <th class="text-left">
                          Number of Errors
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        v-for="(value, key) in JSON.parse(item.number_of_errors)"
                        :key="key"
                      >
                        <td>{{ key }}</td>
                        <td>{{ value }}</td>
                      </tr>
                    </tbody>
                  </template>
                </v-simple-table>
              </v-card>
            </v-tooltip>
          </template>
        </v-data-table>

        <v-dialog
          v-if="utilitySkycar['boolRefresh']"
          v-model="utilitySkycar['boolRefresh']"
          max-width="500"
          @keydown.enter="utilitySkycar['boolRefresh']=false"
        >
          <v-card>
            <v-toolbar
              dark
              :color="getSubmitStatus(utilitySkycar['txtRefresh'].status)"
            >
              <v-toolbar-title>Status: {{ utilitySkycar['txtRefresh'].status }}</v-toolbar-title>
            </v-toolbar>
            <v-progress-linear
              v-if="utilitySkycar['txtRefresh'].status == 'Pending'"
              indeterminate
              color="green"
            />
            <v-card-text class="pt-6">
              <pre class="text-wrap">{{ utilitySkycar['txtRefresh'].message }}</pre>
            </v-card-text>
            <v-card-actions>
              <v-spacer />
              <v-btn
                color="green darken-1"
                text
                @click="utilitySkycar['boolRefresh'] = false"
              >
                Close
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>
      </v-tab-item>
    </v-tabs-items>
    <CardSkycarMessages ref="cardSkycarMessages" />
    <v-btn 
      v-show="this.shell.boolChat && showMoveButton()"
      dark
      color="deep-purple accent-4"
      :style="moveButtonStyle" 
      small
      @mousedown="startTracking"
      @mousemove="trackMovement"
      @mouseup="stopTracking"
      @mouseover="increaseSize"
      @mouseleave="reduceSize"
    >
      <v-icon>
        mdi-cursor-move
      </v-icon>
    </v-btn>
    <v-btn
      dark
      color="deep-purple accent-4"
      @click="configChatDialog"
      :style="buttonStyle"
      small
    >
      <v-icon left>
        mdi-message-text
      </v-icon> Start Chat
      <div v-if="shell.selectedSid.length > 0">
        <v-icon class="ml-1 mr-1">
          mdi-car
        </v-icon>
        <span v-if="shell.selectedSid.length > 1">(Group)</span>
        <span v-else-if="shell.selectedSid.length === 1">(SC{{ shell.selectedSid[0] }})</span>
      </div>
      <v-badge
        class="ml-2"
        v-if="shell.selectedSid.length > 0 && badgeContent" 
        :content="badgeContent" 
        color="red"
      />
    </v-btn>
  
    <DialogAction
      :show-notification="showNotification"
      :sync-skycar="syncSkycar"
      ref="skycarActionDialog"
    />
    <DialogCheckBin ref="checkBinDialog" />
    <SnackbarNotification ref="snackbarNotification" />
    <DialogAddSkycar
      :show-notification="showNotification"
      :sync-skycar="syncSkycar"
      ref="dialogAddSkycar"
    />
  
    <DialogSkycarChatbox
      :chat-bool="shell.boolChat"
      :chat-txt="shell.specificTxtChat"
      :skycar-selected="shell.selectedSid"        
      :xMovements="xMovements"
      :yMovements="yMovements"
      @close-dialog="configChatDialog"
      @send-message="sendConversation"
      ref="dialogSkycarChatbox"
    />
    <DialogEventLog 
      :event-options="[Event.GENERAL, Event.STORAGE, Event.BATTERY]"
      :show-notification="showNotification" 
      ref="dialogEventLog" 
    />
    <DialogCoordinateSelection 
      ref="dialogCoordinateSelection" 
      @update-coord="updateCoord"
    />
  </v-app>
</template>
  
  <script>
  import CardSkycarMessages from "./skycarMessage/CardSkycarMessages.vue"
  import DialogAddSkycar from "./skycar/DialogAddSkycar"
  import { getCube, getStatus , getHost, convertStringToLocal, 
    convertTimestampToLocal, getRequestHeader, useRefreshToken, 
  } from "../helper/common.js";
  import { Event, Module, RouteSkycar, RouteAnalysis, 
    SkycarRecovery, RouteOperation, SkycarShellCommand, Websocket } from "../helper/enums.js"
  import { AxiosHttpWithAwesomeAlert } from "../helper/http_request.js";
  import DialogSkycarErrorAction from "./dialogs/DialogSkycarErrorAction.vue"
  import DialogSkycarErrorWarningLogs from "./dialogs/DialogSkycarErrorWarningLogs.vue"
  import DialogAction from "./skycarAction/ActionDialog.vue"
  import DialogCheckBin from "./skycarAction/CheckBin.vue"
  import FormAdgTracking from "./skycarAdg/FormAdgTracking.vue";
  import FormSkycarMessages from "./skycarMessage/FormSkycarMessages.vue";
  import SnackbarNotification from "./shared/SnackbarNotification.vue"
  import DialogSkycarChatbox from "./dialogs/DialogSkycarChatbox.vue"
  import DialogEventLog from "./dialogs/DialogEventLog.vue";
import DialogCoordinateSelection from "./dialogs/DialogCoordinateSelection.vue";
  
  
let httpRequest = require("../helper/http_request");
  import axios from "axios";
  import { socket } from "../App.vue"
  
  export default {
    name: "App",
    components: {
      CardSkycarMessages,
      DialogSkycarErrorAction,
      DialogSkycarErrorWarningLogs, 
      DialogAction,
      DialogCheckBin,
      FormAdgTracking,
      FormSkycarMessages,
      SnackbarNotification,
      DialogAddSkycar,
      DialogSkycarChatbox,
      DialogEventLog,
      DialogCoordinateSelection
    },
    created() {
      this.syncSkycar()
      this.getMessage(socket)
      this.syncGW()
      this.updateChatMessage()
    },
    mounted() {
      window.addEventListener('beforeunload', this.handleBeforeUnload);
      if (this.shell.selectedSid.length > 0){
        this.startIdleTimer();
        window.addEventListener("mousemove", this.resetIdleTimer);
        window.addEventListener("keypress", this.resetIdleTimer);
      }

      window.addEventListener("resize", this.handleWindowResize);
      this.updateStyle();
    },
    async beforeDestroy() {
      this.resetChosenSkycar()
      await this.updateChatSid()
      this.toggleIdleTimer();
      window.removeEventListener("resize", this.handleWindowResize);
      window.removeEventListener('beforeunload', this.handleBeforeUnload);
    },
  
    data: () => ({
      Event,
      RouteSkycar,
      convertStringToLocal,
      convertTimestampToLocal,
      SkycarRecovery,
      MaintenanceAction,
      idleTime: 0,
      idleTimeoutMinute: 5,
      timerStarted: false,
      minScreenWidth: 780,
      buttonStyle: null,
      moveButtonStyle: null,
      dragging: null,
      clickX: null,
      clickY: null,
      xMovements: 0,
      yMovements: 0,

      // Skycar Utility V-Model
      utilitySkycar: {
        dtfromto : [
          new Date().toISOString().slice(0,10),
          new Date().toISOString().slice(0,10)
        ],

        headers: [
          { text: "Date", value: "date" },
          { text: "Total Skycar ID", value: "total_skycar_ids" },
          { text: "Enroll Skycar ID", value: "enroll_skycar_ids" },
          { text: "Disenroll Skycar ID", value: "disenroll_skycar_ids" },
          { text: "Available Skycar ID", value: "available_skycar_ids" },
          { text: "Working Skycar ID", value: "working_skycar_ids" },
          { text: "Maintenance Skycar ID", value: "maintenance_skycar_ids" },
          { text: "Error Skycar ID", value: "error_skycar_ids" },
          { text: "Total Number of Errors", value: "number_of_errors" },
        ],
        exportFields : ["date","total_skycar_ids","total_skycar_quantity","enroll_skycar_ids","enroll_skycar_quantity",
        "disenroll_skycar_ids","disenroll_skycar_quantity","available_skycar_ids", "available_skycar_quantity", 
        "working_skycar_ids", "working_skycar_quantity", "maintenance_skycar_ids", "maintenance_skycar_quantity", 
        "error_skycar_ids", "error_skycar_quantity", "number_of_errors"],
        
        result: Array(),
        boolRefresh: false,
        txtRefresh: null,
      },
  
      // Skycar Error V-Model
      modelErrorSkycar:{
  
        // Skycar Error Excel
        dtfromto : [
          new Date().toISOString().slice(0,10),
          new Date().toISOString().slice(0,10)
        ],
        exportFields : ["time","dt_local","zone","sid","error_msg","error_name","error_detail","remark"],
  
        // Skycar Error Action Dialog
        selectedSkycarError : {},
      },
      
      // Skycar Error V-Model
      error: {
        headers: [
          { text: "Time", value: "time" },
          { text: "Zone", value: "zone" },
          { text: "Skycar ID", value: "sid" },
          { text: "Error Message", value: "error_msg" },
          { text: "Maintenance Dock ID", value: "md_id" },
          { text: "Error Name", value: "error_name" },
          { text: "Remark", value: "error_action_remark" },
          { text: "Warning", value: "warning_log" },
          { text: "Recovered Time", value: "recovered_time" },
        ],
        result: Array(),
        boolRefresh: false,
        txtRefresh: null,
        boolDetail: false,
        txtDetail: null,
        boolDialogAction : false,
        boolDialogWarningLog : false,
        
      },
      
  
      shell: {
        recovery: [
          { title: "Connect skycar", action: 0, data: 1, color: "green", icon: "mdi-access-point-check" },
          { title: "Home", action: 1, data: "skycab home", color: "lime", icon: "mdi-home" },
          { title: "Pair", action: 1, data: "skycab system pairing", color: "orange", icon: "mdi-car-connected" },
        ],
        status: [
          {
            title: "Shell",
            data: [
              { title: "Enable", action: 0, color: "green", data: 1, icon: "mdi-access-point-check" },
              { title: "Disable", action: 0, color: "lime", data: 0, icon: "mdi-access-point-remove" },
            ]
          },
          {
            title: "Pairing",
            data: [
              { title: "Pairing", action: 1, color: "green", data: "skycab system pairing", icon: "mdi-car-connected" },
            ]
          },
          {
            title: "System",
            data: [
              {
                title: "Wake Up",
                action: 1,
                color: "green",
                data: "skycab system wakeup",
                icon: "mdi-clock-check-outline",
              },
              {
                title: "Hibernate",
                action: 1,
                color: "red",
                data: "skycab system hibernate",
                icon: "mdi-bed-clock",
              },
            ],
          },
          {
            title: "Skycar",
            data: [
              { title: "Info", action: 1, color: "green", data: "skycab system sram i", icon: "mdi-list-status" },
              { title: "Winch", action: 1, color: "lime", data: "skycab system sram i w", icon: "mdi-list-status" },
              { title: "Clear", action: 1, color: "orange", data: "skycab system sram c", icon: "mdi-list-status" },
            ]
          },
          {
            title: "Battery",
            data: [
              { title: "Info", action: 1, color: "green", data: "skycab battery info", icon: "mdi-list-status" },
              { title: "Lock", action: 1, color: "lime", data: "skycab battery action l", icon: "mdi-lock" },
              { title: "Release", action: 1, color: "orange", data: "skycab battery action r", icon: "mdi-lock-open" },
              { title: "Low Bat", action: 1, color: "red", data: "skycab battery trig_low", icon: "mdi-battery-10" },
            ]
          },
          {
            title: "Bin",
            data: [
              { title: "Data", action: 1, color: "green", data: "skycab scanner bin_value", icon: "mdi-numeric" },
              { title: "Read", action: 1, color: "lime", data: "skycab system read bin", icon: "mdi-book-open" },
            ]
          },
          {
            title: "Winching Board",
            data: [
              { title: "Platform", action: 1, color: "green", data: "skycab system read platform", icon: "mdi-download-multiple" },
              { title: "Gripper", action: 1, color: "lime", data: "skycab system read gripper", icon: "mdi-lock-open-alert" },
              { title: "Relay On", action: 1, color: "orange", data: "skycab system set flag batt_relay 1", icon: "mdi-access-point-check" },
              { title: "Relay Off", action: 1, color: "red", data: "skycab system set flag batt_relay 0", icon: "mdi-access-point-remove" },
              { title: "Ping", action: 1, color: "pink darken-1", data: "skycab xbee ping", icon: "mdi-border-outside" },
            ]
          },
          {
            title: "Contactor",
            data: [
              { title: "Enable", action: 1, color: "green", data: "skycab system set flag auto_contactor 1", icon: "mdi-access-point-check" },
              { title: "Disable", action: 1, color: "orange", data: "skycab system set flag auto_contactor 0", icon: "mdi-access-point-remove" },
            ]
          },
          {
            title: "Recovery",
            data: [
              { title: "Home", action: 1, color: "green", data: "skycab home", icon: "mdi-home" },
              { title: "Recover", action: 1, color: "orange", data: "skycab system init_recovery", icon: "mdi-autorenew" },
              { title: "Reboot", action: 1, color: "red", data: "skycab system reboot", icon: "mdi-autorenew" },
            ]
          },
          {
            title: "Xbee",
            data: [
              { title: "Pan Id", action: 1, color: "green", data: "skycab xbee read_pan_id", icon: "mdi-account-outline" },
              { title: "Mode", action: 1, color: "lime", data: "skycab xbee read_sm", icon: "mdi-cellphone-link" },
              { title: "Option", action: 1, color: "orange", data: "skycab xbee read_so", icon: "mdi-autorenew" },
              { title: "Channel", action: 1, color: "red", data: "skycab xbee read_sc", icon: "mdi-book-open" },
            ]
          }
        ],
        form: null,
        all: false,
        sid: Array(),
        new_sid: null,
        coord_x: null,
        coord_y: null,
        bin_x: null,
        bin_y: null,
        bin_code: 0,
        value: "",
        value_axis: "x",
        value_direction: "f",
        value_quantity: 1,
        force_axis: "x",
        force_direction: "f",
        force_quantity: 1,
        value_z_type: "u",
        value_z_quantity: 1,
        value_z_weight: 1,
        value_z_des: "cb",
        value_o: null,
        value_c: null,
        command: null,
        tab: null,
        item: ["Status", "Setting", "Control", "Custom", "Recovery", "Burn Test"],
        boolBin: false,
        txtBin: null,
        boolAll: false,
        txtAll: null,
        socket: null,
        pitch: {
          x_f: { title: "Forward in X", value: "0" },
          x_b :{ title: "Backward in X", value: "0" },
          y_f: { title: "Forward in Y", value: "0" },
          y_b: { title: "Backward in Y", value: "0" },
        },
        pulse: {
          title: "Set Skycar Pulse",
          direction: {
            value: "u",
            option: ["u", "d"],
            title: "Direction"
          },
          increment: {
            value: "i",
            option: ["i", "d"],
            title: "Increment"
          },
          jog: {
            value: null,
            title: "Jog"
          },
          no_jog: {
            value: null,
            title: "No Jog"
          },
          action: 1,
          value: "skycab system set pulse",
          icon: "mdi-check"
        },
        loop_back: {
          title: "Set Xbee Loop Back",
          no_loop: {
            value: null,
            title: "No Loop"
          },
          delay: {
            value: null,
            title: "delay"
          },
          action: 0,
          value_start: "skycab xbee start loop_back",
          value_stop: "skycab xbee stop loop_back",
          icon: "mdi-check"
        },
        mac_address: {
          title: "Set Xbee Mac Address",
          input: {
            id_1: {
              value: null,
              title: "ID 1"
            },
            id_2: {
              value: null,
              title: "ID 2"
            },
            id_3: {
              value: null,
              title: "ID 3"
            },
            id_4: {
              value: null,
              title: "ID 4"
            },
          },
          action: 1,
          value: "skycab xbee mac m",
          icon: "mdi-check"
        },
        set_time: {
          title: "Time",
          input: {
            day: {
              value: new Date().getDate(),
              title: "Day"
            },
            month: {
              value: new Date().getMonth() + 1,
              title: "Month"
            },
            year: {
              value: new Date().getFullYear(),
              title: "Year"
            },
            hour: {
              value: new Date().getHours(),
              title: "Hour"
            },
            minute: {
              value: new Date().getMinutes(),
              title: "Minute"
            },
            second: {
              value: new Date().getSeconds(),
              title: "Second"
            }
          },
          action: 1,
          value: "skycab system set_time",
          icon: "mdi-check",
          header: "Set Time"
        },
        get_time: {
          action: 1,
          value: "skycab system get_time",
          icon: "mdi-check",
          header: "Get Time"
        },
        auto_charge_in: {
          title: "Auto Charge In",
          action: 1,
          value: "skycab system auto_charge_in",
          icon: "mdi-check",
          axis: {
            title: "Axis",
            value: "x",
            option: ["x", "y"]
          },
          direction: {
            title: "Direction",
            value: "f",
            option: ["f", "b"]
          },
          quantity: {
            title: "Quantity",
            value: null
          }
        },
        force_jog: {
          title: "Force Jog",
          action: 1,
          value: "skycab actuator jog",
          icon: "mdi-check",
          axis: {
            title: "Axis",
            value: "x",
            option: ["x", "y"]
          },
          direction: {
            title: "Direction",
            value: "f",
            option: ["f", "b"]
          },
          speed: {
            title: "Quantity",
            value: null
          }
        },
        force_pairing: {
          title: "Force Pairing",
          action: 1,
          value: "skycab system force_pairing",
          icon: "mdi-check",
          axis: {
            title: "Axis",
            value: "x",
            option: ["x", "y", "xy"]
          },
          bin_value: {
            title: "Bin Value",
            value: null
          }
        },
        bypass_job_flag: {
          title: "Bypass Job Flag",
          action: 1,
          value: "skycab system bypass job_flag",
          icon: "mdi-check",
          color: "green"
        },
        bypass_hardware: {
          title: "Bypass Hardware",
          action: 1,
          value: "skycab system bypass hardware",
          icon: "mdi-check",
          color: "orange"
        },
        boolChat: false,
        txtChat: Array(),
        specificTxtChat: Array(),
        notification: Array(),
        selectedSid: Array()
      },
      burn_test: {
        encoder: {
          title: "Gyems Encoder",
          action: 1,
          value: {
            Once: "skycab actuator mcube enc once",
            Continuous: "skycab actuator mcube enc cont"
          },
          value_stop: "skycab actuator mcube enc stop",
          input: {
            mode: {
              title: "Mode",
              option: ["Once", "Continuous"],
              value: "Once"
            },
            axis: {
              title: "Axis",
              option: ["x", "y", "xy"],
              value: "x"
            }
          }
        },
        gyems: {
          position: {
            title: "Gyems Position",
            action: 1,
            value: "skycab actuator mcube pos",
            input: {
              axis: {
                title: "Axis",
                option: ["x", "y"],
                value: "x"
              },
              direction: {
                title: "Direction",
                option: ["f", "b"],
                value: "f"
              },
              position_1: {
                title: "Position",
                value: null
              },
              position_2: {
                title: "Position",
                value: null
              }
            }
          },
          speed: {
            title: "Gyems Speed",
            action: 1,
            value: "skycab actuator mcube speed",
            value_off: "skycab actuator mcube off",
            value_stop: "skycab actuator mcube stop",
            input: {
              axis: {
                title: "Axis",
                option: ["x", "y"],
                value: "x"
              },
              speed: {
                title: "Speed",
                value: null
              }
            }
          },
          calibrate: {
            title: "Mcube Calibrate",
            action: 1,
            value: "skycab system calibrate",
            input: {
              mode: {
                title: "Mode",
                value: "m",
                option: ["m"]
              },
              axis: {
                title: "Axis",
                value: "x",
                option: ["x", "y"]
              },
              direction: {
                title: "Direction",
                value: "f",
                option: ["f", "b"]
              }
            }
          }
        },
        winching: {
          winch: {
            title: "Pick and Drop",
            action: 1,
            value: "skycab system burn_test",
            input: {
              type: {
                title: "Type",
                value: "winch",
                option: ["winch"]
              },
              count: {
                title: "Count",
                value: null
              },
              level: {
                title: "Level",
                value: null
              },
              weight: {
                title: "Weight",
                value: null
              },
              winch_type: {
                title: "Winch Type",
                value: "cb",
                option: ["cb", "stl", "sth", "qcl", "qch"]
              }
            }
          },
          calibrate: {
            title: "Winching Calibrate",
            action: 1,
            value: "skycab system calibrate",
            input: {
              mode: {
                title: "Mode",
                option: ["w"],
                value: "w"
              },
              level: {
                title: "Level",
                value: null,
              },
              dest_type: {
                title: "Destination",
                option: ["cb", "stl", "sth", "qcl", "qch"],
                value: "cb"
              }
            }
          }
        },
        rssi: {
          title: "RSSI",
          action: 2,
          value: "RSSI",
          input: {
            repeat: {
              title: "Repeat",
              value: 1
            },
            delay: {
              title: "Delay",
              value: 0
            }
          }
        }
      },
      debug: {
        boolSpecific: false,
        resSpecific: Array(),
        headSpecific: [
          { text: "Datetime", value: "date" },
          { text: "Message", value: "msg" },
        ]
      },
      modelGW:{
        clients : null,
        allClients : null,
        btnColor : "red",
        btnText: "GW Disconnected"
  
      },
      
      modelSkycar: {
        skycar: [],
        dtSelected: [],
        alert: null,
      },
      headers: [
        {
          text: "Skycar ID ",
          align: "left",
          sortable: true,
          value: "skycar_id",
        },
        { text: "Status", value: "status" },
        { text: "Connect", value: "connect" },
        { text: "Pair", value: "pair" },
        { text: "Coordinate", value: "coordinate" },
        { text: "Winch", value: "winch" },
        { text: "Battery %", value: "battery" },
        // { text: "Mode", value: "mode"},
        { text: "Mode", value: "maintenance_mode" },
        { text: "Action", value: "action" },
      ],
      UpdateStorage : null ,
      RecoverSkycar : null ,
      tabItems: ["Shell Mode", "Error Mode", "Debug Mode", "Utility Mode"],
      tab: null,
      boolEng: false,
      txtEng: null,
      zones: getCube(),
      currentZone: getCube()[0],
      doneSync: true,
  
      shellMessageColor: function(data) {
        switch (data.from_tc) {
          case true: {
            return data.status ? "green" : "red"
          }
          case false: return "orange"
        }
      },
  
      btnGetErrorDetail: function(detail) {
        this.error["boolDetail"] = true
        this.error["txtDetail"] = detail
      },
  
      btnGetErrorSkycar: async function() {
        this.error["boolRefresh"] = true
        this.error["txtRefresh"] = { status: "Pending", message: "Refreshing...", data: [] }
  
        let daterange = this.modelErrorSkycar.dtfromto
  
        let df = daterange[0] 
        let dt = daterange[1] ?? daterange[0] 
  
        var d1 = Date.parse(daterange[0]);
        var d2 = Date.parse(daterange[1]);
        if (d1 > d2) {
             df = daterange[1]
             dt = daterange[0]
        }
        let res = await getAxiosWithParams(RouteSkycar.ERROR_SKYCAR , this.currentZone , { dateFrom:df , dateTo:dt })
        this.error["result"] =  res.data.data ?? []
        this.error["txtRefresh"] = { status: res.status, message: res.data.reason, data: [] }
        
        
      },
  
      btnPassword: function(password) {
        this.shell["password"] = null
        if (password == "ramzatheboss") {
          this.shell["admin"] = true
          this.boolEng = true
          this.txtEng = { status: "Accepted", reason: "rAmZaThEbOsS ! ! !", data: [] }
        } else {
          this.shell["admin"] = false
          this.boolEng = true
          this.txtEng = { status: "Rejected", reason: "Invalid password!", data: [] }
        }
      },
      
      btnCheckBin: async function(code, coord_x, coord_y) {
        await this.$refs.checkBinDialog.submit(this.currentZone, code, coord_x, coord_y)
      },
  
      btnUpdateSid: async function(sid) {
        let res = await axios.get(`${getHost(this.currentZone)}${RouteOperation.CUBE}`, { headers:getRequestHeader() })
        let json = res.data
        if (json.status) {
          let data = json.data
  
          if (data.HALT_CUBE.status != "AVAILABLE") 
            this.$awn.tip("Cube is currently halted, please ensure no people in the cube if you require to perform any skycar movement.")
        }
  
        if (sid == "all") {
          this.shell["all"] = !this.shell["all"]
          if (this.shell["all"]) {
            this.shell["sid"] = this.getSkycarID()
          } else {
            this.shell["sid"] = Array()
          }
        } else {
          if (this.shell["sid"].includes(sid)) {
            const index = this.shell["sid"].indexOf(sid)
            this.shell["sid"].splice(index, 1)
          } else {
            this.shell["sid"].push(sid)
          }
        }
        this.txtEng = { status: "Accepted", reason: "Updated current skycar selection.", data: Array() }
        this.boolEng = true
        this.updateChatSid()
      },
      btnSendShell: async function (json, repeat=1, delay=0) {
        let here = this
        try {
          here.command = json
          here.command.repeat = repeat
          var data = Array()
          if (json.sid.length == 0) {
            data = Array()
            here.txtEng = { status: "Rejected", reason: "Please select a skycar to send command.", data: data }
            here.boolEng = true
          } else if (json.sid.length > 1 || (json.action == 0 && json.value == 1) || json.value == "skycab system reboot") {
            const sidList = json.sid
            sidList.forEach(function(sid) {
              data.push(`SC,${sid},Q,${json.action},${json.value}`)
            })
            here.command = json
            here.shell.boolAll = true
            here.shell.txtAll = { status: "Warning", reason: "Are you sure to send the following command?", data: data }
          } else if (json.sid.length == 1) {
            json.sid = json.sid[0]
            here.sendMessage({ type: "request", json: json }, repeat, delay)
            if (!here.shell.boolChat){
              here.configChatDialog()
            }
          }
        } catch (error) {
          here.txtEng = { status: "Rejected", reason: error, data: data }
          here.boolEng = true
        }
      },
  
      btnSendAll: async function (json) {
        if (Array.isArray(json.sid)) {
          var sid_list = json.sid
        } else {
          sid_list = [json.sid]
        }
        var here = this
        sid_list.forEach(async function(sid) {
          json.sid = parseInt(sid)
          here.sendMessage({ type: "request", json: json }, json.repeat)
        })
        if (!here.shell.boolChat){
          here.configChatDialog()
        }
      },
  
      btnActionDialog: async function(item) {
        // await this.syncSkycar()
        let coord = item.coordinate.split(",")
        this.$refs.skycarActionDialog.openDialog(
          item.skycar_id,
          item.status,
          parseInt(coord[0]),
          parseInt(coord[1]),
          item.winch,
          item.is_docked,
          this.currentZone,
          this.modelSkycar.skycar,
          item.battery,
          item.mode
        )
      },
      btnEventLogDialog: function(item) {
        this.$refs.dialogEventLog.openDialog(`${Module.SC}-${item.skycar_id}`)
      },
      btnSkycarErrorDialog : async function(item,typeOfDialog){
        // Skycar Error Datatable onClick Handler
        if (typeOfDialog == "warning"){
          this.error.boolDialogWarningLog = true
          this.modelErrorSkycar.selectedSkycarError = item
        
        } else if (typeOfDialog == "remark"){
          this.modelErrorSkycar.selectedSkycarError = item
          this.error.boolDialogAction = true
      }
      },
  
    }),
    
    watch: {
      boolEng(newState) {
        if (!newState) {
          if (this.shell.focus) {
            this.$nextTick(function() {
              this.shell.focus = false
              this.$refs.shell_value.focus()
            })
          }
        }
      },
  
      catchMessage(newValue) {
        var here = this
        this.modelSkycar.skycar.map((skycar) => skycar.skycar_id).forEach(function(sid) {
          // Add notification for Skycar Shell Command Messages
          var head = `SC,${sid},Q,`
          if (newValue.message.includes(head)) {
            if (!here.shell.boolChat) {
              const currentCount = here.shell.notification[sid] || 0;
              const newCount = currentCount < 9 ? currentCount + 1 : "9+";
              here.$set(here.shell.notification, sid, newCount);
            } 

            if (here.shell.txtChat[sid]){
              here.shell.txtChat[sid].push(newValue)
            } else {
              here.shell.txtChat[sid] = [newValue];
            }
            
            here.updateChatTxt()
            // if (!here.shell.boolChat && newValue.user === here.$store.state.user.username){
            //   here.configChatDialog()
            // }
          }  
        })

        // Add notification for User joining skycar chat
        if (newValue.status === null && newValue.from_tc === null){
          if (!here.shell.boolChat) {
            const currentCount = here.shell.notification[newValue.sid] || 0;
            const newCount = currentCount < 9 ? currentCount + 1 : "9+";
            here.$set(here.shell.notification, newValue.sid, newCount);
          } 

          if (this.shell.txtChat[newValue.sid]){
            this.shell.txtChat[newValue.sid].push(newValue)
          } else {
            this.shell.txtChat[newValue.sid] = [newValue];
          }

          this.updateChatTxt()
        }
      },
  
      onChangedErrorLogData: function (newVal) {
        // console.log(newVal, oldVal)
        this.updateTimeToLocal(newVal);
      },
      "shell.boolChat": function() {
        this.updateStyle()
      }
    },
    
  methods: {
    openGrid(){
      this.$refs.dialogCoordinateSelection.openDialog(this.currentZone)
    },
    updateCoord(selectedCells){
      if (selectedCells.length > 0) {
        this.shell["bin_x"] = String(selectedCells[0].x);
        this.shell["bin_y"] = String(selectedCells[0].y);
      }
    },
      getTotalNumberOfErrors(item){
        if (item){
          return Object.values(JSON.parse(item)).reduce((accumulator, currentValue) => accumulator + currentValue, 0);
        } else {
          return null
        }
      },
      async btnGetCubeData(){
        this.utilitySkycar["boolRefresh"] = true
        this.utilitySkycar["txtRefresh"] = { status: "Pending", message: "Refreshing..." }

        let daterange = this.utilitySkycar.dtfromto

        let df = daterange[0] 
        let dt = daterange[1] ?? daterange[0] 

        var d1 = Date.parse(daterange[0]);
        var d2 = Date.parse(daterange[1]);
        if (d1 > d2) {
            df = daterange[1]
            dt = daterange[0]
        }
        
        let res = await httpRequest.axiosRequest(
          "get", 
          getHost(this.currentZone), 
          RouteAnalysis.CUBE_DATA, 
          null,
          false,
          `start_date=${df}&end_date=${dt}`
        )

        this.utilitySkycar["result"] = res.data.data ?? []

        for (const item of this.utilitySkycar["result"]){
          if (item["number_of_errors"]){
            item["number_of_errors"] = JSON.stringify(item.number_of_errors)
          }
        }

        this.utilitySkycar["txtRefresh"] = { status: res.status, message: res.data.message }

      },
      getCardMessage() {
        return this.$refs.cardSkycarMessages
      },
      async handleBeforeUnload() {
        this.resetChosenSkycar()
        await this.updateChatSid()
      },
      updateStyle() {
        const viewportWidth = Math.max(document.documentElement.clientWidth, window.innerWidth || 0);
        const viewportHeight = Math.max(document.documentElement.clientHeight, window.innerHeight || 0);

        this.buttonStyle = {
          position: "fixed",
          right: this.shell.boolChat 
            ? (window.innerWidth <= this.minScreenWidth 
              ? "0px" 
              : this.dragging 
                ? `${(0.1 * viewportWidth) + this.xMovements}px` 
                : `${(0.05 * viewportWidth) + this.xMovements}px`) 
            : "0px",
          width: window.innerWidth <= this.minScreenWidth 
            ? "100%"
            : (this.shell.boolChat 
              ? this.dragging 
                ? "40%" 
                : "45%" 
              : "25%"),
          bottom: this.shell.boolChat ? `${(0.4 * viewportHeight) + this.yMovements}px` : "0px",
          height: "4%",
          "z-index": "1",
          "font-size": "14px"
        };

        this.moveButtonStyle = {
          position: "fixed",
          right: `${(0 * viewportWidth) + this.xMovements}px`,
          width: this.dragging ? "10%" : "5%",
          height: this.dragging ? "10%" : "4%",
          bottom: `${(0.4 * viewportHeight) + this.yMovements}px`,
          "z-index": "1",
          cursor: this.dragging ? "grabbing" : "grab",
        };
      },
      increaseSize(){
        const viewportWidth = Math.max(document.documentElement.clientWidth, window.innerWidth || 0);

        this.buttonStyle.width = "40%"
        this.buttonStyle.right = `${(0.1 * viewportWidth) + this.xMovements}px`

        this.moveButtonStyle.width = "10%"
        this.moveButtonStyle.height = "10%"
      },
      reduceSize(){
        const viewportWidth = Math.max(document.documentElement.clientWidth, window.innerWidth || 0);

        this.buttonStyle.width = "45%"
        this.buttonStyle.right = `${(0.05 * viewportWidth) + this.xMovements}px`

        this.moveButtonStyle.width = "5%"
        this.moveButtonStyle.height = "4%"

        this.stopTracking()
      },
      startTracking(event) {
        this.dragging = true;
        this.clickX = event.clientX;
        this.clickY = event.clientY;

        this.moveButtonStyle.cursor = "grabbing";
      },

      trackMovement(event) {
        if (this.dragging) {
          const offsetX = event.clientX - this.clickX;
          const offsetY = event.clientY - this.clickY;

          this.xMovements = Math.max(0, this.xMovements - offsetX);
          this.yMovements = Math.max(0, this.yMovements - offsetY);
          
          this.clickX = event.clientX;
          this.clickY = event.clientY;

          this.updateStyle()
          this.$refs.dialogSkycarChatbox.updateStyle()
        }
      },
      stopTracking() {
        this.dragging = false;
        this.moveButtonStyle.cursor = "grab";
      },
      handleWindowResize() {
        this.updateStyle()
      },
      showMoveButton(){
        if (window.innerWidth > this.minScreenWidth){
          return true
        }
        this.xMovements = 0
        this.yMovements = 0
        return false
      },
      async userChatLogs(sid, enter){
        var userMessage = null;
        if (enter) {
          userMessage = {
            message: "Entered skycar " + String(sid),
            sid: sid,
          };
        } else {
          userMessage = {
            message: "Left skycar " + String(sid),
            sid: sid,
          };
        }

        await this.sendConversation(userMessage)
      },
      async sendConversation(newValue){
        if (typeof newValue === "string"){
          var here = this
          this.shell.selectedSid.forEach(async function(sid) {
            const finalMessage = {
              "message": newValue,
              "sid": sid
            }
            await here.saveCommand("post", finalMessage);
          })
        } else {
          await this.saveCommand("post", newValue);
        }
      },
      async saveCommand(method, item){
        if (method === "post") {
          let res = await mypost(SkycarShellCommand.SAVE_COMMAND, item, this.currentZone)
          if (res.code !== 200 && res.code !== 401){
            this.showNotification(false, res.message)
          }
        } else{
          return await myget(SkycarShellCommand.SAVE_COMMAND, this.currentZone)
        }
      },
      async updateChatSid(){
        var here = this
        var additional = this.shell["sid"].filter(item => !this.shell.selectedSid.includes(item))
        var removed = this.shell.selectedSid.filter(item => !this.shell["sid"].includes(item))

        additional.forEach(async function(sid) {
          await here.userChatLogs(sid, true)
        })

        removed.forEach(async function(sid) {
          await here.userChatLogs(sid, false)
        })

        this.shell.selectedSid = [...this.shell["sid"]];
        this.toggleIdleTimer()
        this.shell.boolChat = false
      },
      updateChatMessage: async function(){
        var here = this
        var res = await here.saveCommand("get")
        if (res.code === 200){
          here.shell.txtChat = res.data
        } else {
          this.showNotification(false, res.message)
          this.$awn.alert("Failed to receive the historical message")
        }
      },

      configChatDialog(){
        if (this.shell.boolChat){
          this.shell.boolChat = false
        } else {
          if (this.shell.selectedSid.length > 1){
            this.shell.boolChat=true
          }
          else if (this.shell.selectedSid.length == 1){
            this.shell.boolChat=true
          } else {
            this.$awn.alert("Please select a skycar to enter to specific chat group");
          }
          
          var here = this
          this.shell.selectedSid.forEach(function(sid) {
            here.$set(here.shell.notification, sid, 0);
          })
          this.updateChatTxt()
        }
      },

      updateChatTxt(){
        this.shell.specificTxtChat = Array()
        var here = this
        this.shell.selectedSid.forEach(function(sid) {
          here.shell.specificTxtChat.push(...here.shell.txtChat[sid])
        })
        this.shell.specificTxtChat.sort((a, b) => a.time.localeCompare(b.time));
      },

      toggleIdleTimer() {
        if (this.shell.selectedSid.length > 0) {
          if (!this.timerStarted){
            this.startIdleTimer();
            window.addEventListener("mousemove", this.resetIdleTimer);
            window.addEventListener("keypress", this.resetIdleTimer);
            this.timerStarted = true
          }
        } else {
          if (this.timerStarted){
            clearInterval(this.idleInterval);
            window.removeEventListener("mousemove", this.resetIdleTimer);
            window.removeEventListener("keypress", this.resetIdleTimer);
            this.resetIdleTimer()
            this.timerStarted = false
          }
        }
      },

      startIdleTimer(){
        this.idleInterval = setInterval(async () => {
          this.idleTime += 1;
          if (this.idleTime > this.idleTimeoutMinute * 60) {
            this.resetChosenSkycar()
            await this.updateChatSid()
            this.toggleIdleTimer()
            this.shell.boolChat = false
          }
        }, 1000);
      },
      resetChosenSkycar(){
        this.shell["sid"] = Array()
      },
      resetIdleTimer() {
        this.idleTime = 0;
      },
        
      btnAddSkycar() {
        this.$refs.dialogAddSkycar.openDialog(this.currentZone)
      },
      showNotification(success, message) {
        this.$refs.snackbarNotification.showNotification(success, message)
      },
      updateTimeToLocal(errors){
        errors.forEach(error => {
          error["dt_local"] = this.convertStringToLocal(error.time, true);
          error["rt_local"] = error.recovered_time ? this.convertStringToLocal(error.recovered_time, true) : null;
      });
      },
      getMessage(socket) {
        var here = this
        socket.on(Websocket.SKYCAR, function(item) {
          here.shell.socket = item.item
        })
      },
  
      async sendMessage(item, repeat=1, delay=0) {
        for (let i = 0; i < repeat; i++) {
          this.sendMessageWithHttp(item)
          await new Promise(resolve => setTimeout(resolve, delay * 1000))
        }
      },
  
      async sendMessageWithHttp(item) {
        if (item.type == "sync") {
            let res = await myget("/dashboard/skycar", this.currentZone)
            if (Array.isArray(res)) {
              this.modelSkycar.skycar = res
            } else {
              this.modelSkycar.skycar = Array()
            }
            this.doneSync = true
          } else if (item.type == "request") {
            mypost(SkycarShellCommand.MCU_COMMAND, item.json, this.currentZone)
          }
      },
  
      syncSkycar() {
        this.sendMessage({ type: "sync" })
        this.doneSync = false
      },
  
      async syncGW(){
        let res = await myget("/runtime/tcp", this.currentZone)
        if (res["data"] === undefined){return}
  
        this.modelGW.allClients = res.data 
        const filteredResult = res.data.find((e) => e.is_gateway == true);
  
        if (filteredResult) {
          this.modelGW.btnColor = "green"
          this.modelGW.btnText = "GW Connected"
          this.modelGW.clients = filteredResult
        }else{
          this.modelGW.btnColor = "red"
          this.modelGW.btnText = "GW Disconnected"
          this.modelGW.clients = null
      
      }
      },
  
      getMessageColor(item) {
        if (item.color) {
          switch (item.color) {
            case "red":
              return "red--text text--accent-4"
            case "yellow":
                return "yellow--text text--accent-4"
            case "green":
                return "green--text text--accent-3"
          }
        } else if (item.msg_sent!=null) {
          switch (item.msg_sent) {
            case true:
              return "green--text text--accent-3"
            case false:
              return "yellow--text text--accent-4"
          }
        } else if (item.is_completed!=null) {
          switch (item.is_completed) {
            case true:
              return "green--text text--accent-3"
            case false:
              return "yellow--text text--accent-4"
          }
        }
      },
      getSkycarID() {
        const skycarIDs = this.modelSkycar.skycar.map(skycar => skycar.skycar_id)
        return skycarIDs.sort()
      },
      getSubmitStatus(status) {
        if (status == "Accepted") {
          return "green"
        } else if (status == "Rejected" || status == "Warning") {
          return "red"
        } else if (status == "Pending") {
          return "black"
        }
      },
      getColor(boolean) {
        if (boolean === true) {
          return "green";
        } else {
          return "red";
        }
      },
  
      getWinchColor(isActive , assignStorageCode, storageCode){
      // assign storage code = Macaroni and Cheese #F2BB66
      // with storage = green
      // inactive = red
        return isActive === false ? "red" : (isActive && assignStorageCode && !storageCode) ? "#F2BB66" : "green";
      },
      
      getStatus(bool) {
        return getStatus(bool)[1];
      },
      AxiosHttpWithAwesomeAlert,
      getTcHost(){
        return this.tc_host;
      },
      getSidColor(mode) {
        if (mode === "Manual") {
          return "yellow--text";
        } else if (mode === "Error") {
          return "grey--text";
        } else {
          return ""
        }
      }
    },
  
    computed: {
      CubeDataExportFileName(){
        let from = this.utilitySkycar.dtfromto[0]
        let to = this.utilitySkycar.dtfromto[1] ?? this.utilitySkycar.dtfromto[0]
        return `cube_data_${from}_${to}.csv` 
      },
      SkycarErrorExportFileName(){
        let from = this.modelErrorSkycar.dtfromto[0]
        let to = this.modelErrorSkycar.dtfromto[1] ?? this.modelErrorSkycar.dtfromto[0]
        return `skycar_error_${from}_${to}.csv` 
      },
      
      tc_host: function () {
        return getHost(this.currentZone)
      },
  
      catchMessage() {
        return this.shell.socket
      },
  
      onChangedErrorLogData: function () {
        return this.error["result"];
      },
      badgeContent() {
        let badgeNotify = this.shell.selectedSid.reduce((total, value) => {
          const notificationValue = this.shell.notification[value];
          if (typeof notificationValue === "string") {
            return total + 10;
          } else {
            return total + notificationValue;
          } 
        }, 0);
        if (badgeNotify > 9){
          badgeNotify = "9+"
        }
        return badgeNotify
      },
    },
  };
  
  async function mypost(route, body, zone, method="POST") {
    var host = new URL(getHost(zone) + route)
    var requestOptions = {
      method: method,
      body: JSON.stringify(body),
      headers: getRequestHeader()
    }
    try {
      const response = await fetch(host, requestOptions)
      let res = JSON.parse(await response.text())
      if (res.code == 401) {
        return useRefreshToken(this, mypost, route, body, zone, method = "POST")
      }
      return res
    } catch (error) {
      let msg = { status: "Rejected", reason: "Remote end point " + host + " not accessible, please ensure there is valid Input selected." }
      return msg;
    }
  }
  
  async function myget(route, zone) {
    var host = new URL(getHost(zone) + route)
    var requestOptions = {
      method: "GET",
      headers: getRequestHeader()
    }
    try {
      const response = await fetch(host, requestOptions)
      let res = JSON.parse(await response.text())
      if (res.code == 401) {
        return useRefreshToken(this, myget, route, zone)
      }
      return res
    } catch (error) {
      let msg = { status: "Rejected", reason: "Remote end point " + host + " not accessible." }
      return msg;
    }
  }
  
  
  
  
  async function getAxiosWithParams(route,url,...args){
    /**
     * Filter skycar error with date range .
     * @param  {String} route  An endpoint route
     * @param  {String} zone   Host url
     * @param  {Array of Object } ...args Any number of object argument with spread operator
     * @return {Object} object response
     */
  
    var host = new URL(getHost(url) + route)
    const queryParams = args[0]
    try {
      let params = new URLSearchParams(queryParams);
      const res = await axios.get(`${host}?${params}`, { headers: getRequestHeader() })
      return res
  
    } catch (error) {
      if (error.response.status == 401) {
        return useRefreshToken(this, getAxiosWithParams, route, url, ...args);
      }
      let msg = { status: "Rejected", reason: "Remote end point " + host + " not accessible." }
      return msg;
    }
  
  }
    
  const MaintenanceAction = {
    E: "ENROLL",
    S: "SWAP STORAGE"
  }
  
  </script>
  
  <style scoped>
  .fixed-width-chip {
    width: 120px
  }
  </style>
  