<template>
  <div>
    <v-card dark>
      <v-row align="center">
        <v-col cols="auto">
          <v-select
            class="ml-4"
            v-model="currentZone"
            :items="cubes"
            :rules="[(v) => !!v || 'Cube selection is required']"
            label="Cube"
            required
            @change="onCubeChanged"
          />
        </v-col>
        <v-col>Last Updated: {{ lastUpdated }}</v-col>
        <v-col>
          <v-switch
            class="ml-4 mr-2"
            v-model="coordType"
            color="primary"
            label="Visualise Coordinate"
            @change="visualCoordChanged"
            required
          />
        </v-col>
        <v-col>
          <v-switch
            class="ml-4 mr-2"
            v-model="smoothAnimate"
            color="primary"
            :label="switchLabel"
            @change="alertUser"
            required
          />
        </v-col>
        <v-col>
          <v-select
            class="ml-4 mr-2"
            v-model="pathType"
            :items="pathTypes"
            :rules="[(v) => !!v || 'Path selection is required']"
            label="Visualise Computed Path"
            @change="pathChanged"
            required
          />
        </v-col>
        <v-col>
          <v-btn
            class="ma-1"
            @click="onCubeChanged()"
            color="green"
          >
            <v-icon>mdi-restart</v-icon>
            <span>Refresh</span>
          </v-btn>
        </v-col>
      </v-row>
    </v-card>
    <v-row>
      <v-col cols="12" sm="6">
        <AnimationScene 
          :sceneSize=visualSize
          :currentZone=currentZone
          :nodes="adgs.nodes"
          :adgMessage="adgMessage"
          :smoothAnimate="smoothAnimate"
          :skycarProfile="skycarProfile"
          :coordType="coordType"
          :pathType="pathType"
          ref="animationScene" 
        />
      </v-col>
      <v-col cols="12" sm="6">
        <AdgNetworkGraph 
          :graphSize=visualSize
          :adgs="adgs"
          ref="adgNetworkGraph" 
        />
      </v-col>
    </v-row>
  </div>
</template>

<script>
import AdgNetworkGraph from "./visualisation/AdgNetworkGraph";
import AnimationScene from "./visualisation/AnimationScene";

import { getCube, getHost, getCurrentDateTime, getMapping } from "../helper/common";
import { Websocket, routeMatrix } from "../helper/enums.js" 
let httpRequest = require("../helper/http_request.js");
import { socket } from "../App.vue"

export default {
  components: {
    AdgNetworkGraph,
    AnimationScene
  },
  async mounted() {
    this.getMessage(socket)
    await this.initialize()
    window.addEventListener("resize", this.windowResize);
  },
  async beforeDestroy() {
    window.removeEventListener("resize", this.windowResize);
    await this.removeAll()
  },
  data() {
    return {
      smoothAnimate: false,
      coordType: true,
      pathTypes: [
        "BOTH", "STAGED ONLY", "ENQUEUED ONLY", "NONE"
      ],
      pathType: "BOTH",
      visualSize: {
        x: window.innerWidth > 600 ? window.innerWidth / 2 - 65 : window.innerWidth - 60,
        y: window.innerHeight - 150,
      },

      adgSocket: null,
      obstacleSocket: null,
      skycarStatusSocket: null,
      
      cubes: getCube(),
      currentZone: getCube()[0],
      lastUpdated: getCurrentDateTime(),
      skycarProfile: Object(),
      adgMessage: Array(),
      adgs: {
        nodes: Array(),
        links: Array()
      },
    }
  },
  methods: {
    async initialize(){
      await this.initializeNodes()
      await Promise.all([
        this.$refs.adgNetworkGraph.initialization(),
        this.$refs.animationScene.initialization()
      ]);
    },
    async pathChanged(){
      await this.$refs.animationScene.removeAllPaths()
      if (this.pathType !== "NONE"){
        this.$refs.animationScene.updatePath()
      }
    },
    alertUser(){
      if (this.smoothAnimate){
        this.$awn.info("Smooth animations of the skycars may differ from reality")
      }
    },
    async visualCoordChanged(){
      await this.$refs.animationScene.removeAxisLabels()
      if (this.coordType){
        this.$refs.animationScene.addAxisLabels()
      }
    },
    async removeAll(){
      await Promise.all([
        this.$refs.adgNetworkGraph.disposeGraph(),
        this.$refs.animationScene.disposeScene()
      ])
    },
    async onCubeChanged() {
      this.adgs = {
        nodes: Array(),
        links: Array()
      }
      this.adgMessage =  Array()

      await Promise.all([
        this.$refs.adgNetworkGraph.resetGraph(),
        this.$refs.animationScene.resetScene()
      ])
      this.initialize();
      this.lastUpdated = getCurrentDateTime()
    },
    async initializeNodes(){
      const url = getHost(this.currentZone)
      let nodesData = await httpRequest.axiosRequest(
        "GET", url, routeMatrix.ADGNODES
      );
      if (nodesData) {
        if (typeof(nodesData) === "string"){
          nodesData = JSON.parse(nodesData)
        }
        this.adgs.nodes = nodesData.data.model.adg_nodes.nodes
        this.adgs.links = nodesData.data.model.adg_nodes.links
        this.adgMessage = nodesData.data.model.adg_message
        this.skycarProfile = nodesData.data.model.skycar_profile
      }
    },
    windowResize(){
      this.visualSize.x = window.innerWidth > 600 ? window.innerWidth / 2 - 65 : window.innerWidth - 60;
      this.visualSize.y = window.innerHeight - 150;

      this.$refs.adgNetworkGraph.updateGraph()
      this.$refs.animationScene.updateScene()
    },
    getMessage(socket) {
      var here = this
      socket.on(Websocket.SKYCAR_ADG, function(item) {
        here.adgSocket = item.item
      })
      socket.on(Websocket.OBSTACLES, function(item) {
        here.obstacleSocket = item.item
      })
      socket.on(Websocket.VISUALISE_SKYCAR_STATUS, function(item) {
        here.skycarStatusSocket = item.item
      })
    }
  },
  watch: {
    async catchAdgMessage(newValue) {
      if (newValue.zone === getMapping(this.currentZone)){
        if (newValue) {
          this.adgMessage = newValue.adg_message
          this.adgs.nodes = newValue.adg_nodes.nodes
          this.adgs.links = newValue.adg_nodes.links
        }
        await this.$refs.adgNetworkGraph.updateNetwork()
        await this.$refs.animationScene.removeAllPaths()
        await this.$refs.animationScene.updatePath()
        if (this.smoothAnimate){
          await this.$refs.animationScene.moveSkycar()
        } else {
          await this.$refs.animationScene.updateSkycar()
        }
        this.lastUpdated = getCurrentDateTime()
      }
    },
    async catchObstacleMessage(newValue){
      if (newValue.zone === getMapping(this.currentZone)){
        for (const value of newValue.obstacles){
          const msg = value.split(",")
          if (newValue.delete){
            this.$refs.animationScene.removeObstacle(msg[0], msg[1])
          } else{
            this.$refs.animationScene.addObstacle(msg[0], msg[1], msg[2])
          }
        }
        this.lastUpdated = getCurrentDateTime()
      }
    },
    async catchSkycarStatusMessage(newValue){
      if (newValue.zone === getMapping(this.currentZone)){
        this.$refs.animationScene.updateSkycarStatus(newValue.sid, newValue.status)
        this.lastUpdated = getCurrentDateTime()
      }
    }
  },
  computed: {
    catchAdgMessage() {
      return this.adgSocket
    },
    catchObstacleMessage() {
      return this.obstacleSocket
    },
    catchSkycarStatusMessage() {
      return this.skycarStatusSocket
    },
    switchLabel() {
      return this.smoothAnimate ? "Animation (Smooth)" : "Animation (On-Update)";
    },
  }
};
</script>
