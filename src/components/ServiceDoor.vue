<template>
    <v-app app>
      <v-container>
        <v-btn
        color="primary"
        large
        class="ml-3 mb-3"
        @click="refreshBtn()"
        >
          REFRESH
        </v-btn>
        <!-- Normal Service Door -->
        <div class="mt-5">
          <h2>Service Door</h2>
          <v-card
            elevated
            v-for="sd in serviceDoor.normalSDList"
            :key=sd.id
            :color=getSDColor(sd.status)
            class="custom-v-card" 
            :style="{ width:'auto' }"
            >
            <v-card-title>
              {{ sd.id }} : {{ sd.name }}
            </v-card-title>
            <v-card-text
              style="background-color: white;"
            >
              <v-row>
                <v-col>
                  <v-row class="mt-2" align="center" justify="center">
                      <v-chip 
                        elevated
                        small
                        :color="getStatus(sd.is_connected)[0]"
                        dark
                      >
                        {{ getStatus(sd.is_connected)[1] }} Connected
                      </v-chip>
                  </v-row>
                  <v-row class="mt-2" align="center" justify="center">
                      <v-chip 
                        elevated
                        small
                        :color="getStatus(sd.is_active)[0]"
                        dark
                      >
                        {{ getStatus(sd.is_active)[1] }} Paired
                      </v-chip>
                  </v-row>
                </v-col>
              </v-row>
            </v-card-text>
            <div class="mt-3 mb-1" align="center" justify="center">
              <v-btn 
              class="mr-4"
              :disabled="sd.status==='OPEN'"
              @click="openServiceDoor(sd.id,sd.is_active)"
              >
                OPEN
              </v-btn>
              <v-btn
              :disabled="sd.status==='CLOSE'"
              @click="closeServiceDoor(sd.id,sd.is_active)"
              >
                CLOSE
              </v-btn>
            </div>
          </v-card>
        </div>
        <!-- Station Service Door -->
        <div class="mt-10">
          <h2>Station Service Door</h2>
          <h5 class="mt-2">
            <v-icon>
              mdi-information
            </v-icon>
            Station Service Door can only be opened when the station related is e-stopped
            <router-link to="/station">check station status here</router-link>
          </h5>
          <v-card
            elevated
            v-for="sd in serviceDoor.stationSDList"
            :key=sd.id
            :color=getSDColor(sd.status)
            class="custom-v-card" 
            :style="{ width:'auto' }"
            >
            <v-card-title>
              {{ sd.id }} : {{ sd.name }}
            </v-card-title>
            <v-card-text
              style="background-color: white;"
            >
              <v-row>
                <v-col>
                  <v-row class="mt-2" align="center" justify="center">
                      <v-chip 
                        elevated
                        small
                        :color="getStatus(sd.is_connected)[0]"
                        dark
                      >
                        {{ getStatus(sd.is_connected)[1] }} Connected
                      </v-chip>
                  </v-row>
                  <v-row class="mt-2" align="center" justify="center">
                      <v-chip 
                        elevated
                        small
                        :color="getStatus(sd.is_active)[0]"
                        dark
                      >
                        {{ getStatus(sd.is_active)[1] }} Paired
                      </v-chip>
                  </v-row>
                  <v-row class="mt-2" align="center" justify="center">
                    <v-chip 
                      v-for="stationCode in sd.station_list"
                      :key=stationCode
                      elevated
                      small
                      color="orange"
                      dark
                      class="ml-1"
                    >
                      {{ stationCode  }}
                    </v-chip>
                  </v-row>
                </v-col>
              </v-row>
            </v-card-text>
            <div class="mt-3 mb-1" align="center" justify="center">
              <v-btn 
              class="mr-4"
              :disabled="sd.status==='OPEN'"
              @click="openServiceDoor(sd.id,sd.is_active)"
              >
                OPEN
              </v-btn>
              <v-btn
              class="mr-4"
              :disabled="!sd.pending_open"
              @click="resetSD(sd.id)">
                <v-icon>mdi-arrow-u-right-top</v-icon>
              </v-btn>
              <v-btn
              :disabled="sd.status==='CLOSE'"
              @click="closeServiceDoor(sd.id,sd.is_active)"
              >
                CLOSE
              </v-btn>
            </div>
            <div v-if="progressBar.bool && progressBar.processing_sd.includes(sd.id)">
              <v-progress-linear
                v-if="progressBar.bool"
                color="green"
                indeterminate
                class="mt-3"
              ></v-progress-linear>
              <span>
                Waiting related staiton to stop...
              </span>
            </div>
          </v-card>
        </div>
      </v-container>
    </v-app>
</template>

<script>
import { getHccUrl, getCube, getRequestHeader } from "../helper/common.js";
import { Websocket } from "../helper/enums";
import { socket } from "../App.vue";
import axios from "axios";


export default {
  name: "App",
  async created(){
    await this.getServiceDoorList();
    this.getMessage()
  },
  methods: {
    getSDColor(status){
        if(status === "OPEN"){
            return "green"
        }else{
            return "#CFD8DC"
        }
    },
		getStatus(bol) {
      if (bol == true) {
        return ["green", "✓"];
      } else {
        return ["red", "✗"];
      }
    },
    getMessage() {
      let here = this
      socket.on(Websocket.SERVICE_DOOR, function(data) {
        let item = data.item
        if(item.event_name === ServiceDoorEvent.CLOSED){
          here.$awn.info(`SD ${item.sd_id} is closed`)
        }else if (item.event_name === ServiceDoorEvent.OPENED){
          here.$awn.info(`SD ${item.sd_id} is opened`)
          here.progressBar.processing_sd.pop(item.sd_id)
        }else if (item.event_name === ServiceDoorEvent.PROCESSING){
          here.progressBar.bool=true
          here.progressBar.processing_sd.push(item.sd_id)
        }else if (item.event_name === ServiceDoorEvent.DONE_PROCESSING){
          here.progressBar.bool=false
          here.progressBar.processing_sd.pop(item.sd_id)
          here.$awn.info(`SD ${item.sd_id} is done processing and ready to opened.`)
        }
      });
    }
  },
  data: () => ({
    currentZone: getCube()[0],
    getServiceDoorList: async function () {
        let here = this.serviceDoor
        here.doneSync = false
        here.response = await getServiceDoor()
        here.list = here.response.data
        for ( let i=0; i<here.list.length; i++){
          if (here.list[i].type === "NORMAL"){
            here.normalSDList.push(here.list[i])
          }else{
            here.stationSDList.push(here.list[i])
          }
        }
        here.doneSync = true
    },
    openServiceDoor: async function (sd_id,is_active) {
      if (!is_active){
        this.$awn.warning(`SD${sd_id} is not paired to HCC, could not send open request`)
        return 
      } 
      let res = await postServiceDoor("open",{sd_id : sd_id})
      if (res.status){
        this.$awn.success(res.message)
      }else{
        this.$awn.alert(res.message)
      }
    },
    closeServiceDoor: async function (sd_id,is_active) {
      if (!is_active){
        this.$awn.warning(`SD${sd_id} is not paired to HCC, could not send close request`)
        return 
      } 
      let res = await postServiceDoor("close",{sd_id : sd_id})
      if (res.status){
        this.$awn.success(`Successfully Send Close Request to close to SD${sd_id}`)
      }else{
        this.$awn.alert(res.message)
      }
    },
    resetSD: async function (sd_id){
      let res = await postServiceDoor("reset",{sd_id : sd_id})
      if (res.status){
        this.$awn.success(`Successfully reset SD${sd_id}`)
      }else{
        this.$awn.alert(res.message)
      }
    },
    refreshBtn: async function(){
      this.serviceDoor.normalSDList = []
      this.serviceDoor.stationSDList = []
      await this.getServiceDoorList()
    },
    serviceDoor:{
        doneSync : true,
        response : null,
        list : [],
        normalSDList : [],
        stationSDList : []
    },
    progressBar:{
      bool:false,
      processing_sd : [],
    },
  })

}

async function getServiceDoor() {
    let hccUrl = getHccUrl()
    var hwxHost = `${hccUrl}/cube/service_door/sd`
    try {
        const res = await axios.get(`${hwxHost}`, {headers: getRequestHeader()})
        return res.data
    } catch (error) {
        return {
        data: [],
        error: error
        }
    }
}

async function postServiceDoor(url,body) {
  let hccUrl = getHccUrl()
  var hwxHost = `${hccUrl}/cube/service_door/${url}`
  var requestOptions = {
    method: "POST",
    body: JSON.stringify(body),
    headers: getRequestHeader()
  }
  try {
    let res = await fetch(hwxHost, requestOptions)
    return res.json()
  } catch (error) {
    return {
      data: [],
      error: error
    }
  }
}

const ServiceDoorEvent = {
  OPENED: "OPENED",
  CLOSED: "CLOSED",
  PROCESSING: "PROCESSING",
  DONE_PROCESSING: "DONE_PROCESSING",
};

</script>

<style scoped>
.custom-v-card {
  display: inline-block; /* Ensures inline behavior */
  padding: 8px; /* Adjust padding as needed */
  border-radius: 4px; /* Add optional border radius */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Optional subtle shadow */
  margin-left: 25px;
  margin-top: 10px
}
</style>