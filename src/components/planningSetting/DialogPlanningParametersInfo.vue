<template>
  <v-dialog
    v-model="dialogBool"
  >
    <v-card>
      <v-toolbar
        dark
      >
        <v-toolbar-title>{{ title }}</v-toolbar-title>
      </v-toolbar>
      <v-col>
        <v-card-text>
          <v-col>
            <v-row>Default Value: {{ defaultValue }}</v-row>
            <v-row>{{ subtitle }}</v-row>
          </v-col>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="green darken-1"
            text
            @click="closeDialog()"
          >
            Close
          </v-btn>
        </v-card-actions>
      </v-col>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
    data: () => ({
        dialogBool: false,
        title: "",
        subtitle: "",
        defaultValue: ""
    }),
    methods: {
        openDialog(title, subtitle, defaultValue) {
          this.title = title;
          this.subtitle = subtitle;
          this.defaultValue = defaultValue;
          this.dialogBool = true;
        },
        closeDialog() {
            this.dialogBool = false
        },
    }
  }

</script>
