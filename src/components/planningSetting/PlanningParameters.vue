<template>
  <div id="app">
    <SnackbarNotification ref="snackbar" />
    <v-select
      :items="cubes"
      v-model="selectedCube"
      @input="fetchData()"
      label="Cube"
    />
    <setting-list-item
      v-for="(attrs, key) in params"
      :key="key"
      @submitChange="postSetting($event, key)"
      :param-attr="attrs"
      :disabled="loading"
    />
  </div>
</template>

<script>
import settingListItem from "./settingListItem.vue";
import SnackbarNotification from "../shared/SnackbarNotification.vue";
import { getCube, getHost, useRefreshToken } from "../../helper/common.js";
let httpRequest = require("../../helper/http_request.js");

let positiveIntegerRule = [
  value => (!value || Number.isInteger(Number(value))) || "Must be positive integer",
  value => (!value || Number(value)>0) || "Must be positive integer"
];
let nonNegativeIntegerRule = [
  value => (!value || Number.isInteger(Number(value))) || "Must be integer ≥ 0",
  value => (!value || Number(value)>=0) || "Must be integer ≥ 0"
];

export default {
  data () {
    return {
      params: {
        FORCE_REPLAN: {
          name: "Always Enter Planning",
          type: "toggle",
          indent: 0,
          value: false,
          default: false,
          info: "If set to true, TC shall enter planning mode even if there is no job to be planned."
        },
        MIN_REPLAN_INTERVAL: {
          name: "Planning Interval",
          type: "number",
          indent: 0,
          value: 2,
          unit: "seconds",
          hint: "in seconds",
          rule: nonNegativeIntegerRule,
          default: 2,
          info: "The minimum interval between two consecutive planning cycles."
        },
        SOLVER: {
          name: "Pathfinding Solver",
          type: "choice",
          indent: 0,
          value: 0,
          choices: [
            {
              name: "Model C",
              code: 1
            }
          ],
          default: "Model C",
        },
        JOB_ASSIGNMENT: {
          name: "Job Assignment Solver",
          type: "choice",
          indent: 0,
          value: 1,
          choices: [
            {
              name: "Model C",
              code: 4
            },
          ],
          default: "Model C",
        },
        IMPEDED_THRESHOLD: {
          name: "Impedance Threshold",
          type: "number",
          indent: 0,
          value: 20,
          unit: "seconds",
          hint: "in seconds",
          rule: nonNegativeIntegerRule,
          default: 20,
          info: "A skycar is considered as stuck if it has not moved for this many seconds."
        },
        MAX_ACTION_NUM: {
          name: "Skycar Maximum Action Message",
          type: "number",
          indent: 0,
          value: 2,
          unit: "messages",
          hint: "in number of messages",
          rule: positiveIntegerRule,
          default: 1,
          info: "The maximum number of action messages that a skycar can be sent."
        },
        EASY_MODE: {
          name: "Pathfinding Easy Mode",
          type: "toggle",
          indent: 0,
          value: false,
          default: false,
          info: "If set to true, the planning solver will be forced to prioritize completeness over optimality."
        },
        MAX_CONSECUTIVE_ACTION: {
          name: "Skycar Maximum Consecutive Action",
          type: "number",
          indent: 0,
          value: 9,
          unit: "action",
          hint: "1-99",
          rule: positiveIntegerRule,
          default: 15,
          info: "The maximum number of consecutive actions that a skycar can be sent."
        },
        ELAPSE_BUCKET: {
          name: "Elapse Bucket",
          type: "number",
          indent: 0,
          value: 600,
          unit: "seconds",
          hint: "in seconds",
          rule: positiveIntegerRule,
          default: 600,
          info: "If a job without information about which station it is served for has not been planned for this many seconds, it will be prioritized."
        },
        ADG_VERSION: {
          name: "ADG Version",
          type: "choice",
          indent: 0,
          value: 1,
          choices: [
            {
              name: "Old",
              code: 1
            },
            {
              name: "New",
              code: 2
            }
          ],
          default: "New",
          info: "The version of ADG to be used."
        },
        NUM_ADVANCED_ORDERS : {
          name: "Number of Skycars for Advanced Orders",
          type: "number",
          indent: 0,
          value: 0,
          unit: "skycars",
          hint: "in number of skycars",
          rule: positiveIntegerRule,
          default: 0,
          info: "The number of skycars to be reserved for advanced orders."
        },
        ADVANCED_ORDER_FOCAL_WEIGHT: {
          name: "Advanced Order Focal Weight",
          type: "number",
          indent: 0,
          value: 600,
          unit: "seconds",
          hint: "in seconds",
          rule: positiveIntegerRule,
          default: 600,
          info: "The lower the value, the more the advanced order’s priority is shifted toward the order’s creation time.\n\
          The higher the value, the more the advanced order’s priority is shifted toward the order’s distance."
        },
        CONSIDER_FUTURE_JOBS: {
          name: "Consider Future Jobs",
          type: "toggle",
          indent: 0,
          value: true,
          default: true,
          info: "If set to true, the planning solver will consider future jobs for optimal planning. Set this to false when the number of jobs is less then number of skycars to force all skycars to be used."
        },
        DIGGING_RESERVATION_THRESHOLD: {
          name: "Threshold for Digging Reservation",
          type: "number",
          indent: 0,
          value: 5,
          unit: "units",
          hint: "in units",
          rule: positiveIntegerRule,
          default: 5,
          info: "Digging is more likely to be completed by a single skycar when the order distance is below this value."
        },
        FORCE_DIGGING: {
          name: "Force Digging Mode",
          type: "toggle",
          indent: 0,
          value: false,
          default: false,
          info: "If set to true, once the digging operation for a stack starts, it will continue until completion and will not be interrupted by other job priorities."
        },
      },
      cubes: getCube(),
      selectedCube: getCube()[0],
      loading: false
    }
  },
  components: {
    settingListItem: settingListItem,
    SnackbarNotification: SnackbarNotification
  },
  methods: {
    async postSetting(value, settingName) {
        let data = { [settingName]: value };
        let res = await httpRequest.postRequest(
          data,
          "/dashboard/planningparameters",
          this.tcHost
        );
        if (res.code === 401){ // If access token is unauthorized
          // use refresh token to get new access token from auth server 
          return useRefreshToken(this, this.postSetting, value, settingName)
        }
        if (typeof res === "object" && res !== null && !Array.isArray(res)) {
          if (res.code === 200) {
            this.params[settingName].value = value;
            this.$refs.snackbar.showNotification(
              true, `Successfully set ${this.params[settingName].name} to ${value}.`);
            return
          }
        }
        this.$refs.snackbar.showNotification(
          false, `Failed to set ${this.params[settingName].name} to ${value}. ${res}`);
        setTimeout(this.fetchData, 1000);
    },
    async fetchData() {
      try {
        this.loading = true;
        let res = await httpRequest.axiosRequest(
          "get",
          this.tcHost,
          "/dashboard/planningparameters");
        if (res.status === 401) { // If access token is unauthorized
          // use refresh token to get new access token from auth server 
          return useRefreshToken(this, this.fetchData);
        } else if (res.status != 200) {
          // error status
          this.$refs.snackbar.showNotification(false, `Cannot get settings due to ${res.data.message}`);
          return
        }
        let data = res.data.data;
        for (let key in data) {
          if (!(key in this.params)) {
            continue;
          }
          this.params[key].value = data[key];
        }
        this.loading = false;
      } catch (error) {
        // cannot reach
        this.$refs.snackbar.showNotification(false, `Cannot get settings due to error: ${error}`);
      }
    }
  },
  created() {
    this.fetchData();
  },
  computed: {
    tcHost: function() {
      return getHost(this.selectedCube);
    }
  }
}
</script>

<style>

</style>