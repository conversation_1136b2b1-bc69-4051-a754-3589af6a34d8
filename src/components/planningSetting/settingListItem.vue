<template>
  <span>
    <v-list-item>
      <v-list-item-content>
        <v-list-item-icon
          v-for="(_, index) in Array(paramAttr.indent)"
          :key="index"
        />
        <v-list-item-title>{{ paramAttr.name }}</v-list-item-title>

        <!-- === DISABLED -=== -->
        <div v-if="disabled">
          <v-list-item-subtitle v-if="disabled">
            Loading
          </v-list-item-subtitle>
        </div>

        <!-- === TOGGLE -=== -->
        <div v-else-if="paramAttr.type == 'toggle'">
          <v-list-item-subtitle
            class="text-h6 green--text text-uppercase"
            v-if="paramAttr.value"
          >
            on
          </v-list-item-subtitle>
          <v-list-item-subtitle class="text-h6 red--text text-uppercase" v-else>
            off
          </v-list-item-subtitle>
        </div>

        <!-- === NUMBER === -->
        <div v-else-if="paramAttr.type == 'number'">
          <v-list-item-subtitle>
            <span class="text-h6">{{ paramAttr.value }} {{ " " }}</span>
            <span class="text-lowercase">{{ paramAttr.unit }}</span>
          </v-list-item-subtitle>
        </div>

        <!-- === CHOICE === -->
        <div v-else-if="paramAttr.type == 'choice'">
          <v-list-item-subtitle class="text-h6">
            {{ getChoiceName(paramAttr) }}
          </v-list-item-subtitle>
        </div>
      </v-list-item-content>

      <v-btn
        @click="openDialog(paramAttr.name, paramAttr.info, paramAttr.default)"
        icon
      >
        <v-icon>mdi-information</v-icon>
      </v-btn>

      <v-list-item-action>
        <!-- === TOGGLE === -->
        <v-switch
          v-if="paramAttr.type == 'toggle'"
          @click="$emit('submitChange', paramAttr.value)"
          v-model="paramAttr.value"
          :disabled="disabled"
        />

        <!-- === NUMBER === -->
        <v-text-field
          v-else-if="paramAttr.type == 'number'"
          :label="paramAttr.hint"
          :disabled="disabled"
          append-outer-icon="mdi-send"
          v-model.number="textField"
          @keydown.enter="$emit('submitChange', textField)"
          @click:append-outer="$emit('submitChange', textField)"
          :rules="paramAttr.rule"
        />

        <template v-else-if="paramAttr.type === 'choice'">
          <!-- Choice -->
          <v-select
            v-model="paramAttr.value"
            :items="paramAttr.choices"
            item-text="name"
            item-value="code"
            :disabled="disabled"
            @input="onSelectChange"
          ></v-select>

          <v-row v-if="currentChoiceSelected" align="center" justify="center">
            <v-text-field
              v-if="currentChoiceSelected.nested"
              :label="currentChoiceSelected.nested.hint"
              :disabled="disabled"
              @keydown.enter="updateNestedValue"
              @click:append-outer="updateNestedValue"
              @input="onNestedValueChange"
              :rules="currentChoiceSelected.nested.rule"
              append-outer-icon="mdi-send"
              v-model.number="currentChoiceSelected.nested.model"
            ></v-text-field>
          </v-row>
        </template>
      </v-list-item-action>
    </v-list-item>
    <DialogPlanningParametersInfo
      ref="dialogPlanningParametersInfo"
    />
  </span>
</template>

<script>
import DialogPlanningParametersInfo from "./DialogPlanningParametersInfo.vue";
export default {
  components: {
    DialogPlanningParametersInfo
  },
  data() {
    return {
      textField: "",
      currentChoiceSelected: null,
    };
  },
  props: {
    paramAttr: { type: Object, required: true },
    disabled: { type: Boolean, default: false },
  },
  /* format of the object of prop
  {
    // === all ===
    name: { type: String, required: true },
    type: { type: String, default: "number" }, // number, toggle, choice
    indent: { type: Number, default: 0 },
    value: { type: [Number, Boolean], required: true },

    // === number ===
    unit: { type: String, required: true },
    hint: { type: String, default: "" },
    rule: {type: Array, default: null}, // applicable only to textfield

    // === choice ===
    choices: { type: Object, required: false }, // required if type=choice
    nested : { type: Object } //optional
  },
  */
  methods: {
    openDialog(title, subtitle, defaultValue) {
      this.$refs.dialogPlanningParametersInfo.openDialog(title, subtitle, defaultValue);
    },
    getChoiceName(attr) {
      for (let i = 0; i < attr.choices.length; i++) {
        let choice = attr.choices[i];
        if (choice.code == attr.value) {
          return choice.name;
        }
      }
      return "";
    },
    updateNestedValue() {
      let name = this.currentChoiceSelected.nested.name;
      let value = this.currentChoiceSelected.nested.model;
      this.$emit("submitNestedChange", value, name);
    },
    onNestedValueChange(value) {
      this.currentChoiceSelected.nested.value = value;
    },

    initialize() {
      if (this.paramAttr.type != "choice") {
        return;
      }
      const selectedChoice = this.paramAttr.choices.find(
        (choice) => this.paramAttr.value === choice.code
      );
      this.currentChoiceSelected = selectedChoice;
      if (this.currentChoiceSelected.nested) {
        this.currentChoiceSelected.nested.model = this.currentChoiceSelected.nested.value;
      }
    },

    onSelectChange(value) {
      const selectedChoice = this.paramAttr.choices.find(
        (choice) => choice.code === value
      );
      this.currentChoiceSelected = selectedChoice;
      if (this.currentChoiceSelected.nested) {
        this.currentChoiceSelected.nested.model = this.currentChoiceSelected.nested.value;
      }

      this.$emit("submitChange", value);
    },
  },
};
</script>

<style></style>
