<template>
  <v-container>
    <div class="dark-cloud"></div>
    <div class="stars"></div>
    <div class="stars2"></div>
    <div class="stars3"></div>

    <v-tabs dark :background-color="'transparent'" class="pa-4" v-model="tab">
      <v-tab href="#storage">Storage</v-tab>
    </v-tabs>

    <v-tabs-items class="transparent" v-model="tab">
      <v-tab-item value="storage">
        <v-card flat class="pa-4 transparent">
          <v-btn
            large
            @click="reloadData(true, false)"
            :loading="refreshLoading"
            dark
            style="position: fixed; top: 10%; right: 3%; z-index: 999"
            >Refresh</v-btn
          >

          <v-data-table
            v-model="selected"
            :headers="headers"
            :items="Object.values(dataPreps)"
            :items-per-page="maxPageSize"
            :item-class="rowColor"
            :options.sync="options"
            hide-default-footer
            dark
            dense
            class="transparent"
          >
            <template v-slot:top>
              <v-row>
                <v-switch
                  v-model="autoRefresh"
                  :label="`Auto Refresh (${autoRefreshIntervalInSec}s)`"
                  @click="toggleAutoRefresh()"
                ></v-switch>
              </v-row>
              <v-expansion-panels class="transparent">
                <v-expansion-panel class="transparent">
                  <v-expansion-panel-header class="transparent">
                    Advanced Filter
                  </v-expansion-panel-header>
                  <v-expansion-panel-content class="transparent">
                    <v-row class="transparent">
                      <v-col>
                        <v-autocomplete
                          v-model="storageStatusFilter"
                          :items="storageStatusOptions"
                          label="Status"
                          multiple
                          small-chips
                          deletable-chips
                          outlined
                        ></v-autocomplete>
                      </v-col>
                      <v-col md="2">
                        <v-checkbox
                          v-model="storageIncludeDeletedFilter"
                          label="Filter with Only Deleted"
                          class="mt-n1"
                        ></v-checkbox>
                      </v-col>
                    </v-row>
                    <v-row dense>
                      <v-col>
                        <v-text-field
                          class="no-spinner"
                          v-model="storageNodeXFilter"
                          dense
                          outlined
                          label="x-coord"
                          type="number"
                          hide-details="true"
                        ></v-text-field>
                      </v-col>
                      <v-col>
                        <v-text-field
                          class="no-spinner"
                          v-model="storageNodeYFilter"
                          dense
                          outlined
                          label="y-coord"
                          type="number"
                        ></v-text-field>
                      </v-col>
                      <v-col>
                        <v-text-field
                          class="no-spinner"
                          v-model="storageNodeZFilter"
                          dense
                          outlined
                          label="z-coord"
                          type="number"
                        ></v-text-field>
                      </v-col>
                    </v-row>
                    <v-row dense>
                      <v-col>
                        <v-combobox
                          v-model="storageCodeFilter"
                          label="Storage Code"
                          multiple
                          small-chips
                          deletable-chips
                          clearable
                          outlined
                          append-icon=""
                        ></v-combobox>
                      </v-col>
                      <v-col>
                        <v-autocomplete
                          v-model="storageLastMovementFilter"
                          :items="storageLastMOvementOptions"
                          label="Last Movement"
                          multiple
                          small-chips
                          deletable-chips
                          outlined
                        ></v-autocomplete>
                      </v-col>
                    </v-row>
                    <v-expand-transition>
                      <v-row v-show="storageIncludeDeletedFilter" dense>
                        <v-col>
                          <v-text-field
                            class="theme--dark "
                            label="Deleted - Start Time"
                            type="datetime-local"
                            v-model="storageDeletedAtStartTimeFilter"
                            step="2"
                            clearable
                            outlined
                            icon="mdi-calendar-account-outline"
                          />
                        </v-col>
                        <v-col>
                          <v-text-field
                            class="theme--dark "
                            label="Deleted - End Time"
                            type="datetime-local"
                            v-model="storageDeletedAtEndTimeFilter"
                            step="2"
                            clearable
                            outlined
                          />
                        </v-col>
                      </v-row>
                    </v-expand-transition>
                  </v-expansion-panel-content>
                </v-expansion-panel>
              </v-expansion-panels>

              <v-spacer></v-spacer>

              <v-row>
                <v-col cols="4" sm="3">
                  <v-select
                    v-model="perPage"
                    :items="pageSizes"
                    label="Items per Page"
                    @change="handlePageSizeChange"
                  ></v-select>
                </v-col>
                <v-col cols="12" sm="9">
                  <v-pagination
                    v-model="currentPage"
                    :length="totalPages"
                    total-visible="7"
                    next-icon="mdi-menu-right"
                    prev-icon="mdi-menu-left"
                    @input="handlePageChange"
                    dark
                  ></v-pagination>
                </v-col>
              </v-row>
            </template>

            <template v-slot:items="{ item }">
              {{ item }}
            </template>

            <template v-slot:[`item.code`]="{ value }">
              <v-tooltip right>
                <template v-slot:activator="{ on, attrs }">
                  <td v-bind="attrs" v-on="on">
                    {{ value }}
                  </td>
                </template>
                <pre
                  v-for="(val, key) in nodes.find(
                    (x) => x.storageCode === value
                  )"
                  :key="key"
                  :class="
                    nodeTooltipHighlightField.includes(key)
                      ? 'light-green--text text--accent-3'
                      : ''
                  "
                  >{{ `${key} : ${val}` }}</pre
                >
              </v-tooltip>
            </template>

            <template v-slot:[`item.station`]="{ value }">
              <v-tooltip right>
                <template v-slot:activator="{ on, attrs }">
                  <td v-bind="attrs" v-on="on">
                    {{ value }}
                  </td>
                </template>
                <pre
                  v-for="(val, key) in stations.find((x) => x.id === value)"
                  :key="key"
                  :class="
                    stationTooltipHighlightField.includes(key)
                      ? 'light-green--text text--accent-3'
                      : ''
                  "
                  >{{ `${key} : ${val}` }}</pre
                >
              </v-tooltip>
            </template>

            <template v-slot:[`item.tagNames`]="{ value }">
              <v-tooltip right>
                <template v-slot:activator="{ on, attrs }">
                  <td v-bind="attrs" v-on="on">
                    {{ validateStringLength(value.tags) }}
                  </td>
                </template>
                <pre
                  v-for="(val, key) in tags.find((x) => x.key === value.key)
                    .tagNames"
                  :key="key"
                  >{{ `${key} : ${val}` }}</pre
                >
              </v-tooltip>
            </template>

            <template v-slot:[`item.controls`]="props">
              <v-icon dark @click="infoStorage(props.item.code)"
                >mdi-information-outline
              </v-icon>
              <v-icon dark @click="editStorage(props.item.code)"
                >mdi-image-edit-outline
              </v-icon>
              <v-icon dark @click="disenrollStorage(props.item.code)"
                >mdi-delete-forever-outline
              </v-icon>
            </template>
          </v-data-table>
        </v-card>
      </v-tab-item>
    </v-tabs-items>
  </v-container>
</template>

<script>
import { mapGetters } from "vuex";
import { SmOperationAPI } from "../api/sm-operation";
import { GatewayAPI } from "../api/gateway"
import { getAccessToken } from "../helper/authorize";
import { StationAPI } from "../api/station";
import { SettingAPI } from "../api/settings";
import { SmStorageAPI } from "../api/storages";
import { SmSettingsKey } from "../helper/enums";
import { convertStringToLocal } from "../helper/common";
import { Route, SmStorageLastMovement, StorageStatus } from "../helper/enums";

export default {
  name: "Storage",

  data() {
    return {
      oldQuery: null,
      autoRefresh: false,
      autoRefreshTimer: null,
      autoRefreshIntervalInSec: 10,
      tab: null,
      refreshLoading: false,
      isDualStation: false,
      selected: [],
      nodeTooltipHighlightField: ["nodeZoneGroup"],
      stationTooltipHighlightField: ["isActive"],
      storageStatusFilter: Object.values(StorageStatus),
      storageStatusOptions: Object.values(StorageStatus),
      storageLastMovementFilter: [],
      storageLastMOvementOptions: Object.values(SmStorageLastMovement),
      storageCodeFilter: [],
      storageNodeXFilter: "",
      storageNodeYFilter: "",
      storageNodeZFilter: "",
      storageIncludeDeletedFilter: false,
      storageDeletedAtStartTimeFilter: null,
      storageDeletedAtEndTimeFilter: null,
      storages: {},
      storageField: [],
      totalPages: 3,
      currentPage: 1,
      perPage: 15,
      pageSizes: [10, 15, 30, 50, 100],
      maxPageSize: 100,
      totalItem: 0,

      stations: [],
      nodes: [],
      tags: [],

      stationZoneGroup: "",

      options: {},
      headers: [
        { text: "NO", value: "index", groupable: false, sortable: false },
        { text: "CODE", value: "code", groupable: false },
        {
          text: "STATUS",
          value: "status",
          groupable: false,
        },
        {
          text: "COORDINATE",
          value: "coordinate",
          groupable: false,
        },
        {
          text: "ZONE GROUP",
          value: "zoneGroup",
          groupable: false,
        },
        {
          text: "RESERVATION",
          value: "reservationNo",
          groupable: false,
        },
        { text: "LAST MOVEMENT", value: "lastMovement", groupable: false },
        { text: "UPDATED AT", value: "updatedAt", groupable: false },
        { text: "STATION", value: "station", groupable: false },
        { text: "TAGS", value: "tagNames", groupable: false },
        {
          text: "ACTION",
          value: "controls",
          groupable: false,
          sortable: false,
        },
      ],
    };
  },

  beforeMount() {
    this.checkDualStation()
    this.reloadData(true, false);
  },

  beforeDestroy() {
    clearInterval(this.autoRefreshTimer);
  },

  watch: {
    options: {
      async handler() {
        this.reloadData(true, false);
      },
      deep: true,
    },
    storageStatusFilter() {
      this.reloadData(true, false);
    },
    storageNodeXFilter() {
      this.reloadData(true, false);
    },
    storageNodeYFilter() {
      this.reloadData(true, false);
    },
    storageNodeZFilter() {
      this.reloadData(true, false);
    },
    storageCodeFilter() {
      this.reloadData(true, false);
    },
    storageLastMovementFilter() {
      this.reloadData(true, false);
    },
    storageIncludeDeletedFilter() {
      this.reloadData(true, false);
    },
    storageDeletedAtStartTimeFilter() {
      this.reloadData(true, false);
    },
    storageDeletedAtEndTimeFilter() {
      this.reloadData(true, false);
    },
  },

  methods: {
    async checkDualStation(){
      const getDualStationMap = await SettingAPI.getByKey("DualStationMap")
      this.isDualStation = getDualStationMap != null ? Object.keys(getDualStationMap)?.length > 0 ? true : false : false
    },

    toggleAutoRefresh() {
      if (this.autoRefresh) {
        if (this.autoRefreshTimer) clearInterval(this.autoRefreshTimer);

        this.autoRefreshTimer = setInterval(() => {
          this.reloadData(true, false);
        }, this.autoRefreshIntervalInSec * 1000);
      } else {
        clearInterval(this.autoRefreshTimer);
      }
    },
    dynamicDestructure(objData, propertiesArr) {
      if (!objData) return {};
      if (propertiesArr.length > 0) {
        return propertiesArr.reduce(
          (acc, cur) => ({ ...acc, [cur]: objData[cur] }),
          {}
        );
      }
      return objData;
    },

    async getStorages(hardRefresh = false, reRunOldQuery = false) {
      this.refreshLoading = true;

      const { sortDesc } = this.options;

      let params = null;

      if (reRunOldQuery && this.oldQuery) {
        params = this.oldQuery;
      } else {
        params = {
          perPage: this.perPage,
          pageNo: this.currentPage,
          codes: this.storageCodeFilter,
          status: this.storageStatusFilter,
          lastMovement: this.storageLastMovementFilter,
          ordering:
            sortDesc?.length === 1 ? (sortDesc[0] ? "DESC" : "ASC") : "ASC",
        };

        if (
          this.storageNodeXFilter ||
          this.storageNodeYFilter ||
          this.storageNodeZFilter
        ) {
          if (this.storageNodeXFilter.length > 0)
            params.x = this.storageNodeXFilter;
          if (this.storageNodeYFilter.length > 0)
            params.y = this.storageNodeYFilter;
          if (this.storageNodeZFilter.length > 0)
            params.z = this.storageNodeZFilter;
        }

        if (this.storageIncludeDeletedFilter) {
          if (this.storageDeletedAtStartTimeFilter) {
            params.deletedAtStart = this.storageDeletedAtStartTimeFilter;
          }
          if (this.storageDeletedAtEndTimeFilter) {
            params.deletedAtEnd = this.storageDeletedAtEndTimeFilter;
          }
        }
      }

      const storageObj = {};
      const getStoragesAPI = await SmStorageAPI.getStorages(params)
        .then(async (res) => {
          let result = res.data.data;

          if (result.length === 0) return;

          this.totalPages = res.data.pagination.totalPages;
          this.totalItem = res.data.pagination.totalRecords;

          const stationSet = new Set();
          // const nodeSet = new Set();
          result.forEach((storage, index) => {
            storage.index = (this.currentPage - 1) * this.perPage + index + 1;
            storage.tagNames = {
              key: storage.code,
              tags:
                storage.tags && storage.tags.length > 0
                  ? storage.tags.map((x) => x).join(", ")
                  : null,
            };
            storage.createdAt = storage.createdAt
              ? convertStringToLocal(storage.createdAt, true)
              : null;
            storage.updatedAt = storage.updatedAt
              ? convertStringToLocal(storage.updatedAt, true)
              : null;
            storage.deletedAt = storage.deletedAt
              ? convertStringToLocal(storage.deletedAt, true)
              : null;
            storage.lastMovedAt = storage.lastMovedAt
              ? convertStringToLocal(storage.lastMovedAt, true)
              : null;  
   
            storage.zoneGroup = this.isDualStation ? 
                                  storage.node !== null ? 
                                    storage.node.zoneGroup === "LEFT" ? "A / LEFT" : "B / RIGHT" : "" 
                                : storage.node?.zoneGroup

            storageObj[index] = storage;

            if (storage.station) {
              stationSet.add(storage.station);
            }

            if (storage.node) {
              //nodeSet.add(storage.nodeId)
              const data = {
                storageCode: storage.code,
                lastMovedAt: storage.lastMovedAt,
                deletedAt: storage.deletedAt,
                ...(storage.node != null
                  ? {
                      nodeZoneGroup: storage.node.zoneGroup === "LEFT" ? "A / LEFT" : "B / RIGHT",
                      nodeStorageCap: storage.node.storageCap,
                      nodeHardwareIndex: storage.node.hardwareIndex,
                    }
                  : {
                      nodeZoneGroup: null,
                      nodeStorageCap: null,
                      nodeHardwareIndex: null,
                    }),
                //...(storage.station != null? {station: storage.station.station, stationIsActive: storage.station.isActive, stationIsEnrolling: storage.station.isEnrolling }:{station:null, stationIsActive: null, stationIsEnrolling: null})
              };
              //storage.node.storageCode = storage.code
              this.nodes.push(data);
            }

            this.tags.push({ key: storage.code, tagNames: storage.tags });
          });

          if (stationSet.size > 0) {
            await this.getStations(Array.from(stationSet));
          }

          // if (nodeSet.size > 0) {
          //   await this.getNodes(Array.from(getNodes));
          // }

          this.oldQuery = params;
        })
        .catch((err) => { 
          console.error(err)
          alert(err)
        });

      await Promise.all([getStoragesAPI]);

      this.storages = storageObj;

      if (hardRefresh) {
        this.currentPage = 1;
      }
      this.refreshLoading = false;
    },

    async getStations() {
      await StationAPI.getAll()
        .then((res) => {
          let result = res;

          if (result.length === 0) return;

          result.forEach((station) => {
            station.createdAt = convertStringToLocal(station.createdAt, true);
            station.updatedAt = convertStringToLocal(station.createdAt, true);
          });

          this.stations = result;
        })
        .catch((err) => { 
          console.error(err)
          alert(err)
        });
    },

    handlePageChange(value) {
      this.currentPage = value;
      this.reloadData();
    },

    handlePageSizeChange(value) {
      this.perPage = value;
      this.reloadData();
    },

    reloadData(hardRefresh = false, reRunOldQuery = false) {
      this.getStorages(hardRefresh, reRunOldQuery);
      //this.getStats()
    },

    rowColor(item) {
      if (!item.node) return "";

      switch (item.node.zoneGroup) {
        case "LEFT":
          return "yellow--text text--accent-4";
        case "RIGHT":
          return "light-green--text text--accent-3";
        default:
          return "";
      }
    },

    async infoStorage(id) {
      this.$router.push(Route.StorageLogs + `/${id}`);
    },

    async editStorage(id) {
      this.$router.push(Route.Storage + `/${id}`);
    },

    async disenrollStorage(storage) {
      const result = confirm(
        "Are you sure you want to disenroll this storage?"
      );

      if (result) {
        await SmOperationAPI.disenrollBin({ storage })
          .then(async (response) => {
            console.log(response);
          })
          .catch((error) => alert(error.response.data.error));
      }
    },

    async disenrollStorageWithApiGateway(storage, station) {
      const result = confirm(
        "Are you sure you want to disenroll this storage?"
      );

      const token = await getAccessToken()
      
        let stationZoneGroup = ""
      const dualStationMap = await SettingAPI.getByKey(SmSettingsKey.DualStationMap)

      for (const value of Object.values(dualStationMap)) {
        for (const [zoneGroup, number] of Object.entries(value)) {
          if (number === station) {
            stationZoneGroup = zoneGroup;
            //return; // Exit once the direction is found
          }
        }
      }

      if (result) {
        await GatewayAPI.disenrollBinThroughApiGateway({ storage: storage.toString(), lane: stationZoneGroup }, token)
          .then(async (response) => {
            if(response.status === 200){
              alert("Disenroll Successfull")
            }
            // refresh
            this.reloadData()
          })
          .catch((error) => 
            alert(error.response?.data.errors[0].message)
          );
      }
    },

    validateStringLength(value) {
      if (!value) return;
      if (value.length > 10) {
        return value.slice(0, 10) + "...";
      } else {
        return value;
      }
    },
  },

  computed: {
    ...mapGetters(["isAdmin"]),
    dataPreps() {
      return Object.values(this.storages).map((storage) => {
        let coor = "";
        if (storage.node != null) {
          coor = `${storage.node.x}, ${storage.node.y}, ${storage.node.z}`;
        } else {
          coor = "";
        }

        this.storages = {
          ...storage,
          coordinate: coor,
        };

        return this.storages;
      });
    },
  },
};
</script>

<style>
/* @import '~@fortawesome/fontawesome-free/css/fontawesome.min.css';
@import '~@fortawesome/fontawesome-free/css/solid.min.css'; */
@import "../assets/Stars.css";
@import "../assets/Background.css";

.content {
  margin: 50px;
  margin-top: 25px;
}

.no-spinner input[type="number"]::-webkit-inner-spin-button,
.no-spinner input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  appearance: none;
  margin: 0;
}

.theme--dark input[type="datetime-local"]::-webkit-calendar-picker-indicator {
  filter: invert(100%);
}
</style>
