import Vue from 'vue';
import Vuetify from 'vuetify/lib';
import 'vuetify/dist/vuetify.min.css';
import SkycarTable from './skycar/SkycarTable.vue';

Vue.use(Vuetify);

export default {
  title: 'Skycar/SkycarTable',
  component: SkycarTable,
  parameters: {
    docs: {
      description: {
        component: 'A partial story for the Skycar v-data-table, for UI/UX testing with mock data.'
      }
    }
  },
  argTypes: {
    items: { control: 'object', description: 'Array of skycar objects' },
    headers: { control: 'object', description: 'Table headers' }
  }
};

const vuetify = new Vuetify();

export const SkycarDataTable = (args, { argTypes }) => ({
  vuetify,
  components: { SkycarTable },
  props: Object.keys(argTypes),
  template: `
    <v-app>
      <SkycarTable :headers="headers" :items="items" />
    </v-app>
  `
});

SkycarDataTable.args = {
    headers: [
        {
          text: "Skycar ID ",
          align: "left",
          sortable: true,
          value: "skycar_id",
        },
        { text: "Status", value: "status" },
        { text: "Connect", value: "connect" },
        { text: "Pair", value: "pair" },
        { text: "Coordinate", value: "coordinate" },
        { text: "Job", value: "job_id" },
        { text: "Job Elapsed", value: "job_elapsed" },
        { text: "Winch", value: "winch" },
        { text: "Battery %", value: "battery" },
        // { text: "Mode", value: "mode"},
        { text: "Mode", value: "maintenance_mode" },
        { text: "Action", value: "action" },
      ],
  items: [
    {
      skycar_id: 1,
      status: 'AVAILABLE',
      connect: true,
      pair: true,
      direction: 'X',
      coordinate: '16,1,0',
      storage_no: null,
      battery: 100,
      mode: 'Normal',
      orientation: 'North',
      maintenance_mode: null,
      is_docked: false,
      winch: {
        BOTH: {
          storage_no: null,
          is_active: true,
          bin_on_hold: false,
          assign_storage_code: null,
          platform: false
        }
      },
      tentative_job_id: null,
      job_id: null,
      job_begin_at: null
    },
    {
      skycar_id: 2,
      status: 'AVAILABLE',
      connect: true,
      pair: true,
      direction: 'X',
      coordinate: '16,2,0',
      storage_no: 'SBG-001532 (B)',
      battery: 100,
      mode: 'Normal',
      orientation: 'North',
      maintenance_mode: 'TRAVEL (5,33)',
      is_docked: false,
      winch: {
        BOTH: {
          storage_no: 'SBG-001532',
          is_active: true,
          bin_on_hold: false,
          assign_storage_code: 1532,
          platform: false
        }
      },
      tentative_job_id: [16],
      job_id: 15,
      job_begin_at: ["2025-06-27T03:38:17.378364+00:00"]
    }
  ]
}; 