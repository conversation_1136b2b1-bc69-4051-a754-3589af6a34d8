<!--  usage example 

in data: () =>
create a variable = your_message 

in your script block
import Snackbar from "./Snackbar.vue";

in any .vue page , use the Snackbar class and pass in variable or hardcoded string.
<Snackbar :message='your_message'/>

attention , your message must unique everytime else watch wont update the message.

-->


<!-- ./components/Snackbar.vue -->
<template>
  <v-snackbar v-model="show" :top="top" :color="color">
    {{ message }}
    <!-- <v-btn flat color="accent" @click.native="show = false">
      <v-icon>close</v-icon>
    </v-btn> -->

    <template v-slot:action="{ attrs }">
      <v-btn color="pink" text v-bind="attrs" @click="show = false">
        Close
      </v-btn>
    </template>
  </v-snackbar>
</template>

<script>
export default {
  props: {
    message: String,
    show : Boolean
  },
  data() {
    return {
      dark: true,
    //   show: false,
      top: true,
      // message: "",
      color: "",
      timeout: 5000,
    };
  },
  created: function () {
    //   const message = 'test snackbar test snackbar test snackbar test snackbar test snackbar'
    if (this.message) {
      this.show = true;
      // this.message = message;

      // alert(JSON.parse(this.input))
      // alert(this.message)
      // this.color = this.$store.state.snackbar.snack.color
      // this.color = 'red'
      // this.$store.commit("snackbar/setSnack", {});

      // alert(this.$store.state.snackbar.snack.color)
    }
  },

  watch: {
    message: function() {
        // alert(newval,oldval)
        // return this.message;
        if (this.message) {
            this.show = true;
         }
    },
  },
};
</script>