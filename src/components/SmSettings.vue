<template>
  <div>
    <v-container>
      <h2 class="mb-6">Configuration</h2>
    </v-container>

    <v-container>
      <h3 class="mb-4 blue-grey--text text--lighten-1">
        Order's Rules & Dependence
      </h3>

      <v-row>
        <v-col cols="12" lg="6" xl="6">
          <v-skeleton-loader
            v-if="isLoading"
            type="list-item-avatar, divider, list-item-three-line, card-heading, image, actions"
          ></v-skeleton-loader>
          <OrderDispatcher v-else :zoneGroups="zoneGroups"></OrderDispatcher>
        </v-col>

        <v-col cols="12" lg="6" xl="6">
          <v-skeleton-loader
            v-if="isLoading"
            type="list-item-avatar, divider, list-item-three-line, card-heading, image, actions"
          ></v-skeleton-loader>

          <PickDropRules v-else></PickDropRules>
        </v-col>
      </v-row>
    </v-container>

    <v-container>
      <h3 class="mb-4 blue-grey--text text--lighten-1">
        Storage Optimization
      </h3>

      <v-row>
        <v-col cols="12" lg="12" xl="12">
          <v-skeleton-loader
            v-if="isLoading"
            type="list-item-avatar, divider, list-item-three-line, card-heading, image, actions"
          ></v-skeleton-loader>
          <StorageOptimizer v-else :zoneGroups="zoneGroups"></StorageOptimizer>
        </v-col>
      </v-row>
    </v-container>

    <v-container>
      <h3 class="mb-4 blue-grey--text text--lighten-1">Station</h3>
      <v-row>
        <v-col cols="12" xl="6">
          <v-skeleton-loader
            v-if="isLoading"
            type="list-item-avatar, divider, list-item-three-line, card-heading, image, actions"
          ></v-skeleton-loader>

          <StationConfig v-else :stations="stations"></StationConfig>
        </v-col>
        <v-col cols="12" xl="6">
          <v-skeleton-loader
            v-if="isLoading"
            type="list-item-avatar, divider, list-item-three-line, card-heading, image, actions"
          ></v-skeleton-loader>
        </v-col>
      </v-row>
    </v-container>

    <v-container>
      <h3 class="mb-4 blue-grey--text text--lighten-1">Weightages</h3>
      <v-row>
        <v-col>
          <v-skeleton-loader
            v-if="isLoading"
            type="list-item-avatar, divider, list-item-three-line, card-heading, image, actions"
          ></v-skeleton-loader>

          <FindDestinationFactors v-else></FindDestinationFactors>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="12" lg="6" xl="3">
          <v-skeleton-loader
            v-if="isLoading"
            type="list-item-avatar, divider, list-item-three-line, card-heading, image, actions"
          ></v-skeleton-loader>

          <FindStoragesFactors v-else></FindStoragesFactors>
        </v-col>

        <v-col cols="12" lg="6" xl="3">
          <v-skeleton-loader
            v-if="isLoading"
            type="list-item-avatar, divider, list-item-three-line, card-heading, image, actions"
          ></v-skeleton-loader>

          <OrderPrioritisation v-else></OrderPrioritisation>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script>
import { StationAPI } from "@/api/station";
import { ZoneGroupAPI } from "@/api/zone-group";
import OrderDispatcher from "./configuration/OrderDispatcher.vue";
import PickDropRules from "./configuration/PickDropRules.vue";
import StationConfig from "./configuration/StationConfig.vue";
import StorageOptimizer from "./configuration/StorageOptimizer.vue";
import FindStoragesFactors from "./configuration/FindStoragesFactors.vue";
import FindDestinationFactors from "./configuration/FindDestinationFactors.vue";
import OrderPrioritisation from "./configuration/OrderPrioritisation.vue";

export default {
  name: "Configuration",
  components: {
    PickDropRules,
    OrderDispatcher,
    StationConfig,
    StorageOptimizer,
    FindStoragesFactors,
    FindDestinationFactors,
    OrderPrioritisation,
  },
  data() {
    return {
      isLoading: true,
      stations: undefined,
      zoneGroups: undefined,
    };
  },

  methods: {
    async init() {
      await Promise.all([this.getZoneGroups(), this.getStations()]);
      this.isLoading = false;
    },
    async getZoneGroups() {
      try {
        this.zoneGroups = await ZoneGroupAPI.getAll();
      } catch (e) {
        console.error(e);
        console.error(e.response);
      }
    },

    async getStations() {
      try {
        this.stations = await StationAPI.getAll();
      } catch (e) {
        console.error(e);
        console.error(e.response);
      }
    },
  },

  created() {
    this.init();
  },
};
</script>
