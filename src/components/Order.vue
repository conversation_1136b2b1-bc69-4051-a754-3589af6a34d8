<template>
  <v-app>
    <v-container fluid>
      <v-card dark>
        <v-container fluid>
          <!-- Job Status and Quantity -->
          <v-row v-if="showIndicator">
            <v-col cols="4">
              <DashboardIndicator
                :icon="'mdi-chart-bar'"
                :title="'AVAILABLE JOB'"
                :value="modelSkycarJob.cacheSkycarJob.length"
                :background-color="'#000000'"
                :gradient-color="'#92C7C7'"
              />
            </v-col>

            <v-col cols="4">
              <DashboardIndicator
                :icon="'mdi-chart-bar'"
                :title="'PROCESSING JOB'"
                :value="modelSkycarJob.processingJob.length"
                :background-color="'#000000'"
                :gradient-color="`linear-gradient(135deg, #FFA500 , #F2BB66)`"
              />
            </v-col>

            <v-col cols="4">
              <DashboardIndicator
                :icon="'mdi-chart-bar'"
                :title="'ERRORED JOB'"
                :value="modelSkycarJob.errorJob.length"
                :background-color="'#000000'"
                :gradient-color="'#F67280'"
              />
            </v-col>
          </v-row>

          <!-- Diaglog -->
          <v-row v-if="showIndicator">
            <v-col cols="12">
              <SharedStatusChipComponent
                v-for="status in jobTypeLegends"
                :key="status"
                :chip-style="getJobStatus(status).style"
                :chip-color="getJobStatus(status).color"
                :chip-text="getJobStatus(status).desc"
                :tooltip-text="getJobStatus(status).state"
                :current-hover-color="enlargedStatus"
              />
            </v-col>
          </v-row>

          <v-row>
            <v-col cols="2">
              <v-select
                :items="cubeItem"
                label="Cube"
                v-model="cube"
                class="ma-2"
                @change="onCubeChanged"
              />
            </v-col>
            <v-col cols="6">
              <v-select
                :items="jobTypeItems"
                label="Filter Job Type"
                v-model="modelJobType"
                v-on:change="onbtnSkycarJob()"
                class="ma-2"
              />
            </v-col>
            <v-col cols="2">
              <v-btn
                @click="onbtnSkycarJob()"
                :disabled="!doneSync"
                class="ma-2"
              >
                Refresh
              </v-btn>
            </v-col>

            <v-col cols="1">
              <v-switch
                v-model="modelSkycarJob.autoRefreshSwitch"
                hide-details
                color="success"
                :label="`Auto Refresh`"
              />
            </v-col>

            <v-col cols="1">
              <v-text-field
                type="number"
                label="Refresh Interval(s)"
                value="10"
                v-model="modelSkycarJob.autoRefreshInterval"
                @input="validateInterval"
                :disabled="modelSkycarJob.autoRefreshSwitch"
              />
            </v-col>
          </v-row>
        </v-container>
      </v-card>

      <v-data-table
        dark
        v-model="modelSkycarJob.dtSelected"
        :headers="headers"
        hide-default-footer
        item-key="job_id"
        :loading="!doneSync"
        loading-text="Loading... Please wait"
        :items="modelSkycarJob.skycarJob"
        :items-per-page="modelSkycarJob.modelPagination.itemPerPage"
        :items-length="modelSkycarJob.modelPagination.itemTotalLength"
        @update:page="onPageChange"
        class="elevation-1"
        group-by="type"
      >
        <!-- :footer-props=modelSkycarJob.modelPagination.itemPerPageOptions -->
        <!-- @update:items-per-page="onItemPerPageChange" -->
        <!-- cell filtering -->
        <template v-slot:[`item.with_storage`]="{ item }">
          <v-chip 
            :color="getColor(item.with_storage)" 
            dark
          >
            {{ getStatus(item.with_storage)[1] }}
          </v-chip>
        </template>

        <template v-slot:[`item.eta`]="{ item }">
          <v-tooltip bottom>
            <template v-slot:activator="{ on, attrs }">
              <span 
                v-bind="attrs" 
                v-on="on"
              >
                {{ item.eta }}
              </span>
            </template>
            <v-card>
              <v-data-table
                :headers="modelSkycarJob.actionHeader"
                :items="item.eta_actions_data"
                dark
              />
            </v-card>
          </v-tooltip>
        </template>

        <template v-slot:[`item.tc_rank`]="{ item }">
          <v-tooltip bottom>
            <template v-slot:activator="{ on, attrs }">
              <span 
                v-bind="attrs" 
                v-on="on"
              >
                {{ getRank(item.tc_rank) }}
              </span>
            </template>
            {{ item.tc_rank }}
          </v-tooltip>
        </template>

        <template v-slot:item.status="{ item }">
          <v-tooltip right>
            <template v-slot:activator="{ on, attrs }">
              <span
                v-bind="attrs"
                v-on="on"
                @mouseenter="
                  handleChipHover(getJobStatus(item.dashboard_status))
                "
                @mouseleave="handleChipHover('', (leave = true))"
              >
                <v-chip
                  :style="
                    getJobStatus(item.dashboard_status).style || {
                      backgroundColor: getJobStatus(item.dashboard_status)
                        .color,
                    }
                  "
                  dark
                >
                  {{ getJobStatus(item.dashboard_status).alias }}
                </v-chip>
              </span>
            </template>
            <pre> SM Order ID: {{ item.sm_order_id }}</pre>
            <pre> Status: {{ item.status }}</pre>
            <pre>
 Status Desc: {{ getJobStatus(item.dashboard_status).state }}</pre
            >
            <pre> From Node: {{ item.from_node }}</pre>
            <pre> To Node: {{ item.to_node }}</pre>
            <pre>
 Begin At: {{ convertStringToLocal(item.begin_at, true) }}</pre
            >
            <pre>
 Created At: {{ convertStringToLocal(item.created_at, true) }}</pre
            >
            <pre>
 Updated At: {{ convertStringToLocal(item.updated_at, true) }}</pre
            >
            <pre>
 Completed At: {{ convertStringToLocal(item.completed_at, true) }}</pre
            >
            <pre> Completed By: Skycar {{ item.completed_by_skycar }}</pre>
            <pre> Error Name: {{ item.error_name }}</pre>
            <pre> Error Message: {{ item.error_msg }}</pre>
            <pre> Failed Rule: {{ item.failed_rule }}</pre>
            <pre> Start Job Time: {{ convertStringToLocal(item.start_job_time, true) }}</pre>
            <pre> For Station: {{ item.for_station }}</pre>
          </v-tooltip>
        </template>

        <template v-slot:[`item.failed_rule`]="{ item }">
          <v-chip :color="getColor(item.failed_rule == null)" dark>
            {{ getStatus(item.failed_rule == null)[1] }}
          </v-chip>
        </template>

        <template v-slot:[`item.action`]="{ item }">
          <v-btn 
            small 
            class="mr-2" 
            light 
            @click="btnActionDialog(item)"
          >
            Action
          </v-btn>
        </template>
      </v-data-table>

      <v-card dark>
        <v-row justify="end">
          <v-col cols="4">
            <v-card 
              class="ml-2"
              outlined
            >
              <v-row>
                <v-col cols="5">
                  <v-select
                    class="ml-2"
                    v-model="trackKey"
                    :items="trackOptions"
                    outlined
                  >
                  </v-select>
                </v-col>
                <v-col cols="7">
                  <v-text-field
                    class="mr-2"
                    v-model="trackValue"
                    :label="trackRes.label"
                    outlined
                    :hint="trackRes.hint"
                    @keydown.enter="trackRes.action"
                    :type="trackRes.type"
                    persistent-hint
                  >
                    <template v-slot:append>
                      <v-icon
                        v-if="trackValue"
                        @click="trackRes.action"
                        color="green"
                      >
                        mdi-check
                      </v-icon>
                      <v-icon
                        v-else
                        disabled
                      >
                        mdi-check
                      </v-icon>
                    </template>
                  </v-text-field>
              </v-col>
              </v-row>
            </v-card>
          </v-col>
          <v-col cols="6">
            <v-pagination
              dark
              v-model="modelSkycarJob.modelPagination.page"
              :length="modelSkycarJob.modelPagination.totalPages"
              :total-visible="8"
              @input="onPageChange"
            />
          </v-col>
          <v-col 
            cols="2" 
            class="pr-5"
          >
            <v-select
              v-model="modelSkycarJob.modelPagination.itemPerPage"
              :items="modelSkycarJob.modelPagination.itemPerPageOptions"
              label="Jobs per page"
              dense
              outlined
              @change="onItemPerPageChange"
            />
          </v-col>
        </v-row>
      </v-card>

      <!-- Action For Job  -->
      <DialogIsolationJobAction
        :selected-job-item="selectedJobItem"
        :model-skycar-job="modelSkycarJob"
        :target-host="getHost(this.cube)"
        :callable-http="AxiosHttpWithAwesomeAlert"
      />
      <DialogEventLog 
        :default-events="[Event.STORAGE]"
        :event-options="[Event.STORAGE]"
        :show-notification="showNotification"
        ref="dialogTrackStorage" 
      />
      <DialogEventLog 
        :default-events="[Event.OBSTACLE, Event.STORAGE]"
        :event-options="[Event.OBSTACLE, Event.STORAGE]"
        :show-notification="showNotification"
        ref="dialogTrackCoord" 
      />
      <SnackbarNotification ref="snackbarNotification" />
    </v-container>
    <!-- time line card -->
  </v-app>
</template>

<script>
import {
  getStatus,
  getJobStatus,
  getHost,
  convertStringToLocal,
  getCube,
  getRequestHeader,
  useRefreshToken,
} from "../helper/common.js";
import { Event, Module, JobStatus, Role, RouteJob } from "../helper/enums";
import { authorizeRole } from "../helper/authorize";
import DialogIsolationJobAction from "./dialogs/DialogIsolationJobAction.vue";
import { AxiosHttpWithAwesomeAlert } from "../helper/http_request.js";
import SharedStatusChipComponent from "./shared/StatusChipLegend.vue";
import DashboardIndicator from "./shared/DashboardIndicator.vue";
import DialogEventLog from "./dialogs/DialogEventLog.vue";
import SnackbarNotification from "./shared/SnackbarNotification.vue";
export default {
  name: "App",
  components: {
    DialogIsolationJobAction,
    SharedStatusChipComponent,
    DashboardIndicator,
    DialogEventLog,
    SnackbarNotification
  },

  beforeRouteLeave(to, from, next) {
    // Clear any active intervals
    this.removeIntervalWithID(this.intervalIds);
    next();
  },

  watch: {
    "modelSkycarJob.autoRefreshInterval"(newVal) {
      if (!this.modelSkycarJob.autoRefreshSwitch) {
        this.removeIntervalWithID(this.intervalIds);
        return;
      }
      this.modelSkycarJob.autoRefreshInterval = newVal;
      this.startInterval();
    },
    "modelSkycarJob.autoRefreshSwitch"() {
      if (this.modelSkycarJob.autoRefreshSwitch) {
        this.validateInterval();
        this.startInterval();
      }

      if (!this.modelSkycarJob.autoRefreshSwitch) {
        this.removeIntervalWithID(this.intervalIds);
      }
    },

    modelJobType() {
      if (this.modelJobType == "Inactive") {
        this.showIndicator = false;
      } else {
        this.showIndicator = true;
      }
    },
    trackKey(key) {
      this.trackValue = null
      switch (key) {
        case Module.STORAGE: {
          this.trackRes = {
            label: "Track Storage",
            hint: "Fill in the storage code.",
            action: this.btnTrackStorage,
            type: "number"
          }
          break
        }
        case Module.COORD: {
          this.trackRes = {
            label: "Track Coordinate",
            hint: "Example: 5,5 || 5,5,L || 5,5,5,L",
            action: this.btnTrackCoord,
            type: "string"
          }
          break
        }
        case Module.WS: {
          this.trackRes = {
            label: "Track Work Station",
            hint: "Fill in the station code.",
            action: this.btnTrackWS,
            type: "number"
          }
          break
        }
      }
    }
  },

  async created() {
    await this.onbtnSkycarJob(); // Wait for the Skycar jobs to be fetched
    this.startInterval(), this.updateJobDTHeader();
    this.trackKey = Module.STORAGE
  },
  computed: {
    totalPages() {
      // Calculate the total number of pages based on total items and items per page
      var toRenderTotalPage = this.modelSkycarJob.modelPagination.totalPages;
      console.log(
        "Recomputed total pages" +
          this.modelSkycarJob.modelPagination.totalCount
      );
      return toRenderTotalPage;
    },
  },

  data() {
    return {
      Event,
      Module,
      getHost,
      RouteJob,
      convertStringToLocal,
      trackOptions: [Module.STORAGE, Module.COORD, Module.WS],
      trackKey: null,
      trackValue: null,
      trackRes: {},
      cube: getCube()[0],
      cubeItem: getCube(),
      doneSync: false,
      skycarJobDetail: [],
      skycarJobDetailQty: 0,
      modelToolTip: null,
      intervalIds: [],

      // Status chip legend
      showIndicator: true,
      enlargedStatus: "",
      jobTypeItems: ["Active", "Inactive"],
      modelJobType: "Active",
      jobTypeLegends: Object.keys(JobStatus),

      // Tc Job Datatable
      // currentPage : 1 , // current page
      modelSkycarJob: {
        modelPagination: {
          // itemPerPageOptions : {
          //   'items-per-page-options': [ 1,2,5,10,30,50,100],
          //   'items-per-page-text': 'Jobs per page',
          //   'show-current-page': true,  // Hide the current page text in footer
          //   'show-first-last-page': true  // Hide the "first page" and "last page" buttons in footer
          // },

          itemPerPage: 20, // current selection
          itemPerPageOptions: [10, 20, 50, 100], // user selections
          totalPages: 1, // total pages return by BE
          itemTotalLength: 0, // total rows return by BE
          page: 1, // current page
        },
        failedJob: [],
        errorJob: [],
        processingJob: [],
        skycarJob: [],
        cacheSkycarJob: [],
        dtSelected: [],
        singleExpand: false,
        expanded: [],
        autoRefreshInterval: 10,
        autoRefreshSwitch: false,
        boolDialogAction: false,
        actionHeader: [
          { text: "Header", value: "header_id" },
          { text: "Desc", value: "desc" },
          { text: "Actual Elapsed", value: "actual_elapse" },
          { text: "Expected Elapsed", value: "expected_elapse" },
          { text: "Action", value: "action" },
          { text: "Axis", value: "axis" },
          { text: "Qty", value: "qty" },
          { text: "Start", value: "begin_at" },
          { text: "End", value: "complete_at" },
          // { text: "Steps", value: "steps" }
        ],
      },
      headers: [
        {
          text: "Skycar ID ",
          align: "left",
          sortable: true,
          value: "dashboard_skycar_id",
        },
        { text: "Prefer Skycar ", align: "left", value: "prefer_skycar_id" },
        { text: "Job ID", value: "job_id", groupable: false },
        { text: "Type", value: "type" },
        { text: "Status", value: "status", sortable: false },
        { text: "Desc", value: "station_desc", groupable: false },
        { text: "From Coordinate", value: "from_coordinate" },
        { text: "To Coordinate", value: "to_coordinate" },
        { text: "Storage_No", value: "storage_no", groupable: false },
        { text: "With Storage", value: "with_storage", groupable: false },
        { text: "Completed In (s)", value: "eta", groupable: false },
        { text: "Rank", value: "tc_rank", groupable: false },
      ],

      selectedJobItem: {},
      onbtnSkycarJob: async function() {
        this.doneSync = false;
        await this.fetchSkycarJob();
        this.doneSync = true;
      },

      fetchSkycarJob: async function() {
        let returnJson = await this.httpSkycarJob();
        if (returnJson instanceof Error) {
          this.$awn.alert(returnJson);
        } else if (typeof returnJson === "object") {
          this.modelSkycarJob.skycarJob = returnJson;
          this.modelSkycarJob.cacheSkycarJob = returnJson;
        } else {
          this.modelSkycarJob.skycarJob = JSON.parse(returnJson);
          this.modelSkycarJob.cacheSkycarJob = JSON.parse(returnJson);
        }
        this.updateJobQty();
      },

      httpSkycarJob: async function() {
        try {
          var tcHost = new URL(getHost(this.cube) + RouteJob.DASHBOARD_JOB);

          let params = {
            job_status: this.modelJobType,
            per_page: this.modelSkycarJob.modelPagination.itemPerPage,
            page: this.modelSkycarJob.modelPagination.page,
          };

          tcHost.search = new URLSearchParams(params).toString();
          const response = await fetch(tcHost, { headers: getRequestHeader() });
          const myJson = await response.json(); //extract JSON from the http response
          if (myJson.code === 401) {
            // If access token is unauthorized
            // use refresh token to get new access token from auth server
            return useRefreshToken(this, this.httpSkycarJob);
          }
          // Update v-model
          var metadata = myJson.metadata;

          this.modelSkycarJob.modelPagination.itemTotalLength = metadata
            ? metadata.totalCount || 0
            : 0;
          this.modelSkycarJob.modelPagination.totalPages = metadata
            ? metadata.totalPages || 0
            : 0;

          return myJson.data;
        } catch (error) {
          return error;
        }
      },
    };
  },
  methods: {
    // imported functions
    getJobStatus,
    getStatus,
    AxiosHttpWithAwesomeAlert,

    // local functions
    getRank(rank) {
      if (rank) {
        return `${rank}`.split("-")[0]
      }
      return null
    },

    showNotification(success, message) {
      this.$refs.snackbarNotification.showNotification(success, message)
    },

    btnTrackStorage() {
      this.$refs.dialogTrackStorage.openDialog(`${Module.STORAGE}-${this.trackValue}`)
    },
    btnTrackCoord() {
      this.$refs.dialogTrackCoord.openDialog(`${Module.COORD}-${this.trackValue}`)
    },
    btnTrackWS() {
      this.$refs.dialogTrackStorage.openDialog(`${Module.WS}-${this.trackValue}`)
    },
    timeColor(status) {
      if (status === "COMPLETED") {
        return "green";
      } else if (status == "PROCESSING") {
        return "purple";
      } else {
        return "blue";
      }
    },
    getColor(boolean) {
      if (boolean === true) {
        return "green";
      } else {
        return "red";
      }
    },
    removeIntervalWithID(ids) {
      // remove and reset this.intervalIds
      if (ids) {
        ids.forEach((element) => {
          clearInterval(element);
          console.log(`Removed interval id ${element}`);
        });

        this.intervalIds = [];
      }
    },
    startInterval() {
      this.removeIntervalWithID(this.intervalIds);

      if (!this.modelSkycarJob.autoRefreshSwitch) {
        return;
      }
      const ids = setInterval(() => {
        this.fetchSkycarJob();
        console.log(
          `Created new interval id ${ids} with ${this.modelSkycarJob.autoRefreshInterval}(s)`
        );
      }, this.modelSkycarJob.autoRefreshInterval * 1000);

      this.intervalIds.push(ids);
    },
    isValidInteger(value) {
      const intValue = parseInt(value, 10);
      return Number.isInteger(intValue) && intValue > 0;
    },

    validateInterval() {
      if (!this.modelSkycarJob.autoRefreshSwitch) {
        return;
      }

      if (!this.isValidInteger(this.modelSkycarJob.autoRefreshInterval)) {
        this.modelSkycarJob.autoRefreshInterval = "10";
        this.$awn.tip("Minimum 1 second for refresh interval.");
        return;
      }
    },

    updateJobDTHeader() {
      let keyToRemove = "Action";
      this.headers = this.headers.filter((obj) => obj.text !== keyToRemove);
      if (this.$store.state.cubeConfig.mode || authorizeRole(Role.ADMIN)) {
        const indexToAdd = this.headers.length;
        const item = { text: "Action", value: "action" };
        this.headers.splice(indexToAdd, 0, item);
      }
    },

    btnActionDialog(item) {
      this.selectedJobItem = item;
      this.modelSkycarJob.boolDialogAction = true;
    },

    onItemPerPageChange(val) {
      this.modelSkycarJob.modelPagination.itemPerPage = val;
      // add redirect to page 1 when user at page 2 select item per page but page 2 is not exists.
      this.modelSkycarJob.modelPagination.page = 1;

      this.fetchSkycarJob();
    },
    onPageChange(page) {
      // Update the current page and fetch data for the new page
      this.modelSkycarJob.modelPagination.page = page;

      if (this.modelJobType == "Inactive") {
        this.fetchSkycarJob();
      } else {
        let perPage = this.modelSkycarJob.modelPagination.itemPerPage;
        let start = (page - 1) * perPage;
        this.modelSkycarJob.skycarJob = this.modelSkycarJob.cacheSkycarJob.slice(
          start,
          start + perPage
        );
      }
    },

    handleChipHover(jobStatus, leave = false) {
      // Update the hoverText variable when hovering over the chip
      if (!leave) {
        if (jobStatus.style) {
          this.enlargedStatus = jobStatus.style;
          return;
        }
        this.enlargedStatus = jobStatus.color;
        return;
      }

      this.enlargedStatus = "";
    },

    updateJobQty() {
      let _job = this.modelSkycarJob.cacheSkycarJob;
      this.modelSkycarJob.processingJob = _job.filter((item) => {
        return (
          item.dashboard_status === JobStatus.PROCESSING ||
          item.dashboard_status === JobStatus.PREPROCESSING ||
          item.dashboard_status === JobStatus.FAILED_CONDITION_WHILE_PROCESSING
        );
      });

      this.modelSkycarJob.errorJob = _job.filter((item) => {
        return item.dashboard_status === JobStatus.ERROR;
      });

      this.modelSkycarJob.failedJob = _job.filter((item) => {
        return (
          item.dashboard_status === JobStatus.FAILED_CONDITION ||
          item.dashboard_status === JobStatus.FAILED_CONDITION_WHILE_PROCESSING
        );
      });
    },

    onCubeChanged() {
      this.fetchSkycarJob();
    },
  },
};
</script>

<style>
.dashboard-indicator {
  text-align: center;
  padding: 16px;
  border-radius: 8px;
}

.indicator-title {
  font-size: 18px;
  font-weight: bold;
  color: white;
}

.indicator-value {
  padding-top: 15px;
  font-size: 40px;
  font-weight: bold;
  color: white;
}

.indicator-icon {
  margin-bottom: 16px;
}
.indicator-vertical-bar {
  height: 100%;
  width: 30px;
  /* animation: jump 1s ease infinite;
  transform-origin: bottom; */
}

@keyframes jump {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}
</style>
