<template>
    <v-app app>
        <v-container fluid>
            <v-card dark>
                <v-card outlined class="ma-2">
                    <v-row>
                        <v-card-title class="ml-2">
                            Filter
                        </v-card-title>
                        <v-spacer></v-spacer>
                        <v-switch 
                            v-model="live"
                            :loading="streaming"
                            class="mr-12"
                            label="Live Mode"
                            color="green"
                        />
                    </v-row>
                    <v-row class="mx-1">
                        <!-- Filter By Skycar -->
                        <v-col cols=2>
                            <v-text-field
                                v-model="skycar"
                                label="Skycar"
                                type="number"
                                clearable
                            ></v-text-field>
                        </v-col>
                        <!-- Max Size -->
                        <v-col cols=2>
                            <v-text-field
                                v-model="perPage"
                                label="Display Size"
                                type="number"
                            >
                            </v-text-field>
                        </v-col>
                        <!-- Filter By Start Date -->
                        <v-col
                            v-if="!live"
                        >
                            <DateTimePicker
                                label="Start Time" 
                                :defaultDateTime="defaultStartDateTime"
                                @get-datetime="getStartTimestamp"
                            />
                        </v-col>
                    </v-row>
                    <v-row class="mx-1">
                        <!-- Filter By Message -->
                        <v-col>
                            <v-text-field
                                v-model="message"
                                label="Message"
                                clearable
                            ></v-text-field>
                        </v-col>
                        <v-spacer></v-spacer>
                        <v-col>
                            <div class="d-flex justify-end">
                                <span v-if="live">
                                    <v-btn
                                        rounded
                                        color="success"
                                        :disabled="!skycar"
                                        @click="submit"
                                    >
                                        Submit
                                    </v-btn>
                                </span>
                                <span v-else>
                                    <ProgressCircular
                                        class="mt-1 mr-1"
                                        :doneSync="doneSync"
                                    />
                                    <v-btn
                                        rounded
                                        color="success"
                                        :disabled="!skycar || !doneSync"
                                        @click="query"
                                    >
                                        Submit
                                    </v-btn>
                                </span>
                                <v-btn
                                    class="mx-1"
                                    rounded
                                    color="blue"
                                    :disabled="!items.length"
                                >
                                    <download-csv 
                                        :data="realTimeFilter"
                                        :fields="['created_at', 'message']"
                                        :name="getDownloadFileName()"
                                    >
                                        Download
                                    </download-csv>
                                </v-btn>
                            </div>
                        </v-col>
                    </v-row>
                </v-card>
                <v-divider></v-divider>
                <v-card-text>
                    <v-data-table
                        :item-class="style"
                        disable-pagination
                        hide-default-footer
                        :headers="headers"
                        :items="realTimeFilter"
                        dense
                        disable-sort
                    >
                        <template v-slot:[`item.message`]="{ item }">
                            <span v-html="item.message"></span>
                        </template>
                    </v-data-table>
                    <v-row 
                        v-if="!live"
                        class="d-flex justify-end"
                    >
                        <v-col>
                            <v-pagination
                                v-model="currentPage"
                                :length="totalPages"
                                :total-visible="8"
                                @input="onPageChange"
                            />
                        </v-col>
                    </v-row>
                </v-card-text>
            </v-card>
        </v-container>
        <span v-if="live">
            <span
                v-for="helper in helperButton"
                :key="helper.title"
            >
                <v-btn
                    dark
                    rounded
                    :color="helper[helper.status].color"
                    :style="`position:fixed; bottom:2%; right:${helper.right}%`"
                    @click="btnSwitcher(helper.mode)"
                >
                    <v-icon>{{helper[helper.status].icon}}</v-icon>
                </v-btn>
            </span>
        </span>
        <SnackbarNotification ref="snackbarNotification" />
    </v-app>
</template>

<script>
import { RouteSkycarLog, Websocket } from "../helper/enums"
import { convertTimestampToLocal, getBTUrl, getCurrentDateTime, getRequestHeader } from "../helper/common"
import { socket } from "../App.vue"
import Convert from "ansi-to-html"
import DateTimePicker from "./shared/DateTimePicker.vue"
import SnackbarNotification from "./shared/SnackbarNotification.vue"
import ProgressCircular from "./shared/ProgressCircular.vue"
export default {
    components: {
        ProgressCircular,
        DateTimePicker,
        SnackbarNotification
    },
    data: () => ({
        convert: new Convert(),
        headers: [
            { text: "Date Time", value: "created_at", width: 120 },
            { text: "Message", value: "message" }
        ],
        items: [],
        skycar: null,
        message: null,
        startTimestamp: null,
        helperButton: [
            {
                title: "Start",
                status: false,
                true: {
                    color: "green",
                    icon: "mdi-play"
                },
                false: {
                    color: "red",
                    icon: "mdi-pause"
                },
                right: 8,
                mode: 0
            },
            {
                title: "To Bottom",
                status: false,
                true: {
                    color: "yellow",
                    icon: "mdi-close-circle"
                    
                },
                false: {
                    color: "orange",
                    icon: "mdi-arrow-down"
                },
                right: 0,
                mode: 1
            }
        ],
        doneSync: true,
        streaming: false,
        live: true,
        totalPages: 1,
        currentPage: 1,
        perPage: 200
    }),
    methods: {
        btnSwitcher(mode) {
            let helper = this.helperButton[mode]
            helper.status = !helper.status
            switch (mode) {
                case 0: {
                    if (helper.status) {
                        return this.closeMessage()
                    } else {
                        return this.getMessage()
                    }
                }
            }
        },
        async query() {
            try {
                this.doneSync = false
                this.closeMessage()
                this.items = []
                let url = getBTUrl() + RouteSkycarLog.skycarLog
                let qs = new URLSearchParams({
                    "skycar": this.skycar,
                    "start_time": this.startTimestamp / 1000,
                    "page": this.currentPage,
                    "per_page": this.perPage
                })
                let req = await fetch(`${url}?${qs}`, {
                    method: "GET",
                    headers: getRequestHeader()
                })
                let res = JSON.parse(await req.text())
                let here = this
                if (res.status) {
                    res.data.forEach(item => {
                        here.funcConvert(item)
                    })
                    this.items = res.data
                    this.totalPages = res.total_page
                } else {
                    this.$refs.snackbarNotification.showNotification(false, res.message)
                }
            } catch (error) {
                this.$refs.snackbarNotification.showNotification(false, error)
            } finally {
                setTimeout(() => {
                    this.doneSync = true
                }, 500)
            }
        },
        async onPageChange(page) {
            this.currentPage = page
            await this.query()
        },
        async submit() {
            this.closeMessage()
            this.items = []
            this.getMessage()
            this.$refs.snackbarNotification.showNotification(true, "Success")
        },
        closeMessage() {
            this.streaming = false
            socket.emit(Websocket.LEAVE_SKYCAR_LOG)
            socket.off(Websocket.SKYCAR_LOG)
        },
        getMessage() {
            let here = this
            let items = this.items
            this.streaming = true
            socket.emit(Websocket.ENTER_SKYCAR_LOG, this.skycar)
            socket.on(Websocket.SKYCAR_LOG, function(item) {
                here.funcConvert(item)
                items.push(item)
                here.items = items.slice(-parseInt(here.perPage))
                if (here.helperButton[1].status) {
                    window.scrollTo(0, document.body.scrollHeight)
                }
            })
        },
        style() {
            return "style"
        },
        funcConvert(item) {
            // convert to local time
            item["created_at"] = convertTimestampToLocal(item.created_at, false, true)

            // convert to html
            item.message = this.convert.toHtml(item.message)

        },
        getStartTimestamp(dt) {
            this.startTimestamp = dt.getTime()
        },
        defaultStartDateTime() {
            let now = new Date(getCurrentDateTime())
            let date = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, "0")}-${now.getDate().toString().padStart(2, "0")}`
            let time = now.toTimeString().substring(0, 5)
            return [date, time]
        },
        getDownloadFileName() {
            return `SC${this.skycar}-${this.startTimestamp}-${this.perPage}.txt`
        }
    },
    computed: {
        realTimeFilter() {
            let items = this.items
            if (this.message) {
                items = items.filter((i) => {
                    return i.message.includes(this.message)
                })
            }
            return items
        }
    },
    watch: {
        live() {
            this.closeMessage()
            this.items = []
            this.currentPage = 1
            this.totalPages = 1
        },
        perPage() {
            this.currentPage = 1
            this.totalPages = 1
        }
    }
}
</script>
<style>
   .style td {
      height: 0px !important;
      border: hidden !important
   }
</style>
