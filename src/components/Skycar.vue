<template>
  <v-app>
    <v-container fluid>
      <v-card dark>
        <v-col>
          <v-row>
            <!-- Zone dropdown  -->
            <v-select
              v-model="currentZone"
              :items="zones"
              @change="modelSkycar.skycar = Array(), syncSkycar()"
              class="ma-2"
              prepend-icon="mdi-cube"
            />

            <!-- Gateway btn -->
            <v-btn
              @click="syncGW()"
              dark
              class="ma-2"
              :color="modelGW.btnColor"
            >
              <span>{{ modelGW.btnText }}</span>
            </v-btn>
            <v-btn
              @click="btnAddSkycar"
            dark
              class="ma-2"
              color="green"
            >
              Add Skycar
            </v-btn>

            <!-- Sync btn -->
            <v-btn
              @click="syncSkycar()"
              color="green"
              dark
              class="ma-2"
              :disabled="!doneSync"
            >
              Refresh
            </v-btn>

            <!-- Setting btn -->
            <v-btn
              @click="btnSettingDialog"
              color="blue"
              dark
              class="ma-2"
            >
              <v-icon left>mdi-cog</v-icon>
              SETTING
            </v-btn>
          </v-row>
        </v-col>
        <v-progress-linear
          v-if="!doneSync"
          color="green"
          indeterminate
        />
      </v-card>

      <!-- Skycars List Datatable -->
      <v-data-table
        v-model="modelSkycar.dtSelected"
        :headers="headers"
        item-key="skycar_id"
        :items="modelSkycar.skycar"
        :items-per-page="25"
        group-by="status"
        class="elevation-1"
        dark
        sort-by="skycar_id"
      >
        <!-- cell filtering -->
        <template v-slot:[`item.skycar_id`]="{ item }">
          <span :class="getSidColor(item.mode)">{{ item.skycar_id }}</span>
        </template>

        <template v-slot:[`item.connect`]="{ item }">
          <v-chip
            :color="getColor(item.connect)"
            dark
          >
            {{ getStatus(item.connect) }}
          </v-chip>
        </template>

        <template v-slot:[`item.pair`]="{ item }">
          <v-chip
            :color="getColor(item.pair)"
            dark
          >
            {{ getStatus(item.pair) }}
          </v-chip>
        </template>

        <template v-slot:[`item.winch`]="{ item }">
          <v-chip
            v-for="[position, winch] in Object.entries(item.winch).sort()"
            :key="position"
            :color="getWinchColor(winch.is_active, winch.assign_storage_code , winch.storage_no)"
            class="ml-1 fixed-width-chip"
            dark
          >
            <v-tooltip bottom>
              <template v-slot:activator="{ on, attrs }">
                <span
                  v-bind="attrs"
                  v-on="on"
                >
                  {{ getStorage(position[0], winch.storage_no) }}
                </span>
              </template>
              <span> {{ winch.assign_storage_code }} </span>
            </v-tooltip>
          </v-chip>
        </template>

        <template v-slot:[`item.action`]="{ item }">
          <v-btn
            small
            class="mr-2"
            light
            @click="btnActionDialog(item)"
          >
            Action
          </v-btn>
          <v-btn
            small
            class="mr-2"
            light
            @click="btnEventLogDialog(item)"
          >
            Event Log
          </v-btn>
        </template>

      </v-data-table>

      <!-- MCU Skycar Shell Mode -->
      <v-toolbar 
        color="black"
        dark 
        flat
        class="mt-4"
      >
        <template v-slot:extension>
          <v-tabs
            v-model="tab"
            align-with-title
          >
            <v-tabs-slider />
            <v-tab
              v-for="tab in tabItems"
              :key="tab"
            >
              {{ tab }}
            </v-tab>
          </v-tabs>
        </template>
      </v-toolbar>
      <v-tabs-items v-model="tab">
        <!-- Shell Mode -->
        <v-tab-item>
          <v-card
            dark
            color="black"
          >
            <ShellMode
              :model-skycar="modelSkycar"
              :current-zone="currentZone"
              :parent-shell-sid="shell.normalSid"
              @updateSid="updateChatSid"
            />
          </v-card>
          <!-- Check Bin -->
          <v-dialog
            v-if="shell['boolBin']"
            v-model="shell['boolBin']"
            max-width="500"
            @keydown.enter="shell['boolBin']=false"
          >
            <v-card>
              <v-toolbar
                dark
                :color="getSubmitStatus(shell['txtBin'].status)"
              >
                <v-toolbar-title>Status: {{ shell['txtBin'].status }}</v-toolbar-title>
              </v-toolbar>
              <v-progress-linear
                v-if="shell['txtBin'].status == 'Pending'"
                indeterminate
                color="green"
              />
              <v-card-text class="pt-6">
                <span v-if="shell['txtBin'].data == 0">
                  <pre class="text-wrap">{{ shell.txtBin.reason }}</pre>
                </span>
                <span v-else>
                  <v-row
                    v-for="data in shell['txtBin'].data"
                    :key="data[0]"
                  >
                    <v-chip
                      color="green"
                      dark
                      class="ma-1"
                    >
                      <v-icon class="mx-1">mdi-cube</v-icon>
                      <pre>{{ data[0] }}</pre> 
                    </v-chip>
                    <v-chip
                      color="orange"
                      dark
                      class="ma-1"
                      width="150"
                    >
                      <v-icon class="mx-1">mdi-map-marker</v-icon>
                      <pre>{{ data[1] }}</pre> 
                    </v-chip>
                  </v-row>
                </span>
              </v-card-text>
              <v-card-actions>
                <v-spacer />
                <v-btn
                  color="green darken-1"
                  text
                  @click="shell['boolBin'] = false"
                >
                  Close
                </v-btn>
              </v-card-actions>
            </v-card>
          </v-dialog>
        </v-tab-item>
        <!-- Error Mode -->
        <v-tab-item>
          <v-toolbar
            dark 
            color="black"
          >
            <v-row>
              <v-spacer />
              <v-menu
                v-model="modelErrorSkycar.bolmenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    class="ma-1"
                    v-model="modelErrorSkycar.dtfromto"
                    label="Choose dates"
                    prepend-icon="mdi-calendar"
                    readonly
                    v-bind="attrs"
                    v-on="on"
                  >
                    {{ modelErrorSkycar.dtfromto }}
                  </v-text-field>
                </template>
                <v-date-picker
                  range
                  v-model="modelErrorSkycar.dtfromto"
                  @input="modelErrorSkycar.bolmenu = false"
                />
              </v-menu>

            
              <v-btn
                class="ma-1"
                @click="btnGetErrorSkycar()"
                color="green"
              >
                <v-icon>mdi-restart</v-icon>
                <span>Refresh</span>
              </v-btn>
              <v-btn
                class="ma-1"
                color="blue"
              >
                <download-csv 
                  :data="error.result"
                  :fields="modelErrorSkycar.exportFields"
                  :name="SkycarErrorExportFileName"
                >
                  <v-icon>mdi-download</v-icon> DOWNLOAD CSV      
                </download-csv>
              </v-btn>
              <v-btn
                class="ma-1"
                color="orange"
                @click="showImportErrorDialog = true"
              >
                <v-icon>mdi-upload</v-icon>
                <span>Import Error Code</span>
              </v-btn>
            </v-row>
          </v-toolbar>
          <v-data-table
            :headers="error['headers']"
            :items="error['result']"
            :items-per-page="15"
            group-by="zone"
            class="elevation-1"
            dark
            item-class="yellow--text text--accent-4"
          >
            <!-- Local Time -->
            <template v-slot:[`item.time`]="{ item }">
              {{ item.dt_local }}
            </template>

            <template v-slot:[`item.recovered_time`]="{ item }">
              {{ item.rt_local }}
            </template>

            <!-- Error Message -->
            <template v-slot:[`item.error_msg`]="{ item }">
              <v-tooltip bottom>
                <template v-slot:activator="{ on, attrs }">
                  <span
                    v-bind="attrs"
                    v-on="on"
                  >{{ item.error_msg }}</span>
                </template>
                <v-card
                  dark
                  min-width="500"
                >
                  <v-card-title>Error Message</v-card-title>
                  <v-simple-table 
                    fixed-header
                  >
                    <template v-slot:default>
                      <thead>
                        <tr>
                          <th class="text-left">
                            Title
                          </th>
                          <th class="text-left">
                            Data
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr
                          v-for="detail in item.msg_detail"
                          :key="detail.title"
                        >
                          <td>{{ detail.title }}</td>
                          <td>{{ detail.data }}</td>
                        </tr>
                      </tbody>
                    </template>
                  </v-simple-table>
                </v-card>
              </v-tooltip>
            </template>

            <!-- Error Name -->
            <template v-slot:[`item.error_name`]="{ item }">
              <v-tooltip bottom>
                <template v-slot:activator="{ on, attrs }">
                  <v-icon
                    v-bind="attrs"
                    v-on="on"
                    @click="btnGetErrorDetail(item.error_detail)"
                    class="mx-1"
                  >
                    mdi-information
                  </v-icon>
                </template>
                <span>Show Error Detail</span>
              </v-tooltip>
              <span>{{ item.id }}</span>
            </template>
          
            <!-- Action Button -->
            <template v-slot:[`item.error_action_remark`]="{ item }">
              <v-btn
                small
                class="mr-2"
                light
                @click="btnSkycarErrorDialog(item,'remark')"
              >
                Action
              </v-btn>
            </template>

            <!-- Warning Log Button -->
            <template v-slot:[`item.warning_log`]="{ item }">
              <v-btn
                small
                class="mr-2"
                light
                @click="btnSkycarErrorDialog(item,'warning')"
              >
                Warning Log
              </v-btn>
            </template>
          </v-data-table>

          <!-- Action For Error Skycar Detail -->
          <DialogSkycarErrorAction 
            :error="error" 
            :model-error-skycar="modelErrorSkycar" 
            :target-a-p-i="RouteSkycar.ERROR_SKYCAR"
            :target-host="getTcHost()" 
            :callable-http="AxiosHttpWithAwesomeAlert"
          />
       
      

          <!-- Action For Error Skycar Warning Logs -->
          <DialogSkycarErrorWarningLogs 
            :error="error" 
            :model-error-skycar="modelErrorSkycar"
          />
   


          <!-- Detail -->
          <v-dialog
            v-if="error['boolDetail']"
            v-model="error['boolDetail']"
            max-width="900"
            @keydown.enter="error['boolDetail']=false"
          >
            <v-card>
              <v-toolbar
                dark
                color="green"
              >
                <v-toolbar-title>Error Detail</v-toolbar-title>
              </v-toolbar>
              <v-card-text class="pt-6">
                <pre class="text-wrap">{{ error['txtDetail'].reason }}</pre>
                
                <!-- Motor ID Errors Section (if present) -->
                <div v-if="motorIds.length > 0" class="mb-4">
                  <h3 class="mb-2">Motor ID Errors</h3>
                  <v-chip-group>
                    <v-chip
                      v-for="motorId in motorIds"
                      :key="motorId"
                      color="red"
                      text-color="white"
                      class="ma-1"
                    >
                      Motor {{ motorId }}
                    </v-chip>
                  </v-chip-group>
                </div>
                
                <v-simple-table v-if="hasRegularErrors" class="mt-4">
                  <template v-slot:default>
                    <thead>
                      <tr>
                        <th class="text-left">Module</th> 
                        <th class="text-left">Error</th>  
                        <th class="text-left">Description</th> 
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="data in error['txtDetail']" :key="data.module">
                        <!-- Only display rows that have module and message properties and aren't just motor_ids entries -->
                        <template v-if="data.module && data.message && !data.motor_ids">
                          <td>{{ data.module }}</td>
                          <td>{{ data.message }}</td>
                          <td>{{ data.description }}</td>
                        </template>
                      </tr>
                    </tbody>
                  </template>
                </v-simple-table>
              </v-card-text>
              <v-card-actions>
                <v-spacer />
                <v-btn
                  color="green darken-1"
                  text
                  @click="error['boolDetail'] = false"
                >
                  Close
                </v-btn>
              </v-card-actions>
            </v-card>
          </v-dialog>
          <!-- Refresh -->
          <v-dialog
            v-if="error['boolRefresh']"
            v-model="error['boolRefresh']"
            max-width="500"
            @keydown.enter="error['boolRefresh']=false"
          >
            <v-card>
              <v-toolbar
                dark
                :color="getSubmitStatus(error['txtRefresh'].status)"
              >
                <v-toolbar-title>Status: {{ error['txtRefresh'].status }}</v-toolbar-title>
              </v-toolbar>
              <v-progress-linear
                v-if="error['txtRefresh'].status == 'Pending'"
                indeterminate
                color="green"
              />
              <v-card-text class="pt-6">
                <pre class="text-wrap">{{ error['txtRefresh'].message }}</pre>
                <v-row>
                  <v-col>
                    <span v-if="error['txtRefresh'].data.length != 0">
                      <v-row
                        v-for="data in error['txtRefresh'].data"
                        :key="data"
                      >
                        <v-chip
                          color="orange"
                          dark
                          class="ma-1"
                        >
                          <v-icon class="mx-1">mdi-message</v-icon>
                          <pre>{{ data }}</pre> 
                        </v-chip>
                      </v-row>
                    </span>
                  </v-col>
                </v-row>
              </v-card-text>
              <v-card-actions>
                <v-spacer />
                <v-btn
                  color="green darken-1"
                  text
                  @click="error['boolRefresh'] = false"
                >
                  Close
                </v-btn>
              </v-card-actions>
            </v-card>
          </v-dialog>
        </v-tab-item>
        <!-- Debug Mode -->
        <v-tab-item>
          <v-card dark>
            <v-card-text>
              <v-row>
                <!-- TC Log -->
                <v-col>
                  <v-card-title>TC Log</v-card-title>
                  <FormSkycarMessages :get-card-message="getCardMessage"/>
                </v-col>
                <v-divider vertical />
                <!-- TC ADG -->
                <v-col>
                  <v-card-title>TC Adg</v-card-title>
                  <FormAdgTracking :cube-options="zones" />
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
          <v-dialog
            v-model="debug['boolSpecific']"
            @keydown.enter="debug['boolSpecific']=false"
          >
            <v-card>
              <v-card-title>Result</v-card-title>
              <v-col class="ma-1">
                <v-data-table
                  :headers="debug['headSpecific']"
                  :items="debug['resSpecific']"
                  :items-per-page="-1"
                  :sort-by="['date']"
                  dark
                  dense
                  :item-class="getMessageColor"
                  hide-default-footer
                />
              </v-col>
              <v-row>
                <v-spacer />
                <v-btn
                  color="green darken-1"
                  text
                  @click="debug['boolSpecific']=false"
                  class="ma-2"
                >
                  Close
                </v-btn>
              </v-row>
            </v-card>
          </v-dialog>
        </v-tab-item>
        <!-- Utility Mode -->
        <v-tab-item>
          <v-toolbar
            dark 
            color="black"
          >
            <v-row>
              <v-menu
                v-model="utilityModel.bolmenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    class="ma-1"
                    v-model="utilityModel.dtfromto"
                    label="Choose dates"
                    prepend-icon="mdi-calendar"
                    readonly
                    v-bind="attrs"
                    v-on="on"
                  />
                </template>
                <v-date-picker
                  range
                  v-model="utilityModel.dtfromto"
                  @input="utilityModel.bolmenu = false"
                />
              </v-menu>

              <v-btn
                class="ma-1"
                @click="btnGetUtilityData()"
                color="green"
              >
                <v-icon>mdi-restart</v-icon>
                <span>Refresh</span>
              </v-btn>
            </v-row>
          </v-toolbar>
          <v-card dark>
            <v-row class="mx-1">
              <pre class="ml-1 headline font-weight-bold" style="text-decoration: underline;">Cube Data</pre>
              <v-spacer></v-spacer>
              <v-btn
                class="ma-1"
                color="blue"
              >
                <download-csv 
                  :data="utilitySkycar.result"
                  :fields="utilitySkycar.exportFields"
                  :name="CubeDataExportFileName"
                >
                  <v-icon>mdi-download</v-icon> DOWNLOAD CSV      
                </download-csv>
              </v-btn>
            </v-row>

            <v-data-table
              :headers="utilitySkycar['headers']"
              :items="utilitySkycar['result']"
              :items-per-page="15"
              class="elevation-1"
              dark
              item-class="yellow--text text--accent-4"
            >
              <!-- Number of Error Message -->
              <template v-slot:[`item.number_of_errors`]="{ item }">
                <v-tooltip bottom>
                  <template v-slot:activator="{ on, attrs }">
                    <span
                      v-bind="attrs"
                      v-on="on"
                    >{{ getTotalNumberOfErrors(item.number_of_errors) }}</span>
                  </template>
                  <v-card
                    dark
                    min-width="500"
                  >
                    <v-card-title>Error Skycars</v-card-title>
                    <v-simple-table 
                      fixed-header
                    >
                      <template v-slot:default>
                        <thead>
                          <tr>
                            <th class="text-left">
                              Skycar ID
                            </th>
                            <th class="text-left">
                              Number of Errors
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr
                            v-for="(value, key) in JSON.parse(item.number_of_errors)"
                            :key="key"
                          >
                            <td>{{ key }}</td>
                            <td>{{ value }}</td>
                          </tr>
                        </tbody>
                      </template>
                    </v-simple-table>
                  </v-card>
                </v-tooltip>
              </template>
            </v-data-table>
          </v-card>

          <v-card dark v-show="this.$store.state.cubeConfig.dual">
            <v-row class="mx-1 mt-2">
              <pre class="ml-1 headline font-weight-bold" style="text-decoration: underline;"
              >Dual Winch Percentage</pre>
              <v-spacer></v-spacer>
              <v-btn
                class="ma-1"
                color="blue"
              >
                <download-csv 
                  :data="utilityDualPercentage.result"
                  :fields="utilityDualPercentage.exportFields"
                  :name="DualPercentageExportFileName"
                >
                  <v-icon>mdi-download</v-icon> DOWNLOAD CSV      
                </download-csv>
              </v-btn>
            </v-row>

            <v-data-table
              :headers="utilityDualPercentage['headers']"
              :items="utilityDualPercentage['result']"
              :items-per-page="15"
              class="elevation-1"
              dark
              item-class="yellow--text text--accent-4"
            >
            </v-data-table>
          </v-card>

          <v-dialog
            v-if="utilityModel['boolRefresh']"
            v-model="utilityModel['boolRefresh']"
            max-width="500"
            @keydown.enter="utilityModel['boolRefresh']=false"
          >
            <v-card>
              <v-toolbar
                dark
                :color="getSubmitStatus(utilityModel['txtRefresh'].status)"
              >
                <v-toolbar-title>Status: {{ utilityModel['txtRefresh'].status }}</v-toolbar-title>
              </v-toolbar>
              <v-progress-linear
                v-if="utilityModel['txtRefresh'].status == 'Pending'"
                indeterminate
                color="green"
              />
              <v-card-text class="pt-6">
                <pre class="text-wrap">{{ utilityModel['txtRefresh'].message }}</pre>
              </v-card-text>
              <v-card-actions>
                <v-spacer />
                <v-btn
                  color="green darken-1"
                  text
                  @click="utilityModel['boolRefresh'] = false"
                >
                  Close
                </v-btn>
              </v-card-actions>
            </v-card>
          </v-dialog>
        </v-tab-item>
      </v-tabs-items>
      <CardSkycarMessages ref="cardSkycarMessages" />
      
      <MaintenanceShellMode
        :model-skycar="modelSkycar"
        :current-zone="currentZone"
        :parent-shell-sid="shell.advancedSid"
        @updateSid="updateChatSid"
      />

      <AdvanceShellMode
        :model-skycar="modelSkycar"
        :current-zone="currentZone"
        :parent-shell-sid="shell.advancedSid"
        @updateSid="updateChatSid"
      />

      <v-btn 
        v-show="this.shell.boolChat && showMoveButton()"
        dark
        color="deep-purple accent-4"
        :style="moveButtonStyle" 
        small
        @mousedown="startTracking"
        @mousemove="trackMovement"
        @mouseup="stopTracking"
        @mouseover="increaseSize"
        @mouseleave="reduceSize"
      >
        <v-icon>
          mdi-cursor-move
        </v-icon>
      </v-btn>
      <v-btn
        dark
        color="deep-purple accent-4"
        @click="configChatDialog"
        :style="buttonStyle"
        small
      >
        <v-icon left>
          mdi-message-text
        </v-icon> Start Chat
        <div v-if="shell.selectedSid.length > 0">
          <v-icon class="ml-1 mr-1">
            mdi-car
          </v-icon>
          <span v-if="shell.selectedSid.length > 1">(Group)</span>
          <span v-else-if="shell.selectedSid.length === 1">(SC{{ shell.selectedSid[0] }})</span>
        </div>
        <v-badge
          class="ml-2"
          v-if="shell.selectedSid.length > 0 && badgeContent" 
          :content="badgeContent" 
          color="red"
        />
      </v-btn>

      <DialogAction
        :show-notification="showNotification"
        :sync-skycar="syncSkycar"
        ref="skycarActionDialog"
      />
      <DialogCheckBin ref="checkBinDialog" />
      <SnackbarNotification ref="snackbarNotification" />
      <DialogAddSkycar
        :show-notification="showNotification"
        :sync-skycar="syncSkycar"
        ref="dialogAddSkycar"
      />
      <DialogSkycarChatbox
        :chat-bool="shell.boolChat"
        :chat-txt="shell.specificTxtChat"
        :skycar-selected="shell.selectedSid"
        :xMovements="xMovements"
        :yMovements="yMovements"
        @close-dialog="configChatDialog"
        @send-message="sendConversation"
        ref="dialogSkycarChatbox"
      />
      <DialogEventLog 
        :event-options="[Event.GENERAL, Event.STORAGE, Event.BATTERY]"
        :show-notification="showNotification" 
        ref="dialogEventLog" 
      />
      <DialogImportErrorCode
        :show.sync="showImportErrorDialog"
        :api-route="RouteError.ERROR_CODE"
        :zone="currentZone"
      />
      <DialogSkycarSettings
        :current-zone="currentZone"
        :show-notification="showNotification"
        ref="dialogSkycarSettings"
      />
    </v-container>
  </v-app>
</template>

<script>
import CardSkycarMessages from "./skycarMessage/CardSkycarMessages.vue"
import DialogAddSkycar from "./skycar/DialogAddSkycar"
import DialogSkycarSettings from "./skycar/DialogSkycarSettings"
import { mypost }  from "../helper/http_request.js"; 
import { getCube, getStatus , getHost, convertStringToLocal,
  convertTimestampToLocal, getRequestHeader, useRefreshToken,
} from "../helper/common.js";
import { Event, Module, RouteSkycar, RouteAnalysis, SkycarRecovery, SkycarShellCommand, Websocket, RouteError, 
} from "../helper/enums.js"
import { AxiosHttpWithAwesomeAlert } from "../helper/http_request.js";
import DialogSkycarErrorAction from "./dialogs/DialogSkycarErrorAction.vue"
import DialogSkycarErrorWarningLogs from "./dialogs/DialogSkycarErrorWarningLogs.vue"
import DialogAction from "./skycarAction/ActionDialog.vue"
import DialogCheckBin from "./skycarAction/CheckBin.vue"
import FormAdgTracking from "./skycarAdg/FormAdgTracking.vue";
import FormSkycarMessages from "./skycarMessage/FormSkycarMessages.vue";
import SnackbarNotification from "./shared/SnackbarNotification.vue"
import ShellMode from "./skycarShellMode/SkycarShellMode.vue"
import AdvanceShellMode from "./skycarShellMode/SkycarAdvanceShellMode.vue"
import MaintenanceShellMode from "./skycarShellMode/SkycarMaintenanceShellMode.vue"
import DialogSkycarChatbox from "./dialogs/DialogSkycarChatbox.vue"
import DialogEventLog from "./dialogs/DialogEventLog.vue";
import DialogImportErrorCode from "./dialogs/DialogImportErrorCode.vue"


let httpRequest = require("../helper/http_request");
import axios from "axios";
import { socket } from "../App.vue"

export default {
  name: "App",
  components: {
    CardSkycarMessages,
    DialogSkycarErrorAction,
    DialogSkycarErrorWarningLogs, 
    DialogAction,
    DialogCheckBin,
    FormAdgTracking,
    FormSkycarMessages,
    SnackbarNotification,
    DialogAddSkycar,
    DialogSkycarSettings,
    ShellMode,
    AdvanceShellMode,
    MaintenanceShellMode,
    DialogSkycarChatbox,
    DialogEventLog,
    DialogImportErrorCode
  },
  created() {
    this.syncSkycar()
    this.getMessage(socket)
    this.syncGW()
    this.updateChatMessage()
  },
  mounted() {
    window.addEventListener("beforeunload", this.handleBeforeUnload);
    if (this.shell.selectedSid.length > 0){
      this.startIdleTimer();
      window.addEventListener("mousemove", this.resetIdleTimer);
      window.addEventListener("keypress", this.resetIdleTimer);
    }

    window.addEventListener("resize", this.handleWindowResize);
    this.updateStyle();
  },
  async beforeDestroy() {
    window.removeEventListener("beforeunload", this.handleBeforeUnload);
    if (this.shell.selectedSid.length > 0) {
      await this.updateChatSid(null, "Normal")
      await this.updateChatSid(Array(), "Advanced")
    }
    this.resetChosenSkycar()
    this.toggleIdleTimer();
    window.removeEventListener("resize", this.handleWindowResize);
    if (socket) {
      socket.off(Websocket.SKYCAR);
      socket.off(Websocket.UPDATE_SKYCAR);
    }
  },
  data: () => ({
    Event,
    RouteSkycar,
    RouteAnalysis,
    RouteError,
    convertStringToLocal,
    convertTimestampToLocal,
    SkycarRecovery,
    MaintenanceAction,
    idleTime: 0,
    idleTimeoutMinute: 5,
    timerStarted: false,
    minScreenWidth: 780,
    buttonStyle: null,
    moveButtonStyle: null,
    dragging: null,
    clickX: null,
    clickY: null,
    xMovements: 0,
    yMovements: 0,

    // Skycar Utility Model
    utilityModel: {
      dtfromto : [
        new Date().toISOString().slice(0,10),
        new Date().toISOString().slice(0,10)
      ],
      boolRefresh: false,
      txtRefresh: null,
    },
    utilitySkycar: {
      headers: [
        { text: "Date", value: "date" },
        { text: "Total Skycar ID", value: "total_skycar_ids" },
        { text: "Enroll Skycar ID", value: "enroll_skycar_ids" },
        { text: "Disenroll Skycar ID", value: "disenroll_skycar_ids" },
        { text: "Available Skycar ID", value: "available_skycar_ids" },
        { text: "Working Skycar ID", value: "working_skycar_ids" },
        { text: "Maintenance Skycar ID", value: "maintenance_skycar_ids" },
        { text: "Error Skycar ID", value: "error_skycar_ids" },
        { text: "Total Number of Errors", value: "number_of_errors" },
      ],
      exportFields : [
        "date","total_skycar_ids","total_skycar_quantity","enroll_skycar_ids","enroll_skycar_quantity",
        "disenroll_skycar_ids","disenroll_skycar_quantity","available_skycar_ids", "available_skycar_quantity", 
        "working_skycar_ids", "working_skycar_quantity", "maintenance_skycar_ids", "maintenance_skycar_quantity", 
        "error_skycar_ids", "error_skycar_quantity", "number_of_errors"
      ],
      result: Array(),
    },
    utilityDualPercentage: {
      headers: [
        { text: "Date", value: "date" },
        { text: "Working Skycars", value: "all_working_skycars" },
        { text: "Same Pickup & Dropoff Coordinate", value: "both_same_coord" },
        { text: "Same Pickup but Diff Dropoff Coordinate", value: "pick_same_coord" },
        { text: "Diff Pickup but Same Dropoff Coordinate", value: "drop_same_coord" },
        { text: "Diff Pickup & Dropoff Coordinate", value: "both_diff_coord" },
        { text: "Total Utilization", value: "total_percentage" },
      ],
      exportFields : [
        "date","all_working_skycars","both_same_coord","pick_same_coord",
        "drop_same_coord","both_diff_coord","total_percentage"
      ],
      result: Array(),
    },

    // Skycar Error V-Model
    modelErrorSkycar:{

      // Skycar Error Excel
      dtfromto : [
        new Date().toISOString().slice(0,10),
        new Date().toISOString().slice(0,10)
      ],
      exportFields : ["time","dt_local","zone","sid","error_msg","error_name","error_detail","remark", "app_module"],

      // Skycar Error Action Dialog
      selectedSkycarError : {},
    },
    // Skycar Error V-Model
    error: {
      headers: [
        { text: "Time", value: "time" },
        { text: "Zone", value: "zone" },
        { text: "Skycar ID", value: "sid" },
        { text: "Error Message", value: "error_msg" },
        { text: "Maintenance Dock ID", value: "md_id" },
        { text: "App Module", value: "app_module" },
        { text: "App Error", value: "error_name" },
        { text: "Remark", value: "error_action_remark" },
        { text: "Warning", value: "warning_log" },
        { text: "Recovered Time", value: "recovered_time" },
      ],
      result: Array(),
      boolRefresh: false,
      txtRefresh: null,
      boolDetail: false,
      txtDetail: null,
      boolDialogAction : false,
      boolDialogWarningLog : false,
    },
    shell: {
      boolBin: false,
      txtBin: null,
      socket: null,
      boolChat: false,
      txtChat: Array(),
      specificTxtChat: Array(),
      normalSid: null,
      advancedSid: Array(),
      notification: Array(),
      selectedSid: Array()
    },
    debug: {
      boolSpecific: false,
      resSpecific: Array(),
      headSpecific: [
        { text: "Datetime", value: "date" },
        { text: "Message", value: "msg" },
      ]
    },
    modelGW:{
      clients : null,
      allClients : null,
      btnColor : "red",
      btnText: "GW Disconnected"
    },
    modelSkycar: {
      skycar: [],
      dtSelected: [],
      alert: null,
    },
    headers: [
      {
        text: "Skycar ID ",
        align: "left",
        sortable: true,
        value: "skycar_id",
      },
      { text: "Status", value: "status" },
      { text: "Connect", value: "connect" },
      { text: "Pair", value: "pair" },
      { text: "Coordinate", value: "coordinate" },
      { text: "Winch", value: "winch" },
      { text: "Battery %", value: "battery" },
      // { text: "Mode", value: "mode"},
      { text: "Mode", value: "maintenance_mode" },
      { text: "Action", value: "action" },
    ],
    UpdateStorage : null ,
    RecoverSkycar : null ,
    tabItems: ["Shell Mode", "Error Mode", "Debug Mode", "Utility Mode"],
    tab: null,
    zones: getCube(),
    currentZone: getCube()[0],
    doneSync: true,
    btnGetErrorDetail: function(detail) {
      this.error["boolDetail"] = true
      this.error["txtDetail"] = detail
    },
    btnGetErrorSkycar: async function() {
      this.error["boolRefresh"] = true
      this.error["txtRefresh"] = { status: "Pending", message: "Refreshing...", data: [] }

      let daterange = this.modelErrorSkycar.dtfromto

      let df = daterange[0] 
      let dt = daterange[1] ?? daterange[0] 

      var d1 = Date.parse(daterange[0]);
      var d2 = Date.parse(daterange[1]);
      if (d1 > d2) {
           df = daterange[1]
           dt = daterange[0]
      }
      let res = await getAxiosWithParams(RouteSkycar.ERROR_SKYCAR , this.currentZone , { dateFrom:df , dateTo:dt })
      this.error["result"] =  res.data.data ?? []
      this.error["txtRefresh"] = { status: res.status, message: res.data.reason, data: [] }
      
      
    },
    btnActionDialog: async function(item) {
      // await this.syncSkycar()
      let coord = item.coordinate.split(",")
      this.$refs.skycarActionDialog.openDialog(
        item.skycar_id,
        item.status,
        parseInt(coord[0]),
        parseInt(coord[1]),
        item.winch,
        item.is_docked,
        this.currentZone,
        this.modelSkycar.skycar,
        item.battery,
        item.mode
      )
    },
    btnEventLogDialog: function(item) {
      this.$refs.dialogEventLog.openDialog(`${Module.SC}-${item.skycar_id}`)
    },
    btnSkycarErrorDialog : async function(item,typeOfDialog){
      // Skycar Error Datatable onClick Handler
      if (typeOfDialog == "warning"){
        this.error.boolDialogWarningLog = true
        this.modelErrorSkycar.selectedSkycarError = item
      
      } else if (typeOfDialog == "remark"){
        this.modelErrorSkycar.selectedSkycarError = item
        this.error.boolDialogAction = true
    }
    },
    showImportErrorDialog: false,
  }),
  
  watch: {
    onChangedErrorLogData: function (newVal) {
      // console.log(newVal, oldVal)
      this.updateTimeToLocal(newVal);
    },
    catchMessage(newValue) {
      var here = this
      this.modelSkycar.skycar.map((skycar) => skycar.skycar_id).forEach(function(sid) {
        // Add notification for Skycar Shell Command Messages
        var head = `SC,${sid},Q,`
        if (newValue.message.includes(head)) {
          if (!here.shell.boolChat) {
            const currentCount = here.shell.notification[sid] || 0;
            const newCount = currentCount < 9 ? currentCount + 1 : "9+";
            here.$set(here.shell.notification, sid, newCount);
          } 

          if (here.shell.txtChat[sid]){
            here.shell.txtChat[sid].push(newValue)
          } else {
            here.shell.txtChat[sid] = [newValue];
          }
          
          here.updateChatTxt()
          // if (!here.shell.boolChat && newValue.user === here.$store.state.user.username){
          //   here.configChatDialog()
          // }
        }  
      })

      // Add notification for User joining skycar chat
      if (newValue.status === null && newValue.from_tc === null){
        if (!here.shell.boolChat) {
          const currentCount = here.shell.notification[newValue.sid] || 0;
          const newCount = currentCount < 9 ? currentCount + 1 : "9+";
          here.$set(here.shell.notification, newValue.sid, newCount);
        } 

        if (this.shell.txtChat[newValue.sid]){
          this.shell.txtChat[newValue.sid].push(newValue)
        } else {
          this.shell.txtChat[newValue.sid] = [newValue];
        }

        this.updateChatTxt()
      }
    },
    "shell.boolChat": function() {
      this.updateStyle()
    }
  },
  
  methods: {
    getStorage(position, storageNo) {
      return `${PositionMapping[position]} ${ storageNo ? storageNo : "EMPTY" }`
    },
    getTotalNumberOfErrors(item){
      if (item){
        return Object.values(JSON.parse(item)).reduce((accumulator, currentValue) => accumulator + currentValue, 0);
      } else {
        return null
      }
    },
    async btnGetUtilityData(){
      this.utilityModel["boolRefresh"] = true
      this.utilityModel["txtRefresh"] = { status: "Pending", message: "Refreshing..." }

      let daterange = this.utilityModel.dtfromto

      let df = daterange[0] 
      let dt = daterange[1] ?? daterange[0] 

      var d1 = Date.parse(daterange[0]);
      var d2 = Date.parse(daterange[1]);
      if (d1 > d2) {
           df = daterange[1]
           dt = daterange[0]
      }

      let cubeDataRes = await httpRequest.axiosRequest(
        "get", 
        getHost(this.currentZone), 
        RouteAnalysis.CUBE_DATA, 
        null,
        false,
        `start_date=${df}&end_date=${dt}`
      )
      this.utilitySkycar["result"] = cubeDataRes.data.data ?? []

      for (const item of this.utilitySkycar["result"]){
        if (item["number_of_errors"]){
          item["number_of_errors"] = JSON.stringify(item.number_of_errors)
        }
      }

      let dualWinchPercentangeRes

      if (this.$store.state.cubeConfig.dual) {
        dualWinchPercentangeRes = await httpRequest.axiosRequest(
          "get", 
          getHost(this.currentZone), 
          RouteAnalysis.DUAL_WINCH_PERCENTAGE, 
          null,
          false,
          `start_date=${df}&end_date=${dt}`
        )
        this.utilityDualPercentage["result"] = dualWinchPercentangeRes.data.data ?? []
      }
      
      if (cubeDataRes.status !== 200){
        this.utilityModel["txtRefresh"] = { status: "Rejected", message: cubeDataRes.data.message }
      } else if(this.$store.state.cubeConfig.dual && dualWinchPercentangeRes.status != 200){
        this.utilityModel["txtRefresh"] = { 
          status: "Rejected", 
          message: dualWinchPercentangeRes.data.message 
        }
      } else {
        this.utilityModel["txtRefresh"] = { status: "Accepted", message: "All Data Returned Successfully" }
      }
    },
    getCardMessage() {
      return this.$refs.cardSkycarMessages
    },
    async handleBeforeUnload() {
      if (this.shell.selectedSid.length > 0) {
        await this.updateChatSid(null, "Normal")
        await this.updateChatSid(Array(), "Advanced")
      }
    },
    updateStyle() {
      const viewportWidth = Math.max(document.documentElement.clientWidth, window.innerWidth || 0);
      const viewportHeight = Math.max(document.documentElement.clientHeight, window.innerHeight || 0);

      this.buttonStyle = {
        position: "fixed",
        right: this.shell.boolChat 
          ? (window.innerWidth <= this.minScreenWidth 
            ? "0px" 
            : this.dragging 
              ? `${(0.1 * viewportWidth) + this.xMovements}px` 
              : `${(0.05 * viewportWidth) + this.xMovements}px`) 
          : "0px",
        width: window.innerWidth <= this.minScreenWidth 
          ? "100%"
          : (this.shell.boolChat 
            ? this.dragging 
              ? "40%" 
              : "45%" 
            : "25%"),
        bottom: this.shell.boolChat ? `${(0.4 * viewportHeight) + this.yMovements}px` : "0px",
        height: "4%",
        "z-index": "1",
        "font-size": "14px"
      };

      this.moveButtonStyle = {
        position: "fixed",
        right: `${(0 * viewportWidth) + this.xMovements}px`,
        width: this.dragging ? "10%" : "5%",
        height: this.dragging ? "10%" : "4%",
        bottom: `${(0.4 * viewportHeight) + this.yMovements}px`,
        "z-index": "1",
        cursor: this.dragging ? "grabbing" : "grab",
      };
    },
    increaseSize(){
      const viewportWidth = Math.max(document.documentElement.clientWidth, window.innerWidth || 0);

      this.buttonStyle.width = "40%"
      this.buttonStyle.right = `${(0.1 * viewportWidth) + this.xMovements}px`

      this.moveButtonStyle.width = "10%"
      this.moveButtonStyle.height = "10%"
    },
    reduceSize(){
      const viewportWidth = Math.max(document.documentElement.clientWidth, window.innerWidth || 0);

      this.buttonStyle.width = "45%"
      this.buttonStyle.right = `${(0.05 * viewportWidth) + this.xMovements}px`

      this.moveButtonStyle.width = "5%"
      this.moveButtonStyle.height = "4%"

      this.stopTracking()
    },
    startTracking(event) {
      this.dragging = true;
      this.clickX = event.clientX;
      this.clickY = event.clientY;

      this.moveButtonStyle.cursor = "grabbing";
    },

    trackMovement(event) {
      if (this.dragging) {
        const offsetX = event.clientX - this.clickX;
        const offsetY = event.clientY - this.clickY;

        this.xMovements = Math.max(0, this.xMovements - offsetX);
        this.yMovements = Math.max(0, this.yMovements - offsetY);
        
        this.clickX = event.clientX;
        this.clickY = event.clientY;

        this.updateStyle()
        this.$refs.dialogSkycarChatbox.updateStyle()
      }
    },
    stopTracking() {
      this.dragging = false;
      this.moveButtonStyle.cursor = "grab";
    },
    handleWindowResize() {
      this.updateStyle()
    },
    showMoveButton(){
      if (window.innerWidth > this.minScreenWidth){
        return true
      }
      this.xMovements = 0
      this.yMovements = 0
      return false
    },
    async userChatLogs(sid, enter){
      var userMessage = null;
      if (enter) {
        userMessage = {
          message: "Entered skycar " + String(sid),
          sid: sid,
        };
      } else {
        userMessage = {
          message: "Left skycar " + String(sid),
          sid: sid,
        };
      }

      await this.sendConversation(userMessage)
    },
    async sendConversation(newValue){
      if (typeof newValue === "string"){
        var here = this
        this.shell.selectedSid.forEach(async function(sid) {
          const finalMessage = {
            "message": newValue,
            "sid": sid
          }
          await here.saveCommand("post", finalMessage);
        })
      } else {
        await this.saveCommand("post", newValue);
      }
    },
    async saveCommand(method, item){
      var host = new URL(getHost(this.currentZone) + SkycarShellCommand.SAVE_COMMAND)
      if (method === "post") {
        let res = await mypost(host, item)
        if (res.code !== 200 && res.code !== 401){
          this.showNotification(false, res.message)
        }
      } else{
        return await myget(SkycarShellCommand.SAVE_COMMAND, this.currentZone)
      }
    },
    
    async updateChatSid(newSid, method, oldSid){
      var here = this
      if (method == "Normal"){
        here.shell.normalSid = newSid
        if (newSid === null){
          // remove from the advance shell list
          if (here.shell.advancedSid.includes(oldSid)){
            const index = here.shell.advancedSid.indexOf(oldSid);
            here.shell.advancedSid.splice(index, 1);
          }
        }else{
          // add to the advance shell list
          if (!here.shell.advancedSid.includes(newSid))
            here.shell.advancedSid.push(newSid)
      }
    } else {
        here.shell.advancedSid = newSid
      }

      const uniqueValues = new Set(here.shell.advancedSid);
      if (here.shell.normalSid){
        uniqueValues.add(here.shell.normalSid);
      }
      var additional =  Array.from(uniqueValues).filter(item => !here.shell.selectedSid.includes(item))
      var removed =  here.shell.selectedSid.filter(item => !Array.from(uniqueValues).includes(item))

      additional.forEach(async function(sid) {
        await here.userChatLogs(sid, true)
      })

      removed.forEach(async function(sid) {
        await here.userChatLogs(sid, false)
      })

      here.shell.selectedSid = Array.from(uniqueValues);
      here.toggleIdleTimer()
      here.shell.boolChat = false
    },
    updateChatMessage: async function(){
      var here = this
      var res = await here.saveCommand("get")
      if (res.code === 200){
        here.shell.txtChat = res.data
      } else {
        this.showNotification(false, res.message)
        this.$awn.alert("Failed to receive the historical message")
      }
    },
    configChatDialog(){
      if (this.shell.boolChat){
        this.shell.boolChat = false
      } else {
        if (this.shell.selectedSid.length > 1){
          this.shell.boolChat=true
        }
        else if (this.shell.selectedSid.length == 1){
          this.shell.boolChat=true
        } else {
          this.$awn.alert("Please select a skycar to enter to specific chat group");
        }

        
        var here = this
        this.shell.selectedSid.forEach(function(sid) {
          here.$set(here.shell.notification, sid, 0);
        })
        this.updateChatTxt()
      }
    },
    updateChatTxt(){
      this.shell.specificTxtChat = Array()
      var here = this
      this.shell.selectedSid.forEach(function(sid) {
        here.shell.specificTxtChat.push(...here.shell.txtChat[sid])
      })
      this.shell.specificTxtChat.sort((a, b) => a.time.localeCompare(b.time));
    },
    toggleIdleTimer() {
      if (this.shell.selectedSid.length > 0) {
        if (!this.timerStarted){
          this.startIdleTimer();
          window.addEventListener("mousemove", this.resetIdleTimer);
          window.addEventListener("keypress", this.resetIdleTimer);
          this.timerStarted = true
        }
      } else {
        if (this.timerStarted){
          clearInterval(this.idleInterval);
          window.removeEventListener("mousemove", this.resetIdleTimer);
          window.removeEventListener("keypress", this.resetIdleTimer);
          this.resetIdleTimer()
          this.timerStarted = false
        }
      }
    },
    startIdleTimer(){
      this.idleInterval = setInterval(async () => {
        this.idleTime += 1;
        if (this.idleTime > this.idleTimeoutMinute * 60) {
          await this.updateChatSid(null, "Normal")
          await this.updateChatSid(Array(), "Advanced")
          this.resetChosenSkycar()
          this.toggleIdleTimer()
          this.shell.boolChat = false
        }
      }, 1000);
    },
    resetChosenSkycar(){
      this.shell.normalSid = null
      this.shell.advancedSid = Array()
      this.shell.selectedSid = Array()
    },
    resetIdleTimer() {
      this.idleTime = 0;
    },
    btnAddSkycar() {
      this.$refs.dialogAddSkycar.openDialog(this.currentZone)
    },
    showNotification(success, message) {
      this.$refs.snackbarNotification.showNotification(success, message)
    },
    updateTimeToLocal(errors){
      errors.forEach(error => {
        error["dt_local"] = this.convertStringToLocal(error.time, true);
        error["rt_local"] = error.recovered_time ? this.convertStringToLocal(error.recovered_time, true) : null;
      });
    },
    getMessage(socket) {
      var here = this
      socket.on(Websocket.SKYCAR, function(item) {
        here.shell.socket = item.item
      })

      socket.on(Websocket.UPDATE_SKYCAR, function(data) {
        console.log('Received WebSocket data:', data);

        if (data.item) {
          // Check if specific skycar_id is provided for targeted update
          if (data.item.skycar_id || data.item.sid) {
            const skycar_id = data.item.skycar_id || data.item.sid;
            console.log(`Updating specific skycar: ${skycar_id}`);
            here.updateSpecificSkycar(data.item);
          } else {
            // Fall back to full sync if no specific ID
            console.log('No skycar_id found, performing full sync');
            here.doneSync = false;
            here.syncSkycar();
          }
        } else {
          console.error('Unexpected data format:', data);
          // Fallback to full sync for unexpected data
          here.doneSync = false;
          here.syncSkycar();
        }
      });

    },
    async sendMessage(item, repeat=1, delay=0) {
      for (let i = 0; i < repeat; i++) {
        this.sendMessageWithHttp(item)
        await new Promise(resolve => setTimeout(resolve, delay * 1000))
      }
    },
    async sendMessageWithHttp(item) {
      if (item.type == "sync") {
          let res = await myget("/api/v2/skycar/presenter", this.currentZone)
          console.log(res)
          if (Array.isArray(res.data)) {
            this.modelSkycar.skycar = res.data
          } else {
            this.modelSkycar.skycar = Array()
          }
          this.doneSync = true
        }
    },
    syncSkycar() {
      this.sendMessage({ type: "sync" })
      this.doneSync = false
    },
    async updateSpecificSkycar(skycarObject) {
        console.log(skycarObject)
        const skycar_id = skycarObject.skycar_id || skycarObject.sid;
        // Search for the skycar in the array by skycar_id/sid
        const skycarIndex = this.modelSkycar.skycar.findIndex(skycar => 
          skycar.sid === skycar_id || skycar.skycar_id === skycar_id
        );
        
        if (skycarIndex !== -1) {
          // Replace the existing skycar with new data
          this.$set(this.modelSkycar.skycar, skycarIndex, skycarObject);       
        } else {
          // Optionally add it to the array if not found
          this.modelSkycar.skycar.push(skycarObject);
        }
      },
    async syncGW(){
      let res = await myget("/runtime/tcp", this.currentZone)
      if (res["data"] === undefined){return}

      this.modelGW.allClients = res.data 
      const filteredResult = res.data.find((e) => e.is_gateway == true);

      if (filteredResult) {
        this.modelGW.btnColor = "green"
        this.modelGW.btnText = "GW Connected"
        this.modelGW.clients = filteredResult
      }else{
        this.modelGW.btnColor = "red"
        this.modelGW.btnText = "GW Disconnected"
        this.modelGW.clients = null
    
    }
    },
    getMessageColor(item) {
      if (item.color) {
        switch (item.color) {
          case "red":
            return "red--text text--accent-4"
          case "yellow":
              return "yellow--text text--accent-4"
          case "green":
              return "green--text text--accent-3"
        }
      } else if (item.msg_sent!=null) {
        switch (item.msg_sent) {
          case true:
            return "green--text text--accent-3"
          case false:
            return "yellow--text text--accent-4"
        }
      } else if (item.is_completed!=null) {
        switch (item.is_completed) {
          case true:
            return "green--text text--accent-3"
          case false:
            return "yellow--text text--accent-4"
        }
      }
    },
    getSubmitStatus(status) {
      if (status == "Accepted") {
        return "green"
      } else if (status == "Rejected" || status == "Warning") {
        return "red"
      } else if (status == "Pending") {
        return "black"
      }
    },
    getColor(boolean) {
      if (boolean === true) {
        return "green";
      } else {
        return "red";
      }
    },
    getWinchColor(isActive , assignStorageCode, storageCode){
    // assign storage code = Macaroni and Cheese #F2BB66
    // with storage = green
    // inactive = red
      return isActive === false ? "red" : (isActive && assignStorageCode && !storageCode) ? "#F2BB66" : "green";
    },
    getStatus(bool) {
      return getStatus(bool)[1];
    },
    getSidColor(mode) {
      if (mode === "Manual") {
        return "yellow--text";
      } else if (mode === "Error") {
        return "grey--text";
      } else {
        return ""
      }
    },
    AxiosHttpWithAwesomeAlert,
          getTcHost(){
        return this.tcHost;
      },
      btnSettingDialog() {
        this.$refs.dialogSkycarSettings.openDialog();
      }
  },
  computed: {
    DualPercentageExportFileName(){
      let from = this.utilityModel.dtfromto[0]
      let to = this.utilityModel.dtfromto[1] ?? this.utilityModel.dtfromto[0]
      return `dual_percentage_${from}_${to}.csv` 
    },
    CubeDataExportFileName(){
      let from = this.utilityModel.dtfromto[0]
      let to = this.utilityModel.dtfromto[1] ?? this.utilityModel.dtfromto[0]
      return `cube_data_${from}_${to}.csv` 
    },
    SkycarErrorExportFileName(){
      let from = this.modelErrorSkycar.dtfromto[0]
      let to = this.modelErrorSkycar.dtfromto[1] ?? this.modelErrorSkycar.dtfromto[0]
      return `skycar_error_${from}_${to}.csv` 
    },
    tcHost: function () {
      return getHost(this.currentZone)
    },
    catchMessage() {
      return this.shell.socket
    },
    onChangedErrorLogData: function () {
      return this.error["result"];
    },
    badgeContent() {
      let badgeNotify = this.shell.selectedSid.reduce((total, value) => {
        const notificationValue = this.shell.notification[value];
        if (typeof notificationValue === "string") {
          return total + 10;
        } else {
          return total + notificationValue;
        } 
      }, 0);
      if (badgeNotify > 9){
        badgeNotify = "9+"
      }
      return badgeNotify
    },
    motorIds() {
      if (!this.error['txtDetail'] || !Array.isArray(this.error['txtDetail'])) {
        return [];
      }
      
      // Find the entry with motor_ids
      const motorIdsEntry = this.error['txtDetail'].find(item => item.motor_ids);
      
      if (!motorIdsEntry || !motorIdsEntry.motor_ids) {
        return [];
      }
      
      // Convert motor_ids to string, then split each digit into separate items
      const motorIdsString = motorIdsEntry.motor_ids.toString();
      return motorIdsString.split('');
    },
    hasRegularErrors() {
      return Array.isArray(this.error['txtDetail']) && 
             this.error['txtDetail'].some(item => item.module && item.message);
    }
  }
};

async function myget(route, zone) {
  var host = new URL(getHost(zone) + route)
  var requestOptions = {
    method: "GET",
    headers: getRequestHeader()
  }
  try {
    const response = await fetch(host, requestOptions)
    let res = JSON.parse(await response.text())
    if (res.code == 401) {
      return useRefreshToken(this, myget, route, zone)
    }
    return res
  } catch (error) {
    let msg = { status: "Rejected", reason: "Remote end point " + host + " not accessible." }
    return msg;
  }
}




async function getAxiosWithParams(route,url,...args){
  /**
   * Filter skycar error with date range .
   * @param  {String} route  An endpoint route
   * @param  {String} zone   Host url
   * @param  {Array of Object } ...args Any number of object argument with spread operator
   * @return {Object} object response
   */

  var host = new URL(getHost(url) + route)
  const queryParams = args[0]
  try {
    let params = new URLSearchParams(queryParams);
    const res = await axios.get(`${host}?${params}`, { headers: getRequestHeader() })
    return res

  } catch (error) {
    if (error.response.status == 401) {
      return useRefreshToken(this, getAxiosWithParams, route, url, ...args);
    }
    let msg = { status: "Rejected", reason: "Remote end point " + host + " not accessible." }
    return msg;
  }

}
  
const MaintenanceAction = {
  E: "ENROLL",
  S: "SWAP STORAGE"
}

const PositionMapping = {
  L: "A",
  R: "B",
  B: ""
}
</script>

<style scoped>
.fixed-width-chip {
  width: 120px
}
</style>
