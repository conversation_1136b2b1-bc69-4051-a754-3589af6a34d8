<template>
  <div>
    <v-container class="mt-5">
      <v-row>
        <v-col v-for="obj in specialStack" :key="obj.id">
          <v-layout :class="stackBoxTextClass">
            <v-card
              :height="stackBoxHeight"
              :width="stackBoxWidth"
              :min-width="stackBoxWidth"
              :class="stackBoxTextClass"
              :style="{ background: obj.color }"
              >{{ obj.inBoxText }}
            </v-card>
            <v-card-text>{{ obj.label }}</v-card-text>
          </v-layout>
        </v-col>
      </v-row>
      <v-row align="center">
        <v-col>Storage Count</v-col>
        <v-col v-if="Object.keys(stackColor).length === 0" md="10"
          ><v-skeleton-loader type="card-heading"></v-skeleton-loader
        ></v-col>
        <v-col v-else v-for="(color, count) in stackColor" :key="count">
          <v-card
            :height="stackBoxHeight"
            :width="stackBoxWidth"
            :class="stackBoxTextClass"
            :style="{ background: color }"
          >
            {{ count }}
          </v-card>
        </v-col>
      </v-row>
      <v-row align="center">
        <v-col>Origin Position</v-col>
        <v-col v-for="opt in originPositionOptions" :key="opt">
          <v-btn
            :color="originPosition === opt ? 'green' : ''"
            @click="originPosition = opt"
          >
            {{ opt }}
          </v-btn>
        </v-col>
      </v-row>
      <v-skeleton-loader
        v-if="Object.keys(stackColor).length === 0"
        type="card-heading, image"
      ></v-skeleton-loader>
      <v-row v-else>
        <div
          v-for="zoneGroup in localZoneGroups"
          :key="zoneGroup.id"
          class="ml-2 mt-6"
        >
          <Stack
            :zoneGroupName="zoneGroup.name"
            :fromX="zoneGroup.fromX"
            :toX="zoneGroup.toX"
            :fromY="zoneGroup.fromY"
            :toY="zoneGroup.toY"
            :fromZ="zoneGroup.fromZ"
            :toZ="zoneGroup.toZ"
            :originPosition="originPosition"
            :flipYAxis="flipYAxis"
            :stackBoxHeight="stackBoxHeight"
            :stackBoxWidth="stackBoxWidth"
            :stackBoxTextClass="stackBoxTextClass"
            :stackColor="stackColor"
            :obstacleColor="obstacleColor"
            :pickRestrictColor="pickRestrictColor"
            :dropRestrictColor="dropRestrictColor"
            :gatewayInColor="gatewayInColor"
            :gatewayOutColor="gatewayOutColor"
            :gatewayColor="gatewayColor"
            :storageMovements="storageMovements"
          ></Stack>
        </div>
      </v-row>

      <div style="position:fixed; top:10%; right:3%; z-index:999;">
        <v-expansion-panels v-model="storageMovementPanel">
          <v-expansion-panel>
            <v-expansion-panel-header>
              Storage Movement
            </v-expansion-panel-header>
            <v-divider></v-divider>
            <v-expansion-panel-content>
              <v-row dense>
                <v-col cols="9">
                  <v-text-field
                    class="mt-3 mb-n3"
                    dense
                    type="number"
                    label="Storage Code"
                    v-model.number="storageCode"
                    outlined
                    clearable
                    @click:clear="onClearStorageCode()"
                    @keyup.enter="getStorageMovement()"
                  >
                  </v-text-field>
                </v-col>
                <v-col cols="3" class="d-flex justify-center">
                  <v-btn
                    medium
                    @click="getStorageMovement()"
                    light
                    class="mt-3"
                  >
                    <v-icon>mdi-send-circle-outline</v-icon>
                  </v-btn>
                </v-col>
              </v-row>
              <v-divider></v-divider>
              <v-expand-transition>
                <v-card-text v-show="storageMovements.length > 0">
                  <CodeBlock>
                    <span
                      v-for="(value, idx) in storageMovements"
                      :key="idx"
                      :style="{ color: idx % 2 === 0 ? '#000000' : '#0044CC' }"
                      >{{ value.info }}</span
                    >
                  </CodeBlock>
                </v-card-text>
              </v-expand-transition>
            </v-expansion-panel-content>
          </v-expansion-panel>
        </v-expansion-panels>
      </div>
    </v-container>
  </div>
</template>

<script>
import CodeBlock from "@/dashboard/model/CodeBlock.vue";
import Stack from "../components/stack/Stack.vue";
import { mapActions, mapState } from "vuex";
import { SmOperationAPI } from "../api/sm-operation";

export default {
  name: "StackOverview",

  components: { Stack, CodeBlock },

  data() {
    return {
      localZoneGroups: [],

      stackBoxHeight: 30,
      stackBoxWidth: 30,
      stackBoxTextClass:
        "text--secondary text-caption d-flex align-center justify-center",

      stackColor: {},
      obstacleColor: "#FF8A80",
      pickRestrictColor: "#18FFFF",
      dropRestrictColor: "#FFF176",
      gatewayInColor: "#9FA8DA",
      gatewayOutColor: "#EA80FC",
      gatewayColor: "linear-gradient(135deg, #9FA8DA,#EA80FC)",

      originPosition:
        process.env.VUE_APP_STACK_OVERVIEW_DEFAULT_ORIGIN_POSITION ??
        "BOTTOM LEFT",
      originPositionOptions: [
        "TOP LEFT",
        "BOTTOM LEFT",
        "TOP RIGHT",
        "BOTTOM RIGHT",
      ],
      flipYAxis: process.env.VUE_APP_STACK_OVERVIEW_FLIP_Y_AXIS === "true",

      storageMovementPanel: null,
      storageCode: null,
      storageMovements: [],
    };
  },

  methods: {
    async init() {
      try {
        await this.getZoneGroups();
        this.generateStackColor();
        this.localZoneGroups = this.zoneGroups;
      } catch (e) {
        alert(`Overview Page Get Zone Exception - ${e.message}`);
      }
    },

    generateStackColor() {
      let max = 0;
      for (const zoneGroup of this.zoneGroups) {
        const qty = zoneGroup.toZ - zoneGroup.fromZ + 1;
        max = max > qty ? max : qty;
      }
      let stackColor = {};
      for (let i = 0; i < max; i++) {
        const perc = i / (max - 1);
        stackColor[i] = `hsl(${180 - perc * 50}, 100%, ${100 - perc * 70}%)`;
      }
      this.stackColor = stackColor;
    },

    async getStorageMovement() {
      try {
        const res = await SmOperationAPI.getStorageMovements(this.storageCode);
        this.storageMovements = res.data.data.map((x, idx) => {
          const { jobType, info } = this.formatStorageMovement(idx, x);
          return {
            ...x,
            jobType,
            info,
          };
        });
      } catch (e) {
        alert(`Overview Page Get Storage Movements Exception - ${e.message}`);
      }
    },

    formatStorageMovement(idx, value) {
      const number = `${idx + 1}.`.padEnd(4, " ");
      const date = `${
        value.completedAt
          ? // ? convertStringToLocal(value.completedAt, true, true)
            new Date(value.completedAt).toLocaleString("en-GB", {
              day: "2-digit",
              month: "2-digit",
              year: "numeric",
              hour: "numeric",
              minute: "numeric",
              second: "numeric",
              hour12: true,
            })
          : "Ongoing"
      }`.padEnd(23, " ");
      const coordinate = ": " + value.threeDim.padEnd(9, " ");
      const jobType =
        !value.orderType || !value.creationReason
          ? ""
          : value.orderType !== "INTERNAL"
          ? value.orderType
          : value.creationReason === "ADVANCED_ORDER"
          ? "ADVANCED ORDER"
          : value.creationReason === "NATURAL_SLOTTING"
          ? "NATURAL SLOTTING"
          : "DIGGING";
      const bracket = value.station
        ? `(Station ${value.station})`
        : jobType
        ? `(${jobType})`
        : "";
      return { jobType, info: number + date + coordinate + bracket + "\n" };
    },

    onClearStorageCode() {
      this.storageMovements = [];
    },

    ...mapActions(["getZoneGroups"]),
  },

  computed: {
    specialStack() {
      const obstacle = {
        id: "SS01",
        color: this.obstacleColor,
        label: "Obstacle",
        inBoxText: "",
      };
      const pickRestrict = {
        id: "SS02",
        color: this.pickRestrictColor,
        label: "Pick Restrict",
        inBoxText: "",
      };
      const dropRestrict = {
        id: "SS03",
        color: this.dropRestrictColor,
        label: "Drop Restrict",
        inBoxText: "",
      };
      const gatewayIn = {
        id: "SS04",
        color: this.gatewayInColor,
        label: "Station # Pick Point",
        inBoxText: "#-P",
      };
      const gatewayOut = {
        id: "SS05",
        color: this.gatewayOutColor,
        label: "Station # Drop Point",
        inBoxText: "#-D",
      };
      const gateway = {
        id: "SS06",
        color: this.gatewayColor,
        label: "Station # Pick Drop Point",
        inBoxText: "#-DP",
      };
      return [
        obstacle,
        pickRestrict,
        dropRestrict,
        gatewayIn,
        gatewayOut,
        gateway,
      ];
    },

    ...mapState(["zoneGroups"]),
  },

  mounted() {
    setTimeout(() => {
      this.init();
    }, 100);
  },
};
</script>
