<template>
  <v-dialog v-model="dialogBool">
    <v-card dark>
      <v-data-table
        :headers="headers"
        :items="result"
        :items-per-page="-1"
        :sort-by="['created_at']"
        dense
        :item-class="getColorFromChildMessage"
        dark
        hide-default-footer
      >
        <template v-slot:[`item.created_at`]="{ item }">
          {{ convertTimestampToLocal(item.created_at, true, true) }}
        </template>
        <template v-slot:[`item.message`]="{ item }">
          {{ item.message }}
        </template>
      </v-data-table>
      <v-card-actions>
        <v-spacer />
        <v-btn
          color="green darken-1"
          text
          @click="closeDialog"
        >
          Close
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
import { convertTimestampToLocal } from "../../helper/common"


export default {
    data: () => ({
        convertTimestampToLocal,
        dialogBool: false,
        headers: [
            { text: "Datetime", value: "created_at" },
            { text: "Message", value: "message" },
        ],
        result: []
    }),
    props: {
        
    },
    methods: {
        openDialog(result) {
            this.result = result
            this.dialogBool = true
        },
        closeDialog() {
            this.dialogBool = false
        },
        getColorFromChildMessage(item) {
            if (item.sent_by_tc) {
                return "green--text text--accent-3"
            } else {
                return "yellow--text text--accent-3"
            }
        },
    },
    computed: {

    }
}
</script>
