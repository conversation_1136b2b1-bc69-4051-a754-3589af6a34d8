<template>
    <v-form 
        v-model="valid"
        @keydown.enter.native="valid == true ? btnLog() : null"
    >
        <v-row>
            <v-col>
            <DateTimePicker 
                label="Time" 
                :default-date-time="defaultStartDateTime"
                @get-datetime="getStartIsoFormat"
            />
            </v-col>
        </v-row>
        <v-row>
            <v-col>
                <v-text-field
                    v-model="sid"
                    label="Skycar ID"
                    clearable
                    type="number"
                    :rules="[v => !!v || 'Required']"
                />
            </v-col>
            <v-col>
                <v-row>
                    <v-checkbox
                        v-model="duplicatedLog"
                        label="Multiple LOG"
                    />
                    <v-checkbox
                        class="ml-2"
                        v-model="warningLog"
                        label="WRN"
                    />
                </v-row>
            </v-col>
        </v-row>
        <v-row>
            <v-col>
            <v-btn
                :disabled="!valid || !skycarMessageDoneSync()"
                @click="btnLog()"
                class="ma-2"
                color="green"
            >
                <v-icon>mdi-check</v-icon>
                Confirm
            </v-btn>
            </v-col>
        </v-row>
    </v-form>
</template>

<script>
import { getNHourAgo } from "../../helper/common"
import DateTimePicker from "../shared/DateTimePicker.vue"

export default {
    props: {
        getCardMessage: {
            type: Function
        }
    },
    data: () => ({
        valid: null,
        sid: null,
        duplicatedLog: false,
        warningLog: false,
        startIsoformat: null,
    }),
    components: {
        DateTimePicker
    },
    methods: {
        defaultStartDateTime() {
            let now = new Date(getNHourAgo(1))
            let date = `${now.getFullYear()}-${(now.getMonth() + 1).toString()
                .padStart(2, "0")}-${now.getDate().toString().padStart(2, "0")}`
            let time = now.toTimeString().substring(0, 5)
            return [date, time]
        },
        getStartIsoFormat(dt) {
            this.startIsoformat = dt.getTime()
        },
        skycarMessageDoneSync() {
            return this.getCardMessage().doneSync
        },
        btnLog: async function () {
            await this.getCardMessage().openCard(
                parseInt(this.sid), 
                this.startIsoformat / 1000,
                this.duplicatedLog,
                this.warningLog
            )
        }
    }
}
</script>
