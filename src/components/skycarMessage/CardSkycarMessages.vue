<template>
  <v-card
    v-if="cardBool"
    dark
  >
    <v-card-title>Skycar Messages</v-card-title>
    <v-data-table
      :headers="headers"
      :items="filterChildMessage"
      :items-per-page="itemsPerPage"
      :sort-by="['date']"
      dense
      :item-class="getColorFromAlertLevel"
      dark
      :loading="!doneSync"
      @update:page="onPageChange"
      hide-default-footer
    >
      <template v-slot:top>
        <v-toolbar
          flat
          class="transparent"
        >
          <v-text-field
            v-model="search"
            append-icon="mdi-magnify"
            label="Search"
            single-line
            hide-details
            clearable
          />
        </v-toolbar>
      </template>
      <template v-slot:[`item.created_at`]="{ item }">
        {{ convertTimestampToLocal(item.created_at, true, true) }}
      </template>
      <template v-slot:[`item.child_messages`]="{ item }">
        {{ item.child_messages[0].message }}
      </template>
      <template v-slot:[`item.detail`]="{ item }">
        <v-btn
          small
          class="mr-2"
          @click="showChildMessages(item.child_messages)"
          light
        >
          Detail
        </v-btn>
      </template>
    </v-data-table>
    <v-row class="d-flex justify-end">
      <v-col>
        <v-pagination
          v-model="currentPage"
          :length="totalPages"
          :total-visible="8"
          @input="onPageChange"
        />
      </v-col>
      <v-col>
        <v-select
          v-model="itemsPerPage"
          :items="perPageOptions"
          label="Items per page"
          @input="onPerPageChange"
        />
      </v-col>
    </v-row>
    <v-card-actions>
      <v-spacer />
      <ProgressCircular :done-sync="doneSync" />
      <v-btn
        color="green darken-1"
        text
        @click="getResult"
        class="ma-2"
        :disabled="!doneSync"
      >
        Refresh
      </v-btn>
      <v-btn
        color="green darken-1"
        text
        @click="closeCard"
        class="ma-2"
      >
        Close
      </v-btn>
    </v-card-actions>
    <DialogChildMessages ref="dialogChildMessages" />
  </v-card>
</template>

<script>
import axios from "axios"
import { convertTimestampToLocal, getBTUrl, getRequestHeader } from "../../helper/common"
import { SkycarMessage } from "../../helper/enums"
import DialogChildMessages from "./DialogChildMessages.vue"
import ProgressCircular from "../../components/shared/ProgressCircular.vue"
export default {
  components: {
    DialogChildMessages,
    ProgressCircular
  },
  data: () => ({
    convertTimestampToLocal,
    doneSync: true,
    cardBool: false,
    headers: [
      { text: "Datetime", value: "created_at" },
      { text: "Message", value: "child_messages" },
      { text: "Detail", value: "detail" },
    ],
    search: null,
    result: [],
    itemsPerPage: 50,
    perPageOptions: [50, 100, 150, 200],
    totalPages: 1,
    currentPage: 1,
    sid: null,
    startTime: null,
    attempts: -1,
  }),
  methods: {
    async openCard(sid, startTime, duplicatedLog, warningLog) {
      this.sid = sid
      this.startTime = startTime
      this.attempts = duplicatedLog ? 1 : -1
      this.warning = warningLog
      if (duplicatedLog) {
        this.attempts = 1
      }
      this.currentPage = 1
      this.cardBool = true
      await this.getResult()
    },
    closeCard() {
      this.cardBool = false
    },
    async getResult() {
      let here = this
      here.doneSync = false
      let url = getBTUrl() + SkycarMessage.GET_MESSAGE
      let qs = new URLSearchParams({
        "skycar_id": here.sid,
        "start_time": here.startTime,
        "warning": here.warning,
        "page": here.currentPage,
        "per_page": here.itemsPerPage,
        "attempts": here.attempts,
      })
      axios.get(url, {
        params: qs,
        headers: getRequestHeader()
      }).then((res) => {
        here.result = res.data.data
        here.totalPages = res.data.total_page
      }).catch((error) => {
        alert(error)
      }).finally(() => {
        setTimeout(() => {
          here.doneSync = true
        }, 500)
      })
    },
    getColorFromAlertLevel(item) {
      switch (item.alert_level) {
        case 0:
          return "red--text text--accent-4"
        case 1:
          if (item.warning) {
            return "orange--text text--accent-3"
          }
          return "green--text text--accent-3"
        case 3:
          return "red--text text--accent-4"
      }
    },
    showChildMessages(childMessages) {
      this.$refs.dialogChildMessages.openDialog(childMessages)
    },
    onPageChange(val) {
      this.currentPage = val
      this.getResult()
    },
    onPerPageChange(val) {
      this.itemsPerPage = val
      this.currentPage = 1
      this.getResult()
    }
  },
  computed: {
    filterChildMessage() {
      if (this.search) {
        return this.result.filter(item => {
          return item.child_messages.some(child => {
              return child.message.includes(this.search)
          })
        })
      } else {
        return this.result
      }
    },
  }
}
</script>
