<template>
  <div>
    <v-toolbar
      dark 
      color="black"
    >
      <v-row>
        <v-spacer />
        <v-menu
          v-model="modelWarningList.bolmenu"
          :close-on-content-click="false"
          transition="scale-transition"
          offset-y
        >
          <template v-slot:activator="{ on, attrs }">
            <v-text-field
              class="ma-1"
              v-model="modelWarningList.dtfromto"
              label="Choose dates"
              prepend-icon="mdi-calendar"
              readonly
              v-bind="attrs"
              v-on="on"
            >
              {{ modelWarningList.dtfromto }}
            </v-text-field>
          </template>
          <v-date-picker
            range
            v-model="modelWarningList.dtfromto"
            @input="modelWarningList.bolmenu = false"
          />
        </v-menu>

        <v-btn
          class="ma-1"
          @click="btnGetWarningList()"
          color="green"
        >
          <v-icon>mdi-restart</v-icon>
          <span>Refresh</span>
        </v-btn>
        <v-btn
          class="ma-1"
          color="blue"
        >
          <download-csv 
            :data="warningList.result"
            :fields="modelWarningList.exportFields"
            :name="WarningListExportFileName"
          >
            <v-icon>mdi-download</v-icon> DOWNLOAD CSV      
          </download-csv>
        </v-btn>
      </v-row>
    </v-toolbar>
    <v-data-table
      :headers="warningList['headers']"
      :items="warningList['result']"
      :items-per-page="15"
      class="elevation-1"
      dark
      item-class="orange--text text--accent-3"
    >
      <!-- Local Time -->
      <template v-slot:[`item.created_at`]="{ item }">
        {{ item.dt_local }}
      </template>

      <!-- Skycar ID -->
      <template v-slot:[`item.skycar_id`]="{ item }">
        <v-chip
          color="blue"
          dark
          small
        >
        {{ item.skycar_id }}
        </v-chip>
      </template>

      <!-- Status -->
      <template v-slot:[`item.status`]="{ item }">
        <v-chip
          :color="item.status === 'E' ? 'red' : item.status === 'I' ? 'green' : 'grey'"
          dark
          small
        >
          {{ item.status === 'E' ? 'Error' : item.status === 'I' ? 'Info' : item.status }}
        </v-chip>
      </template>

      <!-- Error Code -->
      <template v-slot:[`item.error_code`]="{ item }">
        <v-chip
          color="orange"
          dark
          small
          v-if="item.error_code !== 'N/A'"
        >
          {{ item.error_code }}
        </v-chip>
        <span v-else>{{ item.error_code }}</span>
      </template>

      <!-- Alert Level with color coding -->
      <template v-slot:[`item.alert_level`]="{ item }">
        <v-chip
          :color="getAlertLevelColor(item.alert_level)"
          dark
          small
        >
          Level {{ item.alert_level }}
        </v-chip>
      </template>

      <!-- Full Message -->
      <template v-slot:[`item.full_message`]="{ item }">
        <v-tooltip bottom>
          <template v-slot:activator="{ on, attrs }">
            <span
              v-bind="attrs"
              v-on="on"
              class="text-truncate"
              style="max-width: 600px; display: inline-block;"
            >
              {{ item.full_message }}
            </span>
          </template>
          <span>{{ item.full_message }}</span>
        </v-tooltip>
      </template>

      <!-- App Error - clickable like Error Mode -->
      <template v-slot:[`item.error_name`]="{ item }">
        <v-tooltip bottom>
          <template v-slot:activator="{ on, attrs }">
            <v-icon
              v-bind="attrs"
              v-on="on"
              @click="btnGetErrorDetail(item)"
              class="mx-1"
            >
              mdi-information
            </v-icon>
          </template>
          <span>Show Error Detail</span>
        </v-tooltip>
        <span>{{ item.error_name }}</span>
      </template>
    </v-data-table>

    <!-- Warning Detail Dialog -->
    <v-dialog
      v-if="warningList['boolDetail']"
      v-model="warningList['boolDetail']"
      max-width="900"
      @keydown.enter="warningList['boolDetail']=false"
    >
      <v-card>
        <v-toolbar
          dark
          color="orange"
        >
          <v-toolbar-title>Warning Details</v-toolbar-title>
        </v-toolbar>
        <v-card-text class="pt-6">
          <div class="mb-4">
            <h3>Alert Level: {{ warningList['txtDetail'].alert_level }}</h3>
            <p><strong>Warning Time:</strong> {{ warningList['txtDetail'].dt_local }}</p>
            <p><strong>Skycar ID:</strong> SC{{ warningList['txtDetail'].skycar_id }}</p>
            <p><strong>Status:</strong> {{ warningList['txtDetail'].status }}</p>
            <p><strong>Job ID:</strong> {{ warningList['txtDetail'].job_id }}</p>
            <p><strong>Error Code:</strong> {{ warningList['txtDetail'].error_code }}</p>
            <p><strong>Coordinate:</strong> {{ warningList['txtDetail'].coordinate }}</p>
          </div>
          
          <h3 class="mb-2">Child Messages ({{ warningList['txtDetail'].child_messages.length }})</h3>
          <v-simple-table class="mt-4">
            <template v-slot:default>
              <thead>
                <tr>
                  <th class="text-left">Time</th> 
                  <th class="text-left">Message</th>  
                  <th class="text-left">Sent by TC</th> 
                </tr>
              </thead>
              <tbody>
                <tr 
                  v-for="(msg, index) in warningList['txtDetail'].child_messages" 
                  :key="index"
                  :class="msg.message.startsWith('WRN') ? 'warning--text' : ''"
                >
                  <td>{{ convertTimestampToLocal(msg.created_at, true) }}</td>
                  <td>{{ msg.message }}</td>
                  <td>
                    <v-icon :color="msg.sent_by_tc ? 'green' : 'blue'">
                      {{ msg.sent_by_tc ? 'mdi-arrow-up' : 'mdi-arrow-down' }}
                    </v-icon>
                  </td>
                </tr>
              </tbody>
            </template>
          </v-simple-table>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="orange darken-1"
            text
            @click="warningList['boolDetail'] = false"
          >
            Close
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Warning Detail Dialog Component -->
    <DialogSkycarWarningDetail
      :current-zone="currentZone"
      ref="dialogSkycarWarningDetail"
    />

    <!-- Refresh Status Dialog -->
    <v-dialog
      v-if="warningList['boolRefresh']"
      v-model="warningList['boolRefresh']"
      max-width="500"
      @keydown.enter="warningList['boolRefresh']=false"
    >
      <v-card>
        <v-toolbar
          dark
          :color="getSubmitStatus(warningList['txtRefresh'].status)"
        >
          <v-toolbar-title>Status: {{ warningList['txtRefresh'].status }}</v-toolbar-title>
        </v-toolbar>
        <v-progress-linear
          v-if="warningList['txtRefresh'].status == 'Pending'"
          indeterminate
          color="green"
        />
        <v-card-text class="pt-6">
          <pre class="text-wrap">{{ warningList['txtRefresh'].message }}</pre>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="green darken-1"
            text
            @click="warningList['boolRefresh'] = false"
          >
            Close
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import { convertTimestampToLocal, getBTUrl, getRequestHeader } from "../../helper/common"
import { SkycarMessage } from "../../helper/enums"
import DialogSkycarWarningDetail from "../dialogs/DialogSkycarWarningDetail.vue"
import axios from "axios"

export default {
  name: "FormSkycarWarningMessages",
  components: {
    DialogSkycarWarningDetail
  },
  props: {
    currentZone: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      convertTimestampToLocal,
      // Warning List Model
      modelWarningList: {
        dtfromto: [
          new Date().toISOString().slice(0,10),
          new Date().toISOString().slice(0,10)
        ],
        bolmenu: false,
        exportFields: [
          "created_at", "dt_local", "skycar_id", "status", "job_id", "error_code", "coordinate", "alert_level", "app_module", "error_name", "full_message"
        ],
      },
      warningList: {
        headers: [
          { text: "Time", value: "created_at" },
          { text: "Skycar ID", value: "skycar_id" },
        //   { text: "Status", value: "status" },
          { text: "Job ID", value: "job_id" },
          { text: "Error Code", value: "error_code" },
          { text: "Coordinate", value: "coordinate" },
          { text: "Alert Level", value: "alert_level" },
          { text: "App Module", value: "app_module" },
          { text: "App Error", value: "error_name" },
          { text: "Full Message", value: "full_message" },
        ],
        result: Array(),
        boolDetail: false,
        txtDetail: null,
        boolRefresh: false,
        txtRefresh: null,
      },
    }
  },
  computed: {
    WarningListExportFileName() {
      let from = this.modelWarningList.dtfromto[0]
      let to = this.modelWarningList.dtfromto[1] ?? this.modelWarningList.dtfromto[0]
      return `warning_list_${from}_${to}.csv` 
    },
  },
  methods: {
    async btnGetWarningList() {
      this.warningList["boolRefresh"] = true
      this.warningList["txtRefresh"] = { status: "Pending", message: "Refreshing...", data: [] }

      let daterange = this.modelWarningList.dtfromto
      let df = daterange[0] 
      let dt = daterange[1] ?? daterange[0] 

      var d1 = Date.parse(daterange[0]);
      var d2 = Date.parse(daterange[1]);
      if (d1 > d2) {
           df = daterange[1]
           dt = daterange[0]
      }
      
      // eslint-disable-next-line no-unused-vars
      const _dt = dt  // Currently only using start date for API call
      
      try {
        // For now, use mock data since the API endpoint may not exist yet
        // Replace this with actual API call when available: "/api/v2/skycar/warnings"
        // Convert date to timestamp (start of day)
        let startTime = new Date(df).getTime() / 1000
        
        // Use the same API as CardSkycarMessages but with sid=null to get all skycars warnings
        let url = getBTUrl() + SkycarMessage.GET_MESSAGE
        let qs = new URLSearchParams({       // null to get all skycars
          "start_time": startTime,
          "warning": true,            // only get warnings
          "page": 1,
          "per_page": 1000,          // Get more items for warnings
          "attempts": -1,
        })
        
        const res = await axios.get(url, {
          params: qs,
          headers: getRequestHeader()
        })
        
        if (res.data && res.data.data) {
          // Process the warning data
          this.warningList["result"] = []
          
          res.data.data.forEach(warning => {
            // Find all WRN messages in child_messages
            const wrnMessages = warning.child_messages.filter(msg => msg.message.startsWith('WRN'))
            
            wrnMessages.forEach(wrnMsg => {
              const parsedWarn = this.parseWarningMessage(wrnMsg.message)
              
              this.warningList["result"].push({
                ...warning,
                ...parsedWarn,
                dt_local: this.convertTimestampToLocal(warning.created_at, true),
                full_message: wrnMsg.message,
                message_time: this.convertTimestampToLocal(wrnMsg.created_at, true),
                // Add fields for App Module and App Error
                app_module: this.extractAppModule(warning),
                error_name: this.extractErrorName(warning),
                error_detail: warning.child_messages
              })
            })
            
            // If no WRN messages found, still add the warning entry
            if (wrnMessages.length === 0) {
              this.warningList["result"].push({
                ...warning,
                dt_local: this.convertTimestampToLocal(warning.created_at, true),
                skycar_id: 'N/A',
                status: 'N/A',
                job_id: 'N/A',
                error_code: 'N/A',
                coordinate: 'N/A',
                full_message: 'No WRN messages found',
                app_module: this.extractAppModule(warning),
                error_name: this.extractErrorName(warning),
                error_detail: warning.child_messages
              })
            }
          })
        } else {
          this.warningList["result"] = []
        }
        
        this.warningList["txtRefresh"] = { 
          status: "Accepted", 
          message: `Found ${this.warningList["result"].length} warnings`, 
          data: [] 
        }
      } catch (error) {
        this.warningList["result"] = []
        this.warningList["txtRefresh"] = { 
          status: "Rejected", 
          message: error.response?.data?.message || "Failed to fetch warnings", 
          data: [] 
        }
      }
    },

    parseWarningMessage(message) {
      // Parse WRN message format based on the provided structure:
      // WRN,SC,1,E,S1-197826d4fef0,10,ERR3,xy,34,5,0,,CB,BATTERY_M,15,E_BATT_SEQ_LOW_BATTERY,3,NONE,0,,0,934
      const parts = message.split(',')
      
      if (parts.length < 22) {
        return {
          skycar_id: 'Invalid',
          status: 'Invalid',
          job_id: 'Invalid',
          error_code: 'Invalid',
          coordinate: 'Invalid',
          app_module: 'Invalid',
          app_code: 'Invalid',
          mgr_module: 'Invalid',
          mgr_code: 'Invalid',
          component_module: 'Invalid',
          component_code: 'Invalid',
          motor: 'Invalid',
          bsp: 'Invalid',
          qr: 'Invalid'
        }
      }
      
      return {
        skycar_id: parts[2] || 'N/A',           // Position 2: Dev ID (Skycar ID)
        status: parts[3] || 'N/A',              // Position 3: Cmd (Status)
        job_id: parts[4] || 'N/A',              // Position 4: JOBID
        qty: parts[5] || 'N/A',                 // Position 5: QTY
        error_code: parts[6] || 'N/A',          // Position 6: Action (Error code)
        axis: parts[7] || 'N/A',                // Position 7: Axis
        coordinate: parts[8] && parts[9] ? `${parts[8]},${parts[9]}` : 'N/A',  // Position 8,9: X,Y coordinates
        z_coordinate: parts[10] || 'N/A',       // Position 10: Z
        bin: parts[11] || 'N/A',                // Position 11: BIN
        dest_type: parts[12] || 'N/A',          // Position 12: DEST_TYPE
        app_module: parts[13] || 'N/A',         // Position 13: APPMODULE
        app_code: parts[14] || 'N/A',           // Position 14: APPCODE
        mgr_module: parts[15] || 'N/A',         // Position 15: MGRMODULE
        mgr_code: parts[16] || 'N/A',           // Position 16: MGRCODE
        component_module: parts[17] || 'N/A',   // Position 17: COMPONENTMODULE
        component_code: parts[18] || 'N/A',     // Position 18: COMPONENTCODE
        motor: parts[19] || 'N/A',              // Position 19: MOTOR
        bsp: parts[20] || 'N/A',                // Position 20: BSP
        qr: parts[21] || 'N/A'                  // Position 21: QR
      }
    },
    btnWarningDetail(item) {
      this.warningList["boolDetail"] = true
      this.warningList["txtDetail"] = item
    },
    getAlertLevelColor(alertLevel) {
      switch(alertLevel) {
        case 1: return "orange"
        case 2: return "deep-orange"
        case 3: return "red"
        default: return "grey"
      }
    },
    getSubmitStatus(status) {
      if (status == "Accepted") {
        return "green"
      } else if (status == "Rejected" || status == "Warning") {
        return "red"
      } else if (status == "Pending") {
        return "black"
      }
    },
    extractAppModule(warning) {
      // Extract app module from the first WRN message in child_messages
      const wrnMessage = warning.child_messages.find(msg => msg.message.startsWith('WRN'))
      if (wrnMessage) {
        const parsed = this.parseWarningMessage(wrnMessage.message)
        return parsed.app_module || "N/A"
      }
      return warning.app_module || "N/A"
    },
    extractErrorName(warning) {
      // Extract app code from the first WRN message in child_messages
      const wrnMessage = warning.child_messages.find(msg => msg.message.startsWith('WRN'))
      if (wrnMessage) {
        const parsed = this.parseWarningMessage(wrnMessage.message)
        return parsed.app_code || "N/A"
      }
      return warning.error_name || warning.id || "N/A"
    },
    async btnGetErrorDetail(item) {
      // Show warning detail dialog using the DialogSkycarWarningDetail component
      this.$refs.dialogSkycarWarningDetail.openDialog(item)
    },

  }
}
</script> 