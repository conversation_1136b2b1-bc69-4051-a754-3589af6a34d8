<template>
  <v-chip
    :style="computedChipStyle"
    dark
    class="ma-5 subtitle-1"
    :class="{ 'enlarged-chip': isEnlarged }"
  >
    <b :style="getTextColorStyle">{{ chipText }}</b>
    <v-tooltip bottom>
      <template v-slot:activator="{ on }">
        <v-icon small v-on="on">mdi-information</v-icon>
      </template>
      <span class="subtitle-1">{{ tooltipText }}</span>
    </v-tooltip>
  </v-chip>
</template>

<script>
export default {
  props: {
    chipColor: String,
    chipStyle: String,
    chipText: String,
    tooltipText: String,
    currentHoverColor: String
  },
  computed: {
    isEnlarged() {
      // Enlarge if color match.
      return (
        this.currentHoverColor === this.chipColor ||
        this.currentHoverColor === this.chipStyle
      );
    },
    computedChipStyle() {
      // If chipStyle is provided, use it; otherwise, use chipColor as the background
      return this.chipStyle || `background-color: ${this.chipColor}`;
    },
    getTextColorStyle() {
      const color = this.getTextColor();
      return { color };
    }
  },
  methods: {
    getTextColor() {
      let backgroundColor = this.chipColor;
      let r = parseInt(backgroundColor.slice(1, 3), 16);
      let g = parseInt(backgroundColor.slice(3, 5), 16);
      let b = parseInt(backgroundColor.slice(5, 7), 16);


      const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
      // alert(luminance);

      return luminance > 0.5 ? "black" : "white";
    }
  }
};
</script>

<style>
.enlarged-chip {
  transform: scale(1.4); /* Increase the size */
  transition: transform 0.8s; /* Add a transition effect */
}
</style>
