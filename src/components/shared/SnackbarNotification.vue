<template>
    <v-snackbar
        v-model="notification.bool"
        :value="true"
        :right="!notification.middlePosition"
        :middle="notification.middlePosition"
        top
        :color="notification.color"
        :timeout="notification.middlePosition ? 0 : undefined"
    >
        {{notification.message}}
            <template v-slot:action="{ attrs }">
            <v-btn
                text
                v-bind="attrs"
                @click="notification.middlePosition ? refreshPage() : clearNotification()"
            >
                {{ notification.middlePosition ? "Refresh" : "Close" }}
            </v-btn>
        </template>
    </v-snackbar>
</template>

<script>
export default {
    data: () => ({
        notification: {
            bool: false,
            message: null,
            color: null,
            middlePosition: false
        }
    }),
    methods: {
        showNotification(success, message, middlePosition = false) {
            this.notification = {
                bool: true,
                message: message,
                color: middlePosition ? "gray" : (success ? "green" : "red"),
                middlePosition: middlePosition,
            }
        },
        clearNotification() {
            this.notification = {
                bool: false,
                message: null,
                color: null,
                middlePosition: false
            }
        },
        refreshPage(){
            window.location.reload();
        }
    }
}
</script>
