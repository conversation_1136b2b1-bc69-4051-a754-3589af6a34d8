<template>
  <v-app-bar app color="black" dark>
    <div class="d-flex align-center">
      <v-btn icon @click="toogleDrawer">
        <v-icon>mdi-menu</v-icon>
      </v-btn>
      <v-toolbar-title> Matrix Dashboard</v-toolbar-title>
    </div>
    <v-spacer />

    <v-btn href="https://github.com/pingspace" target="_blank" text>
      <span class="mr-2">Latest Release</span>
      <v-icon>mdi-open-in-new</v-icon>
    </v-btn>

    <div v-if="this.$store.state.user.userLog === false">
      <v-btn :to="'/login'" text>
        <span class="mr-2">Log In</span>
      </v-btn>

      <v-btn :to="'/register'" text>
        <span class="mr-2">Register</span>
      </v-btn>
    </div>

    <div v-else>
      <v-row justify="center" align="center">
        <v-col cols="auto">
          <v-btn :to="'/resetpassword'" text>
            <small>Reset Password</small>
          </v-btn>
        </v-col>

        <v-col cols="auto">
          <small class="text-center"
            >Account: {{ this.$store.state.user.username }}</small
          >

          <div class="text-center">
            <v-btn @click="logout" text>
              <small class="mr-2">Logout</small>
              <v-icon>mdi-account</v-icon>
            </v-btn>
          </div>
        </v-col>
      </v-row>
    </div>
  </v-app-bar>
</template>

<script>
import { resetStateUser } from "../../helper/authorize";

export default {
  name: "App",

  components: {
    // HelloWorld,
  },
  methods: {
    toogleDrawer() {
    if (
        this.$store.state.user.userLog ||
        !this.$store.state.login 
      ) {
        this.$store.commit("updateDrawerState");
      } else {
        // To solve NavigationDuplicated error
        if (this.$route.path !== "/login") {
          this.$router.push({ path: "/login" });
        }
      }
    },

    async logout() {
     await resetStateUser().then(() => {
      this.$router.push({ path: "/login" });
  });
}
  },

  data: () => ({
    appMD: null
  })
};
</script>
