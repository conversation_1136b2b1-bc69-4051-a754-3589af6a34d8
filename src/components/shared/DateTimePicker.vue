<template>
    <v-menu v-model="menu">
        <template v-slot:activator="{ on, attrs }">
            <v-text-field
                v-model="getDateTime"
                :label="label"
                readonly
                v-bind="attrs"
                v-on="on"
            ></v-text-field>
        </template>
        <v-row>
            <v-col>
                <v-date-picker
                    v-model="date"
                    color="green"
                    scrollable
                ></v-date-picker>
            </v-col>
            <v-col>
                <v-time-picker
                    v-model="time"
                    color="green"
                    scrollable
                    format="24hr"
                ></v-time-picker>
            </v-col>
        </v-row>
    </v-menu>
</template>

<script>
export default {
    created() {
        this.init()
    },
    props: {
        label: {
            type: String,
            required: true
        },
        defaultDateTime: {
            type: Function
        }
    },
    computed: {
        getDateTime() {
            this.getTimestamp()
            return `${this.date} ${this.time}`
        }
    },
    data: () => ({
        menu: null,
        date: null,
        time: null
    }),
    methods: {
        init() {
            [this.date, this.time] = this.defaultDateTime()
        },
        getTimestamp() {
            this.$emit("get-datetime", new Date(`${this.date} ${this.time}`))
        }
    }
}
</script>
