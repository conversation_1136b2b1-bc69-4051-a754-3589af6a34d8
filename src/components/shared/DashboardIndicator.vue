<template>
    <v-card class="dashboard-indicator" :color="backgroundColor">
      <v-row>
        <v-col cols="11">
          <div class="indicator-icon">
            <v-icon size="48">{{ icon }}</v-icon>
          </div>
          <div class="indicator-title">{{ title }}</div>
          <div class="indicator-value">{{ value }}</div>
        </v-col>
        <v-col cols="1">
          <div class="indicator-vertical-bar" :style="{ background: gradientColor }"></div>
        </v-col>
      </v-row>
    </v-card>
  </template>
  
  <script>
  export default {
    props: {
      icon: String,
      title: String,
      value: Number,
      backgroundColor: String,
      gradientColor: String
    }
  };
  </script>
  