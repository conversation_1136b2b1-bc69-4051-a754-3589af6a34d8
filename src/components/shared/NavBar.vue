<template>
  <v-navigation-drawer
    v-model="navigationDrawerModel"
    app
    class="nav-drawer"
    dark
    rail
    floating
    expand-on-hover
    width="280"
  >
    <v-list dense nav>
      <template v-for="item in sortedItems">
        <v-list-group
          v-if="item.submenu"
          :key="item.title"
          no-action
          class="nav-group"
        >
          <template v-slot:activator>
            <v-list-item-icon>
              <v-icon>{{ item.icon }}</v-icon>
            </v-list-item-icon>
            <v-list-item-content>
              <v-list-item-title class="nav-title">{{ item.title }}</v-list-item-title>
            </v-list-item-content>
          </template>
          <v-list-item
            v-for="subitem in item.submenu.items"
            :key="subitem.title"
            :to="subitem.path"
            link
            class="nav-subitem"
            @click="handleNav"
            active-class="nav-active"
          >
            <v-list-item-icon>
              <v-icon small>{{ subitem.icon }}</v-icon>
            </v-list-item-icon>
            <v-list-item-title class="nav-subtitle">{{ subitem.title }}</v-list-item-title>
          </v-list-item>
        </v-list-group>
        <v-list-item
          v-else
          :key="item.title + '-single'"
          :to="item.path"
          link
          class="nav-item"
          @click="handleNav"
          active-class="nav-active"
        >
          <v-list-item-icon>
            <v-icon>{{ item.icon }}</v-icon>
          </v-list-item-icon>
          <v-list-item-title class="nav-title">{{ item.title }}</v-list-item-title>
        </v-list-item>
      </template>
    </v-list>
    <template v-slot:append>
      <div class="nav-version pa-2">
        <v-btn block depressed color="grey darken-3" class="version-btn" style="pointer-events: none;">
          v{{ appVersion }}
        </v-btn>
      </div>
    </template>
  </v-navigation-drawer>
</template>

<script>
import { authorizeRole, isloginDisabled } from "../../helper/authorize";
import { Role, Route } from "../../helper/enums";
import { eventType, waitForEvent } from "../../helper/eventBus";
import methods from "../SkycarLog.vue";
import { closeMessage as closeOT } from "../operation/Operation.vue";
import { closeMessage as closeDF } from "../skycarOTA/DialogFlash.vue";
import { closeMessage as closeTC } from "../skycarOTA/TabConsole.vue";
import { closeMessage as closeOTA } from "../skycarOTA/TabSkycarOTAUpdate.vue";
export default {
  name: "NavBar",
  computed: {
    navigationDrawerModel: {
      get() {
        return this.$store.state.drawer;
      },
      set() {},
    },
    appVersion() {
      return this.$store.state.versions?.TC_DASHBOARD_VERSION || "0.0.0";
    },
    sortedItems() {
      return [...this.items].sort((a, b) => (a.order || 99) - (b.order || 99));
    },
  },
  methods: {
    handleNav() {
      methods.methods.closeMessage();
      closeOT();
      closeDF();
      closeTC();
      closeOTA();
    },
  },
  async created() {
    if (!isloginDisabled()) {
      const authValidatedPromise = await waitForEvent(eventType.authValidated);
      if (authValidatedPromise && !authorizeRole(Role.ADMIN)) {
        this.items = this.items.filter((item) => item.title !== "Simulation");
      }
    }
    this.items.sort((a, b) => {
      if (a.order === undefined) return 0;
      if (b.order === undefined) return 0;
      return a.order - b.order;
    });
  },
  data: () => ({
    items: [
      {
        title: "Insight",
        icon: "mdi-chart-line",
        path: "/insight",
        submenu: {
          items: [
            { title: "Matrix Dashboard", icon: "mdi-routes", path: "/insight" },
          ],
        },
      },
      { title: "Operation", icon: "mdi-account-wrench", path: "/operation" },
      { title: "Skycar", icon: "mdi-taxi", path: "/skycar" },
      { title: "Station", icon: "mdi-table-row-remove", path: "/station" },
      { title: "Order", icon: "mdi-border-color", path: "/order" },
      { title: "Charging Service", icon: "mdi-battery-charging-medium", path: "/charging" },
      { title: "Obstacle", icon: "mdi-skull-crossbones", path: "/obstacle" },
      { title: "Service Door", icon: "mdi-door-open", path: "/servicedoor" },
      { title: "Visualisation", icon: "mdi-animation", path: "/visualisation" },
      { title: "Preventive Maintenance", icon: "mdi-robot-industrial", path: "/preventive/manage" },
      { title: "Skycar Log", icon: "mdi-radar", path: "/skycarLog" },
      { title: "SM Order", icon: "mdi-border-color", path: "/smorder" },
      {
        title: "SM Log",
        icon: "mdi-math-log",
        submenu: {
          items: [
            { title: "SM Order Log", icon: "mdi-clipboard-list-outline", path: Route.SmOrderLogs },
            { title: "Storage Log", icon: "mdi-view-list", path: Route.StorageLogs },
          ],
        },
      },
      { title: "Skycar OTA", icon: "mdi-flash", path: "/skycarOTA" },
      { title: "Stack Overview", icon: "mdi-cube", path: "/stackoverview" },
      {
        title: "SM Storages",
        icon: "mdi-archive-marker",
        submenu: {
          items: [
            { title: "Overview", icon: "mdi-archive-marker", path: "/storage" },
            { title: "Call Store Bins", icon: "mdi-archive-marker", path: "/callStoreBin" },
          ],
        },
      },
      { title: "Webhook", icon: "mdi-webhook", path: "/webhook" },
      {
        title: "Setting",
        icon: "mdi-cog",
        submenu: {
          items: [
            { title: "TC Planning", icon: "mdi-toggle-switch", path: "/planningparameters" },
            { title: "SM Setting", icon: "mdi-cog", path: "/settings" },
          ],
        },
      },
      { title: "Simulation", icon: "mdi-animation-play", path: "/simulation" },
      { title: "Analysis", icon: "mdi-chart-line", path: "/analysis" },
    ],
  }),
};
</script>

<style scoped>
.nav-drawer {
  top: 0px;
  background: #181818 !important;
}
.nav-title {
  font-size: 17px;
  font-weight: 500;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
}
.nav-item,
.nav-group {
  margin-bottom: 2px;
  border-radius: 6px;
  transition: background 0.2s;
}
.nav-item:hover,
.nav-group:hover {
  background: #232323 !important;
}
.nav-active {
  background: #1976d2 !important;
  color: #fff !important;
}
.nav-subitem {
  padding-left: 36px !important;
  border-radius: 6px;
  transition: background 0.2s;
}
.nav-subitem:hover {
  background: #232323 !important;
}
.nav-subtitle {
  font-size: 15px;
  font-weight: 400;
  display: flex;
  align-items: center;
}
.nav-version {
  border-top: 1px solid #333;
  margin-top: 12px;
  padding-top: 8px;
}
.version-btn {
  font-size: 14px;
  letter-spacing: 1px;
  color: #bdbdbd !important;
  background: #232323 !important;
  border-radius: 6px;
}
</style>
