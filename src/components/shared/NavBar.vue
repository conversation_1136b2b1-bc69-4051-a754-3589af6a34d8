<template>
  <v-navigation-drawer v-model="navigationDrawerModel" app class="black accent-5" dark style="top: 0px" rail floating
    expand-on-hover width="300px">
    <v-list>
      <template v-for="item in items">
        <!-- Check if the current item has a submenu -->
        <v-list-group v-if="item.submenu" :key="item.title" no-action>
          <template v-slot:activator>
            <v-list-item-icon>
              <v-icon>{{ item.icon }}</v-icon>
            </v-list-item-icon>
            <v-list-item-content>
              <v-list-item-title class="large-text">{{
                item.title
              }}</v-list-item-title>
            </v-list-item-content>
          </template>

          <v-list-item v-for="subitem in item.submenu.items" :key="subitem.title" :to="subitem.path" link class="start">
            <v-list-item class="text-left">
              <v-list-item-icon>
                <v-icon>{{ subitem.icon }}</v-icon>
              </v-list-item-icon>
              <v-list-item-title>{{ subitem.title }}</v-list-item-title>
            </v-list-item>
          </v-list-item>
        </v-list-group>

        <!-- If the current item doesn't have a submenu, render it as a regular list item -->
        <v-list-item v-else :key="item.title" :to="item.path" link @click="closeMessage()" class="start">
          <v-list-item-icon>
            <v-icon>{{ item.icon }}</v-icon>
          </v-list-item-icon>
          <v-list-item-content>
            <v-list-item-title class="large-text">{{
              item.title
            }}</v-list-item-title>
          </v-list-item-content>
        </v-list-item>
      </template>
    </v-list>

    <template v-slot:append>
      <div class="pa-2">
        <v-btn block> v{{ appVersion }} </v-btn>
      </div>
    </template>
  </v-navigation-drawer>
</template>

<script>
import { authorizeRole, isloginDisabled } from "../../helper/authorize";
import {
  getCurrentDateTimeWithMilliseconds,
} from "../../helper/common.js";
import { Role, Route } from "../../helper/enums";
import { eventType, waitForEvent } from "../../helper/eventBus";
import methods from "../SkycarLog.vue";
import { closeMessage as closeOT } from "../operation/Operation.vue";
import { closeMessage as closeDF } from "../skycarOTA/DialogFlash.vue";
import { closeMessage as closeTC } from "../skycarOTA/TabConsole.vue";
import { closeMessage as closeOTA } from "../skycarOTA/TabSkycarOTAUpdate.vue";
export default {
  name: "App",

  components: {},
  computed: {
    navigationDrawerModel: {
      get() {
        return this.$store.state.drawer;
      },
      set() {
        // empty setter
      },
    },

    buildDate() {
      return process.env.BUILD_DATE;
    },
    
    appVersion() {
      return this.$store.state.versions?.TC_DASHBOARD_VERSION || "0.0.0";
    },
  },
  methods: {
    closeMessage() {
      methods.methods.closeMessage();
      closeOT();
      closeDF();
      closeTC();
      closeOTA();
    },
  },
  async created() {
    if (!isloginDisabled()) {
      const authValidatedPromise = await waitForEvent(eventType.authValidated);
      if (authValidatedPromise && !authorizeRole(Role.ADMIN)) {
        console.log(
          `Event invoke authValidated at ${getCurrentDateTimeWithMilliseconds()} with ${JSON.stringify(
            authValidatedPromise
          )}`
        );
        this.items = this.items.filter((item) => item.title !== "Simulation");
      }
    }

    this.items.sort((a, b) => {
      if (a.order === undefined) return 0;
      if (b.order === undefined) return 0;
      return a.order - b.order;
    });
  },

  data: () => ({
    items: [
      // Data goes here
      {
        title: "Insight",
        icon: "mdi-chart-line",
        path: "/insight",
        submenu: {
          title: "Insight",
          icon: "mdi-chart-line",
          path: "/insight",
          items: [
            { title: "Matrix Dashboard", icon: "mdi-routes", path: "/insight" },
          ],
        },
      },

      {
        title: "Operation",
        icon: "mdi-account-wrench",
        path: "/operation",
      },

      { title: "Skycar", icon: "mdi-taxi", path: "/skycar" },
      { title: "Station", icon: "mdi-table-row-remove", path: "/station" },
      { title: "Order", icon: "mdi-border-color", path: "/order" },
      {
        title: "Charging Service",
        icon: "mdi-battery-charging-medium",
        path: "/charging",
      },
      { title: "Obstacle", icon: "mdi-skull-crossbones", path: "/obstacle" },
      { title: "Service Door", icon: "mdi-door-open", path: "/servicedoor" },
      {
        title: "Visualisation",
        icon: "mdi-animation",
        path: "/visualisation",
      },

      {
        title: "Preventive Maintenance",
        icon: "mdi-robot-industrial",
        path: "/preventive/manage",
      },
      {
        title: "Skycar Log",
        icon: "mdi-radar",
        path: "/skycarLog",
      },
      { title: "SM Order", icon: "mdi-border-color", path: "/smorder" },
      {
        title: "SM Log",
        icon: "mdi-math-log",
        submenu: {
          title: "Log",
          path: "/log",
          items: [
            {
              title: "SM Order Log",
              icon: "mdi-clipboard-list-outline",
              path: Route.SmOrderLogs,
            },
            {
              title: "Storage Log",
              icon: "mdi-view-list",
              path: Route.StorageLogs,
            },
          ],
        },
      },
      { title: "Skycar OTA", icon: "mdi-flash", path: "/skycarOTA" },


      {
        title: "Stack Overview",
        icon: "mdi-cube",
        path: "/stackoverview",
      },

      {
        title: "SM Storages",
        icon: "mdi-archive-marker",
        submenu: {
          title: "Storages",
          path: "/storage",
          items: [
            {
              title: "Overview",
              icon: "mdi-archive-marker",
              path: "/storage",
            },
            {
              title: "Call Store Bins",
              icon: "mdi-archive-marker",
              path: "/callStoreBin",
            },
          ],
        },
      },
      {
        title: "Webhook",
        icon: "mdi-webhook",
        path: "/webhook"
      },
    
      // Setting goes here
      {
  title: "Setting",
    icon: "mdi-cog",
      path: "/mdi-toggle-switch",
        submenu: {
    title: "Setting",
      icon: "Setting",
        path: "/planningparameters",
          items: [
            {
              title: "TC Planning",
              icon: "mdi-toggle-switch",
              path: "/planningparameters",
            },
            { title: "SM Setting", icon: "mdi-cog", path: "/settings" },
          ],
        },
},

{
  title: "Simulation",
    icon: "mdi-animation-play",
      path: "/simulation",
        order: 1,
      },

{
  title: "Analysis",
    icon: "mdi-chart-line",
      path: "/analysis",
      },
    ],
  }),
};
</script>

<style>
.large-text {
  font-size: 18px;
  /* Adjust the font size as needed */
}
</style>
