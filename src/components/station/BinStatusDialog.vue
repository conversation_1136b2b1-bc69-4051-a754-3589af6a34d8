<template>
  <v-dialog
    v-if="stationBinStatus.bool"
    v-model="stationBinStatus.bool"
    max-width="700"
  >
    <v-card>
      <v-toolbar dark :color="stationBinStatus.color">
        <v-toolbar-title>Station Recovery</v-toolbar-title>
      </v-toolbar>
      <v-progress-linear
            v-if="!stationBinStatus.doneSync"
            color="green"
            indeterminate
      ></v-progress-linear>
      <v-card-text v-if="!stationBinStatus.error" class="pt-6">
        <h4>
          Please check the physical bin location according to the layout below.
        </h4>
        <h4>
          Click on each index after making sure the index status is tally.
        </h4>
        <h4>
          Please move the bin to the correct position if it is not tally.
        </h4>
        <h4>
          Click on <span style="color:green">CONFIRM</span> button to start
          recovery process for current station
        </h4>
        <!-- Display station layout according to staiton cell  -->
        <v-container
          v-if="stationBinStatus.response.data.length === 0"
          class="my-5"
        >
          <h5>Recovery Mode is off</h5>
        </v-container>
        <!-- cell 3 -->
        <v-container
          v-else-if="stationBinStatus.stationCell === 3"
          class="my-5"
        >
          <v-row justify="center" class="mb-3">
            <h4 class="mr-3">2</h4>
            <v-btn
              class="mr-3"
              :color="getBtnColor(2)"
              @click="onBtnClick(2)"
              >{{ stationBinStatus.response.data[2] }}</v-btn
            >
          </v-row>
          <v-row justify="center" class="mb-3">
            <h4 class="mr-3">1</h4>
            <v-btn
              class="mr-3"
              :color="getBtnColor(1)"
              @click="onBtnClick(1)"
              >{{ stationBinStatus.response.data[1] }}</v-btn
            >
          </v-row>
          <v-row justify="center" class="mb-3">
            <h4 class="mr-2">W</h4>
            <v-btn
              :color="getBtnColor(0, true)"
              @click="onBtnClick(0)"
              class="mr-3"
            >
              {{ stationBinStatus.response.data[0] }}</v-btn
            >
          </v-row>
        </v-container>
        <!-- cell 6 -->
        <v-container
          v-else-if="
            stationBinStatus.stationCell === 6 &&
              stationBinStatus.station.type !== 'BRIDGE'
          "
          class="my-5"
        >
          <v-row justify="center" class="mb-3">
            <h4 class="mr-3">0</h4>
            <v-btn
              class="mr-3"
              :color="getBtnColor(0)"
              @click="onBtnClick(0)"
              >{{ stationBinStatus.response.data[0] }}</v-btn
            >
            <v-btn :color="getBtnColor(5)" @click="onBtnClick(5)">{{
              stationBinStatus.response.data[5]
            }}</v-btn>
            <h4 class="ml-3">5</h4>
          </v-row>
          <v-row justify="center" class="mb-3">
            <h4 class="mr-3">1</h4>
            <v-btn
              class="mr-3"
              :color="getBtnColor(1)"
              @click="onBtnClick(1)"
              >{{ stationBinStatus.response.data[1] }}</v-btn
            >
            <v-btn :color="getBtnColor(4)" @click="onBtnClick(4)">{{
              stationBinStatus.response.data[4]
            }}</v-btn>
            <h4 class="ml-3">4</h4>
          </v-row>
          <v-row justify="center" class="mb-3">
            <h4 class="mr-4">2</h4>
            <v-btn
              class="mr-3"
              :color="getBtnColor(2)"
              @click="onBtnClick(2)"
              >{{ stationBinStatus.response.data[2] }}</v-btn
            >
            <v-btn :color="getBtnColor(3, true)" @click="onBtnClick(3)">{{
              stationBinStatus.response.data[3]
            }}</v-btn>
            <h4 class="ml-3">W</h4>
          </v-row>
        </v-container>
        <!-- cell 7  -->
        <v-container
          v-else-if="stationBinStatus.stationCell === 7"
          class="my-5"
        >
          <v-row justify="center" class="mb-3">
            <h4 class="mr-3">0</h4>
            <v-btn
              class="mr-3"
              :color="getBtnColor(0)"
              @click="onBtnClick(0)"
              >{{ stationBinStatus.response.data[0] }}</v-btn
            >
            <v-btn class="mr-3" :style="{ opacity: 0 }"></v-btn>
            <v-btn :color="getBtnColor(6)" @click="onBtnClick(6)">{{
              stationBinStatus.response.data[6]
            }}</v-btn>
            <h4 class="ml-3">6</h4>
          </v-row>
          <v-row justify="center" class="mb-3">
            <h4 class="mr-3">1</h4>
            <v-btn
              class="mr-3"
              :color="getBtnColor(1)"
              @click="onBtnClick(1)"
              >{{ stationBinStatus.response.data[1] }}</v-btn
            >
            <v-btn class="mr-3" :style="{ opacity: 0 }"></v-btn>
            <v-btn :color="getBtnColor(5)" @click="onBtnClick(5)">{{
              stationBinStatus.response.data[5]
            }}</v-btn>
            <h4 class="ml-3">5</h4>
          </v-row>
          <v-row justify="center" class="mb-3">
            <h4 class="mr-4">2</h4>
            <v-btn
              class="mr-3"
              :color="getBtnColor(2)"
              @click="onBtnClick(2)"
              >{{ stationBinStatus.response.data[2] }}</v-btn
            >
            <v-btn
              :color="getBtnColor(3, true)"
              @click="onBtnClick(3)"
              class="mr-3"
              >{{ stationBinStatus.response.data[3] }}</v-btn
            >
            <v-btn :color="getBtnColor(4)" @click="onBtnClick(4)">{{
              stationBinStatus.response.data[4]
            }}</v-btn>
            <h4 class="ml-3">4</h4>
          </v-row>
          <v-row justify="center">
            <h4>W</h4>
          </v-row>
        </v-container>
        <!-- cell 12 -->
        <v-container
          v-else-if="stationBinStatus.stationCell === 12"
          class="my-5"
        >
          <v-row justify="center" class="mb-3">
            <h4 class="mr-3 ml-1">0</h4>
            <v-btn
              class="mr-3"
              :color="getBtnColor(0)"
              @click="onBtnClick(0)"
              >{{ stationBinStatus.response.data[0] }}</v-btn
            >
            <v-btn :color="getBtnColor(11)" @click="onBtnClick(11)">{{
              stationBinStatus.response.data[11]
            }}</v-btn>
            <h4 class="ml-3">11</h4>
          </v-row>
          <v-row justify="center" class="mb-3">
            <h4 class="mr-3 ml-1">1</h4>
            <v-btn
              class="mr-3"
              :color="getBtnColor(1)"
              @click="onBtnClick(1)"
              >{{ stationBinStatus.response.data[1] }}</v-btn
            >
            <v-btn :color="getBtnColor(10)" @click="onBtnClick(10)">{{
              stationBinStatus.response.data[10]
            }}</v-btn>
            <h4 class="ml-3">10</h4>
          </v-row>
          <v-row justify="center" class="mb-3">
            <h4 class="mr-3">3</h4>
            <v-btn
              class="mr-3"
              :color="getBtnColor(2)"
              @click="onBtnClick(2)"
              >{{ stationBinStatus.response.data[2] }}</v-btn
            >
            <v-btn :color="getBtnColor(9)" @click="onBtnClick(9)">{{
              stationBinStatus.response.data[9]
            }}</v-btn>
            <h4 class="ml-3">9</h4>
          </v-row>
          <v-row justify="center" class="mb-3">
            <h4 class="mr-3">3</h4>
            <v-btn
              class="mr-3"
              :color="getBtnColor(3)"
              @click="onBtnClick(3)"
              >{{ stationBinStatus.response.data[3] }}</v-btn
            >
            <v-btn :color="getBtnColor(8)" @click="onBtnClick(8)">{{
              stationBinStatus.response.data[8]
            }}</v-btn>
            <h4 class="ml-3">8</h4>
          </v-row>
          <v-row justify="center" class="mb-3">
            <h4 class="mr-3">4</h4>
            <v-btn
              class="mr-3"
              :color="getBtnColor(4)"
              @click="onBtnClick(4)"
              >{{ stationBinStatus.response.data[4] }}</v-btn
            >
            <v-btn :color="getBtnColor(7)" @click="onBtnClick(7)">{{
              stationBinStatus.response.data[7]
            }}</v-btn>
            <h4 class="ml-3">7</h4>
          </v-row>
          <v-row justify="center" class="mb-3">
            <h4 class="mr-4">5</h4>
            <v-btn
              class="mr-3"
              :color="getBtnColor(5)"
              @click="onBtnClick(5)"
              >{{ stationBinStatus.response.data[5] }}</v-btn
            >
            <v-btn :color="getBtnColor(6, true)" @click="onBtnClick(6)">{{
              stationBinStatus.response.data[6]
            }}</v-btn>
            <h4 class="ml-3">W</h4>
          </v-row>
        </v-container>
      </v-card-text>
      <v-card-text v-else-if="stationBinStatus.error" class="pt-6">
        <v-card variant="tonal">
        <v-card-subtitle>
          <v-chip
          dark
          color="red">
            Invalid bin status input
          </v-chip>
        </v-card-subtitle>
          <v-card-text>
            <h4>
              <v-icon
                color="red">
                  mdi-alert-circle
              </v-icon>
              {{ stationBinStatus.error_msg }}
            </h4>
          </v-card-text>
        </v-card>
        <v-divider class="mt-7"></v-divider>
        <h3 class="mt-5">
          Help us out by keying in the station status, will try to recover from that.
        </h3>
        <v-row>
          <v-text-field
          light
          label="Ex: 763|||2578||1238"
          class="mt-5 ml-5 mr-2"
          v-model="stationBinStatus.userKeyInStatus"
          >
          Bin Status
        </v-text-field>
        <v-btn
        class="mt-7 mr-5"
        @click="mockBinStatus(stationBinStatus.station_id,stationBinStatus.userKeyInStatus)">
          SEND
        </v-btn>
      </v-row>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn
          v-if="!stationBinStatus.error"
          color="green darken-1"
          text
          :disabled="dummyRecoveryChecking()"
          @click="dummyRecovery(stationBinStatus.station_id)"
          >Confirm
        </v-btn>
        <v-btn
          color="green darken-1"
          text
          @click="stationBinStatus.bool = false"
          >Cancel
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
import { getHccUrl, getRequestHeader } from "../../helper/common";
import axios from "axios";


export default {
  props: {
    stationBinStatus: Object,
  },
  methods: {
    getBtnColor(index, worker) {
      if (this.stationBinStatus.checkingCell[index]) {
        return worker ? "#76FF03" : "green";
      } else {
        return worker ? "#90A4AE" : "blue";
      }
    },
    dummyRecoveryChecking() {
      let allTrue = this.stationBinStatus.checkingCell.every(
        (element) => element === true
      );
      return !allTrue;
    },
  },
  data: () => ({
    dummyRecovery: async function(station_id) {
      let here = this.stationBinStatus;
      here.bool = false;
      let hwxHost = `${getHccUrl()}/cube/operation/dummy_recovery`;
      let data = {
        station_code: station_id,
        bin_list: here.response.data,
      };
      let res = await postHcc(data, hwxHost);
      if (res.status) {
        this.$awn.info(`Set recovery mode for ST${station_id}`);
      } else {
        this.$awn.alert(
          `Fail to set recovert mode for ST${station_id} due to ${res.message}`
        );
      }
    },
    onBtnClick: async function(index) {
      let here = this.stationBinStatus;
      this.$set(here.checkingCell, index, !here.checkingCell[index]);
    },
    mockBinStatus: async function(station_id,binStatus){
      let here = this.stationBinStatus
      here.doneSync = false
      var json = {
        station_code: station_id,
        message: `ST,${station_id},S,${binStatus};`
      }
      var hwxHost = new URL(`${getHccUrl()}/cube/mock/mock_station_msg`);
      var requestOptions = {
        method: "POST",
        body: JSON.stringify(json),
        headers: getRequestHeader(),
      };
      try {
        let res = await fetch(hwxHost, requestOptions);
        await this.showBinStatusDialog(station_id)
        return res;
      } catch (error) {
        this.$awn.alert('Fail to send bin status, please check HCC module')
      }
    },
    showBinStatusDialog: async function(station_id) {
      let here = this.stationBinStatus;
      here.response = await getStationQ("station_plc_status", {
        code: station_id,
      });
      if (!here.response.status) {
        here.color = "red";
        here.error = true;
        here.error_msg = here.response.message;
      } else {
        here.color = "#1565C0";
        (here.error = false), (here.error_msg = null);
        here.checkingCell = new Array(here.stationCell).fill(false);
      }
      here.doneSync = true;
    },
  }),
};

async function postHcc(body, url) {
  var requestOptions = {
    method: "POST",
    body: JSON.stringify(body),
    headers: getRequestHeader(),
  };
  try {
    let res = await fetch(url, requestOptions);
    return res.json();
  } catch (error) {
    return {
      data: [],
      error: error,
    };
  }
}

async function getStationQ(url, params) {
  let hccUrl = getHccUrl();
  var searchParams = new URLSearchParams(params);
  var hwxHost = `${hccUrl}/cube/station/${url}`;
  try {
    const res = await axios.get(`${hwxHost}?${searchParams}`, {
      headers: getRequestHeader(),
      validateStatus: function(status) {
        return (status >= 200 && status < 300) || status == 400; // Only consider 2xx (success) codes as valid
      },
    });
    return res.data;
  } catch (error) {
    return {
      data: [],
      error: error,
    };
  }
}
</script>
