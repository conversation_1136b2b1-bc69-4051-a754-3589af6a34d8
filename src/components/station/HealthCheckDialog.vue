<template>
  <v-dialog v-model="dialog" max-width="1000">
    <v-card>
      <v-toolbar color="blue darken-2" dark>
        <v-toolbar-title>Station Health Check</v-toolbar-title>
        <v-spacer></v-spacer>
        <v-btn icon @click="$emit('input', false)"><v-icon>mdi-close</v-icon></v-btn>
      </v-toolbar>
      <v-card-text>
        <h2 v-if="!hasData" class="text-center grey--text">
          <v-icon color="green" left style="vertical-align: middle; margin-right: 6px;">mdi-check-circle</v-icon>
          No issues found.
        </h2>
        <v-container v-else fluid>
          <h2 class="my-2 mb-3">
            <span aria-label="info" role="img" style="margin-right: 6px;">ℹ️</span>
            Check why some station is not moving or skycar is idling above station. 
          </h2>
          <div v-for="(issues, station) in healthcheckData" :key="station" class="mb-5 station-group">
            <div
              class="station-header mb-3 clickable-header"
              @click="toggleStation(station)"
            >
              <v-row align="center" no-gutters>
                <v-col cols="auto">
                  <v-icon color="white" class="mr-2" size="24">mdi-server</v-icon>
                </v-col>
                <v-col>
                  <span class="station-title">Station {{ station }}</span>
                  <span class="station-issues-count">
                    ({{ issues.length }} issue{{ issues.length > 1 ? 's' : '' }})
                  </span>
                </v-col>
                <v-col cols="auto">
                  <v-icon
                    color="white"
                    size="20"
                    class="expand-icon"
                    :class="{ 'expanded': isStationExpanded(station) }"
                  >
                    {{ isStationExpanded(station) ? 'mdi-chevron-up' : 'mdi-chevron-down' }}
                  </v-icon>
                </v-col>
              </v-row>
            </div>
            <v-expand-transition>
              <div v-show="isStationExpanded(station)" class="station-issues-wrapper">
            <v-row v-for="(issue, idx) in issues" :key="idx">
              <v-col cols="12">
                <v-sheet class="pa-3 station-issue-container" color="grey darken-3" rounded>
                  <!-- Description Row - Full Width -->
                  <v-row>
                    <v-col cols="12">
                      <v-card outlined color="grey darken-2" class="pa-3 issue-description-card">
                        <div class="d-flex align-center mb-2">
                          <v-icon class="mr-2" color="red lighten-1" size="18">mdi-alert-circle</v-icon>
                          <span class="text-subtitle2 white--text text--lighten-2 font-weight-bold">
                            Issue Description
                          </span>
                        </div>
                        <div class="text-body-1 white--text font-weight-medium description-text">
                          {{ issue.description }}
                        </div>
                      </v-card>
                    </v-col>
                  </v-row>

                  <!-- Three Fields Row - Smaller Height, Center Aligned -->
                  <v-row class="mb-2" align="center">
                    <v-col cols="12" sm="4" md="4">
                      <v-card outlined color="grey darken-2" class="pa-2 issue-info-card-small text-center">
                        <div class="d-flex align-center justify-center mb-1">
                          <v-icon class="mr-1" color="blue lighten-1" size="14">mdi-archive</v-icon>
                          <span class="text-caption grey--text text--lighten-2">Storage</span>
                        </div>
                        <div class="text-subtitle-2 white--text font-weight-medium">{{ issue.storage_code }}</div>
                      </v-card>
                    </v-col>
                    <v-col cols="12" sm="4" md="4">
                      <v-card outlined color="grey darken-2" class="pa-2 issue-info-card-small text-center">
                        <div class="d-flex align-center justify-center mb-1">
                          <v-icon class="mr-1" color="orange lighten-1" size="14">mdi-tag</v-icon>
                          <span class="text-caption grey--text text--lighten-2">Type</span>
                        </div>
                        <div class="text-subtitle-2 white--text font-weight-medium">{{ issue.type }}</div>
                      </v-card>
                    </v-col>
                    <v-col cols="12" sm="4" md="4">
                      <v-card outlined color="grey darken-2" class="pa-2 issue-info-card-small text-center">
                        <div class="d-flex align-center justify-center mb-1">
                          <v-icon class="mr-1" color="green lighten-1" size="14">mdi-account-circle</v-icon>
                          <span class="text-caption grey--text text--lighten-2">Responsible</span>
                        </div>
                        <div class="text-subtitle-2 white--text font-weight-medium">{{ issue.responsible_party }}</div>
                      </v-card>
                    </v-col>
                  </v-row>

                  <!-- Details Section - Only show if details exist -->
                  <v-row v-if="issue.details && Object.keys(issue.details).length > 0" class="mt-2">
                    <v-col cols="12">
                      <v-card outlined color="grey darken-2" class="pa-2">
                        <div class="d-flex align-center mb-2">
                          <v-icon class="mr-1" color="blue lighten-1" size="16">mdi-information</v-icon>
                          <span class="text-subtitle2 white--text font-weight-bold">Details</span>
                        </div>
                        <v-simple-table dense dark class="compact-table">
                          <tbody>
                            <tr v-for="(val, key) in issue.details" :key="key">
                              <td class="font-weight-bold grey--text text--lighten-2 compact-cell">{{ key }}</td>
                              <td class="white--text compact-cell">{{ val }}</td>
                            </tr>
                          </tbody>
                        </v-simple-table>
                      </v-card>
                    </v-col>
                  </v-row>
                </v-sheet>
              </v-col>
              <v-col cols="12" v-if="idx < issues.length - 1">
                <v-divider class="my-2"></v-divider>
              </v-col>
            </v-row>
              </div> <!-- Close station-issues-wrapper -->
            </v-expand-transition>
          </div> <!-- Close station-group -->
        </v-container>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn color="green darken-1" text @click="$emit('input', false)">Close</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: "HealthCheckDialog",
  props: {
    value: {
      type: Boolean,
      required: true
    },
    healthcheckData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      expandedStations: {}
    }
  },
  computed: {
    dialog: {
      get() { return this.value },
      set(val) { this.$emit("input", val) }
    },
    hasData() {
      return this.healthcheckData && Object.keys(this.healthcheckData).length > 0;
    }
  },
  methods: {
    toggleStation(station) {
      this.$set(this.expandedStations, station, !this.expandedStations[station]);
    },
    isStationExpanded(station) {
      // Default to expanded if not set
      return this.expandedStations[station] !== false;
    }
  },
  watch: {
    // Reset expanded state when dialog opens with new data
    value(newVal) {
      if (newVal) {
        this.expandedStations = {};
      }
    }
  }
}
</script>

<style scoped>
.v-chip {
  font-size: 14px;
}
/* Station grouping styles */
.station-group {
  border: 2px solid #424242;
  border-radius: 8px;
  padding: 16px;
  background: rgba(66, 66, 66, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.station-header {
  background: #ff0000;
  color: #fff;
  border-radius: 6px;
  padding: 12px 20px;
  font-size: 18px;
  font-weight: bold;
  box-shadow: 0 3px 8px rgba(255, 0, 0, 0.3);
  margin-bottom: 16px;
}
.station-title {
  font-size: 22px;
  font-weight: bold;
  color: #fff;
}
.station-issues-count {
  font-size: 16px;
  color: #ffffff;
  margin-left: 12px;
  opacity: 0.9;
}

.station-issues-wrapper {
  border-left: 3px solid #ff0000;
  padding-left: 16px;
  margin-left: 8px;
}
/* Small card styles for storage, type, responsible party */
.issue-info-card-small {
  height: 100%;
  min-height: 50px;
  max-height: 70px;
  transition: all 0.2s ease-in-out;
}
.issue-info-card-small:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}
.issue-info-card-small .text-caption {
  font-size: 0.65rem;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}
.issue-info-card-small .text-subtitle-2 {
  font-size: 0.8rem;
  line-height: 1.1;
  word-break: break-word;
}

/* Description card - full width, prominent */
.issue-description-card {
  transition: all 0.2s ease-in-out;
  border: 2px solid #180606 !important;
}
.issue-description-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(198, 40, 40, 0.3);
}
.description-text {
  font-size: 0.95rem;
  line-height: 1.4;
  word-break: break-word;
  white-space: normal;
  overflow-wrap: break-word;
}

/* Station issue container */
.station-issue-container {
  border-left: 4px solid #ff0000;
  margin-bottom: 12px;
  background: rgba(66, 66, 66, 0.3) !important;
}

/* Compact table styles */
.compact-table {
  font-size: 0.8rem;
}
.compact-cell {
  padding: 4px 8px !important;
  font-size: 0.8rem;
}
</style>