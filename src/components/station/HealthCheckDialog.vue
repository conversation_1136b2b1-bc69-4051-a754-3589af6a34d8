<template>
  <v-dialog v-model="dialog" max-width="1000">
    <v-card>
      <v-toolbar color="blue darken-2" dark>
        <v-toolbar-title>Station Health Check</v-toolbar-title>
        <v-spacer></v-spacer>
        <v-btn icon @click="$emit('input', false)"><v-icon>mdi-close</v-icon></v-btn>
      </v-toolbar>
      <v-card-text>
        <h2 v-if="!hasData" class="text-center grey--text mt-6">
          <v-icon color="green" left style="vertical-align: middle; margin-right: 6px;">mdi-check-circle</v-icon>
          No issues found.
        </h2>
        <v-container v-else fluid>
          <h2 class="my-2 mb-3">
            <span aria-label="info" role="img" style="margin-right: 6px;">ℹ️</span>
            Check why some station is not moving or skycar is idling above station. 
          </h2>
          <div v-for="(issues, station) in healthcheckData" :key="station" class="mb-6">
            <div class="station-header mb-2">
              <v-row align="center" no-gutters>
                <v-col cols="auto">
                  <v-icon color="white" class="mr-2">mdi-server</v-icon>
                </v-col>
                <v-col>
                  <span class="station-title">Station {{ station }}</span>
                  <span class="station-issues-count">
                    ({{ issues.length }} issue{{ issues.length > 1 ? 's' : '' }})
                  </span>
                </v-col>
              </v-row>
            </div>
            <v-divider></v-divider>
            <v-row v-for="(issue, idx) in issues" :key="idx" class="mb-4 mt-4">
              <v-col cols="12">
                <v-sheet class="pa-4" color="grey darken-3" rounded>
                  <!-- Issue Information Cards -->
                  <v-row class="mb-4">
                    <v-col cols="12" sm="6" md="3">
                      <v-card outlined color="grey darken-2" class="pa-3 issue-info-card">
                        <div class="d-flex align-center mb-2">
                          <v-icon class="mr-2" color="blue lighten-1" size="20">mdi-archive</v-icon>
                          <span class="text-caption grey--text text--lighten-2">Storage Code</span>
                        </div>
                        <div class="text-h6 white--text font-weight-medium">{{ issue.storage_code }}</div>
                      </v-card>
                    </v-col>
                    <v-col cols="12" sm="6" md="3">
                      <v-card outlined color="grey darken-2" class="pa-3 issue-info-card">
                        <div class="d-flex align-center mb-2">
                          <v-icon class="mr-2" color="orange lighten-1" size="20">mdi-tag</v-icon>
                          <span class="text-caption grey--text text--lighten-2">Type</span>
                        </div>
                        <div class="text-h6 white--text font-weight-medium">{{ issue.type }}</div>
                      </v-card>
                    </v-col>
                    <v-col cols="12" sm="6" md="3">
                      <v-card outlined color="grey darken-2" class="pa-3 issue-info-card">
                        <div class="d-flex align-center mb-2">
                          <v-icon class="mr-2" color="green lighten-1" size="20">mdi-account-circle</v-icon>
                          <span class="text-caption grey--text text--lighten-2">Responsible Party</span>
                        </div>
                        <div class="text-h6 white--text font-weight-medium">{{ issue.responsible_party }}</div>
                      </v-card>
                    </v-col>
                    <v-col cols="12" sm="6" md="3">
                      <v-card outlined color="grey darken-2" class="pa-3 issue-info-card">
                        <div class="d-flex align-center mb-2">
                          <v-icon class="mr-2" color="red lighten-1" size="20">mdi-alert-circle</v-icon>
                          <span class="text-caption grey--text text--lighten-2">Description</span>
                        </div>
                        <div class="text-body-1 white--text font-weight-medium">{{ issue.description }}</div>
                      </v-card>
                    </v-col>
                  </v-row>

                  <!-- Details Section - Only show if details exist -->
                  <v-row v-if="issue.details && Object.keys(issue.details).length > 0">
                    <v-col cols="12">
                      <v-card outlined color="grey darken-2" class="pa-3">
                        <div class="d-flex align-center mb-3">
                          <v-icon class="mr-2" color="blue lighten-1" size="20">mdi-information</v-icon>
                          <span class="text-subtitle2 white--text font-weight-bold">Details</span>
                        </div>
                        <v-simple-table dense dark>
                          <tbody>
                            <tr v-for="(val, key) in issue.details" :key="key">
                              <td class="font-weight-bold grey--text text--lighten-2">{{ key }}</td>
                              <td class="white--text">{{ val }}</td>
                            </tr>
                          </tbody>
                        </v-simple-table>
                      </v-card>
                    </v-col>
                  </v-row>
                </v-sheet>
              </v-col>
              <v-col cols="12" v-if="idx < issues.length - 1">
                <v-divider class="my-2"></v-divider>
              </v-col>
            </v-row>
          </div>
        </v-container>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn color="green darken-1" text @click="$emit('input', false)">Close</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: "HealthCheckDialog",
  props: {
    value: {
      type: Boolean,
      required: true
    },
    healthcheckData: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    dialog: {
      get() { return this.value },
      set(val) { this.$emit("input", val) }
    },
    hasData() {
      return this.healthcheckData && Object.keys(this.healthcheckData).length > 0;
    }
  }
}
</script>

<style scoped>
.v-chip {
  font-size: 14px;
}
.station-header {
  background: #ff0000;
  color: #fff;
  border-radius: 6px;
  padding: 10px 18px;
  font-size: 18px;
  font-weight: bold;
  box-shadow: 0 2px 6px rgba(25, 118, 210, 0.08);
}
.station-title {
  font-size: 20px;
  font-weight: bold;
  color: #fff;
}
.station-issues-count {
  font-size: 15px;
  color: #ffffff;
  margin-left: 10px;
}
.issue-info-card {
  height: 100%;
  transition: all 0.2s ease-in-out;
}
.issue-info-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}
.issue-info-card .text-caption {
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
.issue-info-card .text-h6 {
  line-height: 1.2;
  word-break: break-word;
}
.issue-info-card .text-body-1 {
  line-height: 1.3;
  word-break: break-word;
}
</style>