<template>
  <v-dialog v-model="dialog" max-width="1000">
    <v-card>
      <v-toolbar color="blue darken-2" dark>
        <v-toolbar-title>Station Health Check</v-toolbar-title>
        <v-spacer></v-spacer>
        <v-btn icon @click="$emit('input', false)"><v-icon>mdi-close</v-icon></v-btn>
      </v-toolbar>
      <v-card-text>
        <h2 v-if="!hasData" class="text-center grey--text mt-6">
          <v-icon color="green" left style="vertical-align: middle; margin-right: 6px;">mdi-check-circle</v-icon>
          No issues found.
        </h2>
        <v-container v-else fluid>
          <div class="mt-2 mb-4">
            <v-chip
              v-for="station in stationCodes"
              :key="station"
              class="mr-2 mb-2"
              :color="selectedStation === station ? 'primary' : ''"
              @click="toggleStationFilter(station)"
            >
              ST{{ station }}
            </v-chip>
          </div>

          <h3 class="my-2 mb-3">
            <span aria-label="info" role="img" style="margin-right: 6px;">ℹ️</span>
            Check why some station is not moving or skycar is idling above station. 
          </h3>
          <div v-for="(issues, station) in filteredHealthCheckData" :key="station" class="mb-3 station-group">
            <div
              class="station-header mb-2 clickable-header"
              @click="toggleStation(station)"
            >
              <v-row align="center" no-gutters>
                <v-col cols="auto">
                  <v-icon color="white" class="mr-2" size="24">mdi-server</v-icon>
                </v-col>
                <v-col>
                  <span class="station-title">Station {{ station }}</span>
                  <span class="station-issues-count">
                    ({{ issues.length }} issue{{ issues.length > 1 ? 's' : '' }})
                  </span>
                </v-col>
                <v-col cols="auto">
                  <v-icon
                    color="white"
                    size="20"
                    class="expand-icon"
                    :class="{ 'expanded': isStationExpanded(station) }"
                  >
                    {{ isStationExpanded(station) ? 'mdi-chevron-up' : 'mdi-chevron-down' }}
                  </v-icon>
                </v-col>
              </v-row>
            </div>
            <v-expand-transition>
              <div v-show="isStationExpanded(station)" class="station-issues-wrapper">
            <v-row v-for="(issue, idx) in issues" :key="idx" class="compact-issue-row">
              <v-col cols="12">
                <v-sheet class="pa-2 station-issue-container-compact" color="grey darken-3" rounded>
                  <!-- Compact Single Row Layout -->
                  <v-row no-gutters align="center" class="compact-issue-content">
                    <!-- Description takes more space but in same row -->
                    <v-col cols="12" md="7" class="pr-md-2">
                      <div class="issue-description-compact">
                        <div class="d-flex align-center mb-1">
                          <v-icon class="mr-1" color="red lighten-1" size="14">mdi-alert-circle</v-icon>
                          <span class="text-caption red--text text--lighten-2 font-weight-bold">Description</span>
                        </div>
                        <div class="text-body-2 white--text description-text-compact">
                          {{ issue.description }}
                        </div>
                      </div>
                    </v-col>

                    <!-- Three fields in compact horizontal layout -->
                    <v-col cols="12" md="5" class="pl-md-2">
                      <v-row no-gutters class="compact-fields-row">
                        <v-col cols="4" class="px-1">
                          <div class="issue-field-compact text-center">
                            <div class="field-label">
                              <v-icon color="blue lighten-1" size="12" class="mr-1">mdi-archive</v-icon>
                              <span class="text-caption grey--text">Storage</span>
                            </div>
                            <div class="field-value text-caption white--text font-weight-medium">
                              {{ issue.storage_code }}
                            </div>
                          </div>
                        </v-col>
                        <v-col cols="4" class="px-1">
                          <div class="issue-field-compact text-center">
                            <div class="field-label">
                              <v-icon color="orange lighten-1" size="12" class="mr-1">mdi-tag</v-icon>
                              <span class="text-caption grey--text">Type</span>
                            </div>
                            <div class="field-value text-caption white--text font-weight-medium">
                              {{ issue.type }}
                            </div>
                          </div>
                        </v-col>
                        <v-col cols="4" class="px-1">
                          <div class="issue-field-compact text-center">
                            <div class="field-label">
                              <v-icon color="green lighten-1" size="12" class="mr-1">mdi-account-circle</v-icon>
                              <span class="text-caption grey--text">Responsible</span>
                            </div>
                            <div class="field-value text-caption white--text font-weight-medium">
                              {{ issue.responsible_party }}
                            </div>
                          </div>
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>

                  <!-- Compact Details Section -->
                  <v-row v-if="issue.details && Object.keys(issue.details).length > 0" class="mt-1" no-gutters>
                    <v-col cols="12">
                      <div class="details-section-compact">
                        <div class="d-flex align-center mb-1">
                          <v-icon class="mr-1" color="blue lighten-1" size="12">mdi-information</v-icon>
                          <span class="text-caption blue--text text--lighten-2 font-weight-bold">Details</span>
                        </div>
                        <div class="details-content">
                          <span v-for="(val, key, index) in issue.details" :key="key" class="detail-item">
                            <span class="detail-key">{{ key }}:</span>
                            <span class="detail-value">{{ val }}</span>
                            <span v-if="index < Object.keys(issue.details).length - 1" class="detail-separator">•</span>
                          </span>
                        </div>
                      </div>
                    </v-col>
                  </v-row>
                </v-sheet>
              </v-col>
            </v-row>
              </div> <!-- Close station-issues-wrapper -->
            </v-expand-transition>
          </div> <!-- Close station-group -->
        </v-container>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn color="green darken-1" text @click="refreshHealthCheck">Refresh</v-btn>
        <v-btn color="green darken-1" text @click="$emit('input', false)">Close</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: "HealthCheckDialog",
  props: {
    value: {
      type: Boolean,
      required: true
    },
    healthcheckData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      expandedStations: {},
      selectedStation: null
    }
  },
  computed: {
    dialog: {
      get() { return this.value },
      set(val) { this.$emit("input", val) }
    },
    hasData() {
      return this.healthcheckData && Object.keys(this.healthcheckData).length > 0;
    },
    stationCodes() {
      return this.healthcheckData ? Object.keys(this.healthcheckData).sort((a, b) => parseInt(a) - parseInt(b)) : [];
    },
    filteredHealthCheckData() {
      if (!this.selectedStation) {
        return this.healthcheckData;
      }
      if (this.healthcheckData && this.healthcheckData[this.selectedStation]) {
        return { [this.selectedStation]: this.healthcheckData[this.selectedStation] };
      }
      return {};
    }
  },
  methods: {
    toggleStation(station) {
      this.$set(this.expandedStations, station, !this.expandedStations[station]);
    },
    isStationExpanded(station) {
      // Default to expanded if not set
      return this.expandedStations[station] !== false;
    },
    toggleStationFilter(station) {
      if (this.selectedStation === station) {
        this.selectedStation = null;
      } else {
        this.selectedStation = station;
      }
    },
    refreshHealthCheck() {
      this.$emit('refresh');
    }
  },
  watch: {
    // Reset expanded state when dialog opens with new data
    value(newVal) {
      if (newVal) {
        this.expandedStations = {};
        this.selectedStation = null;
      }
    }
  }
}
</script>

<style scoped>
.v-chip {
  font-size: 14px;
}
/* Compact station grouping styles */
.station-group {
  border: 1px solid #424242;
  border-radius: 6px;
  padding: 12px;
  background: rgba(66, 66, 66, 0.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.station-header {
  background: #ff0000;
  color: #fff;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 16px;
  font-weight: bold;
  box-shadow: 0 2px 6px rgba(255, 0, 0, 0.25);
  margin-bottom: 12px;
  transition: all 0.2s ease-in-out;
}

.clickable-header {
  cursor: pointer;
  user-select: none;
}

.clickable-header:hover {
  background: #e53935;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 0, 0, 0.4);
}

.expand-icon {
  transition: transform 0.3s ease-in-out;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}
.station-title {
  font-size: 18px;
  font-weight: bold;
  color: #fff;
}
.station-issues-count {
  font-size: 14px;
  color: #ffffff;
  margin-left: 8px;
  opacity: 0.9;
}

.station-issues-wrapper {
  border-left: 2px solid #ff0000;
  padding-left: 12px;
  margin-left: 6px;
}
/* Small card styles for storage, type, responsible party */
.issue-info-card-small {
  height: 100%;
  min-height: 50px;
  max-height: 70px;
  transition: all 0.2s ease-in-out;
}
.issue-info-card-small:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}
.issue-info-card-small .text-caption {
  font-size: 0.65rem;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}
.issue-info-card-small .text-subtitle-2 {
  font-size: 0.8rem;
  line-height: 1.1;
  word-break: break-word;
}

/* Description card - full width, prominent */
.issue-description-card {
  transition: all 0.2s ease-in-out;
  border: 2px solid #180606 !important;
}
.issue-description-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(198, 40, 40, 0.3);
}
.description-text {
  font-size: 0.95rem;
  line-height: 1.4;
  word-break: break-word;
  white-space: normal;
  overflow-wrap: break-word;
}

/* Compact issue layout */
.compact-issue-row {
  margin-bottom: 8px !important;
}

.station-issue-container-compact {
  border-left: 3px solid #ff0000;
  background: rgba(66, 66, 66, 0.2) !important;
  min-height: auto;
}

.compact-issue-content {
  min-height: 50px;
}

/* Compact description */
.issue-description-compact {
  padding: 8px 12px;
  background: rgba(198, 40, 40, 0.1);
  border-radius: 4px;
  border-left: 2px solid #c62828;
}

.description-text-compact {
  font-size: 0.8rem;
  line-height: 1.3;
  margin-top: 2px;
  word-break: break-word;
}

/* Compact fields */
.compact-fields-row {
  height: 100%;
  align-items: center;
}

.issue-field-compact {
  padding: 4px 2px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.field-label {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2px;
}

.field-label .text-caption {
  font-size: 0.6rem;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.field-value {
  font-size: 0.75rem !important;
  line-height: 1.1;
  word-break: break-word;
}

/* Compact details */
.details-section-compact {
  padding: 6px 12px;
  background: rgba(33, 150, 243, 0.1);
  border-radius: 4px;
  border-left: 2px solid #2196f3;
  margin-top: 8px;
}

.details-content {
  font-size: 0.75rem;
  line-height: 1.2;
}

.detail-item {
  display: inline;
  margin-right: 8px;
}

.detail-key {
  color: #90caf9;
  font-weight: 500;
}

.detail-value {
  color: #ffffff;
  margin-left: 4px;
  margin-right: 8px;
}

.detail-separator {
  color: #666;
  margin: 0 4px;
}

/* Compact table styles */
.compact-table {
  font-size: 0.8rem;
}
.compact-cell {
  padding: 4px 8px !important;
  font-size: 0.8rem;
}
</style>