<template>
  <v-dialog v-model="dialog" max-width="1000">
    <v-card>
      <v-toolbar color="blue darken-2" dark>
        <v-toolbar-title>Station Health Check</v-toolbar-title>
        <v-spacer></v-spacer>
        <v-btn icon @click="$emit('input', false)"><v-icon>mdi-close</v-icon></v-btn>
      </v-toolbar>
      <v-card-text>
        <h2 v-if="!hasData" class="text-center grey--text mt-6">
          <v-icon color="green" left style="vertical-align: middle; margin-right: 6px;">mdi-check-circle</v-icon>
          No issues found.
        </h2>
        <v-container v-else fluid>
          <h2 class="my-2 mb-3">
            <span aria-label="info" role="img" style="margin-right: 6px;">ℹ️</span>
            Check why some station is not moving or skycar is idling above station. 
          </h2>
          <div v-for="(issues, station) in healthcheckData" :key="station" class="mb-6">
            <div class="station-header mb-2">
              <v-row align="center" no-gutters>
                <v-col cols="auto">
                  <v-icon color="white" class="mr-2">mdi-server</v-icon>
                </v-col>
                <v-col>
                  <span class="station-title">Station {{ station }}</span>
                  <span class="station-issues-count">({{ issues.length }} issue{{ issues.length > 1 ? 's' : '' }})</span>
                </v-col>
              </v-row>
            </div>
            <v-divider></v-divider>
            <v-row v-for="(issue, idx) in issues" :key="idx" class="mb-4 mt-4">
              <v-col cols="12">
                <v-sheet class="pa-3" color="#dddddd" rounded>
                  <v-row>
                    <v-col cols="auto">
                      <v-chip color="blue" text-color="white" class="mb-1">Bin: {{ issue.storage_code }}</v-chip>
                    </v-col>
                    <v-col cols="auto">
                      <v-chip color="orange" text-color="white" class="mb-1">Type: {{ issue.type }}</v-chip>
                    </v-col>
                    <v-col cols="auto">
                      <v-chip color="green" text-color="white" class="mb-1">Responsible: {{ issue.responsible_party }}</v-chip>
                    </v-col>
                    <v-col cols="auto">
                      <v-chip color="red" text-color="white" class="mb-1">{{ issue.description }}</v-chip>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12">
                      <div class="font-weight-bold">Details:</div>
                      <v-simple-table dense>
                        <tbody>
                          <tr v-for="(val, key) in issue.details" :key="key">
                            <td class="font-weight-bold">{{ key }}</td>
                            <td>{{ val }}</td>
                          </tr>
                        </tbody>
                      </v-simple-table>
                    </v-col>
                  </v-row>
                </v-sheet>
              </v-col>
              <v-col cols="12" v-if="idx < issues.length - 1">
                <v-divider class="my-2"></v-divider>
              </v-col>
            </v-row>
          </div>
        </v-container>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn color="green darken-1" text @click="$emit('input', false)">Close</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'HealthCheckDialog',
  props: {
    value: {
      type: Boolean,
      required: true
    },
    healthcheckData: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    dialog: {
      get() { return this.value },
      set(val) { this.$emit('input', val) }
    },
    hasData() {
      return this.healthcheckData && Object.keys(this.healthcheckData).length > 0;
    }
  }
}
</script>

<style scoped>
.v-chip {
  font-size: 14px;
}
.station-header {
  background: #ff0000;
  color: #fff;
  border-radius: 6px;
  padding: 10px 18px;
  font-size: 18px;
  font-weight: bold;
  box-shadow: 0 2px 6px rgba(25, 118, 210, 0.08);
}
.station-title {
  font-size: 20px;
  font-weight: bold;
  color: #fff;
}
.station-issues-count {
  font-size: 15px;
  color: #ffffff;
  margin-left: 10px;
}
</style>