<template>
  <v-dialog
    v-if="dialogData.bool"
    v-model="dialogData.bool"
    max-width="1400"
  >
    <v-card>
      <v-toolbar dark color="blue darken-2">
        <v-icon left>mdi-server-network</v-icon>
        <v-toolbar-title>
          TC Runtime Stations ({{ stationCount }} stations)
        </v-toolbar-title>
        <v-spacer></v-spacer>
        <v-btn
          class="mx-1"
          color="green"
          @click="$emit('refresh')"
          :disabled="!dialogData.doneSync"
        >
          <v-icon left>mdi-refresh</v-icon>
          Refresh
        </v-btn>
        <v-btn
          class="mx-1"
          color="red"
          @click="closeDialog"
        >
          <v-icon left>mdi-close</v-icon>
          Close
        </v-btn>
      </v-toolbar>
      <v-progress-linear
        v-if="!dialogData.doneSync"
        color="blue"
        indeterminate
      ></v-progress-linear>
      <v-card-text class="pa-0">
        <v-data-table
          :headers="headers"
          :items="dialogData.stations"
          :items-per-page="25"
          class="elevation-1"
          sort-by="station_code"
          dense
        >
          <!-- Station Code with styling -->
          <template v-slot:[`item.station_code`]="{ item }">
            <v-chip
              small
              :color="item.is_active ? 'green' : 'red'"
              dark
            >
              {{ item.station_code }}
            </v-chip>
          </template>

          <!-- Status with icon -->
          <template v-slot:[`item.is_active`]="{ item }">
            <v-tooltip bottom>
              <template v-slot:activator="{ on, attrs }">
                <v-icon
                  v-bind="attrs"
                  v-on="on"
                  :color="item.is_active ? 'green' : 'red'"
                >
                  {{ item.is_active ? 'mdi-check-circle' : 'mdi-close-circle' }}
                </v-icon>
              </template>
              <span>{{ item.is_active ? 'Active' : 'Inactive' }}</span>
            </v-tooltip>
          </template>

          <!-- Cube with styling -->
          <template v-slot:[`item.cube`]="{ item }">
            <v-chip
              small
              color="orange"
              dark
            >
              {{ item.cube }}
            </v-chip>
          </template>

          <!-- Position with styling -->
          <template v-slot:[`item.position`]="{ item }">
            <v-chip
              small
              color="purple"
              dark
            >
              {{ item.position }}
            </v-chip>
          </template>

          <!-- Storage count with icon -->
          <template v-slot:[`item.number_of_storages`]="{ item }">
            <v-chip
              small
              :color="item.number_of_storages > 0 ? 'blue' : 'grey'"
              dark
            >
              <v-icon left small>mdi-cube</v-icon>
              {{ item.number_of_storages }}
            </v-chip>
          </template>

          <!-- Coordinates with icon -->
          <template v-slot:[`item.drop_node`]="{ item }">
            <v-chip
              small
              color="teal"
              dark
            >
              <v-icon left small>mdi-arrow-down</v-icon>
              {{ item.drop_node }}
            </v-chip>
          </template>

          <template v-slot:[`item.pick_node`]="{ item }">
            <v-chip
              small
              color="indigo"
              dark
            >
              <v-icon left small>mdi-arrow-up</v-icon>
              {{ item.pick_node }}
            </v-chip>
          </template>

          <!-- Last work arrival with time formatting -->
          <template v-slot:[`item.latest_work_arrival`]="{ item }">
            <v-tooltip bottom>
              <template v-slot:activator="{ on, attrs }">
                <span v-bind="attrs" v-on="on">
                  {{ formatTimestamp(item.latest_work_arrival) }}
                </span>
              </template>
              <span>{{ item.latest_work_arrival }}</span>
            </v-tooltip>
          </template>

          <!-- Queue status -->
          <template v-slot:[`item.queue_status`]="{ item }">
            <div class="d-flex flex-column">
              <v-tooltip bottom>
                <template v-slot:activator="{ on, attrs }">
                  <v-chip
                    x-small
                    color="cyan"
                    dark
                    class="mb-1"
                    v-bind="attrs"
                    v-on="on"
                  >
                    <v-icon left x-small>mdi-arrow-right</v-icon>
                    Pre: {{ item.pre_worker_queue.length }}
                  </v-chip>
                </template>
                <div>
                  <div class="font-weight-bold mb-1">Pre Worker Queue:</div>
                  <div v-if="item.pre_worker_queue.length === 0" class="grey--text">
                    No bins in queue
                  </div>
                  <div v-else>
                    <v-chip
                      v-for="(bin, index) in item.pre_worker_queue"
                      :key="`pre-${index}`"
                      x-small
                      color="cyan"
                      dark
                      class="ma-1"
                    >
                      {{ bin }}
                    </v-chip>
                  </div>
                </div>
              </v-tooltip>
              
              <v-tooltip bottom>
                <template v-slot:activator="{ on, attrs }">
                  <v-chip
                    x-small
                    color="#35496E"
                    dark
                    v-bind="attrs"
                    v-on="on"
                  >
                    <v-icon left x-small>mdi-arrow-left</v-icon>
                    Post: {{ item.post_worker_queue.length }}
                  </v-chip>
                </template>
                <div>
                  <div class="font-weight-bold mb-1">Post Worker Queue:</div>
                  <div v-if="item.post_worker_queue.length === 0" class="grey--text">
                    No bins in queue
                  </div>
                  <div v-else>
                    <v-chip
                      v-for="(bin, index) in item.post_worker_queue"
                      :key="`post-${index}`"
                      x-small
                      color="#35496E"
                      dark
                      class="ma-1"
                    >
                      {{ bin }}
                    </v-chip>
                  </div>
                </div>
              </v-tooltip>
            </div>
          </template>
        </v-data-table>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
import { convertStringToLocal } from "../../helper/common.js";

export default {
  name: "RuntimeStationsDialog",
  props: {
    dialogData: {
      type: Object,
      required: true,
      default: () => ({
        bool: false,
        stations: [],
        doneSync: true,
      })
    },
    stationCount: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      headers: [
        { text: "Station Code", value: "station_code", width: "120px" },
        { text: "Station No", value: "station_no", width: "150px" },
        { text: "Status", value: "is_active", width: "100px" },
        { text: "Cube", value: "cube", width: "80px" },
        { text: "IO Model", value: "io_model", width: "100px" },
        { text: "Position", value: "position", width: "100px" },
        { text: "Storage Count", value: "number_of_storages", width: "120px" },
        { text: "Drop Node", value: "drop_node", width: "120px" },
        { text: "Pick Node", value: "pick_node", width: "120px" },
        { text: "Last Work Arrival", value: "latest_work_arrival", width: "180px" },
        { text: "Queue Status", value: "queue_status", width: "150px" },
      ]
    };
  },
  methods: {
    closeDialog() {
      this.$emit("close");
    },
    formatTimestamp(timestamp) {
      return convertStringToLocal(timestamp, true);
    }
  }
};
</script>

<style scoped>
/* Add any specific styles for the dialog here */
</style> 