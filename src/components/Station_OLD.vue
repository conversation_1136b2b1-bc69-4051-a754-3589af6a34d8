
<template>
  <v-app app>
    <v-container fluid>
      <v-card dark>
        <v-col>
          <v-row>
            <v-select
              v-model="currentZone"
              :items="zones"
              @change="viewStationDetail()"
              class="ma-2"
              prepend-icon="mdi-cube"
            ></v-select>
            <v-btn 
              @click="viewStationDetail()"
              color="green"
              dark
              class="ma-2"
              :disabled="!stationDetailData.doneSync"
            >
              <span>Sync</span>
            </v-btn>
          </v-row>
        </v-col>
        <v-progress-linear
          v-if="!stationDetailData.doneSync"
          color="green"
          indeterminate
        ></v-progress-linear>
      </v-card>
      <v-data-table
        :headers="stationDetailData.headers"
        item-key="id"
        :items="stationDetailData.response.data"
        :items-per-page="-1"
        class="elevation-1"
        dark
        sort-by="id"
      >
        <!-- cell filtering -->
        <template v-slot:[`item.storage_quantity`]="{ item }">
          <v-chip
            :color="getColor(item.storage_quantity)"
            dark
          >
            {{ item.storage_quantity }}
          </v-chip>
        </template>

        <template v-slot:[`item.is_active`]="{ item }">
          <v-chip 
            :color="getStatus(item.is_active)[0]"
            dark
          >
            {{ getStatus(item.is_active)[1] }}
          </v-chip>
        </template>
        <template v-slot:[`item.is_maint`]="{ item }">
          <v-chip 
            :color="getStatus(item.is_maint)[0]"
            dark
          >
            {{ getStatus(item.is_maint)[1] }}
          </v-chip>
        </template>
        <template v-slot:[`item.movement`]="{ item }">
          <v-btn
            small
            class="mr-2"
            @click="viewStationMovement(item.station_id)"
            light
          >
            Detail
          </v-btn>
        </template>

        <template v-slot:[`item.is_enroll`]="{ item }">
          <v-btn 
            small
            :color="getStatus(item.is_enroll)[0]"
            @click="updateEnrollStation(item.station_id, item)"
            dark
          >
            {{ getStatus(item.is_enroll)[1] }}
          </v-btn>
        </template>

        <template v-slot:[`item.error_code`]="{ item }">
          <v-btn 
            small
            :color="getErrorColor(item.error_code)[0]"
            @click = "viewStationError(item.station_id)"
            dark
          >
            {{ getErrorColor(item.error_code)[1]}}
          </v-btn>
        </template>

        <template v-slot:[`item.error_jd_msg`]="{ item }">
          <v-btn
            small
            class="mr-2"
            @click="viewStationJDErrorMsg(item.station_id)"
            light
          >
            Log
          </v-btn>
        </template>

        <template v-slot:top> </template>
      </v-data-table>
      
      <v-divider></v-divider>

      <!-- view staiton error -->
      <v-dialog 
        v-if="stationErrorData.bool"
        v-model="stationErrorData.bool"
      >
        <v-card>
          <v-toolbar
            dark
          >
            <v-toolbar-title>Station Error of Station {{stationErrorData.station_id}}</v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              class="mx-1"
              color="green"
              @click="viewStationError(stationErrorData.station_id)"
              :disabled="!stationErrorData.doneSync"
            >
              Refresh
            </v-btn>
            <v-btn
              class="mx-1"
              color="red"
              @click="stationErrorData.bool = false"
            >
              Close
            </v-btn>
          </v-toolbar>
          <v-progress-linear
            v-if="!stationErrorData.doneSync"
            color="green"
            indeterminate
          ></v-progress-linear>
          <v-col>
            <span v-if="stationErrorData.response.error">
              {{stationErrorData.response.error}}
            </span>
            <span v-else>
              <v-data-table
                :headers="stationErrorData.headers"
                item-key="errorCode"
                :items="stationErrorData.response.data"
                :items-per-page="15"
                class="elevation-1"
                group-by="module"
              >

              </v-data-table>
            </span>
          </v-col>
        </v-card>
      </v-dialog>

      <!-- view station movement -->
      <v-dialog 
        v-if="stationMovementData.bool"
        v-model="stationMovementData.bool"
      >
        <v-card>
          <v-toolbar dark>
            <v-toolbar-title>Station Movement of Station {{stationMovementData.station_id}}</v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              class="mx-1"
              color="green"
              @click="viewStationMovement(stationMovementData.station_id)"
              :disabled="!stationMovementData.doneSync"
            >
              Refresh
            </v-btn>
            <v-btn
              class="mx-1"
              color="red"
              @click="stationMovementData.bool = false"
            >
              Close
            </v-btn>
          </v-toolbar>
          <v-progress-linear
            v-if="!stationMovementData.doneSync"
            color="green"
            indeterminate
          ></v-progress-linear>
          <v-col>
            <span v-if="stationMovementData.response.error">
              {{stationMovementData.response.error}}
            </span>
            <span v-else>
              <v-data-table
                :headers="stationMovementData.headers"
                item-key="id"
                :items="stationMovementData.response.data"
                :items-per-page="15"
                class="elevation-1"
                group-by="bin_no"
                sort-by="created_at"
              >

                <template v-slot:[`item.created_at`]="{ item }">
                  {{ convertStringToLocal(item.created_at, true) }}
                </template>

                <template v-slot:[`item.plc_ack`]="{ item }">
                  <v-chip 
                    :color="getStatus(item.plc_ack)[0]"
                    dark
                  >
                    {{ getStatus(item.plc_ack)[1] }}
                  </v-chip>
                </template>

              </v-data-table>
            </span>
          </v-col>
        </v-card>
      </v-dialog>
      <v-dialog 
        v-if="stationErrJDMsg.bool"
        v-model="stationErrJDMsg.bool"
      >
        <v-card>
          <v-toolbar dark>
            <v-toolbar-title>Log for Inner Error of Station {{stationErrJDMsg.station_id}}</v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              class="mx-1"
              color="green"
              @click="viewStationJDErrorMsg(stationErrJDMsg.station_id)"
              :disabled="!stationErrJDMsg.doneSync"
            >
              Refresh
            </v-btn>
            <v-btn
              class="mx-1"
              color="red"
              @click="stationErrJDMsg.bool = false"
            >
              Close
            </v-btn>
          </v-toolbar>
          <v-progress-linear
            v-if="!stationErrJDMsg.doneSync"
            color="green"
            indeterminate
          ></v-progress-linear>
          <v-col>
            <span v-if="stationErrJDMsg.response.error">
              {{stationErrJDMsg.response.error}}
            </span>
            <span v-else>
              <v-data-table
                :headers="stationErrJDMsg.headers"
                item-key="id"
                :items="stationErrJDMsg.response.data"
                :items-per-page="15"
                class="elevation-1"
                sort-by="date"
                sort-desc = True
                dense = True
              >

              </v-data-table>
            </span>
          </v-col>
        </v-card>
      </v-dialog>
      <!-- </v-parallax> -->
      <!--
        <v-divider></v-divider>
      time line card
      <v-flex d-flex v-if="orderObj">
        <v-layout wrap>
          <v-flex md4 v-for="(n, k) in storages.qty" :key="n" :item-key="n">
            check if bin's detail exist
            <v-card
              v-if="orderObj[storages.list[k]]"
              class="pa-5 a-2 d-flex flex-column"
            >
              <v-card-title>{{ storages.list[k] }}</v-card-title>
              <v-row>
                <v-icon class="pa-3"> mdi-file-presentation-box</v-icon>
                <div class="my-4 subtitle-1">
                  {{ storages.list[k] }} Timeline
                </div>
              </v-row>
              <v-row>
                <div class="pl-10">
                  Order id is {{ orderObj[storages.list[k]].order }}.
                </div>
              </v-row>
              <v-row>
                <div class="pl-10">
                  Journey id is {{ orderObj[storages.list[k]].journey }}.
                </div>
              </v-row>
              timeline div 
              <v-timeline v-model="orderObj" v-if="orderObj">
                <v-timeline-item
                  v-for="(n, j) in orderObj[storages.list[k]].qty"
                  :key="j"
                  :color="timeColor(orderObj[storages.list[k]].jobs[j][1])"
                  large
                  class="body-2"
                >
                  {{ orderObj[storages.list[k]].jobs[j][0] }} -
                  {{ orderObj[storages.list[k]].jobs[j][2] }}
                  <v-tooltip v-model="modelToolTip" top>
                    <template v-slot:activator="{ on }">
                      <v-btn icon v-bind="attrs" v-on="on">
                      <v-icon color="grey lighten-1" v-on="on">
                        mdi-cart
                      </v-icon>
                      </v-btn>
                    </template>
                    <v-row>
                      <span
                        >Programmatic tooltip
                        {{ orderObj[storages.list[k]].jobs[j][2] }}</span
                      ></v-row
                    >
                    <v-row> <span>Programmatic tooltip1 </span></v-row>
                    <v-row> <span>Programmatic tooltip2 </span></v-row>
                  </v-tooltip>
                </v-timeline-item>
              </v-timeline>
            </v-card>
          </v-flex>
        </v-layout>
      </v-flex>
      -->     
      
      <v-divider></v-divider>
      <!-- Hardwarex Mock -->
      <v-toolbar color="black" dark flat>
        <v-toolbar-title>HardwareX Mock</v-toolbar-title>
        <v-spacer></v-spacer>
        <template v-slot:extension>
          <!-- <v-row> -->
          <v-tabs v-model="tab" align-with-title>
            <v-tabs-slider color="black"></v-tabs-slider>

            <v-tab v-for="item in tabItems" :key="item" @click="reset_form()">
              {{ item }}
            </v-tab>
          </v-tabs>
        </template>
      </v-toolbar>
      <v-row>
        <v-tabs-items v-model="tab">
          <!-- Mock Job Done -->
          <v-tab-item>
            <v-card flat width="1500">
              <v-card-text>
                <v-row>
                  <v-col>
                    <v-card min-width="550" min-height="350" hover class="mx-auto">
                      <v-col>
                        <v-form ref="mock" v-model="mjd_valid">
                          <v-row>
                            <v-col>
                              <v-text-field
                                v-model="input_1"
                                label="Station Code"
                                placeholder="e.g. 1"
                                clearable
                                type="number"
                                :rules=stationRules
                              ></v-text-field>
                            </v-col>
                          </v-row>
                          <v-row>
                            <v-col>
                              <v-text-field
                                v-model="input_2"
                                label="Job ID"
                                placeholder="e.g. 999"
                                clearable
                                :rules="[v => !!v || 'Required']"
                              ></v-text-field>
                            </v-col>
                            <v-col>
                              <v-text-field
                                v-model="input_3"
                                label="Storage Code"
                                placeholder="e.g. 1100"
                                clearable
                                :rules="[v => !!v || 'Required']"
                              ></v-text-field>
                            </v-col>
                          </v-row>
                          <v-row>
                            <v-col>
                              <v-text-field
                                v-model="input_4"
                                label="From Position"
                                placeholder="e.g. 0"
                                clearable
                                type="number"
                                :rules=zoneRules
                              ></v-text-field>
                            </v-col>
                            <v-col>
                              <v-text-field
                                v-model="input_5"
                                label="To Position"
                                placeholder="e.g. 3"
                                clearable
                                type="number"
                                :rules=zoneRules
                              ></v-text-field>
                            </v-col>
                          </v-row>
                        </v-form>
                      </v-col>
                    </v-card>
                  </v-col>
                  <v-col>
                    <v-card min-width="550" min-height="350" hover class="mx-auto">
                      <v-col>
                        <v-card-title>Preview:</v-card-title>
                        <pre>{{show_preview({ 
                                "station_code": input_1,
                                "job_id": input_2,
                                "storage_code": input_3,
                                "from_pos": input_4,
                                "to_pos": input_5,
                              })}}
                        </pre>
                      </v-col>
                    </v-card>
                  </v-col>
                </v-row>
                <v-row>
                  <v-col>
                    <v-btn :disabled="!mjd_valid" @click="btnMock('mock_job_done', {
                      station_code: input_1,
                      job_id: input_2,
                      storage_code: input_3,
                      from_pos: input_4,
                      to_pos: input_5
                    }), reset_form()"
                      >Mock Job Done</v-btn
                    >
                  </v-col>
                  <v-col>
                    <v-btn @click="reset_form()"
                      >Clear</v-btn
                    >
                  </v-col>
                  <v-col>
                    <v-alert
                      v-if="bolMock"
                      v-model="bolMock"
                      border="left"
                      colored-border
                      color="deep-purple accent-4"
                      elevation="2"
                    >
                      {{ txtMock }}
                    </v-alert>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-tab-item>
          <!-- Mock Move -->
          <v-tab-item>
            <v-card flat width="1500">
              <v-card-text>
                <v-row>
                  <v-col>
                    <v-card min-width="550" min-height="310" hover class="mx-auto">
                      <v-col>
                        <v-form ref="mock" v-model="mm_valid">
                          <v-row>
                            <v-col>
                              <v-text-field
                                v-model="input_1"
                                label="Station Code"
                                placeholder="e.g. 1"
                                clearable
                                type="number"
                                :rules=stationRules
                              ></v-text-field>
                            </v-col>
                          </v-row>
                          <v-row>
                            <v-col>
                              <v-text-field
                                v-model="input_2"
                                label="Job ID"
                                placeholder="e.g. 999"
                                clearable
                                :rules="[v => !!v || 'Required']"
                              ></v-text-field>
                            </v-col>
                            <v-col>
                              <v-text-field
                                v-model="input_3"
                                label="Storage Code"
                                placeholder="e.g. 1100"
                                clearable
                                :rules="[v => !!v || 'Required']"
                              ></v-text-field>
                            </v-col>
                          </v-row>
                          <v-row>
                            <v-col>
                              <v-text-field
                                v-model="input_4"
                                label="From Position"
                                placeholder="e.g. 0"
                                clearable
                                type="number"
                                :rules=zoneRules
                              ></v-text-field>
                            </v-col>
                            <v-col>
                              <v-text-field
                                v-model="input_5"
                                label="To Position"
                                placeholder="e.g. 3"
                                clearable
                                type="number"
                                :rules=zoneRules
                              ></v-text-field>
                            </v-col>
                          </v-row>
                        </v-form>
                      </v-col>
                    </v-card>
                  </v-col>
                  <v-col>
                    <v-card min-width="550" min-height="310" hover class="mx-auto">
                      <v-card-title>Preview:</v-card-title>
                      <v-col>
                      <pre>{{show_preview({
                          "station_code": input_1,
                          "job_id": input_2,
                          "storage_code": input_3,
                          "from_pos": input_4,
                          "to_pos": input_5,
                        })}}
                      </pre>
                      </v-col>
                    </v-card>
                  </v-col>
                </v-row>
                <v-row>
                  <v-col lg=""
                    ><v-btn :disabled="!mm_valid" @click="btnMock('mock_move', {
                      station_code: input_1,
                      job_id: input_2,
                      storage_code: input_3,
                      from_pos: input_4,
                      to_pos: input_5
                    }), reset_form()"
                      >Mock Move</v-btn
                    >
                  </v-col>
                  <v-col lg=""
                    ><v-btn @click="reset_form()"
                      >Clear</v-btn
                    >
                  </v-col>
                  <v-col>
                    <v-alert
                      v-if="bolMock"
                      v-model="bolMock"
                      border="left"
                      colored-border
                      color="deep-purple accent-4"
                      elevation="2"
                    >
                      {{ txtMock }}
                    </v-alert>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-tab-item>
          <!-- Mock Update -->
          <v-tab-item>
            <v-card flat width="1500">
              <v-card-text>
                <v-row>
                  <v-col>
                    <v-card min-width="550" min-height="310" hover class="mx-auto">
                      <v-col>
                        <v-form ref="mock" v-model="mu_valid">
                          <v-row>
                            <v-col>
                              <v-text-field
                                v-model="input_1"
                                label="Station Code"
                                placeholder="e.g. 1"
                                clearable
                                type="number"
                                :rules=stationRules
                              ></v-text-field>
                            </v-col>
                          </v-row>
                          <v-row>
                            <v-col>
                              <v-text-field
                                v-model="input_2"
                                label="Job ID"
                                placeholder="e.g. 999"
                                clearable
                                :rules="[v => !!v || 'Required']"
                              ></v-text-field>
                            </v-col>
                            <v-col>
                              <v-text-field
                                v-model="input_3"
                                label="Storage Code"
                                placeholder="e.g. 1100"
                                clearable
                                :rules="[v => !!v || 'Required']"
                              ></v-text-field>
                            </v-col>
                          </v-row>
                          <v-row>
                            <v-col>
                              <v-select
                                v-model="input_4"
                                :items="actionItems"
                                label="Action"
                                placeholder="e.g. PICK"
                                :rules="[v => !!v || 'Required']"
                              ></v-select>
                            </v-col>
                            <v-col>
                              <v-text-field
                                v-model="input_5"
                                label="Index"
                                placeholder="e.g. 0"
                                clearable
                                type="number"
                                :rules="[v => !!v || 'Required']"
                              ></v-text-field>
                            </v-col>
                          </v-row>
                        </v-form>
                      </v-col>
                    </v-card>
                  </v-col>
                  <v-col>
                    <v-card hover min-width="550" min-height="310" class="mx-auto">
                      <v-card-title>Preview:</v-card-title>
                      <v-col>
                      <pre>{{show_preview({
                          "station_code": input_1,
                          "job_id": input_2,
                          "storage_code": input_3,
                          "action": input_4,
                          "index": input_5,
                        })}}
                      </pre>
                      </v-col>
                    </v-card>
                  </v-col>
                </v-row>
                <v-row>
                  <v-col lg=""
                    ><v-btn :disabled="!mu_valid" @click="btnMock('mock_update', {
                      station_code: input_1,
                      job_id: input_2,
                      storage_code: input_3,
                      action: input_4,
                      index: input_5
                    }), reset_form()"
                      >Mock Update</v-btn
                    >
                  </v-col>
                  <v-col lg=""
                    ><v-btn @click="reset_form()"
                      >Clear</v-btn
                    >
                  </v-col>
                  <v-col>
                    <v-alert
                      v-if="bolMock"
                      v-model="bolMock"
                      border="left"
                      colored-border
                      color="deep-purple accent-4"
                      elevation="2"
                    >
                      {{ txtMock }}
                    </v-alert>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-tab-item>
          <!-- Mock PLC ACK -->
          <v-tab-item>
            <v-card flat width="1500">
              <v-card-text>
                <v-row>
                  <v-col>
                    <v-card min-width="550" min-height="400" hover class="mx-auto">
                      <v-col>
                        <v-form ref="mock" v-model="mpa_valid">
                          <v-row>
                            <v-col>
                              <v-text-field
                                v-model="input_1"
                                label="Station Code"
                                placeholder="e.g. 1"
                                clearable
                                type="number"
                                :rules=stationRules
                              ></v-text-field>
                            </v-col>
                          </v-row>
                          <v-row>
                            <v-col>
                              <v-text-field
                                v-model="input_2"
                                label="Job ID"
                                placeholder="e.g. 999"
                                clearable
                                :rules="[v => !!v || 'Required']"
                              ></v-text-field>
                            </v-col>
                            <v-col>
                              <v-text-field
                                v-model="input_3"
                                label="Storage Code"
                                placeholder="e.g. 1100"
                                clearable
                                :rules="[v => !!v || 'Required']"
                              ></v-text-field>
                            </v-col>
                          </v-row>
                          <v-row>
                            <v-col>
                              <v-text-field
                                v-model="input_4"
                                label="From Position"
                                placeholder="e.g. 0"
                                clearable
                                type="number"
                                :rules="[v => v == '' || v == null || 0 <= v && v <= 12 || 'Invalid Zone']"
                              ></v-text-field>
                            </v-col>
                            <v-col>
                              <v-text-field
                                v-model="input_5"
                                label="To Position"
                                placeholder="e.g. 3"
                                type="number"
                                clearable
                                :rules="[v => v == '' || v == null || 0 <= v && v <= 12 || 'Invalid Zone']"
                              ></v-text-field>
                            </v-col>
                          </v-row>
                          <v-row>
                            <v-col>
                              <v-select
                                v-model="input_6"
                                :items="codeItems"
                                label="Code"
                                placeholder="e.g. B"
                                :rules="[v => !!v || 'Required']"
                              ></v-select>
                            </v-col>
                            <v-col>
                              <v-select
                                v-model="input_7"
                                :items="directionItems"
                                label="Direction"
                                placeholder="e.g. C"
                              ></v-select>
                            </v-col>
                          </v-row>
                        </v-form>
                      </v-col>
                    </v-card>
                  </v-col>
                  <v-col>
                    <v-card min-width="550" min-height="400" hover class="mx-auto">
                      <v-card-title>Preview:</v-card-title>
                      <v-col>
                      <pre>{{show_preview({
                          "station_code": input_1,
                          "movement_string": get_string({
                            station_code: input_1,
                            job_id: input_2,
                            storage_code: input_3,
                            from_pos: input_4,
                            to_pos: input_5,
                            code: input_6,
                            direction: input_7})})}}
                        </pre>
                      </v-col>
                    </v-card>
                  </v-col>
                </v-row>
                <v-row>
                  <v-col lg=""
                    ><v-btn :disabled="!mpa_valid" @click="btnMock('mock_plc_ack', {
                      station_code: input_1,
                      movement_string: get_string({
                        station_code: input_1,
                        job_id: input_2,
                        storage_code: input_3,
                        from_pos: input_4,
                        to_pos: input_5,
                        code: input_6,
                        direction: input_7
                      })
                    }), reset_form()"
                      >Mock PLC ACK</v-btn
                    >
                  </v-col>
                  <v-col lg=""
                    ><v-btn @click="reset_form()"
                      >Clear</v-btn
                    >
                  </v-col>
                  <v-col>
                    <v-alert
                      v-if="bolMock"
                      v-model="bolMock"
                      border="left"
                      colored-border
                      color="deep-purple accent-4"
                      elevation="2"
                    >
                      {{ txtMock }}
                    </v-alert>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-tab-item>
          <!-- Mock Recv Station Msg -->
          <v-tab-item>
            <v-card flat width="1500">
              <v-card-text>
                <v-row>
                  <v-col>
                    <v-card min-width="550" min-height="210" hover class="mx-auto">
                      <v-col>
                        <v-form ref="mock" v-model="m_valid">
                          <v-row>
                            <v-col>
                              <v-text-field
                                v-model="input_1"
                                label="Station Code"
                                placeholder="e.g. 1"
                                clearable
                                type="number"
                                :rules=stationRules
                              ></v-text-field>
                            </v-col>
                          </v-row>
                          <v-row>
                            <v-col>
                              <v-text-field
                                v-model="input_3"
                                label="Input"
                                placeholder="e.g. ST,1,J,1,0|2|C|1234 or ST,1,STOP;"
                                clearable
                                :rules="[v => !!v || 'Required']"
                              ></v-text-field>
                            </v-col>
                          </v-row>
                        </v-form>
                      </v-col>
                    </v-card>
                  </v-col>
                  <v-col>
                    <v-card min-width="550" min-height="210" hover class="mx-auto">
                      <v-card-title>Preview:</v-card-title>
                      <v-col>
                      <pre>{{show_preview({
                            "station_code": input_1,
                            "input": input_3,
                          })}}
                      </pre>
                      </v-col>
                    </v-card>
                  </v-col>
                </v-row>
                <v-row>
                  <v-col lg=""
                    ><v-btn :disabled="!m_valid" @click="btnMock('mock_station_msg', {
                      station_code: input_1,
                      message: input_3,
                      }), reset_form()"
                      >Mock Receive Station</v-btn
                    >
                  </v-col>
                  <v-col lg=""
                    ><v-btn @click="reset_form()"
                      >Clear</v-btn
                    >
                  </v-col>
                  <v-col>
                    <v-alert
                      v-if="bolMock"
                      v-model="bolMock"
                      border="left"
                      colored-border
                      color="deep-purple accent-4"
                      elevation="2"
                    >
                      {{ txtMock }}
                    </v-alert>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-tab-item>
          <!-- Send Station -->
          <v-tab-item>
            <v-card flat width="1500">
              <v-card-text>
                <v-row>
                  <v-col>
                    <v-card min-width="550" min-height="210" hover class="mx-auto">
                      <v-col>
                        <v-form ref="mock" v-model="m_valid">
                          <v-row>
                            <v-col>
                              <v-text-field
                                v-model="input_1"
                                label="Station Code"
                                placeholder="e.g. 1"
                                clearable
                                type="number"
                                :rules=stationRules
                              ></v-text-field>
                            </v-col>
                          </v-row>
                          <v-row>
                            <v-col>
                              <v-text-field
                                v-model="input_3"
                                label="Input"
                                placeholder="e.g. ST,1,M,1,0|2|C|1234"
                                clearable
                                :rules="[v => !!v || 'Required']"
                              ></v-text-field>
                            </v-col>
                          </v-row>
                        </v-form>
                      </v-col>
                    </v-card>
                  </v-col>
                  <v-col>
                    <v-card min-width="550" min-height="210" hover class="mx-auto">
                      <v-card-title>Preview:</v-card-title>
                      <v-col>
                      <pre>{{show_preview({
                            "station_code": input_1,
                            "input": input_3,
                          })}}
                      </pre>
                      </v-col>
                    </v-card>
                  </v-col>
                </v-row>
                <v-row>
                  <v-col lg=""
                    ><v-btn :disabled="!m_valid" @click="btnMock('send_plc_msg', {
                      station_code: input_1,
                      message: input_3,
                      }), reset_form()"
                      >Send Station</v-btn
                    >
                  </v-col>
                  <v-col lg=""
                    ><v-btn @click="reset_form()"
                      >Clear</v-btn
                    >
                  </v-col>
                  <v-col>
                    <v-alert
                      v-if="bolMock"
                      v-model="bolMock"
                      border="left"
                      colored-border
                      color="deep-purple accent-4"
                      elevation="2"
                    >
                      {{ txtMock }}
                    </v-alert>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-tab-item>
          <!-- Send Socketoi Event -->
          <v-tab-item>
            <v-card flat width="1500">
              <v-card-text>
                <v-row>
                  <v-col>
                    <v-card min-width="550" min-height="210" hover class="mx-auto">
                      <v-col>
                        <v-form ref="mock" v-model="m_valid">
                          <v-row>
                            <v-col>
                              <v-select
                                v-model="input_1"
                                :items="['pick_arrival','work_arrival','bin-arrival','request','update']"
                                label="Event"
                                placeholder="select your event"
                                clearable
                                :rules="[v => !!v || 'Required']"
                              ></v-select>
                            </v-col>
                          </v-row>
                          <v-row>
                            <v-col>
                              <v-select
                                v-model="entity"
                                :items="['TC_A','TC_B','TC_C','SM','MEDIATOR']"
                                label="Entity"
                                placeholder="select your entity"
                                clearable
                              ></v-select>
                            </v-col>
                          </v-row>
                          <v-row v-if="entity == 'MEDIATOR'">
                            <v-col>
                              <v-select
                                v-model="mediator_num"
                                :items= create_list_for_station_no()
                                label="Mediator Number"
                                placeholder="select your mediator number"
                                clearable
                                :rules="[v => !!v || 'Required']"
                              ></v-select>
                            </v-col>
                          </v-row>
                          <v-row>
                            <v-col>
                              <v-text-field
                                v-model="namespace"
                                label="Namespace"
                                clearable
                                :rules="[v => !!v || 'Required']"
                              ></v-text-field>
                            </v-col>
                          </v-row>
                          <p> Data </p>
                          <!-- data field -->
                          <v-row>
                            <v-col>
                              <v-select
                                v-model.number="station_code"
                                :items= create_list_for_station_no()
                                label="Station Code"
                                placeholder="select your station code"
                                clearable
                                :rules="[v => !!v || 'Required']"
                              ></v-select>
                            </v-col>
                          </v-row>
                          <v-row v-if= "input_1 == 'pick_arrival'">
                            <v-col>
                              <v-text-field
                                v-model.number="pick_index"
                                label="Pick Index"
                                placeholder="e.g. 10"
                                clearable
                                type="number"
                                :rules="[v => !!v && 0 <= v && v <= 11 || 'Invalid Zone']"
                              ></v-text-field>
                            </v-col>
                          </v-row>
                          <v-row v-if= "input_1 == 'pick_arrival' ||
                           input_1 == 'work_arrival' || 
                           input_1 == 'bin-arrival'"
                           >
                            <v-col>
                              <v-text-field
                                v-model.number="storage_code"
                                label="Storage Code"
                                placeholder="e.g. 1234"
                                clearable
                                type="number"
                                :rules="[v => !!v || 'Required']"
                              ></v-text-field>
                            </v-col>
                          </v-row>
                          <v-row v-if= "input_1 == 'request' || input_1 == 'update'">
                            <v-col>
                              <v-text-field
                                v-model.number="job_id"
                                label="Job ID"
                                placeholder="e.g. 76367"
                                clearable
                                type="number"
                                :rules="[v => !!v || 'Required']"
                              ></v-text-field>
                            </v-col>
                          </v-row>
                          <v-row v-if= "input_1 == 'request' || input_1 == 'update'">
                            <v-col>
                              <v-select
                                v-model="message"
                                :items = "['Approved', 'Declined']"
                                label="Message"
                                placeholder="select your message"
                                clearable
                                :rules="[v => !!v || 'Required']"
                              ></v-select>
                            </v-col>
                          </v-row>
                          <v-row v-if= "input_1 == 'request'">
                            <v-col>
                              <v-checkbox
                                v-model="req_status"
                                label="Status"
                                clearable
                              ></v-checkbox>
                            </v-col>
                          </v-row>
                        </v-form>
                      </v-col>
                    </v-card>
                  </v-col>
                  <v-col>
                    <v-card min-width="550" min-height="210" hover class="mx-auto">
                      <v-card-title>Preview:</v-card-title>
                      <v-col>
                      <pre>{{show_preview({
                            "event": input_1,
                            "entity": mediator_num ? entity + mediator_num : entity,
                            "namespace":namespace,
                            "data": get_ws_request_data(input_1,
                            station_code,
                            storage_code,
                            pick_index, 
                            job_id,
                            message,
                            req_status
                            )
                          })}}
                      </pre>
                      </v-col>
                    </v-card>
                  </v-col>
                </v-row>
                <v-row>
                  <v-col lg=""
                    ><v-btn :disabled="!m_valid" @click="btnMock('mock_ws_request', {
                      event: input_1,
                      entity: mediator_num ? entity + mediator_num : entity,
                      data:get_ws_request_data(input_1,station_code,storage_code,pick_index, job_id,message,req_status),
                      'namespace': namespace
                      })"
                      >Mock SocketIO Event</v-btn
                    >
                  </v-col>
                  <v-col lg=""
                    ><v-btn @click="reset_form()"
                      >Clear</v-btn
                    >
                  </v-col>
                  <v-col>
                    <v-alert
                      v-if="bolMock"
                      v-model="bolMock"
                      border="left"
                      colored-border
                      color="deep-purple accent-4"
                      elevation="2"
                    >
                      {{ txtMock }}
                    </v-alert>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-tab-item>
        </v-tabs-items>
      </v-row>
    </v-container>
  </v-app>
</template>

<script>
import { getCurrentDateTime, getHccUrl, convertStringToLocal, 
  getCube, getRequestHeader } from "../helper/common.js";

export default {
  name: "App",
  components: {
  },
  created() {
    this.viewStationDetail()
  },
  methods: {
    timeColor(status) {
      if (status === "COMPLETED") {
        return "green";
      } else if (status == "PROCESSING") {
        return "purple";
      } else {
        return "blue";
      }
    },
    getColor(item) {
      // console.log(item);
      var qty = parseInt(item);
      // console.log("at getcolor" + qty);
      if (qty > 6) {
        // console.log("red");
        return "red";
      } else if (qty > 0) {
        // console.log("orange");
        return "orange";
      } else {
        // console.log("green");
        return "green";
      }
    },
    getStatus(bol) {
      if (bol == true) {
        return ["green", "✓"];
      } else {
        return ["red", "✗"];
      }
    },
    getErrorColor(errorCode){
      if (errorCode.length ==0){
        return ["green", errorCode];
      }
      else{
        if (errorCode.length >10){
          let slicedArray = errorCode.slice(0,10)
          slicedArray.push("...")
          let stringArray = slicedArray.join(", ")

          return ["red", stringArray];
        }
        else{
          return ["red", errorCode.join(", ")];
        }
        
      }
    },


    reset_form() {
      this.bolMock = false
      this.$refs.mock.reset()
    },
    show_preview(json) {
      var res = new Object()
      for (var [key, val] of Object.entries(json)) {
        if (val == null || val == "") {
          delete res[key]
        } else {
          res[key] = val
        }
      }
      if (Object.entries(res).length === 0) {
        return "  Put some value!"
      } else {
        return res
      }
    },
    
    get_string(input) {
      var station_id = input["station_id"]
      var job_id = input["job_id"]
      var bin_id = input["bin_id"]
      var from_pos = input["from_pos"]
      var to_pos = input["to_pos"]
      var code = input["code"]
      var direction = input["direction"]
      if (station_id == null || station_id == "") {station_id = ""} else {station_id = "ST," + station_id + ","}
      if (job_id == null || job_id == "") {job_id = ""} else {job_id += ","}
      if (bin_id == null || bin_id == "") {bin_id = ""}
      if (from_pos == null || from_pos == "") { from_pos = "" } else {from_pos += "|"}
      if (to_pos == null || to_pos == "") { to_pos = ""} else {to_pos += "|"}
      if (code == null) {code = ""} else {code += ","}
      if (direction == null || direction == "none") { direction = ""} else {direction += "|"}
      var movement_string = station_id + code + job_id + from_pos + to_pos + direction + bin_id
      return movement_string
    },
    create_list_for_station_no(){
      const arr = Array.from({ length: 17 }, (_, index) => index + 1);
      return arr
    },
    get_ws_request_data(event,station_code,storage_code,pick_index, job_id,message,req_status){
      var dict = {}
      dict["station_code"] = station_code
      if (["pick_arrival","work_arrival","bin-arrival"].includes(event)){
        dict["storage_code"] = storage_code
        if (event == "bin-arrival" || event == "work_arrival"){
          dict["bin_code"] = storage_code.toString()
          dict["station_id"] = station_code
        }
      }
      if (event == "pick_arrival")
        dict["pick_index"] = pick_index
      if (["request","update"].includes(event)){
        dict["job_id"] = job_id
        dict["message"] = message
      }
      if (event == "request")
        dict["status"] = req_status
      return dict
    }
},
  data: () => ({
    convertStringToLocal,
    currentZone: getCube()[0],
    zones: getCube(),
    viewStationMovement: async function (station_id) {
      let here = this.stationMovementData
      here.doneSync = false
      here.station_id = station_id
      here.bool = true
      here.response = await getStation("station_movement", "POST", { station_code: station_id })
      here.doneSync = true
    },

    viewStationError: async function (station_id) {
      let here = this.stationErrorData
      here.doneSync = false
      here.station_id = station_id
      here.bool = true
      here.response = await getStation("station_error", "POST", { station_code: station_id })
      here.doneSync = true
    },
    viewStationDetail: async function () {
      let here = this.stationDetailData
      here.doneSync = false
      here.bool = true
      here.response = await getStation("station_detail", "POST", { zone: this.currentZone })
      here.doneSync = true
    },
    updateEnrollStation: async function (station_id, item){
      let here = this.stationEnrollData
      here.doneSync = false
      here.bool = true
      here.response = await updateEnroll(
        "update_enroll_station", 
        "POST", 
        { station_code: station_id, is_enroll : !item.is_enroll }
        )
      here.doneSync = true
      if (!here.response.error){
        item.is_enroll = !item.is_enroll
        this.$awn.info(here.response.data);
      }
      else{
        this.$awn.alert(here.response.error);
      }
    },
    viewStationJDErrorMsg: async function (station_id){
      let here = this.stationErrJDMsg
      here.doneSync = false
      here.station_id = station_id
      here.bool = true
      here.response = await getStation("station_error_msg_log", "POST", { station_code: station_id })
      here.doneSync = true
    },
    
    tab: null,
    mjd_valid: true,
    mm_valid: true,
    mu_valid: true,
    mpa_valid:true,
    m_valid:true,
    bolMock: false,
    txtMock: null,
    input_1: null,
    input_2: null,
    input_3: null,
    input_4: "",
    input_5: "",
    input_6: null,
    input_7: null,
    txtPreview: null,
    entity: " ",
    namespace: "station",
    mediator_num : null,
    // socketio data
    station_code: null,
    storage_code: "",
    pick_index: null,
    job_id: null,
    message: "",
    req_status: false,
    
    tabItems: [
      "Mock Job Done",
      "Mock Move",
      "Mock Update",
      "Mock PLC ACK",
      "Mock PLC MSG",
      "Send PLC MSG",
      "Mock Socketio Event"
    ],
    actionItems: ["PICK","DROP"],
    codeItems: ["B","M","J","U"],
    directionItems: ["none","C","D","P"],
    
    zoneRules: [
      v => !!v || "Required",
      v => v >= 0 && v <= 12 || "Invalid Zone"
    ],
    stationRules: [
      v => !!v || "Required",
      v => v >= 1 && v <= 17 || "Invalid Station"
    ],
    
    stationMovementData : {
      bool: false,
      text: null,
      station_id: null,
      headers: [
        { text: "ID", align: "start", sortable: true, value: "id" },
        { text: "Storage", value: "bin_no" },
        { text: "Status", value: "status" },
        { text: "Type", value: "type" },
        { text: "From Index", value: "from_index" },
        { text: "To Index", value: "to_index" },
        { text: "Acknowledgement", value: "plc_ack" },
        { text: "Created At", value: "created_at" }
      ],
      response: {},
      doneSync: false
    },
    
    stationErrorData: {
      bool: false,
      text: null,
      station_id: null,
      headers:[
        { text: "Error Code", align: "start", sortable: true, value: "errorCode" },
        { text: "Module", value: "module" },
        { text: "Error Message", value: "errorMessage" },
        { text: "Action", value: "action" },
        { text: "Error Level ( 1=Self Service, 2=Technician )", value: "errorLevel" }
      ],
      response: {},
      doneSync: false
    },
    
    stationDetailData : {
      bool: false,
      text: null,
      headers: [
        { text: "ID", align: "start", sortable: true, value: "station_id" },
        { text: "Type", value: "type" },
        { text: "Active", value: "is_active" },
        { text: "Maintenance", value: "is_maint" },
        { text: "Storage Qty", value: "storage_quantity" },
        { text: "Station Movemet", value: "movement" },
        { text: "Error Code", value: "error_code" },
        { text: "Enroll Mode", value: "is_enroll" },
        { text: "Inner Error Log", value:"error_jd_msg" }
      ],
      response: {},
      doneSync: false
    },
    stationEnrollData:{
      bool: false,
      text: null,
      station_id: null,
      response: {},
      doneSync: false
    },
    stationErrJDMsg :{
      bool: false,
      text: null,
      station_id: null,
      headers: [
        { text: "Date", value: "date" },
        { text: "Error Message", value : "error_msg" },
        { text: "Error Reason", value : "error_reason" }
      ],
      response: {},
      doneSync: false

    },
    
    btnMock: async function (url, json) {
      let res = await Mock(url, json);
      this.txtMock = res
      this.bolMock = true
    }    
  })
};

async function getStation(url, method, body) {
  let hccUrl = getHccUrl()
  var hwxHost = `${hccUrl}/station/${url}`
  var requestOptions = {
    method: method,
    body: JSON.stringify(body),
    headers: getRequestHeader()
  }
  try {
    let res = await fetch(hwxHost, requestOptions)
    return res.json()
  } catch (error) {
    return {
      data: [],
      error: error
    }
  }
}

async function Mock(url, json) {
  let hccUrl = getHccUrl()
  var hwxHost = new URL(`${hccUrl}/station/${url}`)
  var requestOptions = {
    method: "POST",
    body: JSON.stringify(json),
    headers: getRequestHeader()
  }
  try {
    const response = await fetch(hwxHost, requestOptions)
    const myTXT = await response.text()
    let res = myTXT + " at " + getCurrentDateTime()
    return res
  } catch (error) {
    let msg =
      "Remote end point " +
      hwxHost +
      " not accessible, please ensure there is valid Input selected " +
      getCurrentDateTime();
    return msg;
  }
}

async function updateEnroll (url, method, body) {
  let hccUrl = getHccUrl()
  var hwxHost = `${hccUrl}/station/${url}`
  var requestOptions = {
    method: method,
    body: JSON.stringify(body),
    headers: getRequestHeader()
  }
  try {
    let res = await fetch(hwxHost, requestOptions)
    return res.json()
  } catch (error) {
    return {
      data: [],
      error: error
    }
  }
}
</script>

<style>
@import "~vue-awesome-notifications/dist/styles/style.css";
</style>

