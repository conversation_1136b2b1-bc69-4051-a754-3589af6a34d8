<template>
  <v-app app>
    <v-container fluid>
      <v-flex>
        <!-- configuration -->
        <v-row>
          <v-icon>mdi-cog-box</v-icon>
          <v-card-title>TC Configuration</v-card-title>
        </v-row>
        <v-card class="mb-10" dark>
          <v-expansion-panels>
            <v-expansion-panel>
              <v-expansion-panel-header>Cube Configuration</v-expansion-panel-header>
              <v-expansion-panel-content>
                <pre>{{ cubeSetting }}</pre>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <!-- <v-expansion-panel>
              <v-expansion-panel-header>SM Setting Configuration</v-expansion-panel-header>
              <v-expansion-panel-content>
                <pre>{{ smSetting }}</pre>
              </v-expansion-panel-content>
            </v-expansion-panel> -->
          </v-expansion-panels>
        </v-card>

        <v-divider></v-divider>

        <v-toolbar color="black" dark flat>
          <v-toolbar-title>Operation Feature</v-toolbar-title>
          <v-spacer></v-spacer>
          <template v-slot:extension>
            <!-- <v-row> -->
            <v-tabs dark v-model="tab" icons-and-text>
              <v-tabs-slider color="black"></v-tabs-slider>

              <v-tab v-for="item in tabItems" :key="item.title">
                {{ item.title }}
                <v-icon>{{item.icon}}</v-icon>


              </v-tab>
            </v-tabs>
          </template>
        </v-toolbar>


        <v-tabs-items v-model="tab">
          <!-- cyclestop -->
          <v-tab-item :key="CycleStop">
            <v-card dark>
              <v-progress-linear v-if="!doneSync" color="green" indeterminate></v-progress-linear>
              <v-card-text>
                <v-row>
                  <v-icon>mdi-hand</v-icon>
                  <v-row>
                    <v-card-title>
                      <v-chip
                        class="ma-4"
                        :color="cyclestopItems.color"
                      >{{ cyclestopItems.text }}</v-chip>
                    </v-card-title>
                    <v-spacer></v-spacer>
                    <v-col align="right">
                      <v-btn
                        light
                        class="mx-2"
                        @click="btnViewCycleStop()"
                        :disabled="!doneSync"
                      >
                        Refresh
                      </v-btn>
                    </v-col>
                  </v-row>

                </v-row>

                <v-row>
                  <v-col cols="4">
                    <v-select
                      solo 
                      hint="Select zone to start/stop TC." 
                      persistent-hint 
                      v-model="comboCycle"
                      :items="items" 
                      label="Select zone" 
                      dense
                      @change="btnViewCycleStop()"
                    ></v-select>
                  </v-col>
                  <v-col cols="8">
                    <v-select solo dense hint="Select the reason for start/stop TC." persistent-hint
                      v-model="modelOperation.selectedReason" :items="modelOperation.reasons"
                      label="Pick one reason here."></v-select>
                  </v-col>

                </v-row>





                <v-row>
                  <v-col cols="2">
                    <v-btn @click="btnCycleStop(cycleStopType.DISABLED)">Start TC</v-btn>
                  </v-col>
                  <v-col cols="2">
                    <v-btn @click="btnCycleStop(cycleStopType.ENABLED)">Stop TC</v-btn>
                  </v-col>

                  <!-- <v-col cols="8">
                    <v-dialog v-model="modelTravel.parking_dialog" width="500">
                      <template v-slot:activator="{ on, attrs }">
                        <v-btn v-bind="attrs" v-on="on">
                          Parking All Skycars
                        </v-btn>
                      </template>

                      <v-card dark>
                        <v-card-title class="text-h5">
                          Parking All Skycars
                        </v-card-title>

                        <v-card-text v-html="modelTravel.parking_dialog_text">
                        </v-card-text>

                        <v-divider></v-divider>

                        <v-card-actions>
                          <v-spacer></v-spacer>
                          <v-btn color="primary" text @click="btnParking()">
                            I confirm
                          </v-btn>
                        </v-card-actions>
                      </v-card>
                    </v-dialog>
                  </v-col> -->


                </v-row>
                <v-col>
                  <v-alert v-if="bolCycleStop" v-model="bolCycleStop" border="left" colored-border
                    color="deep-purple accent-4" elevation="2" icon="mdi-chat">
                    {{ txtCycleStop }}
                  </v-alert>
                </v-col>
              </v-card-text>
            </v-card>

          </v-tab-item>

        </v-tabs-items>


        <v-divider></v-divider>
      </v-flex>
    </v-container>
  </v-app>
</template>

<script>
import { convertStringToLocal, getCube, getCurrentDateTime, 
  getDefaultHost, getHost, getRequestHeader } from "../helper/common.js";
import { CycleStop, RouteSkycar } from "../helper/enums.js";
let httpRequest = require("../helper/http_request.js");
export default {
  name: "App",

  components: {
    // AppBar,
    // NavBar,
  },

  created() {
    this.btnViewCycleStop()
  },

  mounted: function () {
    this.$nextTick(function () {
      // alert("run mounted");
      this.initStatus();
    });
  },
  computed: {
    on_change_skycar_id: function () {
      if (!(this.modelTravel.skycar_id in [null, ""])) {
        return "Selecting skycar id " + this.modelTravel.skycar_id;
      } else {
        return "";
      }
    }
  },
  methods: {
    onChangeMaintenanceDock(selection) {
      /**
       * v-on will trigger this methods by passing v-model as arguments ,
       * update variable desc
       * update variable coordinate */
      let coordinate = this.modelMD.md_stations[selection];
      this.modelMD.coordinate = coordinate;
      this.modelMD.desc = " Maintenance Dock Coodinate at " + coordinate;
    },
    maintenanceDockItems() {
      /**
       * Process dict and return list of string
       */
      let keys = [];
      for (let key in this.modelMD.md_stations) {
        keys.push(key);
      }
      return keys;
    },
    get_status(status) {
      if (status == CycleStop.ENABLED) {
        var output = { color: "red", text: "✓ Enabled" };
      } else if (status == CycleStop.DISABLED) {
        output = { color: "green", text: "✗ Disabled" };
      } else {
        output = { color: "grey", text: "? Inactive" };
      }
      return output;
    }
  },

  data: () => ({
    convertStringToLocal,
    // tabItems: ["Cycle Stop", "Enroll", "Disenroll", "Skycar Travel"],
    tabItems: [
      { "title": "Cycle Stop", "icon": "mdi-stop-circle-outline" }
    ],
    doneSync: false,
    skycar_id: null,
    skycar_x: null,
    skycar_y: null,
    dis_skycar_id: null,

    cubeSetting: null,
    smSetting: null,
    cycleStopType: CycleStop,
    comboSelect: getCube()[0],
    comboOrientationSelect: "North",
    comboIsDual: false,
    comboCycle: getCube()[0],
    comboDisenroll: getCube()[0],
    comboTravel: getCube()[0],
    bolCycleStop: false,
    txtCycleStop: null,

    bolEnroll: false,
    txtEnroll: null,

    bolDisenroll: false,
    txtDisenroll: null,
    items: getCube(),
    orientation: ["South", "North"],

    modelOperation: {
      reasons: [
        "Cube/Grid Operation",
        "Plot/Remove Obstacle",
        "Debug Software TC/SM/HCC/CM",
        "Debug Skycar",
        "Debug Charging Station",
        "Debug WorkStation",
        "Parking"],
      selectedReason: null
    },

    // encapsulate maintenance dock related attribute to object
    modelMD: {
      md_stations: null, //default value , update by call api to backend once on load
      dock_id: null,
      coordinate: null,
      desc: null,
      skycar_id: null,
      alert_txt: null,
      alert_bol: false
    },

    tab: null,
    // encapsulate travel module related attribute to object
    modelTravel: {
      skycar_id: null,
      x: 0,
      y: 0,
      alert_bol: false,
      alert_txt: null,
      parking_dialog: false,
      parking_dialog_text: 
        `Parking all feature require <br/>1. TC cycle stop enabled. <br/>2. 
        TC not in error handling mode. <br/>3. All Skycars in idle mode.`
    },

    btnCycleStop: async function (cyclestop) {

      if (cyclestop == CycleStop.ENABLED) {
        if (this.modelOperation.selectedReason == null) {
          this.txtCycleStop = "Please select reason." + getCurrentDateTime();
          this.bolCycleStop = true;
          return
        }
      }
      
      let res = await sendCycleStop(cyclestop, this.comboCycle, this.modelOperation.selectedReason);
      this.txtCycleStop = res;
      this.bolCycleStop = true;
      this.bolEnroll = false;
      this.modelOperation.selectedReason = null
      this.btnViewCycleStop();

    },
    btnEnroll: async function () {
      this.bolEnroll = true;
      let data = {
        id: this.skycar_id,
        x: this.skycar_x,
        y: this.skycar_y,
        zone: this.comboSelect,
        orientation: this.comboOrientationSelect,
        is_dual: this.comboIsDual
      };
      let res = await httpRequest.postRequest(
        data,
        RouteSkycar.SKYCAR,
        getHost(this.comboSelect)
      );
      if (res.status) {
        this.txtEnroll = "Success"
      } else {
        this.txtEnroll = res.message
      }
    },
    initStatus: async function () {
      let res = await sendGetConfig();
      this.cubeSetting = res.cube;
      this.smSetting = res.sm;
      this.modelMD.md_stations = res.cube.MAINTENANCE_DOCK;
    },
    btnDisenroll: async function () {
      //post to TC
      let data = {
        id: this.dis_skycar_id
      };
      // handle response
      let res = await httpRequest.postRequest(
        data,
        RouteSkycar.DISENROLL,
        getHost(this.comboDisenroll)
      );

      // display alert
      let output = JSON.parse(res);
      this.txtDisenroll = `${output[0].reason} , ${output[0].status}`;
      this.bolDisenroll = true;
    },

    btnTravel: async function () {
      // post to TC
      let data = {
        skycar_id: this.modelTravel.skycar_id,
        x: this.modelTravel.x,
        y: this.modelTravel.y
      };
      // obtain zone
      let tcUrl = getHost(this.comboTravel);

      // handle response
      let res = await httpRequest.postRequest(
        data,
        "/dashboard/travel",
        tcUrl
      );

      // display alert
      let output = JSON.parse(res);
      this.modelTravel.alert_txt = `${output[0].reason} , ${output[0].status
        } ${getCurrentDateTime()}`;
      this.modelTravel.alert_bol = true;
    },
    btnParking: async function () {
      let data = {};
      let tcUrl = getHost(this.comboTravel);

      // handle response
      let res = await httpRequest.postRequest(data, RouteSkycar.PARKING, tcUrl);

      // display alert
      try {
        let output = JSON.parse(res);
        this.modelTravel.alert_txt = `${output[0].reason} , ${output[0].status
          } ${getCurrentDateTime()}`;
        this.modelTravel.alert_bol = true;
        this.modelTravel.parking_dialog = false;
      } catch (error) {
        this.modelTravel.alert_txt = `${res.data} , ${getCurrentDateTime()}`;
        this.modelTravel.alert_bol = true;
        this.modelTravel.parking_dialog = false;
      }
    },

    btnViewCycleStop: async function () {
      this.cyclestopItems = {
        color: "grey",
        text: "Inactive"
      }
      this.doneSync = false
      try {
        let res = await getConfig(this.comboCycle);
        this.doneSync = true
        let model = res.model.cycle_stop
        let updated_at = convertStringToLocal(model.updated_at, true)
        switch (model.status) {
          case (CycleStop.ENABLED): {
            return this.cyclestopItems = {
              color: "red",
              text: `TC is stopped since ${updated_at} due to ${model.reason}`
            }
          }
          case (CycleStop.DISABLED): {
            return this.cyclestopItems = {
              color: "green",
              text: `TC is running since ${updated_at}`
            }
          }
        }
      } catch (error) {
        this.doneSync = true
        alert(error)
      }
    },

    CycleStop: null,
    Enroll: null,
    Disenroll: null,
    headers: [
      { text: "Cube", value: "cube", align: "center" },
      { text: "Status", value: "status", align: "center" },
      { text: "Last Update", value: "updated_at", align: "center" }
    ],
    cyclestopItems: {
      color: "grey",
      text: "Inactive"
    }
  })
};

async function sendCycleStop(cyclestop, zone, strReason) {
  var tcUrl = getHost(zone);

  var tcHost = new URL(tcUrl + "/api/cyclestop");

  // console.log(cycleStopTypes.DISABLED);
  var requestOptions = {
    method: "POST",
    body: JSON.stringify({ status: cyclestop, reason: strReason }),
    headers: getRequestHeader()
  };
  try {
    const response = await fetch(tcHost, requestOptions);
    const myJson = JSON.parse(await response.json());
    //   console.log("myJson", myJson);
    let res = myJson["tc"] + " at " + getCurrentDateTime();
    return res;
  } catch (error) {
    let msg =
      "Remote end point " +
      tcHost +
      " not accessible, please ensure there is valid Zone selected " +
      getCurrentDateTime();
    return msg;
  }
}

async function getConfig(cube) {
  let tcHost = new URL(getHost(cube) + "/operation/healthcheck");

  var requestOptions = {
    method: "GET",
    headers: getRequestHeader()
  };

  let res = await fetch(tcHost, requestOptions);
  return await res.json()
}

async function sendGetConfig() {
  var tcHost = new URL(getDefaultHost() + "/api/cyclestop");

  // console.log(cycleStopTypes.DISABLED);
  var requestOptions = {
    method: "GET",
    // body: JSON.stringify({ status: cyclestop }),
    headers: getRequestHeader()
  };

  const response = await fetch(tcHost, requestOptions);
  // console.log("response", response);
  let data = null;
  let res = await response.json();
  //   const myJson = JSON.parse(await response.json());
  //   console.log("myJson", myJson);
  // console.log("myjson", res);
  //   let res = null;
  res = JSON.parse(res);
  data = { cube: res.cube, sm: res.sm, cyclestop: res.cyclestop };
  //   console.log("data",data)
  return data;
}
</script>

