<template>
  <v-app app>
    <v-container fluid>
      <v-card-title class="grey lighten-5"
        >Today {{ getCurrentDateTime() }}</v-card-title
      >
      <v-row>
        <v-col>
          <v-card>
            <v-parallax dark height="250" src="@/assets/background-card.jpg">
              <v-card-title>Available Orders </v-card-title>
              <v-card-subtitle class="white--text">
                We gonna be very busy soon , hurry up buddies.
              </v-card-subtitle>
              <v-card-text>
                <div class="my-2">
                  <v-row>
                    <v-col cols="2">
                      <v-btn color="secondary" fab x-large dark disabled>
                        <v-icon>mdi-alarm</v-icon>
                      </v-btn>
                    </v-col>
                    <v-col cols="10">
                      <!-- <div class="text-h5"> -->
                      <v-card-text class="text-h4 white--text">{{
                        modelData.data.available_jobs
                      }}</v-card-text>
                    </v-col>
                  </v-row>
                </div>
              </v-card-text>
            </v-parallax>
          </v-card>
        </v-col>

        <v-col>
          <v-card>
            <v-parallax dark height="250" src="@/assets/background-card.jpg">
              <v-card-title>Processing Orders </v-card-title>
              <v-card-subtitle class="white--text">
                We striking those orders right now, please be patient.
              </v-card-subtitle>
              <v-card-text>
                <div class="my-2">
                  <v-row>
                    <v-col cols="2">
                      <v-btn color="secondary" fab x-large dark disabled>
                        <v-icon>mdi-alarm</v-icon>
                      </v-btn>
                    </v-col>
                    <v-col cols="10">
                      <!-- <div class="text-h5"> -->
                      <v-card-text class="text-h4 white--text">{{
                        modelData.data.processing_jobs
                      }}</v-card-text>
                    </v-col>
                  </v-row>
                </div>
              </v-card-text>
            </v-parallax>
          </v-card>
        </v-col>
      </v-row>

      <v-card>
        <v-card-title
          >Last 7 days statistics
          <v-col class="d-flex" style="margin-left: 60%">
            <v-select
              :items="zones"
              label="Select Zone"
              v-model="selectZone"
              v-on:change="viewData()"
            >
            </v-select>
          </v-col>
        </v-card-title>
        <v-expansion-panels focusable>
          <v-expansion-panel
            v-for="(item, index) in modelData.data.completed_jobs"
            :key="index"
          >
            <v-expansion-panel-header class="font-weight-medium">
              {{ item.date }} Total : {{ item.count }}
            </v-expansion-panel-header>

            <v-expansion-panel-content>
              <v-row v-for="t in modelData.jobType" :key="t">
                <v-col
                  ><v-card-subtitle class="black--text">{{
                    t
                  }}</v-card-subtitle></v-col
                >
                <v-col>
                  <v-card-subtitle>{{
                    item[t.toLowerCase()]
                  }}</v-card-subtitle></v-col
                >
              </v-row>
            </v-expansion-panel-content>
          </v-expansion-panel>
        </v-expansion-panels>

        <v-card-subtitle
          >Alright, we'are getting more factor to present as we go on the
          journey..</v-card-subtitle
        >
      </v-card>
    </v-container>
  </v-app>
</template>

<script>
import { getCurrentDateTime, getCube, getHost, getRequestHeader, getMapping, useRefreshToken } from "../helper/common";

export default {
  name: "App",

  components: {},

  created() {
    return;
  },
  mounted: function () {
    this.$nextTick(function () {
      this.viewData();
    });
  },
  methods: {
    timeColor() {
      return;
    },
    getImages() {}
  },
  data: () => ({
    getCurrentDateTime,
    timerState: "Stopped",
    zones: getCube(),
    selectZone: getCube()[0],
    modelData: {
      data: {
        processing_jobs: 0,
        available_jobs: 0,
        completed_jobs: []
      },
      jobType: ["RETRIEVING", "PUTAWAY", "INTERNAL", "INTERSTATION", "TRAVEL"],
      alert: null
    },
    queryJobTypeQty: function (jobType, item) {
      let jobtype = jobType.toLowerCase();
      let qty = item[jobtype];
      return qty;
    },
    btnData: async function () {
      var tcHost = new URL(getHost(this.selectZone) + "/dashboard/data");

      let params = { type: "data" };
      tcHost.search = new URLSearchParams(params).toString();
      const response = await fetch(tcHost, { headers: getRequestHeader() });
      const myJson = await response.json(); //extract JSON from the http response

      if (myJson.code === 401){ // If access token is unauthorized
        // use refresh token to get new access token from auth server 
        return useRefreshToken(this, this.btnData)
      }
      return myJson;
    },
    viewData: async function () {
      // let returnJson = await this.mockSkycarJob();
      let returnJson = await this.btnData();
      if (typeof returnJson === "object") {
        this.modelData.data;
      } else {
        let parseJson = JSON.parse(returnJson);
        this.modelData.data = parseJson[getMapping(this.selectZone)];
      }
      this.modelData.alert = "Last update on " + getCurrentDateTime();
    }
  })
};
</script>
