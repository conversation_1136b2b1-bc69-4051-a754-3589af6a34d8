<template>
  <v-dialog 
    v-model="dialogBool" 
    width="440"
    persistent
  >
    <v-card>
      <v-toolbar 
        color="primary" 
        dark
      >
        <v-toolbar-title class="d-flex align-center">
          <v-img
            class="mr-2"
            style="height: 10%; max-width: 9%;"
            src="@/assets/pingspace.png"
            alt="Pingspace Icon"
          />
          PINGSPACE
        </v-toolbar-title>
      </v-toolbar>
      <v-col>
        <v-card-title style="font-size: 30px; font-weight: bold; display: flex; align-items: center;">
          Stay signed in?
        </v-card-title>
        <v-card-text style="height: 30px">
          Click YES to reduce the number of times you are asked to sign in.
        </v-card-text>
        <v-col>
          <v-checkbox 
            v-model="checkbox" 
            :label="getText()" 
            style="height: 20px"
          />
        </v-col>
        <v-card-actions>
          <v-spacer />
          <ProgressCircular 
            :doneSync="doneSync" 
            :color="color" 
            style="margin-right: 10px;"
          />
          <v-btn
            color="grey lighten-1"
            @click="btnAction(false)"
          >
            No
          </v-btn>
          <v-btn 
            color="primary" 
            @click="btnAction(true)"
          >
            Yes
          </v-btn>
        </v-card-actions>
      </v-col>
    </v-card>
  </v-dialog>
</template>

<script>
import { getAuthServerUrl, getRefreshTokenBody } from "../../../helper/common";
import { setTokens } from "../../../helper/authorize";
import { RouteAuthServer } from "../../../helper/enums";
let httpRequest = require("../../../helper/http_request.js");
import ProgressCircular from "../../shared/ProgressCircular.vue"

export default {
  name: "StaySignIn",
  components: {
    ProgressCircular
  },
  data: () => ({
    dialogBool: false,
    checkbox: false,
    userInfo: null,
    doneSync: true,
    color: "primary"
  }),
  methods: {
    openDialog(userInfo) {
      this.dialogBool = true;
      this.userInfo = userInfo
    },
    closeDialog() {
      this.dialogBool = false;
      this.checkbox = false
    },
    getText(){
      return "Don't show this again"
    },
    async btnAction(type){
      this.doneSync = false
      var requestBody = {
        "email": this.userInfo.email,
        "remove_dialog": this.checkbox,
        "session_prolonged": type
      }

      let response = await httpRequest.axiosRequest(
        "post",
        getAuthServerUrl(), 
        RouteAuthServer.staySignedIn, 
        requestBody
      )
      if (response.status === 200){
        if (type){
          const res = await httpRequest.axiosRequest(
            "post",
            getAuthServerUrl(), 
            RouteAuthServer.authorizeToken,
            getRefreshTokenBody(),
            true
          )

          if (res.status === 200) { 
            setTokens(res.data);
          }
          else{
            this.$awn.alert(getAuthServerUrl() + RouteAuthServer.authorizeToken + " Request Failed")
            return null
          }
        }
        this.closeDialog()
        this.$router.push({ path: "/insight" })
      } else {
        this.$awn.alert(response.data.message)
      }
      this.doneSync = true
    }
  }
};
</script>
