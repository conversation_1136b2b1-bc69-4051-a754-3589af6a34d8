<template>
  <v-container fluid>
    <v-row justify="center">
      <v-col cols="12" sm="10" md="8" lg="6">
        <v-card elevation="5">
          <v-row justify="center">
            <v-card-title class="text-h6 text-md-h5 text-lg-h4 font-weight-bold text-center">Register</v-card-title>
          </v-row>
          <v-divider></v-divider>
          <v-card-text class="text-center" style="margin-top: -15px;">
            <v-form ref="form" fast-fail @submit.prevent>
              <v-text-field 
                  v-model="username" 
                  label="Username"
                  required>
              </v-text-field>

              <v-row>
                <v-col cols="12" sm="6">
                  <v-text-field
                    v-model="email"
                    label="Email Address"
                    :rules="rules.emailRules"
                    @blur="handleBlur"
                    required
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6">
                  <v-select
                    v-model="selectedDomain"
                    :items="domainOptions"
                    label="Select Domain"
                  ></v-select>
                </v-col>
              </v-row>

              <v-text-field 
                  :type="showPassword1 ? 'text' : 'password'" 
                  :append-icon="showPassword1 ? 'mdi-eye' : 'mdi-eye-off'" 
                  @click:append="showPassword1 = !showPassword1"
                  :rules="rules.minimum"
                  required
                  v-model="password" label="Password">
              </v-text-field>

              <v-text-field 
                  :type="showPassword2 ? 'text' : 'password'" 
                  :append-icon="showPassword2 ? 'mdi-eye' : 'mdi-eye-off'" 
                  @click:append="showPassword2 = !showPassword2"
                  :rules="rules.passwordMatch"
                  required
                  v-model="confirmPassword" label="Confirm Password">
              </v-text-field>

              <v-alert v-if="showAlert === 'error'" color="red" outlined>
                  Username/Email have already been taken!
              </v-alert>
              <v-alert v-else-if="showAlert === 'success'" color="green" outlined>
                  Successfully created the user! 
                  <br/>
                  <strong>Important:</strong> Please contact the TC Team to activate your account. 
                  Pingspace.co email can no longer send activation links.
                  <br/> 
                  <small>Did not receive the email?
                  <a @click.prevent="handleLinkClick"
                  :style="{ pointerEvents: isLinkDisabled ? 'none' : 'auto',  
                  color: isLinkDisabled ? 'gray' : 'blue', 
                  textDecoration: isLinkDisabled ? 'none' : 'underline'}">
                  Click here</a></small>
                  <small v-show="isLinkDisabled"> ({{ timeRemaining }}s)</small>
              </v-alert>
              <v-alert v-else-if="showAlert === 'email-failed'" color="red" outlined>
                Email sent unsuccessfully! Contact TC Team please.
              </v-alert>
              
              <div class="text-center" style="margin-top: -10px;">
                  <small>Already have an Account? <router-link to="/login">
                      <span>Click Here</span></router-link>
                  </small>
              </div>

              <v-divider class="my-2"></v-divider>

              <v-progress-linear
                :active="loading"
                :indeterminate="loading"
                absolute
                top
                color="primary"
              ></v-progress-linear>

              <v-btn type="submit" color="primary" block @click="submitForm">Register</v-btn>
              <v-btn type="clear" block class="mt-3" @click="reset">Clear</v-btn>
            </v-form>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
// Module Functions
import { getAuthServerUrl } from "../../helper/common";
import { RouteAuthServer } from "../../helper/enums";
let httpRequest = require("../../helper/http_request.js");

export default {
  name: "App",
  data() {
    return {
      getAuthServerUrl,
      RouteAuthServer,

      username: "",
      email: "",
      password: "",
      confirmPassword: "",
      showPassword1: false,
      showPassword2: false,
      showAlert: "",
      rules: {
        minimum: [v => v.length >= 6 || "Min 6 characters"],
        emailRules: [v => /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/.test(v) || "E-mail must be valid"],
        passwordMatch: [v => v === this.password || "Password must match"]
      },
      selectedDomain: "pingspace.co",
      domainOptions: ["pingspace.co", "pentamaster.com.my"],
      resendEmail: "",
      isLinkDisabled: false,
      timeRemaining: 0,
      loading: false
    }
  },
  watch: {
    selectedDomain(newDomain) {
      if (this.email && this.email.includes("@")) {
        const username = this.email.split("@")[0];
        this.email = `${username}@${newDomain}`;
      }
    }
  },
  methods: {
    async handleLinkClick() {
      if (!this.isLinkDisabled) {
        this.isLinkDisabled = true;
        this.timeRemaining = 60;

        const timer = setInterval(() => {
          if (this.timeRemaining === 1) {
            clearInterval(timer);
            this.timeRemaining = 0;
            this.isLinkDisabled = false;
          } else {
            this.timeRemaining--;
          }
        }, 1000);

        var requestBody = { 
          "email": this.resendEmail
        }
        this.loading = true

        let response = await httpRequest.axiosRequest(
          "post",
          getAuthServerUrl(), 
          RouteAuthServer.resendVerification,
          requestBody
        )

        if (response.status === 200) {
          this.$awn.success("New verification email sent!")
        } else {
          this.$awn.alert("Email sent unsuccessfully! Contact TC Team please.")
        }
        
        this.loading = false
      }
    },

    handleBlur() {
      if (this.email && this.email.indexOf("@") === -1) {
        this.email += `@${this.selectedDomain}`;
      }
    },

    async submitForm() {
      if (this.$refs.form.validate()){
        var requestBody = { 
            "username": this.username.toUpperCase(),
            "email": this.email, 
            "password": this.password
        }

        this.resendEmail = this.email
        this.loading = true

        let response = await httpRequest.axiosRequest(
          "post",
          getAuthServerUrl(), 
          RouteAuthServer.register,
          requestBody
        )

        this.reset()

        if (response.status == 200) {
            this.showAlert = "success"
        } else if (response.status == 500) {
          this.showAlert = "email-failed"
        } else {
            this.showAlert = "error"
        }
        this.loading = false
      }
    },

    reset () {
      this.$refs.form.reset()
      this.$nextTick(() => {
          this.selectedDomain = "pingspace.co"
      })
      this.showAlert = ""
      this.password = ""
    }
  }
}
</script>

<style>

</style>