<template>
  <v-container fluid>
    <v-row justify="center">
      <v-col cols="12" sm="10" md="8" lg="6">
        <v-card elevation="5">
          <v-row justify="center">
            <v-card-title
              class="text-h6 text-md-h5 text-lg-h4 font-weight-bold text-center"
              >Reset Password</v-card-title
            >
          </v-row>
          <v-card-text class="text-center" style="margin-top: -20px;">
            <v-divider></v-divider>
            <v-form ref="form" fast-fail @submit.prevent>
              <v-text-field
                :type="showPassword1 ? 'text' : 'password'"
                :append-icon="showPassword1 ? 'mdi-eye' : 'mdi-eye-off'"
                @click:append="showPassword1 = !showPassword1"
                :rules="rules.minimum"
                required
                v-model="oldPassword"
                label="Old Password"
              >
              </v-text-field>

              <v-text-field
                :type="showPassword2 ? 'text' : 'password'"
                :append-icon="showPassword2 ? 'mdi-eye' : 'mdi-eye-off'"
                @click:append="showPassword2 = !showPassword2"
                :rules="rules.minimum"
                required
                v-model="newPassword"
                label="New Password"
              >
              </v-text-field>

              <v-text-field
                :type="showPassword3 ? 'text' : 'password'"
                :append-icon="showPassword3 ? 'mdi-eye' : 'mdi-eye-off'"
                @click:append="showPassword3 = !showPassword3"
                :rules="rules.passwordMatch"
                required
                v-model="confirmNewPassword"
                label="Confirm New Password"
              >
              </v-text-field>

              <v-alert v-if="showAlert === 'error'" color="red" outlined>{{
                alertText
              }}</v-alert>
              <v-alert
                v-else-if="showAlert === 'success'"
                color="green"
                outlined
                >Password Changed Successfully</v-alert
              >

              <v-divider class="my-2"></v-divider>

              <v-progress-linear
                :active="loading"
                :indeterminate="loading"
                absolute
                top
                color="primary"
              ></v-progress-linear>

              <v-btn type="submit" color="primary" block @click="submitForm"
                >Reset Password</v-btn
              >
              <v-btn type="clear" block class="mt-3" @click="reset"
                >Clear</v-btn
              >
            </v-form>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
// Module Functions
import { getAuthServerUrl } from "../../helper/common";
import { RouteAuthServer } from "../../helper/enums";
let httpRequest = require("../../helper/http_request.js");

export default {
  name: "App",
  data() {
    return {
      getAuthServerUrl,
      RouteAuthServer,

      oldPassword: "",
      newPassword: "",
      confirmNewPassword: "",
      showPassword1: false,
      showPassword2: false,
      showPassword3: false,
      showAlert: "",
      alertText: "",
      rules: {
        minimum: [(v) => v.length >= 6 || "Min 6 characters"],
        passwordMatch: [
          (v) => v === this.newPassword || "New Password must match"
        ]
      },
      loading: false
    };
  },
  methods: {
    reset() {
      this.$refs.form.reset();
      this.showAlert = "";
      this.oldPassword = "";
      this.newPassword = "";
    },

    async submitForm() {
      if (this.$refs.form.validate()) {
        var requestBody = {
          username: this.$store.state.user.username,
          // eslint-disable-next-line camelcase
          old_password: this.oldPassword,
          // eslint-disable-next-line camelcase
          new_password: this.newPassword
        }

        this.loading = true;
        let response = await httpRequest.axiosRequest(
          "patch",
          getAuthServerUrl(), 
          RouteAuthServer.resetPassword,
          requestBody
        )
        
        this.reset()

        if (response.status == 200) {
          this.showAlert = "success"
        } else{
          if (response.status === 401) {
            this.showAlert = "error";
            this.alertText = "Incorrect Old Password";
          } else if (response.status === 409) {
            this.showAlert = "error";
            this.alertText = "Same New and Old Password Entered";
          }
        }

        this.loading = false
      }
    }
  }
};
</script>

<style></style>
