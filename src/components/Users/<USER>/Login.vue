<template>
  <v-container fluid>
    <v-row justify="center">
      <v-col 
        cols="12" 
        sm="10" 
        md="8" 
        lg="6"
      >
        <v-card elevation="5">
          <v-row justify="center">
            <v-card-title class="text-h6 text-md-h5 text-lg-h4 font-weight-bold text-center">
              Login
            </v-card-title>
          </v-row>
          <v-divider />
          <v-card-text class="text-center">
            <v-form 
              ref="form" 
              fast-fail 
              @submit.prevent
            >
              <v-row>
                <v-col 
                  cols="12" 
                  sm="6"
                >
                  <v-text-field
                    v-model="email"
                    label="Email Address"
                    :rules="rules.emailRules"
                    @blur="handleBlur"
                    required
                  />
                </v-col>
                <v-col 
                  cols="12" 
                  sm="6"
                >
                  <v-select
                    v-model="selectedDomain"
                    :items="domainOptions"
                    label="Select Domain"
                  />
                </v-col>
              </v-row>

              <v-text-field
                :type="showPassword ? 'text' : 'password'"
                :append-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
                @click:append="showPassword = !showPassword"
                required
                v-model="password"
                label="Password"
              />

              <v-alert 
                v-if="showAlert === 'error'" 
                color="red" 
                outlined
              >
                Incorrect Email Address or Password!
              </v-alert>
              <v-alert
                v-else-if="showAlert === 'half'" 
                color="red" 
                outlined
              >
                Please verify your account before logging in. The verification
                link has been sent to your email!
                <br>
                <small>
                  Did not receive the email?
                  <a
                    @click.prevent="handleLinkClick"
                    :style="{
                      pointerEvents: isLinkDisabled ? 'none' : 'auto',
                      color: isLinkDisabled ? 'gray' : 'blue',
                      textDecoration: isLinkDisabled ? 'none' : 'underline',
                    }"
                  >Click here</a>
                </small>
                <small v-show="isLinkDisabled"> ({{ timeRemaining }}s)</small>
              </v-alert>
              <v-alert v-else-if="showAlert === 'email-failed'" color="red" outlined>
                Email sent unsuccessfully! Contact TC Team please.
              </v-alert>

              <v-divider class="my-1" />

              <div class="text-center">
                <div>
                  <small>Don't have an Account?
                    <router-link to="/register">
                      <span>Click Here</span></router-link>
                  </small>
                </div>
                <div>
                  <small>Forgot Password?
                    <router-link to="/forgotpassword">
                      <span>Click Here</span></router-link>
                  </small>
                </div>
              </div>

              <v-divider class="my-2" />

              <v-progress-linear
                :active="loading"
                :indeterminate="loading"
                absolute
                top
                color="primary"
              />

              <v-btn 
                type="submit"
                color="primary" 
                block 
                @click="submitForm"
              >
                Login
              </v-btn>
              <v-btn 
                type="clear" 
                block 
                class="mt-3" 
                @click="reset"
              >
                Clear
              </v-btn>
            </v-form>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <DialogStaySignIn
      ref="dialogStaySignIn"
    />
  </v-container>
</template>

<script>
// Module Functions
import { emitAuth } from "../../../App.vue";
import { setStateUser } from "../../../helper/authorize";
import { getAuthServerUrl, getClientID, getClientSecret } from "../../../helper/common";
import { RouteAuthServer, cookieKey } from "../../../helper/enums";
import DialogStaySignIn from "./DialogStaySignIn.vue";
let httpRequest = require("../../../helper/http_request.js");

export default {
    name: "Login",
    components: {
      DialogStaySignIn
    },
    data() {
      return {
        getAuthServerUrl,
        getClientID,
        getClientSecret,
        RouteAuthServer,

        email: "",
        password: "",
        showPassword: false,
        showAlert: "",
        rules: {
            emailRules: [v => /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/.test(v) || "E-mail must be valid"]
        },
        selectedDomain: "pingspace.co",
        domainOptions: ["pingspace.co", "pentamaster.com.my"], 
        resendEmail: "",
        isLinkDisabled: false,
        timeRemaining: 0,
        loading: false
      }
    },
    watch: {
      selectedDomain(newDomain) {
        if (this.email && this.email.includes("@")) {
          const username = this.email.split("@")[0];
          this.email = `${username}@${newDomain}`;
      }
    }
  },
    methods: {
        reset () {
          this.$refs.form.reset()
          this.$nextTick(() => {
            this.selectedDomain = "pingspace.co"
          })
          this.showAlert = ""
        },

        async handleLinkClick() {
          if (!this.isLinkDisabled) {
            this.isLinkDisabled = true;
            this.timeRemaining = 60;

            const timer = setInterval(() => {
              if (this.timeRemaining === 1) {
                clearInterval(timer);
                this.timeRemaining = 0;
                this.isLinkDisabled = false;
              } else {
                this.timeRemaining--;
              }
            }, 1000);
            
            var requestBody = { 
              "email": this.resendEmail
            }
            this.loading = true

            let response = await httpRequest.axiosRequest(
              "post", 
              getAuthServerUrl(), 
              RouteAuthServer.resendVerification,
              requestBody
            )

            if (response.status === 200) {
              this.$awn.success("New verification email sent!")
            } else {
              this.$awn.alert("Email sent unsuccessfully! Contact TC Team please.")
            }
            
            this.loading = false
          }
        },
        
        handleBlur() {
          if (this.email && this.email.indexOf("@") === -1) {
            this.email += `@${this.selectedDomain}`;
          }
        },

        setCookie(userid, username, dayExpired) {
          const datetime = new Date();
          datetime.setTime(datetime.getTime() + (dayExpired*60*60*60*1000));
          let expires = "expires="+ datetime.toUTCString();
          document.cookie = `${process.env.VUE_APP_DOC_TITLE}-${cookieKey.USERID}=${userid}; ${expires};`;
          document.cookie = `${process.env.VUE_APP_DOC_TITLE}-${cookieKey.USERNAME}=${username}; ${expires};`;
        },

        async submitForm() {
          if (this.$refs.form.validate()){
            const formData = new FormData();
            formData.append("username", this.email);
            formData.append("password", this.password);
            formData.append("grant_type", "password");
            formData.append("scope", "tc:dashboard")

            this.resendEmail = this.email
            this.loading = true
            
            let response = await httpRequest.axiosRequest(
              "post",
              getAuthServerUrl(), 
              RouteAuthServer.login, 
              formData, 
              true
            )
            
            this.reset()

            if (response.status == 200) {
              let userInfo = response.data.data
              setStateUser(userInfo);
              this.showAlert = ""
              emitAuth(userInfo.id)
              this.setCookie(userInfo.id, userInfo.username, 30)

              if (userInfo["user_metadata"]["remove_dialog"]){
                this.$router.push({ path: "/insight" })
              } else {
                this.$refs.dialogStaySignIn.openDialog(userInfo)
              }
            } else{
              if (response.status === 401) {
                this.showAlert = "error"
              } else if (response.status === 403) {
                this.showAlert = "half"
              } else if (response.status == 500) {
                this.showAlert = "email-failed"
              }
            }

            this.loading = false
          }
        }
    }

}
</script>

<style></style>
