<template>
    <v-dialog
        v-model="dialogBool"
        width="1000"
    >
        <v-card>
            <v-toolbar 
                dark 
                :color="color"
            >
                <v-toolbar-title>{{ status }}</v-toolbar-title>
            </v-toolbar>
            <v-card-text>
                <v-alert
                    v-if="status == OTA_STATUS.FAILED"
                    type="error"
                    class="mt-2"
                    outlined
                >
                    <div class="text-h7 grey--text">
                        Failed at {{ convertStringToLocal(data.done_at, true) }}
                    </div>
                    {{ data.error }}
                </v-alert>
                <span v-else-if="status == OTA_STATUS.COMPLETED">
                    <v-alert
                        type="success"
                        class="mt-2"
                        outlined
                    >
                        <div class="text-h7 grey--text">
                            Completed at {{ convertStringToLocal(data.done_at, true) }} by {{ data.created_by }}
                        </div>
                        The firmware update was completed successfully. Please use the buttons below to reboot the skycar and validate the firmware version.
                        <br>
                        <v-checkbox 
                            v-model="checkbox" 
                            label="I have confirmed that the skycar is not running and ready to be rebooted." 
                        />
                        <ProgressCircular
                            class="mr-2 mt-2"
                            indeterminate
                            color="green"
                            v-if="!doneSync"
                        ></ProgressCircular>
                        <div
                            v-else
                            style="width: 20px; height: 20px; margin-right: 8px; display: inline-block;"
                        ></div>
                        <v-btn
                            small
                            color="green"
                            class="mr-2 mt-2"
                            @click="rebootSkycar()"
                            :disabled="!doneSync || !checkbox"
                        >
                            Reboot
                        </v-btn>
                        <v-btn
                            small
                            color="green"
                            class="mt-2"
                            @click="checkFirmwareVersion()"
                            :disabled="!doneSync"
                        >
                            Check Version
                        </v-btn>
                        
                    </v-alert>
                    <v-card
                        v-if="chatTxt.length > 0"
                        dark
                        min-height="400"
                        max-height="400"
                        class="overflow-y-auto"
                    >
                        <v-col>
                            <v-row
                                v-for="(item, index) in chatTxt"
                                :key="index"
                            >
                                <v-spacer v-if="!item.from_tc && item.status"/>
                                <div>
                                    <div
                                        class="d-flex justify-end mr-1"
                                        v-if="!item.from_tc && item.status"
                                    >
                                        <pre style="font-size: 11px">{{ convertStringToLocal(item.time, true) }}</pre>
                                    </div>
                                    <div 
                                        class="ml-1"
                                        v-else
                                    >
                                        <pre style="font-size: 11px">{{ convertStringToLocal(item.time, true) }}</pre>
                                    </div>
                                    
                                    <v-chip
                                        :color="item.from_tc ? 'green' : 'orange'"
                                        class="ma-1"
                                    >
                                        <pre>{{ item.user }}: {{ item.message }}</pre>
                                    </v-chip>
                            </div>
                            </v-row>
                        </v-col>
                    </v-card>
                </span>
                <v-alert
                    v-else-if="status == OTA_STATUS.WARNING"
                    type="warning"
                    class="mt-2"
                    outlined
                >
                    <div class="text-h7 grey--text">
                        Started at {{ convertStringToLocal(data.acked_at || data.created_at, true) }} by {{ data.created_by }}
                    </div>
                    <span v-if="data.is_acked">
                        The firmware update is longer than expected, it should be completed at {{ convertStringToLocal(data.etc, true) }}. Please check the Skycar.
                    </span>
                    <span v-else>
                        The Skycar is not acknowledging the update. Please check the Skycar.
                    </span>
                </v-alert>
                <v-alert
                    v-else-if="status == OTA_STATUS.UPDATING"
                    type="info"
                    class="mt-2"
                    outlined
                >
                    <div class="text-h7 grey--text">
                        Started at {{ convertStringToLocal(data.acked_at || data.created_at, true) }} by {{ data.created_by }}
                    </div>
                    The firmware update is in progress. Please wait for the update to complete. 
                    <p>Estimated time to completion: {{ convertStringToLocal(data.etc, true) }}</p>
                </v-alert>
                <v-alert
                    v-else-if="status == OTA_STATUS.NO_DATA"
                    type="info"
                    class="mt-2"
                    outlined
                    color="grey"
                >
                    There is no firmware update currently in progress.
                </v-alert>
            </v-card-text>
            
            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn
                    color="green darken-1"
                    text
                    @click="closeDialog()"
                >Close
                </v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>
<script>
import { convertStringToLocal, getHost, getRequestHeader } from "../../helper/common"
import { OTA_STATUS, OTA_TYPE, SkycarShellCommand, Websocket } from "../../helper/enums"
import { socket } from "../../App.vue"
import ProgressCircular from "../shared/ProgressCircular.vue"
export default {
    components: {
        ProgressCircular
    },
    props: {
        showNotification: {
            type: Function
        }
    },
    data: () => ({
        OTA_STATUS,
        OTA_TYPE,
        convertStringToLocal,
        doneSync: true,
        dialogBool: false,
        data: null,
        status: "",
        color: "black",
        chatTxt: [],
        zone: null,
        checkbox: false
    }),
    methods: {
        openDialog(data, zone) {
            this.data = data
            this.status = data.ota_status
            this.color = data.otaColor
            this.chatTxt = []
            this.zone = zone
            this.checkbox = false
            this.dialogBool = true
        },
        closeDialog() {
            this.dialogBool = false
        },
        async rebootSkycar() {
            try {
                this.doneSync = false
                await this.shellCommand("0", "1")
                await this.shellCommand("1", "skycab system reboot")
            } finally {
                setTimeout(() => {
                    this.doneSync = true
                }, 500)
            }
        },
        async checkFirmwareVersion() {
            let command
            switch (this.data.type) {
                case OTA_TYPE.MAIN:
                    command = "skycab firmware main"
                    break
                case OTA_TYPE.WINCH:
                    command = "skycab firmware winch"
                    break
            }
            try {
                this.doneSync = false
                await this.shellCommand("0", "1")
                await this.shellCommand("1", command)
            } finally {
                setTimeout(() => {
                    this.doneSync = true
                }, 500)
            }
        },
        async shellCommand(action, value) {
            try {
                let url = getHost(this.zone) + SkycarShellCommand.MCU_COMMAND
                let req = await fetch(url, { 
                    method: "POST",
                    body: JSON.stringify({
                        sid: this.data.sid,
                        action: action,
                        value: value
                    }),
                    headers: getRequestHeader() 
                })
                let res = JSON.parse(await req.text())
                if (res.status) {
                    this.showNotification(true, "Success")
                } else {
                    this.showNotification(false, res.message)
                }
            } catch (err) {
                this.showNotification(false, err)
            }
        }
    },
    watch: {
        dialogBool(newVal) {
            if (newVal) {
                let here = this
                socket.on(Websocket.SKYCAR, function(data) {
                    if (data.item.message.includes(`SC,${here.data.sid},Q,`)) {
                        here.chatTxt.push(data.item)
                        if (here.chatTxt.length > 50) {
                            here.chatTxt.splice(0, here.chatTxt.length - 50)
                        }
                    }
                })
            } else {
                socket.off(Websocket.SKYCAR)
            }
        }
    }
    
}
</script>
