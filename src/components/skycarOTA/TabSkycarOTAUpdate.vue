<template>
    <v-card>
        <v-data-table
            v-model="sids"
            :headers="headers"
            :items="Object.values(items)"
            sort-by="sid"
            show-select
            item-key="sid"
        >
            <template v-slot:top>
                <v-toolbar flat dark>
                    <v-row class="mt-6">
                        <v-col cols="4">
                            <v-select
                                v-model="currentZone"
                                :items="zones"
                                outlined
                                label="Cube"
                            ></v-select>
                        </v-col>
                        <v-col cols="4">
                            <v-select
                                v-model="type"
                                :items="Object.values(OTA_TYPE)"
                                outlined
                                label="Firmware Type"
                                @change="btnRefresh()"
                            ></v-select>
                        </v-col>
                        <v-col cols="4" class="text-right">
                            <v-btn
                                color="green"
                                class="mr-5 mt-2"
                                @click="btnRefresh()"
                                :disabled="!doneSyncRefresh"
                            >
                                Refresh
                            </v-btn>
                            <v-btn
                                color="blue"
                                class="mr-5 mt-2"
                                @click="btnUpdate()"
                                :disabled="!doneSyncUpdate || sids.length === 0"
                            >
                                Update
                            </v-btn>
                            <v-btn
                                color="red"
                                class="mr-5 mt-2"
                                @click="btnCancel()"
                                :disabled="!doneSyncCancel || sids.length === 0"
                            >
                                Cancel
                            </v-btn>
                        </v-col>
                    </v-row>
                </v-toolbar>
                <v-progress-linear 
                    v-if="!doneSyncUpdate || !doneSyncCancel || !doneSyncRefresh"
                    height="5"
                    color="blue"
                    indeterminate
                />
            </template>
            <template v-slot:[`item.ota_status`]="{ item }">
                <v-chip
                    dark
                    class="ml-1 fixed-width-chip"
                    :color="item.otaColor"
                    style="justify-content: flex-start;"
                >
                    <v-progress-circular
                        v-if="[OTA_STATUS.UPDATING, OTA_STATUS.WARNING].includes(item.ota_status)"
                        indeterminate
                        size="20"
                        class="mr-2"
                    ></v-progress-circular>
                    <v-icon 
                        v-else-if="item.ota_status === OTA_STATUS.COMPLETED"
                        class="mr-2"
                    >
                        mdi-check
                    </v-icon>
                    <v-icon 
                        v-else-if="[OTA_STATUS.FAILED, OTA_STATUS.NO_DATA].includes(item.ota_status)"
                        class="mr-2"
                    >
                        mdi-close
                    </v-icon>
                    {{ item.ota_status }}
                </v-chip>
                <v-btn
                    icon
                    @click="openDialog(item)"
                >
                    <v-icon>mdi-information</v-icon>
                </v-btn>
            </template>
            <template v-slot:[`item.elapsedTime`]="{ item }">
                {{ getElapsedTime(item) }}
            </template>
        </v-data-table>
        <SnackbarNotification ref="snackbarNotification"/>
        <DialogUpdate 
            :showNotification="showNotification" 
            ref="dialogUpdate"
        />
        <DialogCancelUpdate 
            :showNotification="showNotification" 
            ref="dialogCancelUpdate"
        />
    </v-card>
</template>

<script>
import { socket } from "../../App.vue"
import { convertStringToLocal, getCube, getHost, getRequestHeader } from "../../helper/common"
import { OTA_STATUS, OTA_TYPE, RouteSkycarOTA, Websocket } from "../../helper/enums"
import DialogCancelUpdate from "./DialogCancelUpdate.vue"
import DialogUpdate from "./DialogUpdate.vue"
import SnackbarNotification from "../shared/SnackbarNotification.vue"
export default {
    components: {
        DialogCancelUpdate,
        DialogUpdate,
        SnackbarNotification
    },
    async created() {
        this.getMessage()
        await this.btnRefresh()
    },
    data: () => ({
        OTA_STATUS,
        OTA_TYPE,
        convertStringToLocal,
        zones: getCube(),
        currentZone: getCube()[0],
        sids: [],
        type: OTA_TYPE.MAIN,
        headers: [
            { text: "Skycar", align: "start", value: "sid" },
            { text: "OTA Status", align: "center", value: "ota_status" },
            { text: "Estimated Time of Completion", align: "center", value: "otaETC" },
            { text: "Elapsed Time", align: "center", value: "elapsedTime" }
        ],
        items: {},
        doneSyncRefresh: true,
        doneSyncUpdate: true,
        doneSyncCancel: true,
    }),
    methods: {
        showNotification(success, message) {
            this.$refs.snackbarNotification.showNotification(success, message)
        },
        async btnRefresh(showProgress = true) {
            try {
                if (showProgress) {
                    this.doneSyncRefresh = false
                }
                let url = getHost(this.currentZone) + RouteSkycarOTA.Update + `?type=${this.type}`
                let req = await fetch(url, {
                    headers: getRequestHeader() 
                })
                let res = JSON.parse(await req.text())
                if (res.status) {
                    this.items = res.model
                    Object.values(this.items).forEach(item => {
                        this.mapItems(item)
                    })
                } else {
                    this.showNotification(false, res.message)
                }
            } catch (err) {
                this.showNotification(false, err)
            } finally {
                setTimeout(() => {
                    this.doneSyncRefresh = true
                }, 500)
            }
        },
        async btnUpdate() {
            try {
                this.doneSyncUpdate = false
                let url = getHost(this.currentZone) + RouteSkycarOTA.Update
                let req = await fetch(url, { 
                    method: "POST",
                    body: JSON.stringify({
                        sids: this.sids.map(item => item.sid),
                        type: this.type
                    }),
                    headers: getRequestHeader() 
                })
                let res = JSON.parse(await req.text())
                if (res.status) {
                    this.showNotification(true, "Updating")
                } else {
                    this.showNotification(false, res.message)
                }
            } catch (err) {
                this.showNotification(false, err)
            } finally {
                setTimeout(() => {
                    this.doneSyncUpdate = true
                    this.btnRefresh(false)
                }, 500)
            }
        },
        async btnCancel() {
            this.$refs.dialogCancelUpdate.openDialog(this.currentZone, this.sids, this.type)
        },
        mapItems(item) {
            item.otaColor = this.getColor(item.ota_status)
            item.otaETC = this.getETC(item)
            return item
        },
        getColor(status) {
            switch (status) {
                case OTA_STATUS.FAILED:
                    return "error"
                case OTA_STATUS.COMPLETED:
                    return "green"
                case OTA_STATUS.WARNING:
                    return "warning"
                case OTA_STATUS.UPDATING:
                    return "info"
                case OTA_STATUS.NO_DATA:
                    return "grey"
            }
        },
        getETC(data) {
            if (data["is_done"] === false) {
                return convertStringToLocal(data.etc, true)
            } else {
                return "N/A"
            }
        },
        openDialog(data) {
            this.$refs.dialogUpdate.openDialog(data, this.currentZone)
        },
        getMessage() {
            let here = this
            socket.on(Websocket.OTA_UPDATE, function(data) {
                if (data.item.type === here.type) {
                    here.items[data.item.sid] = here.mapItems(data.item)
                }
            })
        },
        getElapsedTime(data) {
            let end
            if (data["is_done"] === false) {
                end = new Date()
            } else if (data["is_done"] === true) {
                end = new Date(data.done_at)
            } else {
                return "N/A"
            }
            let start = new Date(data.acked_at || data.created_at)
            let diff = end.getTime() - start.getTime()
            if (diff < 0) {
                return "N/A"
            }
            let diffMinutes = Math.floor(diff / (1000 * 60))
            let diffSeconds = Math.floor((diff % (1000 * 60)) / 1000)
            return `${diffMinutes}m ${diffSeconds}s`
        }
    }
}

export function closeMessage() {
    socket.off(Websocket.OTA_UPDATE)
}
</script>

<style scoped>
.fixed-width-chip {
  width: 150px;
  align-items: center;
  justify-content: center;
  text-align: center;
}
</style>
