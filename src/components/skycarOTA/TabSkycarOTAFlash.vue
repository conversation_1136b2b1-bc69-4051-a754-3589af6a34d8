<template>
    <v-card>
        <v-card dark>
            <v-col>
                <v-row>
                    <v-col cols="10">
                        <v-file-input
                            class="mx-1"
                            v-model="files"
                            color="deep-purple accent-4"
                            counter
                            multiple
                            label="File input"
                            placeholder="Select your files"
                            prepend-icon="mdi-paperclip"
                            outlined
                            :show-size="1024"
                        >
                            <template v-slot:selection="{ index, text }">
                                <v-chip
                                    v-if="index < 2"
                                    color="deep-purple accent-4"
                                    dark
                                    label
                                    small
                                >
                                    {{ text }}
                                </v-chip>
                                <span
                                    v-else-if="index === 2"
                                    class="text-overline grey--text text--darken-3 mx-2"
                                >
                                    +{{ files.length - 2 }} File(s)
                                </span>
                            </template>
                        </v-file-input>
                    </v-col>
                    <v-col cols="2" class="text-right">
                        <ProgressCircular 
                            class="mt-2 ml-2"
                            :doneSync="doneSyncUpload"
                        />
                        <v-btn
                            class="mt-2"
                            @click="btnUpload"
                            :disabled="!files.length || !doneSyncUpload"
                            color="primary"
                            rounded
                        >Upload</v-btn>
                    </v-col>
                </v-row>
            </v-col>
        </v-card>
        <v-divider></v-divider>
        <v-data-table
            :headers="headers"
            :items="items"
            :search="search"
            sort-by="created_at"
            sort-desc
        >
            <template v-slot:top>
                <v-toolbar flat>
                    <v-col cols="9">
                        <v-text-field
                            v-model="search"
                            append-icon="mdi-magnify"
                            label="Search"
                            hide-details
                        ></v-text-field>
                    </v-col>
                    <v-col cols="3" class="text-right">
                        <ProgressCircular :doneSync="doneSyncRefresh"/>
                        <v-btn
                            color="green"
                            dark
                            class="mr-5 mb-2"
                            @click="btnRefresh()"
                            :disabled="!doneSyncRefresh"
                        >
                            Refresh
                        </v-btn>
                        <v-btn
                            color="orange"
                            class="mb-2"
                            dark
                            @click="btnFlash(null)"
                        >
                            Skycar
                        </v-btn>
                    </v-col>
                </v-toolbar>
            </template>
            <template v-slot:[`item.file_name`]="{ item }">
                <v-chip
                    color="deep-purple accent-4"
                    dark
                    label
                    small
                >
                    {{ item.file_name }}
                </v-chip>
            </template>
            <template v-slot:[`item.file_size`]="{ item }">
                {{ convertBytesToKB(item.file_size) }}
            </template>
            <template v-slot:[`item.created_at`]="{ item }">
                {{ convertStringToLocal(item.created_at, true) }}
            </template>
            <template v-slot:[`item.action`]="{ item }">
                <v-btn
                    class="mr-1"
                    @click="btnFlash(item.file_name)"
                    small
                    color="green"
                    dark
                >Flash</v-btn>
                <!-- <v-btn
                    class="mr-1"
                    @click="btnDownload(item.file_name)"
                    small
                    color="green"
                    dark
                >Download</v-btn> -->
                <v-btn
                    class="mr-1"
                    @click="btnDelete(item.file_name)"
                    small
                    color="red"
                    dark
                >Delete</v-btn>
            </template>
        </v-data-table>
        <SnackbarNotification ref="snackbarNotification"/>
        <DialogFlash ref="dialogFlash" :showNotification="showNotification"/>
    </v-card>
</template>

<script>
import { convertStringToLocal, getBTUrl, getRequestHeader, getRequestHeaderWithoutJson } from "../../helper/common"
import { RouteSkycarOTA } from "../../helper/enums"
import ProgressCircular from "../shared/ProgressCircular.vue"
import SnackbarNotification from "../shared/SnackbarNotification.vue"
import DialogFlash from "./DialogFlash.vue"
export default {
    components: {
        DialogFlash,
        ProgressCircular,
        SnackbarNotification
    },
    async created() {
        await this.btnRefresh()
    },
    data: () => ({
        convertStringToLocal,
        search: "",
        files: [],
        headers: [
            { text: "File Name", align: "start", value: "file_name" },
            { text: "File Size", value: "file_size" },
            { text: "Created At", value: "created_at" },
            { text: "Action", value: "action" }
        ],
        items: [],
        doneSyncRefresh: true,
        doneSyncUpload: true
    }),
    methods: {
        showNotification(success, message) {
            this.$refs.snackbarNotification.showNotification(success, message)
        },
        async btnRefresh() {
            try {
                this.doneSyncRefresh = false
                let url = getBTUrl() + RouteSkycarOTA.File
                let req = await fetch(url, { headers: getRequestHeader() })
                let res = JSON.parse(await req.text())
                if (res.status) {
                    this.items = res.data
                } else {
                    this.showNotification(false, res.message)
                }
            } catch (err) {
                this.showNotification(false, err)
            } finally {
                setTimeout(() => {
                    this.doneSyncRefresh = true
                }, 500)
            }
        },
        async btnUpload() {
            try {
                this.doneSyncUpload = false
                let formData = new FormData()
                this.files.forEach(function(file) {
                    formData.append("file", file)
                })
                let url = getBTUrl() + RouteSkycarOTA.File
                let req = await fetch(url, {
                    method: "POST",
                    body: formData,
                    headers: getRequestHeaderWithoutJson()
                })
                let res = JSON.parse(await req.text())
                if (res.status) {
                    this.files = []
                    this.showNotification(true, "Uploaded.")
                    await this.btnRefresh()
                } else {
                    this.showNotification(false, res.message)
                }
            } catch (err) {
                this.showNotification(false, err)
            } finally {
                setTimeout(() => {
                    this.doneSyncUpload = true
                }, 500)
            }
        },
        async btnFlash(fileName) {
            this.$refs.dialogFlash.openDialog(fileName)
        },
        async btnDelete(fileName) {
            try {
                let url = getBTUrl() + RouteSkycarOTA.File
                let req = await fetch(url, {
                    method: "DELETE",
                    body: JSON.stringify({
                        // eslint-disable-next-line camelcase
                        file_name: fileName
                    }),
                    headers: getRequestHeader()
                })
                let res = JSON.parse(await req.text())
                if (res.status) {
                    this.showNotification(true, "Deleted.")
                    await this.btnRefresh()
                } else {
                    this.showNotification(false, res.message)
                }
            } catch (err) {
                this.showNotification(false, err)
            }
        },
        async btnDownload(fileName) {
            try {
                console.log(fileName)
            } catch (err) {
                this.showNotification(false, err)
            }
        },
        convertBytesToKB(bytes) {
            if (bytes === 0) {
                return "0 KB"
            }
            let sizes = ["Bytes", "KB", "MB", "GB", "TB"]
            const i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)))
            return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${sizes[i]}`
        }
    }
}
</script>
