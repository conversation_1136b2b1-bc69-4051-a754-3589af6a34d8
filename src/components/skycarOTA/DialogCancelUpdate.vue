<template>
    <v-dialog
        v-model="dialogBool"
        width="1000"
    >
        <v-card>
            <v-toolbar 
                dark 
                color="red"
            >
                <v-toolbar-title>Cancel Update</v-toolbar-title>
            </v-toolbar>
            <v-card-text>
                <v-alert
                    type="error"
                    class="mt-2"
                    outlined
                >
                    Are you sure you want to cancel the update?
            </v-alert>
            <v-checkbox 
                v-model="checkbox" 
                label="I have inspected the skycar and found that the update is not needed. I want to cancel the update." 
            />
            </v-card-text>
            <v-card-actions>
                <v-spacer></v-spacer>
                <ProgressCircular v-if="!doneSync"/>
                <v-btn
                    color="green darken-1"
                    text
                    @click="btnCancel()"
                    :disabled="!checkbox || !doneSync"
                >Confirm
                </v-btn>
                <v-btn
                    color="green darken-1"
                    text
                    @click="closeDialog()"
                >Close
                </v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>
<script>
import { getHost, getRequestHeader } from "../../helper/common"
import { RouteSkycarOTA } from "../../helper/enums"
import ProgressCircular from "../shared/ProgressCircular.vue"
export default {
    components: {
        ProgressCircular
    },
    props: {
        showNotification: {
            type: Function
        }
    },
    data: () => ({
        dialogBool: false,
        zone: null,
        sids: null,
        type: null,
        checkbox: false,
        doneSync: true
    }),
    methods: {
        openDialog(zone, sids, type) {
            this.checkbox = false
            this.zone = zone
            this.sids = sids
            this.type = type
            this.dialogBool = true
        },
        closeDialog() {
            this.dialogBool = false
        },
        async btnCancel() {
            try {
                this.doneSync = false
                let url = getHost(this.zone) + RouteSkycarOTA.Update
                let req = await fetch(url, { 
                    method: "DELETE",
                    body: JSON.stringify({
                        sids: this.sids.map(item => item.sid),
                        type: this.type
                    }),
                    headers: getRequestHeader() 
                })
                let res = JSON.parse(await req.text())
                if (res.status) {
                    this.showNotification(true, "Canceled")
                    this.closeDialog()
                } else {
                    this.showNotification(false, res.message)
                }
            } catch (err) {
                this.showNotification(false, err)
            } finally {
                setTimeout(() => {
                    this.doneSync = true
                }, 500)
            }
        },
    }
}
</script>
