<template>
    <v-container fluid>
        <v-tabs
            dark
            v-model="tabs"
            align-with-title
        >
            <v-tabs-slider></v-tabs-slider>
            <v-tab>OTA Flash</v-tab>
            <v-tab>OTA Update</v-tab>
            <v-tab>Console</v-tab>
        </v-tabs>
        <v-tabs-items v-model="tabs">
            <v-tab-item>
                <TabSkycarOTAFlash/>
            </v-tab-item>
            <v-tab-item>
                <TabSkycarOTAUpdate/>
            </v-tab-item>
            <v-tab-item eager>
                <TabConsole/>
            </v-tab-item>
        </v-tabs-items>
    </v-container>
</template>

<script>
import TabConsole from './TabConsole.vue'
import TabSkycarOTAFlash from './TabSkycarOTAFlash.vue'
import TabSkycarOTAUpdate from './TabSkycarOTAUpdate.vue'
export default {
    components: {
        TabConsole,
        TabSkycarOTAFlash,
        TabSkycarOTAUpdate
    },
    data: () => ({
        tabs: null
    })
}
</script>
