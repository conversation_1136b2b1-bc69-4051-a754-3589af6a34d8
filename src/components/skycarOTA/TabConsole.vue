<template>
    <v-card dark>
        <v-card>
            <v-col>
                <v-row>
                    <v-col cols="4">
                        <v-combobox
                            v-model="skycars"
                            label="Skycar"
                            clearable
                            type="number"
                            outlined
                            multiple
                            chips
                        >
                            <template v-slot:selection="data">
                                <v-chip
                                    color="green"
                                >{{ data.item }}</v-chip>
                            </template>
                        </v-combobox>
                    </v-col>
                    <v-col cols="4">
                        <v-text-field
                            v-model="size"
                            label="Display Size"
                            type="number"
                            outlined
                        />
                    </v-col>
                </v-row>
            </v-col>
        </v-card>
        <v-data-table
            :item-class="style"
            disable-pagination
            hide-default-footer
            :headers="headers"
            :items="items"
            dense
            disable-sort
        >
            <template v-slot:[`item.created_at`]="{ item }">
                {{ convertStringToLocal(item.created_at) }}
            </template>
        </v-data-table>
        <span
                v-for="helper in helperButton"
                :key="helper.title"
            >
                <v-btn
                    dark
                    rounded
                    :color="helper[helper.status].color"
                    :style="`position:fixed; bottom:2%; right:${helper.right}%`"
                    @click="btnSwitcher(helper.mode)"
                >
                    <v-icon>{{helper[helper.status].icon}}</v-icon>
                </v-btn>
            </span>
    </v-card>
</template>
<script>
import { Websocket } from '../../helper/enums'
import { convertStringToLocal } from '../../helper/common'
import { socket } from '../../App.vue'
export default {
    created() {
        this.getMessage()
    },
    data: () => ({
        convertStringToLocal,
        headers: [
            {text: "Date Time", value: "created_at", width: 120},
            {text: "Skycar", value: "skycar", width: 120},
            {text: "Message", value: "message"},
        ],
        items: [],
        skycars: [],
        size: 20,
        helperButton: [
            {
                title: "Start",
                status: false,
                true: {
                    color: 'green',
                    icon: 'mdi-play'
                },
                false: {
                    color: 'red',
                    icon: 'mdi-pause'
                },
                right: 8,
                mode: 0
            },
            {
                title: "To Bottom",
                status: false,
                true: {
                    color: 'yellow',
                    icon: 'mdi-close-circle'
                    
                },
                false: {
                    color: 'orange',
                    icon: 'mdi-arrow-down'
                },
                right: 0,
                mode: 1
            }
        ]
    }),
    methods: {
        getMessage() {
            let here = this
            let items = here.items
            socket.on(Websocket.SKYCAR_OTA, function(item) {
                if (!here.suppress(item.skycar)) {
                    items.push(item)
                    here.items = items.slice(-parseInt(here.size))
                    if (here.helperButton[1].status) {
                        window.scrollTo(0, document.body.scrollHeight)
                    }
                }
            })
        },
        style() {
            return 'style'
        },
        btnSwitcher(mode) {
            let helper = this.helperButton[mode]
            helper.status = !helper.status
        },
        suppress(skycar) {
            if (this.helperButton[0].status) {
                return true
            }
            if (this.skycars.length == 0) {
                return false
            }
            return !this.skycars.includes(skycar)
        }
    }
}

export function closeMessage() {
    socket.off(Websocket.SKYCAR_OTA)
}
</script>
<style>
   .style td {
      height: 0px !important;
      border: hidden !important
   }
</style>
