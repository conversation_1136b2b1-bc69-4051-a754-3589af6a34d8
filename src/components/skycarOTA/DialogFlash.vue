<template>
    <v-dialog
        v-model="dialogBool"
        width="1000"
    >
        <v-card>
            <v-data-table
                v-model="clients"
                :headers="headers"
                :items="Object.values(items)"
                show-select
                item-key="skycar"
                sort-by="skycar"
            >
                <template v-slot:top>
                    <v-toolbar flat dark>
                        <v-toolbar-title>
                            <v-chip
                                v-if="fileName"
                                color="deep-purple accent-4"
                                dark
                                label
                                small
                            >{{ fileName }}
                            </v-chip>
                        </v-toolbar-title>
                        <v-spacer></v-spacer>
                        <ProgressCircular :doneSync="doneSyncRefresh"/>
                        <v-btn
                            color="green"
                            dark
                            class="mb-2"
                            @click="btnRefresh()"
                            :disabled="!doneSyncRefresh"
                        >
                            Refresh
                        </v-btn>
                    </v-toolbar>
                </template>
                <template v-slot:[`item.address`]="{ item }">
                    <v-tooltip
                        v-if="item.address"
                        bottom
                    >
                      <template v-slot:activator="{ on, attrs }">
                        <v-chip
                            v-bind="attrs"
                            v-on="on"
                            dark
                            label
                            small
                        >{{ item.address }}</v-chip>
                      </template>
                      <span>Last updated at {{ convertStringToLocal(item.updated_at, true) }}</span>
                    </v-tooltip>
                </template>
                <template v-slot:[`item.status`]="{ item }">
                    <v-chip
                        v-if="item.status"
                        :color=statusColor[item.status]
                        dark
                        label
                        small
                    >{{ item.status }}
                    </v-chip>
                </template>
                <template v-slot:[`item.file_name`]="{ item }">
                    <v-chip
                        v-if="item.file_name"
                        color="deep-purple accent-4"
                        dark
                        label
                        small
                    >{{ item.file_name }}
                    </v-chip>
                </template>
                <template v-slot:[`item.progress`]="{ item }">
                    <v-progress-circular
                        v-if="item.file_name"
                        :rotate="360"
                        :size="40"
                        :width="5"
                        :value="item.progress"
                        color="teal"
                    >
                        {{ parseInt(item.progress) }}
                    </v-progress-circular>
                </template>
                <template v-slot:[`item.error`]="{ item }">
                    <v-chip
                        v-if="item.error"
                        color="red"
                        dark
                        label
                        small
                    >{{ item.error }}
                    </v-chip>
                </template>
            </v-data-table>
            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn
                    v-if="fileName"
                    color="green darken-1"
                    text
                    @click="btnStartFlashing()"
                    :disabled="clients.length == 0 || !doneSyncFlash"
                >Flash
                </v-btn>
                <v-btn
                    color="green darken-1"
                    text
                    @click="btnCancelFlashing()"
                    :disabled="clients.length == 0 || !doneSyncCancel"
                >Cancel
                </v-btn>
                <v-btn
                    color="green darken-1"
                    text
                    @click="closeDialog()"
                >Close
                </v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>
<script>
import { socket } from "../../App.vue"
import { convertStringToLocal, getBTUrl, getRequestHeader } from "../../helper/common"
import { RouteSkycarOTA, Websocket } from "../../helper/enums"
import ProgressCircular from "../shared/ProgressCircular.vue"
export default {
    components: {
        ProgressCircular
    },
    created() {
        this.getMessage()
    },
    data: () => ({
        convertStringToLocal,
        dialogBool: false,
        headers: [
            { text: "Skycar", value: "skycar" },
            { text: "Address", value: "address" },
            { text: "Flashing Status", value: "status" },
            { text: "Flashing By", value: "file_name" },
            { text: "Progress (%)", value: "progress" },
            { text: "Error", value: "error" }
        ],
        items: {},
        fileName: null,
        clients: [],
        doneSyncFlash: true,
        doneSyncCancel: true,
        doneSyncRefresh: true,
        statusColor: {
            "UPLOADING": "green",
            "FLASHING": "green",
            "COMPLETED": "green",
            "CANCELLING": "red",
            "CANCELLED": "red"
        }
    }),
    props: {
        showNotification: {
            type: Function
        }
    },
    methods: {
        getMessage() {
            let here = this
            socket.on(Websocket.SKYCAR_OTA_PROGRESS, function(item) {
                if (item.skycar in here.items) {
                    Object.assign(here.items[item.skycar], item)
                }
            })
        },
        async openDialog(fileName) {
            this.fileName = fileName
            this.clients = []
            await this.btnRefresh()
            this.dialogBool = true
        },
        closeDialog() {
            this.dialogBool = false
        },
        async btnRefresh() {
            try {
                this.doneSyncRefresh = false
                let url = getBTUrl() + RouteSkycarOTA.Flash
                let req = await fetch(url, { headers:getRequestHeader() })
                let res = JSON.parse(await req.text())
                if (res.status) {
                    this.items = res.model
                } else {
                    this.showNotification(false, res.message)
                }
            } catch (error) {
                this.showNotification(false, error)
            } finally {
                setTimeout(() => {
                    this.doneSyncRefresh = true
                }, 500)
            }
        },
        async btnStartFlashing() {
            try {
                this.doneSyncFlash = false
                let url = getBTUrl() + RouteSkycarOTA.Flash
                let req = await fetch(url, {
                    method: "POST",
                    body: JSON.stringify({
                        "file_name": this.fileName,
                        "skycars": this.clients.map(client => client.skycar)
                    }),
                    headers: getRequestHeader()
                })
                let res = JSON.parse(await req.text())
                if (res.status) {
                    await this.btnRefresh()
                    this.showNotification(true, "Flushing.")
                } else {
                    this.showNotification(false, res.message)
                }
            } catch (err) {
                this.showNotification(false, err)
            } finally {
                setTimeout(() => {
                    this.doneSyncFlash = true
                }, 500)
            }
        },
        async btnCancelFlashing() {
            try {
                this.doneSyncCancel = false
                let url = getBTUrl() + RouteSkycarOTA.Flash
                let req = await fetch(url, {
                    method: "DELETE",
                    body: JSON.stringify({
                        "skycars": this.clients.map(client => client.skycar)
                    }),
                    headers: getRequestHeader()
                })
                let res = JSON.parse(await req.text())
                if (res.status) {
                    await this.btnRefresh()
                    this.showNotification(true, "Cancelling.")
                } else {
                    this.showNotification(false, res.message)
                }
            } catch (err) {
                this.showNotification(false, err)
            } finally {
                setTimeout(() => {
                    this.doneSyncCancel = true
                }, 500)
            }
        }
    }
}

export function closeMessage() {
    socket.off(Websocket.SKYCAR_OTA_PROGRESS)
}
</script>
