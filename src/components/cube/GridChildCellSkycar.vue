<template>
  <v-card color="transparent" flat>
    <v-container fluid>
      <v-layout justify-center align-center color="transparent">
        <v-flex shrink color="transparent">
          <v-img
            :style="{
              marginBottom: computedMarginBottomStyle,
              width: computedImageWidth,
              height: computedImageHeight,
            }"
            src="@/assets/pingspace.png"
            alt="Skycar Icon"
          ></v-img>
            <v-card :style="{ color: 'black', fontWeight: 'bold' }" flat color="transparent">{{
            computedSkycarValue
            }}</v-card>
        </v-flex>
      </v-layout>
    </v-container>
  </v-card>
</template>

<script>
export default {
  props: {
    skycarValue: Number,
    bottomStyle: {
      type: String,
      default: "30%", // Default value for bottomStyle
    },
    imageWidth: {
      type: String,
      default: "100%", // Default value for imageWidth
    },
    imageHeight: {
      type: String,
      default: "100%", // Default value for imageHeight
    },
  },
  computed: {
    computedMarginBottomStyle() {
      return this.bottomStyle;
    },
    computedImageWidth() {
      return this.imageWidth;
    },
    computedImageHeight() {
      return this.imageHeight;
    },
    computedSkycarValue() {
      return this.skycarValue;
    },
  },
};
</script>
