<!-- This component non-functional requirement can cope until matrix xy 600 stacks , 
    if larger matrix require refactor or remove the mousemove event during selection to prevent 
    latency, but reduce ui/ux. -->

<template>
  <v-container flex>
    <v-expansion-panels dark>
      <v-expansion-panel>
        <v-expansion-panel-header class="text-h5">
          Matrix {{ cubeDescName }}
          <div class="ml-4 align-right justify-space-around">
            <v-avatar size="x-large">
              <v-icon :color="gridConnected ? 'green' : 'red'">
                {{ gridConnected ? "mdi-checkbox-marked" : "mdi-close-box" }}
              </v-icon>
            </v-avatar>
          </div>
        </v-expansion-panel-header>


        <!-- legend  -->
        <v-expansion-panel-content>
          <GridChildLegend :legendCardStyle="legendCardStyle" :legendTextStyle="legendTextStyle"></GridChildLegend>
        </v-expansion-panel-content>

        <!-- action panel (drag mode, show parking and etc) -->
        <v-expansion-panel-content>
          <v-card flat class="text-h5">
            Action Panel
            <v-row align="center">
              <v-col cols="12" md="2"><v-checkbox c v-model="optionSelect.dragMode" label="Drag Mode"></v-checkbox>
              </v-col>
              <v-col cols="12" md="2"><v-checkbox c v-model="optionShow.qr" label="Show QR"></v-checkbox>
              </v-col>
              <v-col cols="12" md="2"><v-checkbox c v-model="optionShow.skycar" label="Show Skycar"></v-checkbox>
              </v-col>
              <v-col>
                <v-checkbox
                  v-model="optionShow.parking"
                  label="Parking Coordinate"
                ></v-checkbox>
              </v-col>
              <v-col cols="12" md="2">
                <v-btn class="white--text" @click="resetSelection()">
                  Clear
                  <v-icon right>mdi-backspace</v-icon>
                </v-btn>
              </v-col>
             
            </v-row>
          </v-card>
        </v-expansion-panel-content>
      </v-expansion-panel>
    </v-expansion-panels>

    <div class="scroll-container">
      <div class="content">
        <div class="grid2">
          <div class="row2" v-for="y in matrixYRange" :key="y">
            <div class="y-label2" :style="cellStyle()">
              {{ displayYLabel(y) }}
            </div>

            <div v-for="x in matrixXRange" :key="x">
              <v-tooltip right>
                <template v-slot:activator="{ on, attrs }">
                  <v-hover v-slot="{}">
                    <div :key="y" class="column2" :class="{ selected: isCellSelected(x, y) }"
                      :style="getCellColor(x, y)" @mousedown="startSelection(x, y, $event)"
                      @touchmove="touchUpdate(x, y)" @mousemove="updateSelection(x, y)"
                      @mouseup="endSelection(x, y, $event)" @contextmenu.prevent v-bind="attrs" v-on="on">

                      <!-- <v-card class="d-flex align-center justify-center" v-if=optionShow.qr flat color="black">
                        {{ getQRCellText(x, y) }}
                        </v-card> -->

                      <GridChildCellQR :key="componentKey"
                        v-if="optionShow.qr"
                        :qrValue="getQRCellText(x, y)"></GridChildCellQR>

                      <GridChildCellSkycar :key="componentKey"
                        v-if="skycarDisplay(x, y) && optionShow.skycar && !optionShow.qr"
                        :skycarValue="skycarDisplay(x, y)"></GridChildCellSkycar>
                    </div>
                  </v-hover>
                </template>

                <v-container fluid>
                  <v-card flat width="200">
                    <v-card flat class="ma-2 text-h6">
                      Stack: {{ displayXY(x, y) }}
                    </v-card>

                    <v-card v-for="obstacleType in isObstacle(x, y)" :key="obstacleType">
                      <v-row class="ma-2">
                        <v-col cols="9">
                          <pre>{{ obstacleType }}</pre>
                        </v-col>

                        <v-col cols="3">
                          <v-card flat :style="{ height: '100%', width: '100%' }"
                            :color="getColorByEnumType(obstacleType)"></v-card>
                        </v-col>
                      </v-row>
                    </v-card>
                  </v-card>
                </v-container>
              </v-tooltip>
            </div>
          </div>

          <div class="row2" :key="matrixMaxY + 1" v-if="matrixMaxY - matrixMinY != 0">
            <div class="x-label2" :style="cellStyle()" v-html="displayXLabel"></div>

            <div class="x-label2" :style="cellStyle()" v-for="x in matrixXRange" :key="x">
              {{ x }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <v-dialog v-model="dialog" transition="dialog-bottom-transition" @close="dialog = false">
      <v-container fluid>
        <v-card>
          <v-card-title>{{ this.longPressSelection.x }},{{
            this.longPressSelection.y
            }}</v-card-title>
          <v-card-text>
            <!-- The content of your context menu -->
            <ul>
              <li>Menu Item 1</li>
              <li>Menu Item 2</li>
              <li>Menu Item 3</li>
            </ul>
          </v-card-text>
          <v-card-actions>
            <v-btn @click="dialog = false">Close</v-btn>
          </v-card-actions>
        </v-card>
      </v-container>
    </v-dialog>
  </v-container>
</template>

<script>
import GridChildLegend from "../cube/GridChildLegend.vue";
import GridChildCellSkycar from "../cube/GridChildCellSkycar.vue";
import GridChildCellQR from "../cube/GridChildCellQR.vue";
import { SkycarStatus } from "../../helper/enums.js";
import { getGridStatus } from "../../helper/common.js";

export default {
  emits: [
    "grid-selected",
    "grid-selected-reset",
    "cell-long-press",
    "cell-right-press",
  ],
  expose: ["updateObstacle", "updateSkycar", "updateGrid", "updateAll", "resetAll"],
  props: {
    cubeDescName: String,
    gridObject: {
      type: Object,
      default: () => ({
        gridName: "C",
        gridMinX: 0,
        gridMinY: 0,
        gridMinZ: 0,
        gridMaxX: 25,
        gridMaxY: 18,
        gridMaxZ: 11,
      }),
    },
    obstacleObject: {
      type: Object,
      default: () => ({
        "1,1": {
          x: 1,
          y: 1,
          type: ["SKYCAR"],
          sid: [9],
        },
        "2,1": {
          x: 2,
          y: 1,
          type: ["MAINTENANCE"],
          sid: [1],
        },
        "3,9": {
          x: 3,
          y: 9,
          type: ["MAINTENANCE"],
          sid: [3],
        },
        "7,7": {
          x: 7,
          y: 7,
          type: ["PILLAR"],
          sid: null,
        },
        "7,0": {
          x: 7,
          y: 0,
          type: ["INACTIVE"],
          sid: null,
        },
        "6,0": {
          x: 6,
          y: 0,
          type: ["INACTIVE"],
          sid: null,
        },
        "8,1": {
          x: 8,
          y: 1,
          type: ["SKYCAR_DOCKED"],
          sid: [1],
        },
        "9,1": {
          x: 9,
          y: 1,
          type: ["SERVICE_DOCK"],
          sid: null,
        },
        "10,1": {
          x: 10,
          y: 1,
          type: ["SERVICE_DOCK"],
          sid: null,
        },
        "10,0": {
          x: 10,
          y: 0,
          type: ["SERVICE_RAILWAY"],
          sid: null,
        },
        "9,0": {
          x: 9,
          y: 0,
          type: ["SERVICE_RAILWAY"],
          sid: null,
        },
        "8,0": {
          x: 8,
          y: 0,
          type: ["SERVICE_RAILWAY"],
          sid: null,
        },
        "11,0": {
          x: 11,
          y: 0,
          type: ["SERVICE_RAILWAY"],
          sid: null,
        },
      }),
    },
    skycarObject: {
      type: Object,
      default: () => ({
        "4,7": {
          sid: 7,
        },
        "3,9": {
          sid: 3,
          status: "MAINTENANCE",
        },
        "7,9": {
          sid: 11,
          status: "MAINTENANCE",
        },
        "4,9": {
          sid: 15,
        },
        "6,4": {
          sid: 17,
        },
        "4,3": {
          sid: 10,
        },
        "8,3": {
          sid: 12,
          status: "MAINTENANCE",
        },
      }),
    },
    singleSelection: {
      type: Boolean,
      default: false
    },
    gridQRObject: {
      type: Object,
      default: () => ({
        "0,1": "23333",
        "0,3": "13333"
      })
    }
  },
  watch: {
    dialog(newVal) {
      if (!newVal) {
        // The dialog is closing
        this.onDialogClosed();
      }
    },

    "optionSelect.dragMode"() {
      this.resetSelection();
    },
    "optionShow.parking"() {
      this.parkingCellsXYSet.forEach(coord => {
        this.modifyColorInTwoD(coord, "PARKING", this.optionShow.parking);
      });
    }
   
  },
  components: { GridChildLegend, GridChildCellSkycar , GridChildCellQR },
  data() {
    return {
      //Local
      obstacleObjectData: this.obstacleObject,
      skycarObjectData: this.skycarObject,
      gridObjectData: this.gridObject,
      gridSingleSelection: this.singleSelection,
      gridQRData: this.gridQRObject,

      optionShow: {
        coordinate: false,
        skycar: true,
        smMode: false,
        networkDown: false,
        qr: false,
        parking: false
      },

      optionSelect: {
        dragMode: true,
      },

      // Grid
      cellColorsInitialized: false,
      matrixOffset: 1,
      cellWidth: 40,
      cellHeight: 50,

      // Select
      isSelecting: false,
      startX: null,
      startY: null,
      endX: null,
      endY: null,
      selectedCells: [],

      cellColors: {},
      restrictSelectCellsXYSet: new Set(),

      // Long press
      longPress: null,
      longPressTimer: null,
      longPressDuration: 1000,
      longPressSelection: {
        x: null,
        y: null,
      },

      // Context Menu
      dialog: false,

      // Child
      componentKey: 0,
    };
  },
  created() {
    console.log(`Screen Size:${this.$vuetify.breakpoint.name}`);
    this.initializeCellColor();
  },

  computed: {
    gridConnected() {
      console.log(
        `Grid Status: ${this.matrixMaxY - this.matrixMinY === 0 ? false : true}`
      );
      return this.matrixMaxY - this.matrixMinY === 0 ? false : true;
    },
    matrixYRange() {
      const start = this.matrixMinY;
      const end = this.matrixMaxY;
      const range = [];
      if (start == 0 && end == 0) {
        return range;
      }
      for (let i = start; i <= end; i++) {
        if (i !== undefined && i >= 0) {
          range.push(i);
        }
      }
      return range;
    },
    matrixXRange() {
      const start = this.matrixMinX;
      const end = this.matrixMaxX;
      const range = [];
      for (let i = start; i <= end; i++) {
        range.push(i);
      }
      return range;
    },
    matrixMaxX() {
      return this.gridObjectData.gridMaxX;
    },
    matrixMaxY() {
      return this.gridObjectData.gridMaxY;
    },
    matrixMinX() {
      return this.gridObjectData.gridMinX;
    },
    matrixMinY() {
      return this.gridObjectData.gridMinY;
    },

    matrixLengthX() {
      return this.matrixMaxX - this.matrixMinX;
    },
    matrixLengthY() {
      return this.matrixMaxY - this.matrixMinY;
    },

    labelStyle() {
      return {
        width: this.cellWidth + "px",
        height: this.cellHeight + "px",
      };
    },
    legendTextStyle() {
      return {
        width: this.cellWidth * 1.5 + "px",
        height: this.cellHeight * 0.5 + "px",
        "font-size": "15px",
        "white-space": "nowrap",
      };
    },
    legendCardStyle() {
      return {
        width: this.cellWidth * 1.5 + "px",
        height: this.cellHeight * 1.5 + "px",
      };
    },
    displayXLabel() {
      return "y &uarr; <br /> x &rarr;";
    },
  },
  methods: {
    getGridStatus,
    displayYLabel(y) {
      return this.matrixMaxY + this.matrixMinY - y;
    },
    displayXY(x, y) {
      return `${x},${this.matrixMaxY + this.matrixMinY - y}`;
    },
    cellStyle() {
      return {
        width: this.cellWidth + "px",
        height: this.cellHeight + "px",
      };
    },
    updateStartXY(x, y) {
      const { inverseX, inverseY } = this.inverse(x, y);
      this.startX = inverseX;
      this.startY = inverseY;

      if (this.gridSingleSelection) {
        this.selectedCells = []
      }

      if (this.optionSelect.dragMode) {
        this.selectedCells.push({ x: inverseX, y: inverseY });
      } else {
        const cellKey = `${inverseX},${inverseY}`;
        this.selectedCells.push({ x: inverseX, y: inverseY, twod: cellKey });
      }
    },
    updateEndXY(x, y) {
      if (this.optionSelect.dragMode) {
        const { inverseX, inverseY } = this.inverse(x, y);
        this.endX = inverseX;
        this.endY = inverseY;
        this.selectedCells = this.getSelectedCells();
      }
    },

    startSelection(x, y, event) {
      if (event.button === 2) {
        // right click not user friendly for mobile when technician inside cube , not priority to add feature on right click.
        this.handleRightClick(x, y);
      } else {
        // left click or scroll , ignore scroll for now.
        this.handleLeftClick(x, y);
      }
    },
    handleRightClick() {
      if (!this.gridSingleSelection) {
        this.resetSelection();
        // this.updateStartXY(x, y);
        this.onRightPressEvent();
      }
    },
    handleLeftClick(x, y) {
      const { inverseX, inverseY } = this.inverse(x, y);
      if (
        this.selectedCells.length === 1 &&
        this.selectedCells[0].x === inverseX &&
        this.selectedCells[0].y === inverseY
      ) {
        this.resetSelection();
        return;
      }

      // this.resetSelection();
      if (!this.gridSingleSelection) {
        this.isSelecting = true;
      }
      // this.isSelecting = true;
      this.updateStartXY(x, y);
      // Start the long-press timer
      this.startLongPressTimer();
    },
    startLongPressTimer() {
      this.longPress = false;
      console.log("start long press timer");
      this.longPressTimer = setTimeout(() => {
        this.longPress = true;
        this.onLongPressEvent();
        // this.openDialog();
      }, this.longPressDuration);
    },
    updateSelection(x, y) {
      if (this.isSelecting) {
        this.updateEndXY(x, y);

        // Reset long press
        console.log("clear long press");
        clearTimeout(this.longPressTimer);
      }
    },
    touchUpdate(x, y) {
      console.log(`Touch update ${x},${y}`);
    },
    isCellSelected(x, y) {
      const { inverseX, inverseY } = this.inverse(x, y);
      const cellKey = `${inverseX},${inverseY}`;

      if (this.restrictSelectCellsXYSet.has(cellKey)) {
        return;
      }

      return this.selectedCells.some(
        (cell) => cell.x === inverseX && cell.y === inverseY
      );
    },
    getSelectedCells() {
      const selected = [];

      for (
        let x = Math.min(this.startX, this.endX);
        x <= Math.max(this.startX, this.endX);
        x++
      ) {
        for (
          let y = Math.min(this.startY, this.endY);
          y <= Math.max(this.startY, this.endY);
          y++
        ) {
          const cellKey = `${x},${y}`;

          if (!this.restrictSelectCellsXYSet.has(cellKey)) {
            selected.push({ x, y, twod: cellKey });
          }
        }
      }
      return selected;
    },
    inverse(x, y) {
      // Inverse to cartesian plane coordinatea
      return { inverseX: x, inverseY: this.matrixMaxY + this.matrixMinY - y };
    },
    skycarDisplay(x, y) {
      // Not handle multilple skycar at same coordinate rendering.
      const { inverseX, inverseY } = this.inverse(x, y);
      const cellKey = `${inverseX},${inverseY}`;
      const value = this.skycarObjectData[cellKey];
      if (value) {
        if (value.status === SkycarStatus.MAINTENANCE) {
          const obstacleObject = this.obstacleObjectData[cellKey];
          if (obstacleObject && obstacleObject.sid) {
            if (obstacleObject.sid.includes(value.sid)) {
              return value.sid;
            }
          } else {
            return null;
          }
        }
      }

      return value && value.sid ? value.sid : null;
    },
    isObstacle(x, y) {
      const { inverseX, inverseY } = this.inverse(x, y);
      const cellKey = `${inverseX},${inverseY}`;
      const value = this.obstacleObjectData[cellKey];

      if (value) {
        return this.obstacleObjectData[cellKey].type;
      }
      return false;
    },
    onClickOutside() {
      this.optionShow.networkDown = false;
    },

    //#region color region
    initializeCellColor() {
      this.cellColors = [];
      this.restrictSelectCellsXYSet = new Set();
      this.parkingCellsXYSet = new Set();
      Object.entries(this.obstacleObjectData).forEach(([key, value]) => {
        value.type.forEach((e) => {
          this.enumToColor(key, e);
        });
      });

      // update parking 
      this.parkingCellsXYSet.forEach(coord => {
        this.modifyColorInTwoD(coord, "PARKING", this.optionShow.parking);
      });



    },
    getCellColor(x, y) {
      const { inverseX, inverseY } = this.inverse(x, y);
      const cellKey = `${inverseX},${inverseY}`;
      const style = this.cellStyle();
      const selectedColor = ["#006666", "#9e9e9e"];

      if (this.cellColors[cellKey]) {
        let val = this.cellColors[cellKey];

        if (val.length === 1) {
          if (this.isCellSelected(x, y)) {
            style.background = `linear-gradient(375deg, ${val[0]
              }, ${selectedColor.join(", ")}`;
          } else {
            style.backgroundColor = val[0];
          }
        } else if (val.length > 1) {
          if (this.isCellSelected(x, y)) {
            style.background = `linear-gradient(375deg, ${val.join(
              ", "
            )}, ${selectedColor.join(", ")}`;
          } else {
            style.background = `linear-gradient(375deg, ${val.join(", ")}`;
          }
        }
        return style;
      } else {
        return style;
      }
    },
    getColorByEnumType(type) {
      const legend = getGridStatus(type);
      return legend.color;
    },
    enumToColor(twoD, type) {
      if (getGridStatus(type)) {
        const legend = getGridStatus(type);
        const cellColor = legend.color || "#FFFFFF";

        if (!Array.isArray(this.cellColors[twoD])) {
          this.cellColors[twoD] = [];
        }

        if (["INACTIVE", "PILLAR", "SERVICE_RAILWAY"].includes(type)) {
          this.restrictSelectCellsXYSet.add(twoD);
        }

        if (type == "PARKING"){
          this.parkingCellsXYSet.add(twoD)
          return
        }

        this.cellColors[twoD].push(cellColor);
      }
    },
    getQRCellText(x, y) {
      const { inverseX, inverseY } = this.inverse(x, y);
      const cellKey = `${inverseX},${inverseY}`;

      return this.gridQRData[cellKey] || "";

    },

  /**
   * Modifies the color in the two-dimensional grid based on the specified type.
   * 
   * @param {string} twoD - The key representing the two-dimensional coordinate. e.g "1,1"
   * @param {string} type - The type of coordinate, used to determine the color. e.g "PARKING"
   * @param {boolean} isAdd - A flag indicating whether to add or remove the color. 
   */
    modifyColorInTwoD(twoD, type, isAdd) {
      // When user toogle on/off to display specific type of coordinate
      const legend = getGridStatus(type);
      console.log(legend)
      const cellColor = legend.color || "#FFFFFF";

      if (!Array.isArray(this.cellColors[twoD])) {
        this.cellColors[twoD] = [];
      }

      if (isAdd) {
        this.cellColors[twoD].push(cellColor)
      } else {
        this.cellColors[twoD] = this.cellColors[twoD].filter(color => color !== cellColor);     
      }
    },
    //#endregion

    //#region Parent mutate child , public function

    updateAll(ObstacleObject, SkycarObject, GridObject, GridQRObject, SingleSelection = false) {
      this.obstacleObjectData = ObstacleObject;
      this.skycarObjectData = SkycarObject;
      this.gridObjectData = GridObject;
      this.gridSingleSelection = SingleSelection;
      this.gridQRData = GridQRObject;
      this.initializeCellColor();
    },

    // updateGridQr(GridQRObject){
    //   this.gridQRData = GridQRObject
    //   this.initializeCellColor();
    // },

    updateObstacle(ObstacleObject) {
      this.obstacleObjectData = ObstacleObject;
      this.initializeCellColor();
    },
    updateSkycar(SkycarObject) {
      this.skycarObjectData = SkycarObject;
      this.initializeCellColor();
    },

    updateGrid(GridObject) {
      this.gridObjectData = GridObject;
      this.initializeCellColor();
    },

    resetAll() {
      this.resetSelection()
    },
    // updateSize(GridSizeObject) {
    //   this.cellWidth = GridSizeObject.width;
    //   this.cellHeight = GridSizeObject.height;
    //   this.componentKey += 1;
    // },
    //#endregion

    //#region Dialog feature
    openDialog() {
      this.longPressSelection = {
        x: this.startX,
        y: this.startY,
      };

      // this.dialog = true;
    },

    onDialogClosed() {
      // Handle the dialog being closed
      console.log("Dialog has been closed.");
      this.resetSelection();
    },

    //#endregion

    //#region events handler for parent register to receive on event change.
    onLongPressEvent() {
      this.$emit("cell-long-press");
    },
    onRightPressEvent() {
      // this.$awn.info("right click");
      this.$emit("cell-right-press");
    },
    resetSelection() {
      this.startX = null;
      this.startY = null;
      this.endX = null;
      this.endY = null;
      this.isSelecting = false;
      this.selectedCells = [];

      this.$emit("grid-selected-reset");
    },
    endSelection(x, y, event) {
      clearTimeout(this.longPressTimer);
      this.updateSelection(x, y);
      this.isSelecting = false;
      // this.capturedRegion();
      if (!(this.gridSingleSelection && event.button === 2)) {
        this.$emit("grid-selected", this.selectedCells);
      }
    },

    //#endregion
  },
};
</script>

<style scoped>
.scroll-container {
  width: 100%;
  overflow: auto;
  border: 1px solid #746f6f;
}

.content {
  width: 1000px;
}

.grid {
  white-space: nowrap;
}

.selected {
  background: linear-gradient(135deg, #006666, #9e9e9e);
  animation: pulse 1s infinite alternate;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  100% {
    transform: scale(1.07);
  }
}

/* Latest */
.grid2 {
  display: flex;
  flex-direction: column;
}

.row2 {
  display: flex;
}

.column2 {
  flex: 0 0 auto;
  border: 1px solid #e2e0e0;
  color: #413f3f;
  text-align: center;
  width: 80px;
  height: 100px;
  font-size: 11px;
}

/* Axes label css */
.x-label2,
.y-label2 {
  flex: 0 0 auto;
  text-align: center;
  display: inline-block;
  font-size: 14px;
  width: 100%;
  height: 100%;
  color: #a8a1a1;
}

.x-label2 {
  border-right: thin dotted #ccc;
  border-top: 1px solid #e2e0e0;
}

.y-label2 {
  border-right: 1px solid #e2e0e0;
  border-bottom: thin dotted #ccc;
}

.x-label2,
.y-label2,
.column2 {
  user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}
</style>
