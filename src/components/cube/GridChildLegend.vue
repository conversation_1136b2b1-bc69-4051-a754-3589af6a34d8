<template>
    <v-card flex class="pa-4" dark>
      <v-row>
        <v-col cols="4" xl="1" md="2" sm="6" >
          <v-card color="white" class="legend-column" :style="legendCardStyle">
            <img
              src="@/assets/pingspace.png"
              alt="Skycar Icon"
              class="skycar-icon no-drag"
            />
  
            <div color="transparent" class="legend-skycar-display">1</div>
            <div class="legend-desc-display-bottom d-flex align-center">
              Skycar
            </div>

          
          </v-card>
          <div :style="legendTextStyle"> Active Skycar</div>
        </v-col>
         
        <v-col cols="4" xl="1" md="2" sm="3" v-for="desc in gridLegends" :key="desc">
          <v-tooltip bottom>
            <template v-slot:activator="{ on }">
              <v-card
                :color="getGridStatus(desc).color"
                class="legend-column d-flex align-center"
                :style="legendCardStyle"
                v-on="on"
              >
               
              </v-card>
              <v-card  flat v-on="on" :style="legendTextStyle">{{ getGridStatus(desc).text }}</v-card>
            </template>
            <span><b>{{ getGridStatus(desc).desc }}</b></span>
          </v-tooltip>
        </v-col>
      </v-row>
    </v-card>
  </template>
  
  <script>
  import { getGridStatus } from "../../helper/common.js";
  import { GridStatus } from "../../helper/enums.js";
  export default {
    props: {
      legendCardStyle: Object,
      legendTextStyle: Object 
    },
    data() {
      return {
        gridLegends: Object.keys(GridStatus)
      }
    },
    methods: {
      getGridStatus
    }
  };
  </script>
  
  <style scoped>

.legend-skycar-display {
  font-size: 15px;
  font-weight: bold;
}

.legend-desc-display-bottom {
  font-size: 12px;
  font-weight: bold;
  justify-content: center;
}
.skycar-display {
  font-size: 20px;
  font-weight: bold;
  background-color: transparent;
}

.skycar-icon {
  width: 30%; /* Adjust the width as needed */
  height: 30%; /* Adjust the height as needed */
}
.legend-column {
  flex: 0 0 auto;
  border: 1px solid #ffffff;
  color: #413f3f;
  text-align: center;
  justify-content: center;
  font-size: bold 11px;
  user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}

  </style>
  