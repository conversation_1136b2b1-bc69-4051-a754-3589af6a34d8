<template>
    <v-card color="transparent" flat>
        <v-container fluid>
            <v-layout justify-start align-start color="transparent">
                <v-flex shrink color="transparent">
                    <v-card flat color="transparent" class="text-container">{{
                        computedQR
                        }}</v-card>
                </v-flex>
            </v-layout>
        </v-container>
    </v-card>
</template>

<script>
export default {
    props: {
        qrValue: String,
    },
    computed: {
        computedQR() {
            return this.qrValue;
        },
    },
};
</script>

<style scoped>
.text-container {
    font-size: 0.6rem; /* Makes the text smaller */
    white-space: nowrap; /* Prevents text from wrapping to the next line */
    text-align: left; /* Aligns the text to the left */
    margin-left: -0.5rem; /* Moves the text further to the left */
}
</style>