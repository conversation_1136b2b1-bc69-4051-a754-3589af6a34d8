<template>
    <apexchart
        type="bar" 
        :options="options" 
        :series="series"
        ref="apexchart"
        height="370"
        @zoomed="emitInterval"
    />
</template>
  
<script>
import { getBTUrl, getRequestHeader } from "../../../helper/common"
import { Palette, RouteAnalysis } from "../../../helper/enums"

export default {
    data: () => ({
        props: {
            showNotification: {
                type: Function
            }
        },
        series: [],
        options: {
            chart: {
                type: "bar",
                toolbar: {
                    tools: {
                        pan: false
                    }
                }
            },
            theme: {
                mode: "dark",
                monochrome: {
                    enabled: true,
                    color: "#fca503"
                }
            },
            dataLabels: {
                enabled: false
            },
            xaxis: {
                title: {
                    text: "Job Duration (seconds)"
                }
            },
            yaxis: {
                title: {
                    text: "Number of Jobs"
                }
            },
            legend: {
                showForSingleSeries: true,
                onItemClick: {
                    toggleDataSeries: false
                }
            },
            tooltip: {
                x: {
                    formatter: function(value) {
                        return `${value} seconds`
                    }
                },
                y: {
                    formatter: function(value) {
                        return `${value} jobs`
                    }
                }
            }
        },
    }),
    methods: {
        async getData(ids) {
            try {
                let url = getBTUrl() + RouteAnalysis.JOB_DURATION_STATISTICS
                let qs = new URLSearchParams()
                ids.forEach(element => {
                    qs.append("ids", element)
                })
                let req = await fetch(`${url}?${qs}`, {
                    method: "GET",
                    headers: getRequestHeader()
                })
                let res = JSON.parse(await req.text())
                res.data.forEach((element, index) => {
                    element.color = Palette[index]
                })
                this.series = res.data

            } catch (error) {
                this.showNotification(false, error)
            }
        },
        emitInterval () {
            this.$emit("get-interval", arguments[1].xaxis)
        },
        toggleZoom(bool) {
            this.$refs.apexchart.updateOptions({
                chart: {
                    zoom: {
                        enabled: bool
                    }
                }
            })
        }
    }
}
</script>
