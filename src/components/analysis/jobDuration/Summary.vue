<template>
    <v-data-table
        :headers="headers"
        :items="items"
        :items-per-page="5"
        :value="value"
        :loading="!doneSync"
        show-select
        @input="selectRow($event)"
        class="secondary"
    ></v-data-table>
</template>

<script>
import { getBTUrl, getRequestHeader } from "../../../helper/common";
import { RouteAnalysis } from "../../../helper/enums";

export default {
    props: {
        getJobDuration: {
            type: Function
        },
        toggleZoom: {
            type: Function
        },
        showNotification: {
            type: Function
        }
    },
    async created() {
        await this.getData()
        this.value.push(this.items[0])
    },
    data: () => ({
        doneSync: true,
        headers: [
            { text: "Id", value: "id" },
            { text: "Average", value: "avg" },
            { text: "0% Percentile", value: "0%ile" },
            { text: "5% Percentile", value: "5%ile" },
            { text: "50% Percentile", value: "50%ile" },
            { text: "95% Percentile", value: "95%ile" },
            { text: "100% Percentile", value: "100%ile" },
            { text: "Sample Size", value: "size" }
        ],
        items: [],
        value: []
    }),
    methods: {
        async getData(lower, upper) {
            try {
                this.toggleSync(false)
                let url = getBTUrl() + RouteAnalysis.JOB_DURATION_STATISTICS_SUMMARY
                if (lower && upper) {
                    let qs = new URLSearchParams({
                        lower: Math.floor(lower),
                        upper: Math.ceil(upper)
                    })
                    url = `${url}?${qs}`
                }
                let req = await fetch(url, {
                    method: "GET",
                    headers: getRequestHeader()
                })
                let res = JSON.parse(await req.text())
                this.items = this.sort(res.data)
            } catch (error) {
                this.showNotification(false, error)
            } finally {
                setTimeout(() => {
                    this.toggleSync(true)
                }, 1000)
            }
        },
        selectRow(series) {
            if (series.length != 0) {
                let ids = []
                series.forEach(element => {
                    ids.push(element.id)
                })
                this.getJobDuration(ids)
            }
        },
        toggleSync(bool) {
            this.doneSync = bool
            // try {
            //     this.toggleZoom(bool)
            // } catch (err) {
            //     console.log(err)
            // }
        },
        sort(items) {
            return items.sort((a, b) => {
                return parseInt(a.id.substring(2)) - parseInt(b.id.substring(2))
            })
        }
    }
}
</script>
