<template>
    <v-container>
        <v-row>
            <v-col cols="5">
                <Summary
                    :get-job-duration="getJobDuration"
                    :toggle-zoom="toggleZoom"
                    :show-notification="showNotification"
                    ref="summary"
                />
            </v-col>
            <v-col cols="7">
                <Chart 
                    :show-notification="showNotification"
                    @get-interval="getInterval"
                    ref="chart"
                />
            </v-col>
        </v-row>
    </v-container>
</template>

<script>
import Chart from "./Chart.vue"
import Summary from "./Summary.vue"
export default {
    props: {
        showNotification: {
            type: Function
        }
    },
    components: {
        Chart,
        Summary,
    },
    data: () => ({

    }),
    methods: {
        getJobDuration(ids) {
            this.$refs.chart.getData(ids)
        },
        getInterval(interval) {
            this.$refs.summary.getData(interval.min, interval.max)
        },
        toggleZoom(bool) {
            this.$refs.chart.toggleZoom(bool)
        }
    }
}
</script>
