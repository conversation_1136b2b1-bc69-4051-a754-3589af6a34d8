<template>
    <apexchart
        type="bar" 
        :options="options" 
        :series="series"
        ref="apexchart"
    />
</template>
  
<script>
import { getBTUrl, getRequestHeader } from "../../../helper/common"
import { Palette, RouteAnalysis } from "../../../helper/enums"

export default {
    props: {
        showNotification: {
            type: Function
        }
    },
    async created() {
        await this.getJob()
    },
    data: () => ({
        series: [],
        options: {
            chart: {
                type: "bar",
                stacked: true,
                toolbar: {
                    tools: {
                        zoom: false
                    }
                }
            },
            title: {
                text: "Number of Jobs",
                align: "centre",
            },
            theme: {
                mode: "dark"
            },
            dataLabels: {
                enabled: false
            },
            xaxis: {
                type: "datetime",
                labels: {
                    format: "dd MMM"
                }
            },
            tooltip: {
                y: {
                    formatter: function(value) {
                        return `${value} jobs`
                    }
                },
            },
            plotOptions: {
              bar: {
                dataLabels: {
                  total: {
                    enabled: true,
                    style: {
                      color:  "#FFFFFF"
                    }
                  }
                }
              },
            },
        },
    }),
    methods: {
        async getJob() {
            try {
                let url = getBTUrl() + RouteAnalysis.JOB_RATIO
                let req = await fetch(url, {
                    method: "GET",
                    headers: getRequestHeader()
                })
                let res = JSON.parse(await req.text())
                this.$refs.apexchart.updateOptions({
                  xaxis: { categories: res.model.categories }
                })
                res.model.data.forEach((element, index) => {
                    element.color = Palette[index]
                })
                this.series = res.model.data
            } catch (error) {
                this.showNotification(false, error)
            }
        }
    }
}
</script>
