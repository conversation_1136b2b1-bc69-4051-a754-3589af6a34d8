<template>
    <apexchart
        type="line" 
        :options="options" 
        :series="series"
        ref="apexchart"
    />
</template>
  
<script>
import { getBTUrl, getRequestHeader } from "../../../helper/common"
import { Palette, RouteAnalysis } from "../../../helper/enums"

export default {
    props: {
        showNotification: {
            type: Function
        }
    },
    created() {
        this.getData()
    },
    data: () => ({
        series: [],
        options: {
            chart: {
                type: "line",
                toolbar: {
                    tools: {
                        pan: false
                    }
                }
            },
            title: {
                text: "UPH",
                align: "centre"
            },
            theme: {
                mode: "dark"
            },
            dataLabels: {
                enabled: false
            },
            xaxis: {
                type: "datetime",
                labels: {
                    format: "dd MMM"
                }
            },
            tooltip: {
                y: {
                    formatter: function(value) {
                        return `${value} jobs per hour`
                    }
                }
            },
            markers: {
                size: 5
            }
        },
    }),
    methods: {
        async getData() {
            try {
                let url = getBTUrl() + RouteAnalysis.UPH
                let req = await fetch(url, {
                    method: "GET",
                    headers: getRequestHeader()
                })
                let res = JSON.parse(await req.text())
                this.$refs.apexchart.updateOptions({
                    xaxis: { categories: res.model.categories }
                })
                this.series.push({
                    name: "Max",
                    data: res.model.max,
                    color: Palette[0]
                })
                this.series.push({
                    name: "Mean",
                    data: res.model.avg,
                    color: Palette[1]
                })
            } catch (error) {
                this.showNotification(false, error)
            }
        }
    }
}
</script>
