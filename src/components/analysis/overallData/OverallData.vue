<template>
    <v-container>
        <v-row>
            <v-col cols="6">
                <UPHChart
                    :show-notification="showNotification"
                />
            </v-col>
            <v-col cols="6">
                <JobRatio
                    :show-notification="showNotification"
                />
            </v-col>
        </v-row>
    </v-container>
</template>


<script>
import JobRatio from "./JobRatio.vue";
import UPHChart from "./UPHChart.vue";
export default {
    props: {
        showNotification: {
            type: Function
        }
    },
    components: {
        JobRatio,
        UPHChart,
    },
    data: () => ({

    }),
    methods: {

    }
}
</script>
