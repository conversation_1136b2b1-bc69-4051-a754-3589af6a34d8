<template>
    <v-container dark>
        <v-row>
            <v-col cols="2">
                <v-select :items="cubeItem" label="Cube" v-model="cube" class="ma-2" @change="onCubeChanged" />
            </v-col>
            <v-col cols="6">
                <v-btn @click="fetchPKS" class="ma-2">
                    Refresh
                </v-btn>
            </v-col>
        </v-row>
        <v-row>
            <v-data-table dark item-key="pks_id" v-model="modelPickJobs.Selected" :items="modelPickJobs.Jobs"
                :headers="headers"  sort-by="rank"  :items-per-page="50">

                <template v-slot:item.penalty_bin="{ item }">
                    <!-- Clickable text to open dialog -->
                    <v-btn color="primary" text @click="showPenaltyBin(item)">
                        View Dependent Detail
                    </v-btn>
                </template>

                <template v-slot:item.reward_bin="{ item }">
                    <!-- Clickable text to open dialog -->
                    <v-btn color="primary" text @click="showRewardBin(item)">
                        View Successors Detail
                    </v-btn>
                </template>
            </v-data-table>

            <!-- Dialog for showing the penalty_bin details -->
            <v-dialog v-model="penaltyDialog" max-width="600px">
                <v-card>
                    <v-card-title>{{selectedItem.pks_id}}</v-card-title>
                    <v-card-subtitle>Listed PKS will degraded performance if executed current PKS </v-card-subtitle>
                    <v-card-text>
                        <v-data-table :items="penaltyBinItems" :headers="penaltyBinHeaders" >
                        </v-data-table>
                    </v-card-text>
                    <v-card-actions>
                        <v-btn text @click="penaltyDialog = false">Close</v-btn>
                    </v-card-actions>
                </v-card>
            </v-dialog>

                 <!-- Dialog for showing the penalty_bin details -->
                 <v-dialog v-model="rewardDialog" max-width="600px">
                <v-card  class="mx-auto">
                    <v-card-title>{{selectedItem.pks_id}}</v-card-title>
                    <v-card-subtitle>Listed PKS will be optimize performance if executed current PKS</v-card-subtitle>
                    <v-card-text>
                        <v-data-table :items="rewardBinItems" :headers="rewardBinHeaders">
                        </v-data-table>
                    </v-card-text>
                    <v-card-actions>
                        <v-btn text @click="rewardDialog = false">Close</v-btn>
                    </v-card-actions>
                </v-card>
            </v-dialog>




        </v-row>
    </v-container>
</template>
<script>

import { RouteOperation } from "../../../helper/enums";
import { getRequestHeader, getHost, useRefreshToken, getCube } from "../../../helper/common";
export default {
    props: {
        showNotification: {
            type: Function
        }
    },
    components: {
        // Chart,
        // Statistics,
        // Summary,
    },
    data() {
        return {
            // cube
            cube: getCube()[0],
            cubeItem: getCube(),

            // datatable
            showDialog: false,
            doneSync: false,
            selectedItem: [],
            modelPickJobs: {
                Jobs: [], 
                Jobs1: [
                    {
                        pks: "A124",
                        totalOverlapBinQty: 1,
                        totalOverlapPKS: 2
                    },
                ]
            },
            headers: [
                { text: "PKS ID", value: "pks_id" },
                { text: "Total Overlap Bin Qty", value: "totalOverlapBinQty" },
                { text: "Total Overlap PKS", value: "totalOverlapPKSQty" },
                { text: "Overlap PKS & Bin Qty", value: "totalOverlapPKSandBinsQty" },

                { text: "Total Digging Bins Qty", value: "totalDiggingBinsQty" },
                { text: "Total Retrieving Bins Qty ", value: "totalRetrievingBins" },
                { text: "Total Digging Bins Qty Unique ", value: "totalDiggingBinsQtyUnique" },

                // { text: "Digging Bins", value: "diggingBins" },


            ],

            // sub datatable
            penaltyDialog: false,
            penaltyBinItems: [],
            penaltyBinHeaders: [
                { text: "PKS", value: "pks_id" },
                { text: "Bin Qty", value: "qty" },
                { text: "Bins ", value: "storages" },
            ],

            rewardDialog: false,
            rewardBinItems: [],
            rewardBinHeaders: [
                { text: "PKS", value: "pks_id" },
                { text: "Bin Qty", value: "qty" },
                { text: "Bins", value: "storages" },
            ],
        }
    },

    methods: {
        onCubeChanged() {
            this.fetchPKS();
        },
        async fetchPKS() {
            try {

                let url = new URL(getHost(this.cube) + RouteOperation.PKS_DATA);
                let req = await fetch(url, {
                    method: "GET",
                    headers: getRequestHeader()
                })

                const myJson = await req.json();
                if (myJson.code === 401) {
                    return useRefreshToken(this, this.fetchSkycarJob);
                }
                this.headers = myJson.model.headers

                if (myJson.data) {
                    this.modelPickJobs.Jobs = myJson.data

                    this.showNotification(true, myJson.message)
                }


            } catch (error) {
                this.showNotification(false, error)
            }
        },


        showPenaltyBin(item) {
            this.penaltyBinItems = item.penalty_bin
            this.selectedItem = item
            this.penaltyDialog = true;
        },
        showRewardBin(item) {
            this.rewardBinItems = item.reward_bin
            this.selectedItem = item
            this.rewardDialog = true;
        },



    }
}
</script>
