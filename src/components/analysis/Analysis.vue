<template>
    <v-card dark>
      <OverallData
        :show-notification="showNotification"
      />
      <v-expansion-panels
        v-model="panel"
        multiple
      >
        <v-expansion-panel>
          <v-expansion-panel-header>Jobs per hour</v-expansion-panel-header>
          <v-expansion-panel-content>
            <JobRate
              :show-notification="showNotification"
            />
          </v-expansion-panel-content>
        </v-expansion-panel>
        <v-expansion-panel>
          <v-expansion-panel-header>Job Duration (seconds)</v-expansion-panel-header>
          <v-expansion-panel-content>
            <JobDuration
              :show-notification="showNotification"
            />
          </v-expansion-panel-content>
        </v-expansion-panel>

        <v-expansion-panel>
          <v-expansion-panel-header>Pick Jobs Rank</v-expansion-panel-header>
          <v-expansion-panel-content>
            <v-card> <PickJob :show-notification="showNotification"/></v-card>
           
          </v-expansion-panel-content>
        </v-expansion-panel>

      </v-expansion-panels>
      <SnackbarNotification ref="snackbarNotification"/>
    </v-card>
</template>

<script>
import JobDuration from "./jobDuration/JobDuration.vue";
import JobRate from "./jobRate/JobRate.vue"
import OverallData from "./overallData/OverallData.vue"
import SnackbarNotification from "../shared/SnackbarNotification.vue";
import PickJob from "./pksData/PickJob.vue";
export default {
  components: {
    JobDuration,
    JobRate,
    OverallData,
    SnackbarNotification,
    PickJob
  },
  data: () => ({
    panel: []
  }),
  methods: {
    showNotification(success, message) {
      this.$refs.snackbarNotification.showNotification(success, message)
    }
  }
}
</script>
