<template>
    <v-container>
        <v-row>
            <v-col cols="5">
                <Summary
                    :get-job-rate="getJobRate"
                    :toggle-zoom="toggleZoom"
                    :show-notification="showNotification"
                    ref="summary"
                />
            </v-col>
            <v-col cols="7">
                <Chart 
                    @get-timestamp="getTimestamp"
                    :show-notification="showNotification"
                    ref="chart"
                />
                <Statistics ref="statistics"/>
            </v-col>
        </v-row>
    </v-container>
</template>

<script>
import Chart from "./Chart.vue"
import Statistics from "./Statistics.vue"
import Summary from "./Summary.vue"
export default {
    props: {
        showNotification: {
            type: Function
        }
    },
    components: {
        Chart,
        Statistics,
        Summary,
    },
    data: () => ({

    }),
    methods: {
        getJobRate(ids) {
            this.$refs.chart.getData(ids)
            this.$refs.statistics.getData(ids)
        },
        getTimestamp(timestamp) {
            this.$refs.summary.getData(timestamp.min, timestamp.max)
            this.$refs.statistics.getData([], timestamp.min, timestamp.max)
        },
        toggleZoom(bool) {
            this.$refs.chart.toggleZoom(bool)
        }
    }
}
</script>
