<template>
    <v-data-table
        :headers="headers"
        :items="items"
        :items-per-page="10"
        :value="value"
        :loading="!doneSync"
        show-select
        @input="selectRow($event)"
        class="secondary"
        group-by="Module"
    ></v-data-table>
</template>

<script>
import { getBTUrl, getRequestHeader } from "../../../helper/common";
import { RouteAnalysis } from "../../../helper/enums";

export default {
    props: {
        getJobRate: {
            type: Function
        },
        toggleZoom: {
            type: Function
        },
        showNotification: {
            type: Function
        }
    },
    async mounted() {
        await this.getData()
        this.value.push(this.items[0])
    },
    data: () => ({
        doneSync: true,
        headers: [
            { text: "Id", value: "id" },
            { text: "Min", value: "min" },
            { text: "Max", value: "max" },
            { text: "Mean", value: "mean" },
            { text: "Median", value: "median" },
            { text: "Sample Size", value: "size" },
            { text: "Module", value: "Module" }
        ],
        items: [],
        value: []
    }),
    methods: {
        async getData(minTimestamp, maxTimestamp) {
            try {
                this.toggleSync(false)
                let url = getBTUrl() + RouteAnalysis.JOB_RATE_STATISTICS_SUMMARY
                if (minTimestamp && maxTimestamp) {
                    let qs = new URLSearchParams({
                        start: Math.floor(minTimestamp / 1000),
                        end: Math.ceil(maxTimestamp / 1000)
                    })
                    url = `${url}?${qs}`
                }
                let req = await fetch(url, {
                    method: "GET",
                    headers: getRequestHeader()
                })
                let res = JSON.parse(await req.text())
                res.data.forEach(element => {
                    element.Module = Module[element.id.substring(0, 2)]
                })
                this.items = this.sort(res.data)
            } catch (error) {
                this.showNotification(false, error)
            } finally {
                setTimeout(() => {
                    this.toggleSync(true)
                }, 1000)
            }
        },
        selectRow(series) {
            if (series.length != 0) {
                let ids = []
                series.forEach(element => {
                    ids.push(element.id)
                })
                this.getJobRate(ids)
            }
        },
        toggleSync(bool) {
            this.doneSync = bool
            // try {
            //     this.toggleZoom(bool)
            // } catch (err) {
            //     console.log(err)
            // }
        },
        sort(items) {
            return items.sort((a, b) => {
                let moduleA = a.id.substring(0, 2)
                let moduleB = b.id.substring(0, 2)
                let codeA = parseInt(a.id.substring(2))
                let codeB = parseInt(b.id.substring(2))
                if (moduleA == moduleB) {
                    return codeA - codeB
                } else if (moduleA == "ST") {
                    return 1
                } else {
                    return -1
                }
            })
        }
    }
}

const Module = {
    SC: "SKYCAR",
    ST: "STATION"
}
</script>
