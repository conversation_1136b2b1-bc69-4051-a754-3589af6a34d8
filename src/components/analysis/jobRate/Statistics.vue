<template>
    <apexchart
        type="line" 
        :options="options" 
        :series="series"
        height="350"
    />
</template>
  
<script>
import { getBTUrl, getRequestHeader } from "../../../helper/common"
import { Palette, RouteAnalysis } from "../../../helper/enums"

export default {
    props: {
        showNotification: {
            type: Function
        }
    },
    data: () => ({
        ids: [],
        series: [],
        options: {
            chart: {
                type: "area",
                toolbar: {
                    tools: {
                        pan: false
                    }
                }
            },
            stroke: {
                curve: "smooth"
            },
            theme: {
                mode: "dark",
                monochrome: {
                    enabled: true,
                    color: "#fca503"
                }
            },
            dataLabels: {
                enabled: false
            },
            xaxis: {
                title: {
                    text: "Number of jobs / hour"
                }
            },
            yaxis: {
                title: {
                    text: "Count"
                }
            },
            legend: {
                showForSingleSeries: true
            },
            tooltip: {
                x: {
                    formatter: function(value) {
                        return `${value} jobs per hour`
                    }
                }
            }
        },
    }),
    methods: {
        async getData(ids=[], minTimestamp=null, maxTimestamp=null) {
            try {
                let url = getBTUrl() + RouteAnalysis.JOB_RATE_STATISTICS
                let qs = new URLSearchParams()
                if (ids.length != 0) {
                    this.ids = ids
                } else {
                    ids = this.ids
                }
                ids.forEach(element => {
                    qs.append("ids", element)
                })
                if (minTimestamp && maxTimestamp) {
                    qs.append("start",  Math.floor(minTimestamp / 1000))
                    qs.append("end", Math.ceil(maxTimestamp / 1000))
                }
                let req = await fetch(`${url}?${qs}`, {
                    method: "GET",
                    headers: getRequestHeader()
                })
                let res = JSON.parse(await req.text())
                res.data.forEach((element, index) => {
                    element.color = Palette[index]
                })
                this.series = res.data

            } catch (error) {
                this.showNotification(false, error)
            }
        }
    }
}
</script>
