<template>
    <apexchart
        v-if="series.length != 0"
        type="bar" 
        :options="options" 
        :series="series"
        height="350"
        ref="apexchart"
        @zoomed="handleZoom"
    />
</template>
  
<script>
import { convertStringToLocal, getBTUrl, getRequestHeader } from "../../../helper/common"
import { Palette, RouteAnalysis } from "../../../helper/enums"

export default {
    props: {
        showNotification: {
            type: Function
        }
    },
    data() {
        return {
            series: [],
            options: {
                chart: {
                    type: "bar",
                    stacked: true,
                    horizontal: true,
                    toolbar: {
                        tools: {
                            pan: false
                        }
                    }
                },
                theme: {
                    mode: "dark",
                    monochrome: {
                        enabled: true,
                        color: "#fca503"
                    }
                },
                dataLabels: {
                    enabled: false
                },
                xaxis: {
                    type: "datetime",
                    labels: {
                        datetimeUTC: false
                    },
                },
                yaxis: {
                    title: {
                        text: "Number of jobs / hour"
                    }
                },
                legend: {
                    showForSingleSeries: true
                },
                tooltip: {
                    x: {
                        format: "MMM dd HH:mm"
                    },
                    y: {
                        formatter: function(value) {
                            return `${value} jobs per hour`
                        }
                    }
                },
            },
        }
    },
    methods: {
        async getData(ids) {
            try {
                let url = getBTUrl() + RouteAnalysis.JOB_RATE
                let qs = new URLSearchParams()
                ids.forEach(element => {
                   qs.append("ids", element)
                })
                let req = await fetch(`${url}?${qs}`, {
                    method: "GET",
                    headers: getRequestHeader()
                })
                let res = JSON.parse(await req.text())
                let categories = res.model.categories.map(value => {
                    return convertStringToLocal(value, true)
                })
                this.updateXaxisCategories(categories)
                res.model.data.forEach((element, index) => {
                    element.color = Palette[index]
                })
                this.series = res.model.data
            } catch (error) {
                this.showNotification(false, error)
            }
        },
        handleZoom() {
            this.$emit("get-timestamp", arguments[1].xaxis)
        },
        toggleZoom(bool) {
            this.options.chart.zoom.enabled = bool
        },
        updateXaxisCategories(categories) {
            let apexchart = this.$refs.apexchart
            if (apexchart) {
                apexchart.updateOptions({
                    xaxis: {
                        categories: categories
                    }
                })
            } else {
                this.options.xaxis.categories = categories
            }
        }
    },
}
</script>
