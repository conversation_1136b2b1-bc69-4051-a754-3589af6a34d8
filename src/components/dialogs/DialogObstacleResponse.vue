<template>
    <v-dialog
        v-model="dialogBool"
        width="600"
    >
        <v-card>
            <v-toolbar
                color="primary"
                dark
            >
                <v-toolbar-title>Response</v-toolbar-title>
            </v-toolbar>
            <v-data-table
                :headers="headers"
                :items="data"
                dense
                :custom-sort="sort"
                :page.sync="currentPage"
            >
                <template v-slot:[`item.status`]="{ item }">
                    <v-chip 
                        class="custom-chip"
                        :color="statusColor[item.status]" 
                        dark
                        small
                    >
                        {{ item.status }}
                    </v-chip>
                </template>
            </v-data-table>
        </v-card>
    </v-dialog>
</template>

<script>
export default {
    data: () => ({
        statusColor,
        dialogBool: false,
        headers: [
            { text: "Coordinate", value: "id", width: 120 },
            { text: "Status", value: "status" },
            { text: "Message", value: "message" }
        ],
        data: {},
        order: ["error", "warning", "success"],
        currentPage: 1
    }),
    methods: {
        async openDialog(data) {
            this.currentPage = 1
            this.dialogBool = true
            this.data = data
        },
        closeDialog() {
            this.dialogBool = false
        },
        sort(items) {
            return items.sort((a, b) => {
                return this.order.indexOf(a.status) - this.order.indexOf(b.status)
            })
        }
    }
}

const statusColor = {
    success: "green",
    warning: "yellow",
    error: "red"
}
</script>

<style>
.custom-chip {
  width: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-transform: uppercase;
}
</style>
