<template>
  <div>
    <v-dialog
      v-model="dialog"
      max-width="1000px"
      @keydown.esc="closeDialog"
    >
    <v-card dark color="black">
      <!-- Header -->
      <v-card-title class="pa-0">
        <v-toolbar
          dark
          color="grey darken-4"
          flat
        >
          <v-icon class="mr-3">mdi-car</v-icon>
          <span class="text-h5">Skycar Details</span>
          <v-spacer />
          <v-chip
            :color="getStatusColor(skycarData.status)"
            dark
            small
          >
            <v-icon left size="16">{{ getStatusIcon(skycarData.status) }}</v-icon>
            {{ skycarData.status }}
          </v-chip>
          <v-btn
            icon
            @click="closeDialog"
            class="ml-2"
          >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
      </v-card-title>

      <!-- Loading State -->
      <v-card-text v-if="loading" class="text-center pa-8">
        <v-progress-circular
          indeterminate
          color="white"
          size="50"
        />
        <div class="mt-4 text-subtitle1">Loading skycar details...</div>
      </v-card-text>

      <!-- Skycar Details Content -->
      <v-card-text v-else class="pa-0">
        <v-container fluid class="pa-4">
          <!-- Basic Info Row -->
          <v-row class="mb-4">
            <v-col cols="12" md="3">
              <v-card outlined color="grey darken-3" class="pa-4">
                <div class="d-flex align-center mb-2">
                  <v-icon class="mr-2" color="white">mdi-identifier</v-icon>
                  <span class="text-subtitle2 grey--text text--lighten-2">Skycar ID</span>
                </div>
                <div class="text-h4 white--text">{{ skycarData.skycar_id }}</div>
              </v-card>
            </v-col>
            <v-col cols="12" md="3">
              <v-card outlined color="grey darken-3" class="pa-4">
                <div class="d-flex align-center mb-2">
                  <v-icon class="mr-2" color="white">mdi-map-marker</v-icon>
                  <span class="text-subtitle2 grey--text text--lighten-2">Coordinates</span>
                </div>
                <div class="text-h5 white--text">{{ skycarData.coordinate }}</div>
              </v-card>
            </v-col>
            <v-col cols="12" md="3">
              <v-card outlined color="grey darken-3" class="pa-4">
                <div class="d-flex align-center mb-2">
                  <v-icon class="mr-2" color="white">mdi-battery</v-icon>
                  <span class="text-subtitle2 grey--text text--lighten-2">Battery</span>
                </div>
                <div class="text-h4 white--text">{{ skycarData.battery }}%</div>
              </v-card>
            </v-col>
            <v-col cols="12" md="3">
              <v-card outlined color="grey darken-3" class="pa-4">
                <div class="d-flex align-center mb-2">
                  <v-icon class="mr-2" color="white">mdi-compass</v-icon>
                  <span class="text-subtitle2 grey--text text--lighten-2">Orientation</span>
                </div>
                <div class="text-h5 white--text">{{ skycarData.orientation }}</div>
              </v-card>
            </v-col>
          </v-row>

          <!-- Connection & Status Row -->
          <v-row class="mb-4">
            <v-col cols="12" md="4">
              <v-card outlined color="grey darken-3" class="pa-4">
                <div class="text-h6 white--text mb-3">
                  <v-icon class="mr-2">mdi-connection</v-icon>
                  Connection Status
                </div>
                <div class="d-flex justify-space-between mb-2">
                  <span class="grey--text">Connected:</span>
                  <v-chip
                    :color="skycarData.connect ? 'green' : 'red'"
                    dark
                    x-small
                  >
                    <v-icon left size="16">{{ skycarData.connect ? 'mdi-check' : 'mdi-close' }}</v-icon>
                    {{ skycarData.connect ? 'Yes' : 'No' }}
                  </v-chip>
                </div>
                <div class="d-flex justify-space-between mb-2">
                  <span class="grey--text">Paired:</span>
                  <v-chip
                    :color="skycarData.pair ? 'green' : 'red'"
                    dark
                    x-small
                  >
                    <v-icon left size="16">{{ skycarData.pair ? 'mdi-check' : 'mdi-close' }}</v-icon>
                    {{ skycarData.pair ? 'Yes' : 'No' }}
                  </v-chip>
                </div>
                <div class="d-flex justify-space-between mb-2">
                  <span class="grey--text">Docked:</span>
                  <v-chip
                    :color="skycarData.is_docked ? 'green' : 'orange'"
                    dark
                    x-small
                  >
                    <v-icon left size="16">{{ skycarData.is_docked ? 'mdi-home' : 'mdi-car' }}</v-icon>
                    {{ skycarData.is_docked ? 'Yes' : 'No' }}
                  </v-chip>
                </div>
              </v-card>
            </v-col>
            <v-col cols="12" md="4">
              <v-card outlined color="grey darken-3" class="pa-4">
                <div class="text-h6 white--text mb-3">
                  <v-icon class="mr-2">mdi-cog</v-icon>
                  Mode Information
                </div>
                <div class="d-flex justify-space-between mb-2">
                  <span class="grey--text">Operation Mode:</span>
                  <v-chip
                    :color="getModeColor(skycarData.mode)"
                    dark
                    small
                  >
                    {{ skycarData.mode }}
                  </v-chip>
                </div>
                <div class="d-flex justify-space-between mb-2">
                  <span class="grey--text">Maintenance Mode:</span>
                  <span class="white--text">{{ skycarData.maintenance_mode || 'None' }}</span>
                </div>
                <div class="d-flex justify-space-between mb-2">
                  <span class="grey--text">Direction:</span>
                  <span class="white--text">{{ skycarData.direction }}</span>
                </div>
              </v-card>
            </v-col>
            <v-col cols="12" md="4">
              <v-card outlined color="grey darken-3" class="pa-4">
                <div class="text-h6 white--text mb-3">
                  <v-icon class="mr-2">mdi-briefcase-outline</v-icon>
                  Current Job
                </div>
                <div v-if="skycarData.job_id" class="text-center">
                  <v-btn
                    color="blue"
                    dark
                    @click="openJobDetail(skycarData.job_id)"
                    class="mb-2"
                  >
                    <v-icon left>mdi-briefcase-outline</v-icon>
                    Job {{ skycarData.job_id }}
                  </v-btn>
                  <div v-if="skycarData.job_begin_at" class="text-caption grey--text">
                    Started: {{ formatDateTime(skycarData.job_begin_at) }}
                  </div>
                </div>
                <div v-else class="text-center">
                  <v-icon size="48" color="grey">mdi-briefcase-off</v-icon>
                  <div class="text-subtitle2 grey--text mt-2">No Active Job</div>
                </div>
              </v-card>
            </v-col>
          </v-row>

          <!-- Winch Information -->
          <v-row class="mb-4" v-if="skycarData.winch && Object.keys(skycarData.winch).length > 0">
            <v-col cols="12">
              <v-card outlined color="grey darken-3" class="pa-4">
                <div class="text-h6 white--text mb-3">
                  <v-icon class="mr-2">mdi-crane</v-icon>
                  Winch Information
                </div>
                <v-row>
                  <v-col 
                    v-for="(winch, position) in skycarData.winch" 
                    :key="position"
                    cols="12" 
                    md="6"
                  >
                    <v-card outlined color="grey darken-4" class="pa-3">
                      <div class="text-subtitle1 white--text mb-2">
                        <v-icon class="mr-1">mdi-crane</v-icon>
                        Position {{ getPositionName(position) }}
                      </div>
                      
                      <div class="d-flex justify-space-between mb-1">
                        <span class="grey--text">Status:</span>
                        <v-chip
                          :color="winch.is_active ? 'green' : 'red'"
                          dark
                          x-small
                        >
                          {{ winch.is_active ? 'Active' : 'Inactive' }}
                        </v-chip>
                      </div>
                      
                      <div class="d-flex justify-space-between mb-1">
                        <span class="grey--text">Storage No:</span>
                        <span class="white--text">{{ winch.storage_no || 'Empty' }}</span>
                      </div>
                      
                      <div class="d-flex justify-space-between mb-1">
                        <span class="grey--text">Assigned Code:</span>
                        <span class="white--text">{{ winch.assign_storage_code || 'None' }}</span>
                      </div>
                      
                      <div class="d-flex justify-space-between mb-1">
                        <span class="grey--text">Bin on Hold:</span>
                        <v-chip
                          :color="winch.bin_on_hold ? 'orange' : 'green'"
                          dark
                          x-small
                        >
                          <v-icon left size="14">{{ winch.bin_on_hold ? 'mdi-pause-circle' : 'mdi-check-circle' }}</v-icon>
                          {{ winch.bin_on_hold ? 'Yes' : 'No' }}
                        </v-chip>
                      </div>
                      
                      <div class="d-flex justify-space-between">
                        <span class="grey--text">Platform:</span>
                        <v-chip
                          :color="winch.platform ? 'blue' : 'grey'"
                          dark
                          x-small
                        >
                          {{ winch.platform ? 'Yes' : 'No' }}
                        </v-chip>
                      </div>
                    </v-card>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>

          <!-- Storage Information -->
          <v-row v-if="skycarData.storage_no">
            <v-col cols="12">
              <v-card outlined color="grey darken-3" class="pa-4">
                <div class="text-h6 white--text mb-3">
                  <v-icon class="mr-2">mdi-package-variant</v-icon>
                  Storage Information
                </div>
                <div class="text-h5 white--text text-center">
                  {{ skycarData.storage_no }}
                </div>
              </v-card>
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>

      <!-- Actions -->
      <v-card-actions class="pa-4">
        <v-spacer />
        <v-btn
          color="grey darken-1"
          text
          @click="closeDialog"
        >
          Close
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
  </div>
</template>

<script>
import { convertStringToLocal } from "../../helper/common.js";

export default {
  name: "DialogSkycarDetail",
  props: {
    currentZone: {
      type: String,
      required: true
    },
    showNotification: {
      type: Function,
      required: true
    }
  },
  data() {
    return {
      dialog: false,
      loading: false,
      skycarData: {}
    };
  },
  methods: {
    openDialog(skycarData) {
      if (!skycarData) {
        this.showNotification(false, "No skycar data provided");
        return;
      }

      this.skycarData = skycarData;
      this.dialog = true;
    },

    closeDialog() {
      this.dialog = false;
      this.skycarData = {};
    },

    formatDateTime(dateString) {
      if (!dateString) return 'N/A';
      return convertStringToLocal(dateString, true);
    },

    getStatusColor(status) {
      switch (status) {
        case 'AVAILABLE':
          return 'green';
        case 'WORKING':
          return 'blue';
        case 'MAINTENANCE':
          return 'orange';
        case 'ERROR':
          return 'red';
        case 'CHARGING':
          return 'yellow darken-2';
        default:
          return 'grey';
      }
    },

    getStatusIcon(status) {
      switch (status) {
        case 'AVAILABLE':
          return 'mdi-check-circle';
        case 'WORKING':
          return 'mdi-cog';
        case 'MAINTENANCE':
          return 'mdi-wrench';
        case 'ERROR':
          return 'mdi-alert-circle';
        case 'CHARGING':
          return 'mdi-battery-charging';
        default:
          return 'mdi-help-circle';
      }
    },

    getModeColor(mode) {
      switch (mode) {
        case 'Normal':
          return 'green';
        case 'Manual':
          return 'orange';
        case 'Error':
          return 'red';
        case 'Maintenance':
          return 'blue';
        default:
          return 'grey';
      }
    },

    getPositionName(position) {
      const positionMap = {
        'L': 'A (Left)',
        'R': 'B (Right)', 
        'BOTH': 'Both',
        'B': 'Both'
      };
      return positionMap[position] || position;
    },

    openJobDetail(jobId) {
      // Emit event to parent to open job detail dialog
      this.$emit('open-job-detail', jobId);
    }
  }
};
</script>

<style scoped>
.v-card {
  border-radius: 8px;
}

.v-card--outlined {
  border: 1px solid rgba(255, 255, 255, 0.12);
}
</style> 