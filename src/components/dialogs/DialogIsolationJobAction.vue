<template>
  <v-dialog
    v-model="modelSkycarJob.boolDialogAction"
    max-width="500"
    transition="dialog-bottom-transition"
  >
    <v-card>
      <v-toolbar color="color" dark>
        <v-toolbar-title>Action</v-toolbar-title>
      </v-toolbar>
      <v-card-text>
        <v-row>
          <v-col>
            <v-card-subtitle
              >Job : {{ this.selectedJobItem.job_id }}
            </v-card-subtitle>
          </v-col>
          <v-col>
            <v-card-subtitle
              >Type : {{ this.selectedJobItem.type }}
            </v-card-subtitle>
          </v-col>
        </v-row>

        <v-row justify="space-around" class="mb-4">
          <v-btn color="red darken-1" @click="btnUndoHault()">Undo Halt </v-btn>

          <v-btn color="red darken-1" @click="btnDeleteJob()"
            >Delete Job
          </v-btn>
        </v-row>

        <v-row justify="space-around" class="mb-4">
          <v-btn color="red" @click="btnRemoveSid()">Unassign Skycar </v-btn>

          <v-btn color="red darken-1" @click="btnRemoveStorageFromSkycar()"
            >Remove Skycar Storage
          </v-btn>
        </v-row>
        <v-card-actions>
          <v-spacer></v-spacer>

          <v-btn color="green darken-1" text @click="onCloseButtonClick()"
            >Close
          </v-btn>
        </v-card-actions>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
import axios from "axios";
import { Status, RouteJob, RouteSkycar } from "../../helper/enums";
import { getRequestHeader } from "../../helper/common";
export default {
  name: "IoslationJobAction",
  props: {
    selectedJobItem: {
      type: Object,
      required: true,
    },
    modelSkycarJob: {
      type: Object,
      required: true,
    },
    targetHost: {
      type: String,
      required: true,
    },
    callableHttp: {
      type: Function,
      required: true,
    },
  },
  computed: {},
  watch: {},

  methods: {
    async btnUndoHault(isHault = false) {
      // Skycar Error Databable Dialog TextArea Confirm Btn onClick
      if (!this.selectedJobItem) {
        return;
      }
      let data = {
        id: this.selectedJobItem.job_id,
        status: Status.AVAILABLE,
        // eslint-disable-next-line camelcase
        is_halt: isHault,
      };
      let url = this.targetHost + RouteJob.UPDATE_JOB;
      let promise = axios.patch(url, data, { headers: getRequestHeader() });

      try {
        const response = await this.callableHttp(this.$awn, promise, true);
        console.log(response)
        // Update runtime
        // console.log(response);
      } catch (error) {
        console.log(error);
      } finally {
        this.tearOffDialog();
      }
    },

    async btnDeleteJob() {
      let url = this.targetHost + RouteJob.DELETE_JOB;
      console.log(this.selectedJobItem.job_id);
      let data = {
        id: this.selectedJobItem.job_id,
      };
      console.log(data);

      let promise = axios.delete(url, {
        data: data,
        headers: getRequestHeader(),
      });

      try {
        const response = await this.callableHttp(this.$awn, promise, true);

        // Update runtime
        console.log(response);
      } catch (error) {
        console.log(error);
      } finally {
        this.tearOffDialog();
      }
    },

    async btnRemoveSid() {
      if (!this.selectedJobItem) {
        return;
      }
      let data = {
        id: this.selectedJobItem.job_id,
        // eslint-disable-next-line camelcase
        skycar_id: null,
        // eslint-disable-next-line camelcase
        prefer_skycar_id: null,
      };
      let url = this.targetHost + RouteJob.UPDATE_JOB;
      console.log(url);

      let promise = axios.patch(url, data, { headers: getRequestHeader() });

      try {
        const response = await this.callableHttp(this.$awn, promise, true);

        // Update runtime
        console.log(response);
      } catch (error) {
        console.log(error);
      } finally {
        this.tearOffDialog();
      }
    },

    async btnRemoveStorageFromSkycar() {
      if (!this.selectedJobItem) {
        return;
      }
      let data = {
        sid:
          this.selectedJobItem.skycar_id ||
          this.selectedJobItem.prefer_skycar_id,
        position: this.selectedJobItem.position,
        // eslint-disable-next-line camelcase
        storage_code: null,
      };
      let url = this.targetHost + RouteSkycar.WINCH;

      let promise = axios.patch(url, data, { headers: getRequestHeader() });

      try {
        const response = await this.callableHttp(this.$awn, promise, true);

        // Update runtime
        console.log(response);
      } catch (error) {
        console.log(error);
      } finally {
        this.tearOffDialog();
      }
    },

    tearOffDialog() {
      this.modelSkycarJob.boolDialogAction = false;
    },

    onCloseButtonClick() {
      this.modelSkycarJob.boolDialogAction = false;
      // this.tearOffDialog()
    },
  },
  data: () => ({
    // errorActionTextArea: {
    //     value: null,
    //     rules: [v => !!v && v.length <= 1000 || 'Max 1000 characters'],
    //     label: "The actual root cause of this error, after troubleshooting, is due to the reason..."
    // },
    // errorActionCheckBox: false,
    // errorActionRemarkExist: false,
  }),
};
</script>
