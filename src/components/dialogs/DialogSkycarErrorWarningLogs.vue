
<template>
    <v-dialog v-if="error.boolDialogWarningLog" v-model="error.boolDialogWarningLog" 
        transition="dialog-bottom-transition">
        <v-card>
            <v-toolbar color="color" dark>
                <v-toolbar-title>Warning Logs</v-toolbar-title>
            </v-toolbar>

            <v-data-table :items-per-page="15" :headers="errorSkycarWarningHeaders"
                :items="modelErrorSkycar.selectedSkycarError.warning_messages" dense>

            </v-data-table>




        </v-card>
    </v-dialog>

</template>

<script>

export default {
    name: "SkycarErrorWarningLogs",
    props: {
        error: {
            type: Object,
            required: true,
        },
        modelErrorSkycar: {
            type: Object,
            required: true,
        },
    },

    methods: {},
    data: () => (
        {
            errorSkycarWarningHeaders: [
                { text: "Message", value: "message" },
                { text: "Error Name", value: "error_name" },
                { text: "Error Detail", value: "error_detail" }
            ],
        }),
}

</script>