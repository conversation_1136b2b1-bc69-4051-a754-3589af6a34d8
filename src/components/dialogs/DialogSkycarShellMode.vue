<template>
	<!-- Feedback -->
	<v-dialog
		v-if="localBoolEng"
		v-model="localBoolEng"
		max-width="800"
		@keydown.enter="localBoolEng=false"
		>
		<v-card>
			<v-toolbar
				dark
				:color="getSubmitStatus(txtEng.status)"
				>
				<v-toolbar-title>Status: {{txtEng.status}}</v-toolbar-title>
			</v-toolbar>
			<v-progress-linear
				v-if="txtEng.status == 'Pending'"
				indeterminate
				color="green"
				></v-progress-linear>
				<v-card-text class="pt-6">
				<pre class="text-wrap">{{ txtEng.reason }}</pre>
				<v-row>
						<v-col>
						<v-row>
								<v-chip
								color="green"
								dark
								class="ma-1"
								v-for="sid in sids"
								:key="sid"
								>
								<v-icon class='mx-1'>mdi-car</v-icon>
								<pre>SC {{sid}}</pre>
								</v-chip>
						</v-row>
						</v-col>
						<v-col>
						<span v-if="txtEng.data.length != 0">
								<v-row
								v-for="data in txtEng.data"
								:key="data"
								>
								<v-chip
										color="orange"
										dark
										class="ma-1"
								>
										<v-icon class='mx-1'>mdi-message</v-icon>
										<pre>{{ data }}</pre> 
								</v-chip>
								</v-row>
						</span>
						</v-col>
				</v-row>
				</v-card-text>
				<v-card-actions>
					<v-spacer></v-spacer>
					<v-btn
							color="green darken-1"
							text
							@click="localBoolEng = false"
					>Close
					</v-btn>
				</v-card-actions>
		</v-card>
	</v-dialog>
</template>

<script>

	export default{
		name: "DialogSkycarShellMode",
		props:{
			boolEng:{
				type:Boolean,
				default:false
			},
			txtEng:null,
			sids:Array
		},
		created(){
			this.localBoolEng = this.boolEng
			this.localTxtEng = this.txtEng
		},
		data(){
			return {
				localBoolEng: false,
				localTxtEng: null
			}
		},
		methods:{
			getSubmitStatus(status) {
				if (status == "Accepted") {
					return "green"
				} else if (status == "Rejected" || status == "Warning") {
					return "red"
				} else if (status == "Pending") {
					return "black"
				}
		}
		},
		watch:{
			localBoolEng(bool){
				if(!bool)
			this.$emit("close-dialog");
			},
			boolEng(value){
				this.localBoolEng = value
			}
		}
	}
</script>

