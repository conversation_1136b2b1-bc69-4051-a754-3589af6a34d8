<template>
  <v-dialog 
    v-model="dialogBool" 
    width="800"
  >
    <v-card>
      <Grid
        :cubeDescName="currentZone"
        :gridObject="gridObject"
        :obstacleObject="gridObstacle"
        :skycarObject="gridSkycar"
        @grid-selected="handleGridSelect"
        ref="gridComponent"
      />
      
      <v-card-actions>
        <v-spacer />
        <v-btn
          color="green darken-1"
          text
          @click="closeDialog()"
        >
          Close
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
import Grid from "../../components/cube/Grid.vue";
import { initializeGrid } from "../../api/grid.js"; // Import the initializeGrid function from the grid.js file

export default {
  components: {
    Grid,
  },
  data: () => ({
    dialogBool: false,
    coordX: null,
    coordY: null,

    // Zone
    currentZone: null,

    // Child object
    gridObject: {
      gridName: "C",
      gridMinX: 0,
      gridMaxX: 0,
      gridMinY: 0,
      gridMaxY: 0,
    },
    gridObstacle: {},
    gridSkycar: {},
  }),
  methods: {
    async openDialog(cube) {
      this.currentZone = cube
      this.dialogBool = true;
      this.coordX = null;
      this.coordY = null;
      await this.initialize();
    },
    async closeDialog() {
      this.dialogBool = false;
    },
    async initialize() {
      const gridData = await initializeGrid(this.currentZone);

      if (gridData) {
        this.$refs.gridComponent.updateAll(
          gridData.gridObstacle,
          gridData.gridSkycar,
          gridData.gridObject,
          true
        );
      }
    },
    handleGridSelect(selectedCells) {
      this.$emit("update-coord", selectedCells)
      
      this.closeDialog()
    }
  },
};
</script>
