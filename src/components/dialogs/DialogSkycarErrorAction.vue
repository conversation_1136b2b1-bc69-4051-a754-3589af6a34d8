
<template>
    <v-dialog v-model="error.boolDialogAction" max-width="500" transition="dialog-bottom-transition">
        <v-card>
            <v-toolbar color="color" dark>
                <v-toolbar-title>Update Remark</v-toolbar-title>
            </v-toolbar>
            <v-card-text>
                <v-row>
                    <v-col>
                        <v-card-subtitle>App Module : {{ modelErrorSkycar.selectedSkycarError.app_module }}
                        </v-card-subtitle>
                    </v-col>
                    <v-col>
                        <v-card-subtitle>Skycar : {{ modelErrorSkycar.selectedSkycarError.sid }} </v-card-subtitle>
                    </v-col>

                </v-row>

                <v-row>
                    <v-col><v-textarea v-model=errorActionTextArea.value clearable clear-icon="mdi-close-circle"
                            :label="errorActionTextArea.label" :rules="errorActionTextArea.rules"></v-textarea>
                    </v-col>
                </v-row>

                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-checkbox label="I agreed to overwrite existing remark."
                        v-model="errorActionCheckBox"></v-checkbox>
                    <v-btn color="green darken-1" text @click="btnUpdateSkycarErrorRemark()"
                        :disabled="(Boolean(modelErrorSkycar.selectedSkycarError.remark) && 
                        !errorActionCheckBox) || errorActionTextArea.value == null">
                        Confirm
                    </v-btn>
                    <v-btn color="green darken-1" text @click="onCloseButtonClick()">Close
                    </v-btn>
                </v-card-actions>


            </v-card-text>
        </v-card>
    </v-dialog>

</template>

<script>
import axios from "axios";
import { getRequestHeader } from "../../helper/common"
export default {
    name: "SkycarErrorAction",
    props: {
        error: {
            type: Object,
            required: true
        },
        modelErrorSkycar: {
            type: Object,
            required: true
        },
        targetAPI: {
            type: String,
            required: true
        },
        targetHost: {
            type: String,
            required: true
        },
        callableHttp: {
            type: Function,
            required: true
        }
    },
    computed: {
        selectedError: function () {
            return this.modelErrorSkycar.selectedSkycarError;
        }
    },
    watch: {
        selectedError: function (newVal) {
            // console.log(newVal, oldVal)
            this.setText(newVal.remark);
        }
    },

    methods: {
        async btnUpdateSkycarErrorRemark() {
            // Skycar Error Databable Dialog TextArea Confirm Btn onClick
            console.log(this.modelErrorSkycar.selectedSkycarError.eid, "eid")
            console.log(this.errorActionTextArea.value, "error")
            let data = {
                eid: this.modelErrorSkycar.selectedSkycarError.eid,
                remark: this.errorActionTextArea.value

            }
            let url = this.targetHost + this.targetAPI;
            console.log(url)

            let promise = axios.patch(url, data, { headers:getRequestHeader() });

            try {
                const response = await this.callableHttp(this.$awn, promise,true)
                
                // Update runtime
                this.modelErrorSkycar.selectedSkycarError.remark = this.errorActionTextArea.value
                console.log(response)

            } catch (error) {
                console.log(error)
            }
            finally{
                this.tearOffDialog()
            }
        },

        setText(newVal) {
            this.errorActionTextArea.value = newVal
        },

        tearOffDialog(){
            this.error.boolDialogAction = false
            this.errorActionCheckBox = false
        },

        onCloseButtonClick(){
            this.error.boolDialogAction = false
            this.tearOffDialog()
        }

    },
    data: () => ({
        errorActionTextArea: {
            value: null,
            rules: [v => !!v && v.length <= 1000 || "Max 1000 characters"],
            label: "The actual root cause of this error, after troubleshooting, is due to the reason..."
        },
        errorActionCheckBox: false,
        errorActionRemarkExist: false
    })

};

</script>