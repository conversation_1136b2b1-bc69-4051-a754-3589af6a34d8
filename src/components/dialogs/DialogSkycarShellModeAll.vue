<template>
	<v-dialog
		v-if="localBoolAll"
		v-model="localBoolAll"
		max-width="800"
		@keydown.enter="btnSendAll()"
	>
		<v-card>
			<v-toolbar
				dark
				:color="getSubmitStatus(txtAll.status)"
			>
				<v-toolbar-title>Status: {{txtAll.status}}</v-toolbar-title>
			</v-toolbar>
			<v-progress-linear
				v-if="txtAll.status == 'Pending'"
				indeterminate
				color="green"
		></v-progress-linear>
			<v-card-text class="pt-6">
				<pre class="text-wrap">{{ txtAll.reason }}</pre>
				<v-row>
					<v-col cols="3">
						<v-row
							v-for="sid in sids"
							:key="sid"
						>
							<v-chip
								color="green"
								dark
								class="ma-1"
							>
								<v-icon class='mx-1'>mdi-car</v-icon>
								<pre>SC {{sid}}</pre>
							</v-chip>
						</v-row>
					</v-col>
					<v-col>
						<v-row
							v-for="data in txtAll.data"
							:key="data"
						>
							<v-chip
								color="orange"
								dark
								class="ma-1"
							>
								<v-icon class='mx-1'>mdi-message</v-icon>
								<pre>{{ data }}</pre> 
							</v-chip>
						</v-row>
					</v-col>
				</v-row>
			</v-card-text>
			<v-card-actions>
				<v-spacer></v-spacer>
				<v-btn
					color="green darken-1"
					text
					@click="btnSendAll()"
				>Yes
				</v-btn>
				<v-btn
					color="green darken-1"
					text
					@click="localBoolAll = false"
				>No
				</v-btn>
			</v-card-actions>
		</v-card>
	</v-dialog>
</template>

<script>

	export default{
		name: "DialogSkycarShellModeAll",
		props:{
			boolAll:{
				type:Boolean,
				default:false
			},
			txtAll:null,
			sids:Array
		},
		data:()=>({
			localBoolAll:false,
			localTxtAll:null,
			btnSendAll: function () {
				this.$emit("send-all")
			}
		}),
		created(){
			this.localBoolAll = this.boolAll
			this.localTxtAll = this.txtAll
		},
		methods:{
			getSubmitStatus(status) {
				if (status == "Accepted") {
					return "green"
				} else if (status == "Rejected" || status == "Warning") {
					return "red"
				} else if (status == "Pending") {
					return "black"
				}
			}
		},
		watch:{
			localBoolAll(bool){
				if(!bool)
			this.$emit("close-dialog");
			},
			boolAll(value){
				this.localBoolAll = value
			}
		}
	}

</script>