<template>
	<v-dialog
		v-if="localResBool"
		v-model="localResBool"
		max-width="800"
		@keydown.enter="localResBool=false"
		scrollable
	>
		<v-card>
			<v-toolbar
				dark
				max-height="300"
			>
				<v-toolbar-title>Last Message</v-toolbar-title>
			</v-toolbar>
			<v-card-text class="pt-6">
				<v-card
					dark
					v-if="resTxt.length != 0"
				>
					<v-col>
						<v-row
							v-for="(item, index) in resTxt"
							:key="index"
						>
							<v-spacer v-if="!item.from_tc"></v-spacer>
							<v-chip
								:color="shellMessageColor(item)"
								class="ma-1"
							>
								<pre>{{item.message}}</pre>
							</v-chip>
						</v-row>
					</v-col>
				</v-card>
				<span v-else>No History</span>
			</v-card-text>
			<v-card-actions>
				<v-spacer></v-spacer>
				<v-btn
					color="green darken-1"
					text
					@click="localResBool = false"
				>Close
				</v-btn>
			</v-card-actions>
		</v-card>
	</v-dialog>
</template>

<script>

	export default{
		name: "ShellModeResponse",
		props:{
			resBool:{
				type:Boolean,
				default:false
			},
			resTxt: Array
		},
		created(){
			this.localResBool = this.resBool
			this.localResTxt = this.resTxt
		},
		data: () =>({
			localResBool:false,
			localResTxt:null,
			shellMessageColor: function(data) {
				switch (data.from_tc) {
					case true: {
						return data.status ? 'green' : 'red'
					}
					case false: return 'orange'
				}
			}
		}),
		watch:{
			localResBool(bool){
				if(!bool)
			this.$emit("close-dialog");
			},
			resBool(value){
				this.localResBool = value
			}
			
		}
	}
</script>