<template>
  <v-dialog 
    v-model="dialogOpen" 
    max-width="500"
    @click:outside="handleClickOutside"
  >
    <v-card>
      <v-toolbar
        dark
        :color="responseStatus.color"
        v-if="responseStatus.show"
      >
        <v-toolbar-title>{{ responseStatus.title }}</v-toolbar-title>
      </v-toolbar>
      
      <v-progress-linear
        v-if="loading"
        indeterminate
        color="primary"
      ></v-progress-linear>
      
      <v-card-title>Import Error Code</v-card-title>
      
      <v-card-text>
        <v-file-input
          v-model="file"
          label="Select CSV or Excel file"
          accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
          outlined
          dense
          :disabled="loading || responseStatus.show"
        />
        
        <!-- Display Last Uploaded User -->
        <v-card outlined class="mt-3 mb-3">
          <v-card-text class="py-2">
            <div class="d-flex align-center">
              <v-icon class="mr-3">mdi-account-circle</v-icon>
              <div>
                <div class="text-caption grey--text">Last Uploaded By</div>
                <div class="text-subtitle-1 font-weight-medium">
                  {{ lastUploader !== null ? (lastUploader || 'N/A') : "Loading..." }}
                </div>
                <div v-if="lastUploadTime" class="text-caption grey--text">
                  {{ new Date(lastUploadTime).toLocaleString() }}
                </div>
              </div>
            </div>
          </v-card-text>
        </v-card>
        
        <!-- Response Message Display -->
        <v-alert
          v-if="responseStatus.show"
          :type="responseStatus.alertType"
          class="mt-3"
        >
          {{ responseStatus.message }}
        </v-alert>
      </v-card-text>
      
      <v-card-actions>
        <v-spacer />
        <v-btn 
          color="primary" 
          :loading="loading" 
          @click="onImport" 
          :disabled="!file || loading || responseStatus.show"
        >
          Import
        </v-btn>
        <v-btn 
          text 
          @click="close"
        >
          {{ responseStatus.show ? 'Close' : 'Cancel' }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
import { getHost, getRequestHeader } from "../../helper/common.js";

export default {
  name: 'DialogImportErrorCode',
  props: {
    apiRoute: { type: String, required: true },
    zone: { type: String, required: true },
    show: { type: Boolean, required: true },
  },
  data() {
    return {
      file: null,
      loading: false,
      responseStatus: {
        show: false,
        color: "primary",
        title: "",
        message: "",
        alertType: "info"
      },
      lastUploader: null,
      lastUploadTime: null,
    }
  },
  computed: {
    dialogOpen: {
      get() {
        return this.show;
      },
      set(value) {
        this.$emit('update:show', value);
      }
    }
  },
  watch: {
    show(newVal) {
      if (newVal === true) {
        // Reset state when dialog is opened
        this.resetState();
        this.fetchLastUploader();
      }
    }
  },
  methods: {
    handleClickOutside() {
      // Only allow closing by clicking outside if not loading and no response shown
      if (!this.loading && !this.responseStatus.show) {
        this.close();
      }
    },
    resetState() {
      this.file = null;
      this.loading = false;
      this.responseStatus = {
        show: false,
        color: "primary",
        title: "",
        message: "",
        alertType: "info"
      };
      this.lastUploader = null;
      this.lastUploadTime = null;
    },
    close() {
      this.$emit('update:show', false)
      this.resetState();
    },
    setSuccess(message) {
      this.responseStatus = {
        show: true,
        color: "green",
        title: "Import Successful",
        message: message || "Error codes imported successfully.",
        alertType: "success"
      };
      // Also show snackbar notification
      this.$root.$emit('show-snackbar', { success: true, message: message || 'Import successful' });
      this.fetchLastUploader();
    },
    setError(message) {
      this.responseStatus = {
        show: true,
        color: "red",
        title: "Import Failed",
        message: message || "Failed to import error codes. Please try again.",
        alertType: "error"
      };
      // Also show snackbar notification
      this.$root.$emit('show-snackbar', { success: false, message: 'Import failed: ' + message });
    },
    async onImport() {
      if (!this.file) return
      this.loading = true
      this.responseStatus.show = false;
      
      try {
        const formData = new FormData()
        formData.append('file', this.file)
        
        // Use getHost pattern like other API calls
        const host = getHost(this.zone) + this.apiRoute
        
        // Use fetch with proper headers like myget function does
        const requestOptions = {
          method: "POST",
          headers: getRequestHeader(),
          body: formData
        }
        
        // Remove Content-Type from headers to let browser set it properly with boundary
        delete requestOptions.headers['Content-Type'];
        
        const response = await fetch(host, requestOptions)
        const result = await response.json()
        
        if (result.status === true) {
          this.setSuccess(result.message || result.reason);
        } else {
          this.setError(result.message || result.reason || "Unknown error");
        }
      } catch (err) {
        console.error('Import error:', err)
        this.setError(err.message || "Unknown error");
      } finally {
        this.loading = false
      }
    },
    async fetchLastUploader() {
        this.lastUploader = null;
        this.lastUploadTime = null;
        try {
            const fetchUrl = getHost(this.zone) + this.apiRoute;

            const requestOptions = {
              method: "GET",
              headers: getRequestHeader(),
            };

            const response = await fetch(fetchUrl, requestOptions);
            const result = await response.json();

            if (result.status === true && result.data && result.data.last_uploader) {
                const uploaderData = result.data.last_uploader;
                this.lastUploader = uploaderData.username || "N/A (No previous uploads)";
                this.lastUploadTime = uploaderData.upload_time;
            } else {
                this.lastUploader = "N/A (No previous uploads)";
                this.lastUploadTime = null;
            }
        } catch (error) {
            console.error("Error fetching last uploader:", error);
            this.lastUploader = "Error fetching uploader";
            this.lastUploadTime = null;
        }
    },
  },
}
</script> 