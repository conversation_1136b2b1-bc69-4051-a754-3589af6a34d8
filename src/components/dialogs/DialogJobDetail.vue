<template>
  <div>
    <v-dialog
      v-model="dialog"
      max-width="1000px"
      @keydown.esc="closeDialog"
    >
    <v-card dark color="black">
      <!-- Header -->
      <v-card-title class="pa-0">
        <v-toolbar
          dark
          color="grey darken-4"
          flat
        >
          <v-icon class="mr-3">mdi-briefcase-outline</v-icon>
          <span class="text-h5">Job Details</span>
          <v-spacer />
          <v-chip
            :color="getStatusColor(jobData.status)"
            dark
            small
          >
            <v-icon left size="16">{{ getStatusIcon(jobData.status) }}</v-icon>
            {{ jobData.status }}
          </v-chip>
          <v-btn
            icon
            @click="closeDialog"
            class="ml-2"
          >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
      </v-card-title>

      <!-- Loading State -->
      <v-card-text v-if="loading" class="text-center pa-8">
        <v-progress-circular
          indeterminate
          color="white"
          size="50"
        />
        <div class="mt-4 text-subtitle1">Loading job details...</div>
      </v-card-text>

      <!-- Error State -->
      <v-card-text v-else-if="error" class="text-center pa-8">
        <v-icon size="64" color="red">mdi-alert-circle</v-icon>
        <div class="mt-4 text-h6 red--text">Failed to load job details</div>
        <div class="text-body-2 grey--text">{{ error }}</div>
      </v-card-text>

      <!-- Job Details Content -->
      <v-card-text v-else class="pa-0">
        <v-container fluid class="pa-4">
          <!-- Basic Info Row -->
          <v-row class="mb-4">
            <v-col cols="12" md="4">
              <v-card outlined color="grey darken-3" class="pa-4">
                <div class="d-flex align-center mb-2">
                  <v-icon class="mr-2" color="white">mdi-identifier</v-icon>
                  <span class="text-subtitle2 grey--text text--lighten-2">Job ID</span>
                </div>
                <div class="text-h4 white--text">{{ jobData.job_id }}</div>
              </v-card>
            </v-col>
            <v-col cols="12" md="4">
              <v-card outlined color="grey darken-3" class="pa-4">
                <div class="d-flex align-center mb-2">
                  <v-icon class="mr-2" color="white">mdi-car</v-icon>
                  <span class="text-subtitle2 grey--text text--lighten-2">Skycar ID</span>
                </div>
                <div class="text-h4 white--text">{{ jobData.skycar_id }}</div>
              </v-card>
            </v-col>
            <v-col cols="12" md="4">
              <v-card outlined color="grey darken-3" class="pa-4">
                <div class="d-flex align-center mb-2">
                  <v-icon class="mr-2" color="white">mdi-cube-outline</v-icon>
                  <span class="text-subtitle2 grey--text text--lighten-2">Zone</span>
                </div>
                <div class="text-h4 white--text">{{ jobData.zone }}</div>
              </v-card>
            </v-col>
          </v-row>

          <!-- Job Type & Description -->
          <v-row class="mb-4">
            <v-col cols="12" md="6">
              <v-card outlined color="grey darken-3" class="pa-4">
                <div class="d-flex align-center mb-2">
                  <v-icon class="mr-2" color="white">mdi-format-list-bulleted-type</v-icon>
                  <span class="text-subtitle2 grey--text text--lighten-2">Job Type</span>
                </div>
                <v-chip
                  :color="getTypeColor(jobData.type)"
                  dark
                  class="text-subtitle1"
                >
                  <v-icon left>{{ getTypeIcon(jobData.type) }}</v-icon>
                  {{ jobData.type }}
                </v-chip>
              </v-card>
            </v-col>
            <v-col cols="12" md="6">
              <v-card outlined color="grey darken-3" class="pa-4">
                <div class="d-flex align-center mb-2">
                  <v-icon class="mr-2" color="white">mdi-information-outline</v-icon>
                  <span class="text-subtitle2 grey--text text--lighten-2">Description</span>
                </div>
                <div class="text-subtitle1 white--text">{{ jobData.station_desc || 'No description' }}</div>
              </v-card>
            </v-col>
          </v-row>

          <!-- Location Info -->
          <v-row class="mb-4">
            <v-col cols="12">
              <v-card outlined color="grey darken-3" class="pa-4">
                <div class="text-h6 white--text mb-3">
                  <v-icon class="mr-2">mdi-map-marker-path</v-icon>
                  Route Information
                </div>
                <v-row>
                  <v-col cols="12" md="6">
                    <div class="text-center">
                      <v-icon size="48" color="green">mdi-map-marker-check</v-icon>
                      <div class="text-subtitle2 grey--text text--lighten-2 mt-2">FROM</div>
                      <div class="text-h6 white--text">
                        <template v-if="jobData.from_station_code === 0">
                          Cube
                        </template>
                        <template v-else>
                          Station {{ jobData.from_station_code }}
                        </template>
                      </div>
                      <div class="text-body-2 grey--text">{{ jobData.from_coordinate }}</div>
                      <div class="text-caption grey--text">Node: {{ jobData.from_node }}</div>
                    </div>
                  </v-col>
                  <v-col cols="12" md="6">
                    <div class="text-center">
                      <v-icon size="48" color="red">mdi-map-marker</v-icon>
                      <div class="text-subtitle2 grey--text text--lighten-2 mt-2">TO</div>
                      <div class="text-h6 white--text">
                        <template v-if="jobData.to_station_code === 0">
                          Cube
                        </template>
                        <template v-else>
                          Station {{ jobData.to_station_code }}
                        </template>
                      </div>
                      <div class="text-body-2 grey--text">{{ jobData.to_coordinate }}</div>
                      <div class="text-caption grey--text">Node: {{ jobData.to_node }}</div>
                    </div>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>

          <!-- Storage Info -->
          <v-row class="mb-4" v-if="jobData.with_storage">
            <v-col cols="12" md="6">
              <v-card outlined color="grey darken-3" class="pa-4">
                <div class="d-flex align-center mb-2">
                  <v-icon class="mr-2" color="white">mdi-package-variant</v-icon>
                  <span class="text-subtitle2 grey--text text--lighten-2">Storage</span>
                </div>
                <div class="text-h6 white--text">{{ jobData.storage_no }}</div>
                <div class="text-body-2 grey--text">Code: {{ jobData.storage_code }}</div>
                <div class="text-caption grey--text">Position: {{ jobData.position }}</div>
              </v-card>
            </v-col>
            <v-col cols="12" md="6">
              <v-card outlined color="grey darken-3" class="pa-4">
                <div class="d-flex align-center mb-2">
                  <v-icon class="mr-2" color="white">mdi-clock-outline</v-icon>
                  <span class="text-subtitle2 grey--text text--lighten-2">ETA</span>
                </div>
                <div class="text-h6 white--text">
                  {{ jobData.eta ? `${jobData.eta}s` : 'Not available' }}
                </div>
              </v-card>
            </v-col>
          </v-row>

          <!-- Timestamps -->
          <v-row class="mb-4">
            <v-col cols="12" md="3">
              <v-card outlined color="grey darken-3" class="pa-4">
                <div class="d-flex align-center mb-2">
                  <v-icon class="mr-2" color="white">mdi-clock-start</v-icon>
                  <span class="text-subtitle2 grey--text text--lighten-2">SM Created</span>
                </div>
                <div class="text-body-1 white--text">{{ formatDateTime(jobData.start_job_time) }}</div>
              </v-card>
            </v-col>
            <v-col cols="12" md="3">
              <v-card outlined color="grey darken-3" class="pa-4">
                <div class="d-flex align-center mb-2">
                  <v-icon class="mr-2" color="white">mdi-calendar-plus</v-icon>
                  <span class="text-subtitle2 grey--text text--lighten-2">Created</span>
                </div>
                <div class="text-body-1 white--text">{{ formatDateTime(jobData.created_at) }}</div>
              </v-card>
            </v-col>
            <v-col cols="12" md="3">
              <v-card outlined color="grey darken-3" class="pa-4">
                <div class="d-flex align-center mb-2">
                  <v-icon class="mr-2" color="white">mdi-play-circle</v-icon>
                  <span class="text-subtitle2 grey--text text--lighten-2">Begin At</span>
                </div>
                <div class="text-body-1 white--text">{{ formatDateTime(jobData.begin_at) }}</div>
              </v-card>
            </v-col>
            
            <v-col cols="12" md="3">
              <v-card outlined color="grey darken-3" class="pa-4">
                <div class="d-flex align-center mb-2">
                  <v-icon class="mr-2" color="white">mdi-check-circle</v-icon>
                  <span class="text-subtitle2 grey--text text--lighten-2">Completed</span>
                </div>
                <div class="text-body-1 white--text">
                  {{ jobData.completed_at ? formatDateTime(jobData.completed_at) : (jobData.skycar_id ? 'In Progress' : 'Not Start') }}
                </div>
              </v-card>
            </v-col>
          </v-row>

          <!-- Error Info (if any) -->
          <v-row v-if="jobData.error_name || jobData.failed_rule" class="mb-4">
            <v-col cols="12">
              <v-card outlined color="red darken-4" class="pa-4">
                <div class="text-h6 red--text mb-3">
                  <v-icon class="mr-2" color="red">mdi-alert-circle</v-icon>
                  Issues & Errors
                </div>
                <div v-if="jobData.error_name" class="mb-2">
                  <div class="text-subtitle2 grey--text text--lighten-2">Error Name</div>
                  <div class="text-body-1 white--text">{{ jobData.error_name }}</div>
                </div>
                <div v-if="jobData.error_msg" class="mb-2">
                  <div class="text-subtitle2 grey--text text--lighten-2">Error Message</div>
                  <div class="text-body-1 white--text">{{ jobData.error_msg }}</div>
                </div>
                <div v-if="jobData.failed_rule" class="mb-2">
                  <div class="text-subtitle2 grey--text text--lighten-2">Failed Rule</div>
                  <div class="text-body-1 white--text">{{ jobData.failed_rule }}</div>
                </div>
                <div v-if="jobData.halt_reason && (Array.isArray(jobData.halt_reason) ? jobData.halt_reason.length > 0 : !!jobData.halt_reason)">
                  <div class="text-subtitle2 grey--text text--lighten-2">Halt Reasons</div>
                  <v-chip
                    v-for="(reason, index) in Array.isArray(jobData.halt_reason) ? jobData.halt_reason.filter(r => r) : [jobData.halt_reason]"
                    :key="index"
                    small
                    color="red darken-2"
                    dark
                    class="ma-1"
                  >
                    {{ reason }}
                  </v-chip>
                </div>
              </v-card>
            </v-col>
          </v-row>

          <!-- Additional Info -->
          <v-row>
            <v-col cols="12" md="6">
              <v-card outlined color="grey darken-3" class="pa-4">
                <div class="text-subtitle2 grey--text text--lighten-2 mb-2">Additional Information</div>
                <div class="d-flex justify-space-between mb-1">
                  <span class="grey--text">Prefer Skycar:</span>
                  <span class="white--text">{{ jobData.prefer_skycar_id || 'None' }}</span>
                </div>
                <div class="d-flex justify-space-between mb-1">
                  <span class="grey--text">SM Order ID:</span>
                  <v-btn
                    v-if="jobData.sm_order_id"
                    text
                    small
                    color="blue"
                    @click="openSmOrderDialog(jobData.sm_order_id)"
                  >
                    <v-icon left size="16">mdi-package-variant-closed</v-icon>
                    {{ jobData.sm_order_id }}
                  </v-btn>
                  <span v-else class="white--text">None</span>
                </div>
                <div class="d-flex justify-space-between mb-1">
                  <span class="grey--text">TC Rank:</span>
                  <span class="white--text">{{ jobData.tc_rank || 'None' }}</span>
                </div>
                <div class="d-flex justify-space-between mb-1">
                  <span class="grey--text">Is Halt:</span>
                  <v-chip
                    :color="jobData.is_halt ? 'red' : 'green'"
                    dark
                    x-small
                  >
                    {{ jobData.is_halt ? 'Yes' : 'No' }}
                  </v-chip>
                </div>
                <div class="d-flex justify-space-between mb-1">
                  <span class="grey--text">Is Deleted:</span>
                  <v-chip
                    :color="jobData.is_deleted ? 'red' : 'green'"
                    dark
                    x-small
                  >
                    {{ jobData.is_deleted ? 'Yes' : 'No' }}
                  </v-chip>
                </div>
              </v-card>
            </v-col>
            <v-col cols="12" md="6">
              <v-card outlined color="grey darken-3" class="pa-4">
                <div class="text-subtitle2 grey--text text--lighten-2 mb-2">Last Updated</div>
                <div class="text-h6 white--text">{{ formatDateTime(jobData.updated_at) }}</div>
                <div v-if="jobData.completed_by_skycar" class="mt-2">
                  <span class="grey--text">Completed by:</span>
                  <span class="white--text ml-2">Skycar {{ jobData.completed_by_skycar }}</span>
                </div>
              </v-card>
            </v-col>
          </v-row>

          <!-- Station Information -->
          <v-row v-if="jobData.for_station && jobData.for_station.length > 0" class="mt-4">
            <v-col cols="12">
              <v-card outlined color="grey darken-3" class="pa-4">
                <div class="text-h6 white--text mb-3">
                  <v-icon class="mr-2">mdi-home-group</v-icon>
                  Contributing Stations
                </div>
                <div class="text-body-2 grey--text text--lighten-1 mb-3">
                  <v-icon size="16" class="mr-1">mdi-information-outline</v-icon>
                  This internal job contributes to the retrieving bin progress of the following station(s):
                </div>
                <v-chip-group>
                  <v-chip
                    v-for="(station, index) in jobData.for_station"
                    :key="index"
                    color="blue darken-2"
                    dark
                    class="ma-1"
                  >
                    <v-icon left size="16">mdi-home</v-icon>
                    Station {{ station }}
                  </v-chip>
                </v-chip-group>
              </v-card>
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>

      <!-- Actions -->
      <v-card-actions class="pa-4">
        <v-spacer />
        <v-btn
          color="grey darken-1"
          text
          @click="closeDialog"
        >
          Close
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>

  <!-- SM Order Details Dialog -->
  <v-dialog
    v-model="smOrderDialog"
    max-width="800px"
    @keydown.esc="closeSmOrderDialog"
  >
    <v-card dark color="black">
      <!-- Header -->
      <v-card-title class="pa-0">
        <v-toolbar
          dark
          color="purple darken-3"
          flat
        >
          <v-icon class="mr-3">mdi-package-variant-closed</v-icon>
          <span class="text-h5">SM Order Details</span>
          <v-spacer />
          <v-chip
            :color="getSmOrderStatusColor(smOrderData.status)"
            dark
            small
          >
            <v-icon left size="16">{{ getSmOrderStatusIcon(smOrderData.status) }}</v-icon>
            {{ smOrderData.status }}
          </v-chip>
          <v-btn
            icon
            @click="closeSmOrderDialog"
            class="ml-2"
          >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
      </v-card-title>

      <!-- Loading State -->
      <v-card-text v-if="smOrderLoading" class="text-center pa-8">
        <v-progress-circular
          indeterminate
          color="white"
          size="50"
        />
        <div class="mt-4 text-subtitle1">Loading SM order details...</div>
      </v-card-text>

      <!-- Error State -->
      <v-card-text v-else-if="smOrderError" class="text-center pa-8">
        <v-icon size="64" color="red">mdi-alert-circle</v-icon>
        <div class="mt-4 text-h6 red--text">Failed to load SM order details</div>
        <div class="text-body-2 grey--text">{{ smOrderError }}</div>
      </v-card-text>

      <!-- SM Order Details Content -->
      <v-card-text v-else class="pa-0">
        <v-container fluid class="pa-4">
          <!-- Basic Info Row -->
          <v-row class="mb-4">
            <v-col cols="12" md="4">
              <v-card outlined color="grey darken-3" class="pa-4">
                <div class="d-flex align-center mb-2">
                  <v-icon class="mr-2" color="white">mdi-identifier</v-icon>
                  <span class="text-subtitle2 grey--text text--lighten-2">Order ID</span>
                </div>
                <div class="text-h4 white--text">{{ smOrderData.id }}</div>
              </v-card>
            </v-col>
            <v-col cols="12" md="4">
              <v-card outlined color="grey darken-3" class="pa-4">
                <div class="d-flex align-center mb-2">
                  <v-icon class="mr-2" color="white">mdi-format-list-bulleted-type</v-icon>
                  <span class="text-subtitle2 grey--text text--lighten-2">Type</span>
                </div>
                <v-chip
                  :color="getOrderTypeColor(smOrderData.type)"
                  dark
                  class="text-subtitle1"
                >
                  {{ smOrderData.type }}
                </v-chip>
              </v-card>
            </v-col>
            <v-col cols="12" md="4">
              <v-card outlined color="grey darken-3" class="pa-4">
                <div class="d-flex align-center mb-2">
                  <v-icon class="mr-2" color="white">mdi-priority-high</v-icon>
                  <span class="text-subtitle2 grey--text text--lighten-2">Priority Tier</span>
                </div>
                <div class="text-h4 white--text">{{ smOrderData.priorityTier }}</div>
              </v-card>
            </v-col>
          </v-row>

          <!-- Order Details -->
          <v-row class="mb-4">
            <v-col cols="12" md="6">
              <v-card outlined color="grey darken-3" class="pa-4">
                <div class="text-h6 white--text mb-3">
                  <v-icon class="mr-2">mdi-information-outline</v-icon>
                  Order Information
                </div>
                <div class="d-flex justify-space-between mb-2">
                  <span class="grey--text">Order No:</span>
                  <span class="white--text text-caption">{{ smOrderData.orderNo || 'N/A' }}</span>
                </div>
                <div class="d-flex justify-space-between mb-2">
                  <span class="grey--text">Chain No:</span>
                  <span class="white--text">{{ smOrderData.chainNo || 'None' }}</span>
                </div>
                <div class="d-flex justify-space-between mb-2">
                  <span class="grey--text">Creation Reason:</span>
                  <span class="white--text">{{ smOrderData.creationReason || 'N/A' }}</span>
                </div>
                <div class="d-flex justify-space-between mb-2">
                  <span class="grey--text">Requested By:</span>
                  <span class="white--text">{{ smOrderData.requestedBy || 'N/A' }}</span>
                </div>
                <div class="d-flex justify-space-between mb-2">
                  <span class="grey--text">Is Transfer:</span>
                  <v-chip
                    :color="smOrderData.isTransfer ? 'green' : 'grey'"
                    dark
                    x-small
                  >
                    {{ smOrderData.isTransfer ? 'Yes' : 'No' }}
                  </v-chip>
                </div>
              </v-card>
            </v-col>
            <v-col cols="12" md="6">
              <v-card outlined color="grey darken-3" class="pa-4">
                <div class="text-h6 white--text mb-3">
                  <v-icon class="mr-2">mdi-clock-outline</v-icon>
                  Timestamps
                </div>
                <div class="d-flex justify-space-between mb-2">
                  <span class="grey--text">Created:</span>
                  <span class="white--text">{{ formatDateTime(smOrderData.createdAt) }}</span>
                </div>
                <div class="d-flex justify-space-between mb-2">
                  <span class="grey--text">Updated:</span>
                  <span class="white--text">{{ formatDateTime(smOrderData.updatedAt) }}</span>
                </div>
                <div class="d-flex justify-space-between mb-2">
                  <span class="grey--text">Requested:</span>
                  <span class="white--text">{{ formatDateTime(smOrderData.requestedAt) || 'N/A' }}</span>
                </div>
                <div class="d-flex justify-space-between mb-2">
                  <span class="grey--text">Dispatched:</span>
                  <span class="white--text">{{ formatDateTime(smOrderData.dispatchedAt) || 'N/A' }}</span>
                </div>
                <div class="d-flex justify-space-between mb-2">
                  <span class="grey--text">Completed:</span>
                  <span class="white--text">{{ formatDateTime(smOrderData.completedAt) || 'N/A' }}</span>
                </div>
              </v-card>
            </v-col>
          </v-row>

          <!-- Storage Information -->
          <v-row class="mb-4" v-if="smOrderData.storage">
            <v-col cols="12">
              <v-card outlined color="grey darken-3" class="pa-4">
                <div class="text-h6 white--text mb-3">
                  <v-icon class="mr-2">mdi-package-variant</v-icon>
                  Storage Information
                </div>
                <v-row>
                  <v-col cols="12" md="6">
                    <div class="d-flex justify-space-between mb-2">
                      <span class="grey--text">Storage Code:</span>
                      <span class="white--text">{{ smOrderData.storage.code }}</span>
                    </div>
                    <div class="d-flex justify-space-between mb-2">
                      <span class="grey--text">Status:</span>
                      <v-chip
                        :color="getStorageStatusColor(smOrderData.storage.status)"
                        dark
                        x-small
                      >
                        {{ smOrderData.storage.status }}
                      </v-chip>
                    </div>
                    <div class="d-flex justify-space-between mb-2">
                      <span class="grey--text">Last Movement:</span>
                      <span class="white--text">{{ smOrderData.storage.lastMovement }}</span>
                    </div>
                    <div class="d-flex justify-space-between mb-2">
                      <span class="grey--text">Last Moved:</span>
                      <span class="white--text">{{ formatDateTime(smOrderData.storage.lastMovedAt) }}</span>
                    </div>
                  </v-col>
                  <v-col cols="12" md="6" v-if="smOrderData.storage.node">
                    <div class="text-subtitle2 grey--text text--lighten-2 mb-2">Node Information</div>
                    <div class="d-flex justify-space-between mb-1">
                      <span class="grey--text">Node ID:</span>
                      <span class="white--text">{{ smOrderData.storage.node.id }}</span>
                    </div>
                    <div class="d-flex justify-space-between mb-1">
                      <span class="grey--text">Coordinates:</span>
                      <span class="white--text">{{ smOrderData.storage.node.x }},{{ smOrderData.storage.node.y }},{{ smOrderData.storage.node.z }}</span>
                    </div>
                    <div class="d-flex justify-space-between mb-1">
                      <span class="grey--text">Zone Group:</span>
                      <span class="white--text">{{ smOrderData.storage.node.zoneGroup }}</span>
                    </div>
                    <div class="d-flex justify-space-between mb-1">
                      <span class="grey--text">Type:</span>
                      <span class="white--text">{{ smOrderData.storage.node.type }}</span>
                    </div>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>

          <!-- Error Information (if any) -->
          <v-row v-if="smOrderData.errorReason" class="mb-4">
            <v-col cols="12">
              <v-card outlined color="red darken-4" class="pa-4">
                <div class="text-h6 red--text mb-3">
                  <v-icon class="mr-2" color="red">mdi-alert-circle</v-icon>
                  Error Information
                </div>
                <div class="d-flex justify-space-between mb-2">
                  <span class="grey--text">Error Reason:</span>
                  <span class="white--text">{{ smOrderData.errorReason }}</span>
                </div>
                <div class="d-flex justify-space-between mb-2">
                  <span class="grey--text">Last Errored:</span>
                  <span class="white--text">{{ formatDateTime(smOrderData.lastErroredAt) }}</span>
                </div>
                <div class="d-flex justify-space-between mb-2">
                  <span class="grey--text">Recovery Type:</span>
                  <span class="white--text">{{ smOrderData.recoveryType || 'None' }}</span>
                </div>
              </v-card>
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>

      <!-- Actions -->
      <v-card-actions class="pa-4">
        <v-spacer />
        <v-btn
          color="grey darken-1"
          text
          @click="closeSmOrderDialog"
        >
          Close
        </v-btn>
             </v-card-actions>
     </v-card>
   </v-dialog>
  </div>
</template>

<script>
import { getHost, getRequestHeader, useRefreshToken, convertStringToLocal } from "../../helper/common.js";
import axios from "axios";

export default {
  name: "DialogJobDetail",
  props: {
    currentZone: {
      type: String,
      required: true
    },
    showNotification: {
      type: Function,
      required: true
    },
    storybookMode: {
      type: Boolean,
      default: false
    },
    mockJobData: {
      type: Object,
      default: () => ({})
    },
    mockSmOrderData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialog: false,
      loading: false,
      error: null,
      jobData: {},
      // SM Order dialog data
      smOrderDialog: false,
      smOrderLoading: false,
      smOrderError: null,
      smOrderData: {}
    };
  },
  methods: {
    async openDialog(jobId) {
      if (!jobId) {
        this.showNotification(false, "No job ID provided");
        return;
      }

      this.dialog = true;
      this.loading = true;
      this.error = null;
      this.jobData = {};

      // If in Storybook mode, always use mock data and never call API
      if (this.storybookMode) {
        this.jobData = this.mockJobData || {};
        this.loading = false;
        console.log("Job Data:", this.jobData);
        return;
      }

      try {
        await this.fetchJobDetail(jobId);
      } catch (error) {
        console.error("Error fetching job detail:", error);
        this.error = error.message || "Failed to fetch job details";
      } finally {
        this.loading = false;
      }
    },

    async fetchJobDetail(jobId) {
      const host = getHost(this.currentZone);
      const url = `${host}/job?id=${jobId}`;
      
      try {
        const response = await axios.get(url, {
          headers: getRequestHeader()
        });

        if (response.data.status && response.data.data && response.data.data.length > 0) {
          this.jobData = response.data.data[0];
        } else {
          throw new Error("Job not found or invalid response");
        }
      } catch (error) {
        if (error.response && error.response.status === 401) {
          return useRefreshToken(this, this.fetchJobDetail, jobId);
        }
        throw error;
      }
    },

    closeDialog() {
      this.dialog = false;
      this.jobData = {};
      this.error = null;
    },

    formatDateTime(dateString) {
      if (!dateString) return 'N/A';
      return convertStringToLocal(dateString, true);
    },

    getStatusColor(status) {
      switch (status) {
        case 'PROCESSING':
          return 'blue';
        case 'COMPLETED':
          return 'green';
        case 'FAILED':
        case 'ERROR':
          return 'red';
        case 'PENDING':
          return 'orange';
        default:
          return 'grey';
      }
    },

    getStatusIcon(status) {
      switch (status) {
        case 'PROCESSING':
          return 'mdi-cog';
        case 'COMPLETED':
          return 'mdi-check-circle';
        case 'FAILED':
        case 'ERROR':
          return 'mdi-alert-circle';
        case 'PENDING':
          return 'mdi-clock';
        default:
          return 'mdi-help-circle';
      }
    },

    getTypeColor(type) {
      switch (type) {
        case 'RETRIEVING':
          return 'blue darken-2';
        case 'STORING':
          return 'green darken-2';
        case 'MAINTENANCE':
          return 'orange darken-2';
        default:
          return 'grey darken-2';
      }
    },

    getTypeIcon(type) {
      switch (type) {
        case 'RETRIEVING':
          return 'mdi-download';
        case 'STORING':
          return 'mdi-upload';
        case 'MAINTENANCE':
          return 'mdi-wrench';
        default:
          return 'mdi-briefcase';
      }
    },

    // SM Order Dialog Methods
    async openSmOrderDialog(smOrderId) {
      if (this.storybookMode) {
        console.log("Storybook mode is enabled");
        this.smOrderDialog = true;
        this.smOrderLoading = false;
        this.smOrderError = null;
        this.smOrderData = this.mockSmOrderData || {};
        console.log("SM Order Data:", this.smOrderData);
        return;
      }

      if (!smOrderId) {
        this.showNotification(false, "No SM Order ID provided");
        return;
      }

      this.smOrderDialog = true;
      this.smOrderLoading = true;
      this.smOrderError = null;
      this.smOrderData = {};

      try {
        await this.fetchSmOrderDetail(smOrderId);
      } catch (error) {
        console.error("Error fetching SM order detail:", error);
        this.smOrderError = error.message || "Failed to fetch SM order details";
      } finally {
        this.smOrderLoading = false;
      }
    },

    async fetchSmOrderDetail(smOrderId) {
      const smApiUrl = process.env.VUE_APP_SM_API_URL || "http://172.16.29.64:3120";
      const url = `${smApiUrl}/v3/sm-orders/${smOrderId}`;
      
      const response = await axios.get(url, {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.data && response.data.data) {
        this.smOrderData = response.data.data;
      } else {
        throw new Error("SM Order not found or invalid response");
      }
    },

    closeSmOrderDialog() {
      this.smOrderDialog = false;
      this.smOrderData = {};
      this.smOrderError = null;
    },

    getSmOrderStatusColor(status) {
      switch (status) {
        case 'AVAILABLE':
          return 'green';
        case 'DISPATCHED':
          return 'blue';
        case 'PROCESSING':
          return 'orange';
        case 'COMPLETED':
          return 'green darken-2';
        case 'FAILED':
        case 'ERROR':
          return 'red';
        default:
          return 'grey';
      }
    },

    getSmOrderStatusIcon(status) {
      switch (status) {
        case 'AVAILABLE':
          return 'mdi-check-circle';
        case 'DISPATCHED':
          return 'mdi-send';
        case 'PROCESSING':
          return 'mdi-cog';
        case 'COMPLETED':
          return 'mdi-check-all';
        case 'FAILED':
        case 'ERROR':
          return 'mdi-alert-circle';
        default:
          return 'mdi-help-circle';
      }
    },

    getOrderTypeColor(type) {
      switch (type) {
        case 'INTERNAL':
          return 'blue darken-2';
        case 'EXTERNAL':
          return 'green darken-2';
        case 'MAINTENANCE':
          return 'orange darken-2';
        default:
          return 'grey darken-2';
      }
    },

    getStorageStatusColor(status) {
      switch (status) {
        case 'AVAILABLE':
          return 'green';
        case 'OCCUPIED':
          return 'orange';
        case 'RESERVED':
          return 'blue';
        case 'ERROR':
          return 'red';
        default:
          return 'grey';
      }
    }
  }
};
</script>

<style scoped>
.v-card {
  border-radius: 8px;
}

.v-card--outlined {
  border: 1px solid rgba(255, 255, 255, 0.12);
}
</style> 