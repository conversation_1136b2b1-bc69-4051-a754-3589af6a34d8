<template>
  <v-dialog
    v-model="dialogBool"
    max-width="1000"
  >
    <v-card>
      <v-col>
        <v-data-table
          :headers="headers"
          :items="result"
          :search="search"
          :loading="!doneSync"
          :items-per-page="itemsPerPage"
          @update:page="onPageChange"
          hide-default-footer
        >
          <template v-slot:top>
            <v-toolbar
              flat
              dark
            >
              <v-toolbar-title>Event Log: {{ modules }} </v-toolbar-title>
            </v-toolbar>
            <v-row>
              <v-col>
                <DateTimePicker 
                  label="Start Time" 
                  :default-date-time="defaultDateTime"
                  @get-datetime="getStartTimestamp"
                />
              </v-col>
              <v-col>
                <v-text-field
                  v-model="search"
                  append-icon="mdi-magnify"
                  label="Search"
                  hide-details
                />
              </v-col>
            </v-row>
            <v-row v-if="eventOptions.length > 1">
              <v-col>
                <v-select
                  v-model="events"
                  :items="eventOptions"
                  label="Events"
                  @input="onEventChange"
                  multiple
                  chips
                />
              </v-col>
            </v-row>
          </template>
          <template v-slot:[`item.created_at`]="{ item }">
            {{ convertTimestampToLocal(item.created_at, true) }}
          </template>
        </v-data-table>
        <v-row class="d-flex justify-end">
          <v-col>
            <v-pagination
              v-model="currentPage"
              :length="totalPages"
              :total-visible="8"
              @input="onPageChange"
            />
          </v-col>
          <v-col>
            <v-select
              v-model="itemsPerPage"
              :items="perPageOptions"
              label="Items per page"
              @input="onPerPageChange"
            />
          </v-col>
        </v-row>
        <v-card-actions>
          <v-spacer />
          <ProgressCircular :done-sync="doneSync" />
          <v-btn
            color="green darken-1"
            text
            @click="btnRefresh()"
            :disabled="!doneSync || invalidEvent"
          >
            Refresh
          </v-btn>
          <v-btn
            color="green darken-1"
            text
            @click="closeDialog()"
          >
            Close
          </v-btn>
        </v-card-actions>
      </v-col>
    </v-card>
  </v-dialog>
</template>

<script>
import { convertTimestampToLocal, getBTUrl, getCurrentDateTime, 
    getRequestHeader, useRefreshToken } from "../../helper/common"
import { Event, RouteEventLog } from "../../helper/enums"
import DateTimePicker from "../shared/DateTimePicker.vue"
import ProgressCircular from "../shared/ProgressCircular.vue"
export default {
    components: {
        DateTimePicker,
        ProgressCircular
    },
    props: {
      defaultEvents: {
        type: Array,
        default: () => [Event.GENERAL]
      },
      eventOptions: {
        type: Array,
        default: () => [Event.GENERAL]
      },
      showNotification: {
        type: Function
      }
    },
    created() {
      this.events = this.defaultEvents
    },
    data: () => ({
        convertTimestampToLocal,
        dialogBool: false,
        doneSync: true,
        headers: [
            { text: "Created At", value: "created_at", width: "200px" },
            { text: "Triggered By", value: "triggered_by", width: "200px" },
            { text: "Description", value: "description" }
        ],
        result: [],
        modules: null,
        events: null,
        startTimestamp: null,
        search: "",
        itemsPerPage: 10,
        perPageOptions: [10, 20, 50, 100, 200],
        totalPages: 1,
        currentPage: 1
    }),
    methods: {
        openDialog(modules) {
            this.currentPage = 1
            this.modules = modules
            this.dialogBool = true
            if (this.startTimestamp !== null) {
              this.btnRefresh()
            }
        },
        closeDialog() {
            this.dialogBool = false
        },
        async btnRefresh() {
            try {
                if (this.invalidEvent) {
                    this.result = []
                    this.currentPage = 1
                    this.totalPages = 1
                    return
                }

                this.doneSync = false
                let url = getBTUrl() + RouteEventLog.eventLog
                let qs = new URLSearchParams({
                    modules: this.modules,
                    "start_time": this.startTimestamp / 1000,
                    page: this.currentPage,
                    "per_page": this.itemsPerPage
                })
                this.events.forEach((event) => {
                  qs.append("events", event)
                })
                let req = await fetch(`${url}?${qs}`, {
                    method: "GET",
                    headers: getRequestHeader()
                })
                let res = JSON.parse(await req.text())
                if (res.code === 401){
                    res = useRefreshToken(this, this.btnRefresh)
                }
                if (res.status) {
                    this.result = res.data
                    this.totalPages = res.total_page
                } else {
                    this.showNotification(false, res.message)
                }
            } catch (error) {
                this.showNotification(false, error)
            } finally {
                setTimeout(() => {
                    this.doneSync = true
                }, 500)
            }
        },
        defaultDateTime() {
            let now = new Date(getCurrentDateTime())
            let date = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, "0")}-${now.getDate().toString().padStart(2, "0")}`
            now.setHours(0, 0, 0, 0)
            let time = now.toTimeString().substring(0, 5)
            return [date, time]
        },
        getStartTimestamp(dt) {
            this.startTimestamp = dt.getTime()
        },
        onPageChange(val) {
            this.update(val)
        },
        onPerPageChange(val) {
            this.itemsPerPage = val
            this.update(1)
        },
        onEventChange() {
          this.update(1)
        },
        update(page) {
          this.currentPage = page
          this.btnRefresh()
        },
        
    },
    computed: {
      invalidEvent() {
        return this.events.length == 0
      }
    },
    watch: {
      startTimestamp() {
        this.update(1)
      }
    }
}
</script>
