<template>
  <v-card
    v-if="chatBool"
    @keydown.enter="sendConvoMessage"
    :style="cardStyle"
  >
    <v-card-text class="pt-6">
      <v-card
        dark
        :style="chatStyle"
        ref="chatDialog"
        v-if="chatTxt.length !== 0"
      >
        <v-col>
          <v-row
            v-for="(item, index) in latest50ChatTxt"
            :key="index"
          >
            <v-spacer v-if="!item.from_tc && item.status" />
            <div>
              <div
                class="d-flex justify-end"
                v-if="!item.from_tc && item.status"
              >
                <pre style="font-size: 11px">{{ convertStringToLocal(item.time, true) }}</pre>
              </div>

              <div v-else>
                <pre style="font-size: 11px">{{ convertStringToLocal(item.time, true) }}</pre>
              </div>

              <v-chip
                :color="shellMessageColor(item)"
                class="ma-1"
                v-if="item.message"
              >
                <pre>{{ item.user }}: {{ item.message }}</pre>
              </v-chip>
            </div>
          </v-row>
        </v-col>
      </v-card>
      <span v-else>No History</span>
    </v-card-text>
    <v-card-actions :style="sendMessageStyle">
      <v-row>
        <v-col cols="9">
          <v-text-field
            v-model="chatConversation"
            label="Send a message"
            outlined
            dense
          />
        </v-col>
        <v-col cols="3">
          <v-btn
            color="blue"
            text 
            @click="sendConvoMessage"
          >
            Send
          </v-btn>
        </v-col>
      </v-row>
    </v-card-actions>
  </v-card>
</template>

<script>
import { convertStringToLocal } from "../../helper/common.js";
export default {
  name: "ShellModeChatbox",
  props: {
    chatBool: {
      type: Boolean,
      default: false,
    },
    chatTxt: {
      type: Array,
      default: () => [],
    },
    skycarSelected: {
      type: Array,
      default: () => [],
    },
    xMovements: {
      type: Number,
    },
    yMovements: {
      type: Number,
    }
  },
  created() {
    this.localChatBool = this.chatBool;
    this.localChatTxt = this.chatTxt;
    this.localSkycarSelected = this.skycarSelected;
  },
  mounted() {
    window.addEventListener("resize", this.handleWindowResize);
    this.updateStyle();
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleWindowResize);
  },
  data: () => ({
    convertStringToLocal,
    chatConversation: "",
    selectedSid: null,
    minScreenWidth: 780,
    localSkycarSelected: Array(),
    chatStyle: null,
    cardStyle: null,
    shellMessageColor: function(data) {
      switch (data.from_tc) {
        case true: {
          return data.status ? "green" : "red";
        }
        case false:
          return "orange";
      }
    },
  }),

  methods: {
    updateStyle() {
      const viewportHeight = Math.max(document.documentElement.clientHeight, window.innerHeight || 0);

      this.cardStyle = {
        position: "fixed",
        right: `${this.xMovements}px`,
        width: window.innerWidth < this.minScreenWidth ? "100%" : "50%",
        bottom: `${this.yMovements}px`,
        height: "40%",
        "z-index": "1"
      }
      this.chatStyle = {
        position: "fixed",
        height: "31%",
        width: window.innerWidth < this.minScreenWidth ? "92%" : "47.5%",
        "overflow-y": "auto",
        bottom: `${(0.08 * viewportHeight) + this.yMovements}px`
      }
    },
    handleWindowResize() {
      this.updateStyle()
    },
    sendConvoMessage() {
      if (this.chatConversation) {
        this.$emit("send-message", this.chatConversation);
        this.chatConversation = "";
      } else {
        this.$awn.tip("Please enter something before sending a message");
      }
    },
    scrollCardToBottom() {
      this.$nextTick(() => {
        const chatDialog = this.$refs.chatDialog;

        if (chatDialog) {
          chatDialog.$el.scrollTop = chatDialog.$el.scrollHeight;
        }
      });
    },
  },

  watch: {
    localChatBool(bool) {
      if (!bool) this.$emit("close-dialog");
    },
    chatBool(value) {
      if (value) {
        this.scrollCardToBottom();
      }
      this.localChatBool = value;
    },
    chatTxt(value) {
      this.localChatTxt = value;
      if (this.localChatBool) {
        this.scrollCardToBottom();
      }
    },
    skycarSelected(value) {
      this.localSkycarSelected = value;
    }
  },

  computed: {
    sendMessageStyle() {
      const style = {
        width: "105%",
        display: "flex",
        height: "160%",
      };
      return style;
    },
    latest50ChatTxt() {
      if (this.localSkycarSelected.length > 1) {
        return this.chatTxt.slice(-250);
      } else { 
        return this.chatTxt.slice(-50);
      }
    }
  }
};
</script>
