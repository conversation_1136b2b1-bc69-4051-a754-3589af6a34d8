<template>
  <v-dialog
    v-model="showDialog"
    max-width="900"
    @keydown.enter="closeDialog"
  >
    <v-card dark>
      <v-toolbar
        dark
        color="black"
      >
        <v-toolbar-title>Warning Details</v-toolbar-title>
      </v-toolbar>
      <v-card-text class="pt-6">
        <!-- Error Code API Details Section -->
        <div v-if="errorCodeData || errorCodeLoading" class="mb-6">
          <v-card outlined dark color="grey darken-4">
            <v-card-title class="subtitle-1 white--text">
              <v-icon class="mr-2" color="white">mdi-alert-circle</v-icon>
              Error Code Details
            </v-card-title>
            <v-card-text>
              <!-- Loading state -->
              <div v-if="errorCodeLoading" class="text-center py-4">
                <v-progress-circular indeterminate color="white" class="mb-2"></v-progress-circular>
                <div>Loading error code details...</div>
              </div>
              
              <!-- Error code data -->
              <div v-else-if="errorCodeData">
                <v-row>
                  <v-col cols="6">
                    <div class="mb-2"><strong>Error Name:</strong> {{ errorCodeData.error_name }}</div>
                    <div class="mb-2"><strong>Module Name:</strong> {{ errorCodeData.module_name }}</div>
                    <div class="mb-2"><strong>Layer:</strong> {{ errorCodeData.layer }}</div>
                  </v-col>
                  <v-col cols="6">
                    <div class="mb-2"><strong>Error Code:</strong> {{ errorCodeData.error_code }}</div>
                    <div class="mb-2"><strong>Cycle Stop:</strong> 
                      <v-chip 
                        :color="errorCodeData.cycle_stop ? 'red' : 'green'"
                        dark 
                        small
                      >
                        {{ errorCodeData.cycle_stop ? 'Yes' : 'No' }}
                      </v-chip>
                    </div>
                  </v-col>
                </v-row>
                <div class="mt-3">
                  <strong>Description:</strong>
                  <p class="mt-2">{{ errorCodeData.description }}</p>
                </div>
              </div>
              
              <!-- No data state -->
              <div v-else class="text-center py-4">
                <v-icon color="grey" class="mb-2">mdi-information-outline</v-icon>
                <div>No error code details available</div>
              </div>
            </v-card-text>
          </v-card>
        </div>

        <!-- Parsed WRN Details Section -->
        <div v-if="parsedWrnDetails" class="mb-6">
          <h3 class="mb-3 white--text">Warning Details</h3>
          <v-row>
            <v-col cols="6">
              <v-card outlined dark color="grey darken-4">
                <v-card-title class="subtitle-1 white--text">Basic Information</v-card-title>
                <v-card-text>
                  <div class="mb-2"><strong>Skycar ID:</strong> {{ parsedWrnDetails.skycar_id }}</div>
                  <div class="mb-2"><strong>Job ID:</strong> {{ parsedWrnDetails.job_id }}</div>
                  <div class="mb-2"><strong>Quantity:</strong> {{ parsedWrnDetails.qty }}</div>
                  <div class="mb-2"><strong>Error Code:</strong> {{ parsedWrnDetails.error_code }}</div>
                  <div class="mb-2"><strong>Axis:</strong> {{ parsedWrnDetails.axis }}</div>
                  <div class="mb-2"><strong>Bin:</strong> {{ parsedWrnDetails.bin || 'N/A' }}</div>
                </v-card-text>
              </v-card>
            </v-col>
            <v-col cols="6">
              <v-card outlined dark color="grey darken-4">
                <v-card-title class="subtitle-1 white--text">Error Details</v-card-title>
                <v-card-text>
                  <div class="mb-2"><strong>App Module:</strong> {{ parsedWrnDetails.app_module }}</div>
                  <div class="mb-2"><strong>App Code:</strong> {{ parsedWrnDetails.app_code }}</div>
                  <div class="mb-2"><strong>Manager Module:</strong> {{ parsedWrnDetails.mgr_module }}</div>
                  <div class="mb-2"><strong>Manager Code:</strong> {{ parsedWrnDetails.mgr_code }}</div>
                  <div class="mb-2"><strong>Component Module:</strong> {{ parsedWrnDetails.component_module }}</div>
                  <div class="mb-2"><strong>Component Code:</strong> {{ parsedWrnDetails.component_code }}</div>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>
          <v-row class="mt-3">
            <v-col cols="12">
              <v-card outlined dark color="grey darken-4">
                <v-card-title class="subtitle-1 white--text">
                  <v-icon class="mr-2" color="white">mdi-map-marker</v-icon>
                  Location & QR Information
                </v-card-title>
                <v-card-text>
                  <v-row>
                    <v-col cols="6">
                      <div class="d-flex align-center mb-2">
                        <v-icon color="white" class="mr-2">mdi-crosshairs-gps</v-icon>
                        <strong>Coordinate (X,Y):</strong> 
                        <span class="ml-2">{{ parsedWrnDetails.coordinate }}</span>
                      </div>
                    </v-col>
                    <v-col cols="6">
                      <div class="d-flex align-center mb-2">
                        <v-icon color="white" class="mr-2">mdi-qrcode</v-icon>
                        <strong>QR Code:</strong> 
                        <span class="ml-2">{{ parsedWrnDetails.qr }}</span>
                      </div>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>
          <v-row class="mt-3">
            <v-col cols="12">
              <v-card outlined dark color="grey darken-4">
                <v-card-title class="subtitle-1 white--text">
                  <v-icon class="mr-2" color="white">mdi-cog</v-icon>
                  Other Details
                </v-card-title>
                <v-card-text>
                  <v-row>
                    <v-col cols="6">
                      <div class="mb-2">
                        <strong>Motor IDs:</strong>
                        <div v-if="motorIds.length > 0" class="mt-1">
                          <v-chip
                            v-for="(motorId, index) in motorIds"
                            :key="index"
                            class="ma-1"
                            color="orange"
                            dark
                            small
                          >
                            <v-icon class="mr-1" small>mdi-engine</v-icon>
                            {{ motorId }}
                          </v-chip>
                        </div>
                        <span v-else class="grey--text">N/A</span>
                      </div>
                    </v-col>
                    <v-col cols="6"><strong>BSP:</strong> {{ parsedWrnDetails.bsp }}</v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>
        </div>

        <div class="mb-4">
          <h3 class="mb-2 white--text">Raw Messages</h3>
          <v-simple-table class="mt-4" dark>
            <template v-slot:default>
              <thead>
                <tr>
                  <th class="text-left">Time</th> 
                  <th class="text-left">Message</th>  
                  <th class="text-left">Sent by TC</th> 
                </tr>
              </thead>
              <tbody>
                <tr 
                  v-for="(msg, index) in errorDetail.child_messages" 
                  :key="index"
                  :class="msg.message.startsWith('WRN') ? 'warning--text' : ''"
                >
                  <td>{{ convertTimestampToLocal(msg.created_at, true) }}</td>
                  <td>{{ msg.message }}</td>
                  <td>
                    <v-icon :color="msg.sent_by_tc ? 'green' : 'blue'">
                      {{ msg.sent_by_tc ? 'mdi-arrow-up' : 'mdi-arrow-down' }}
                    </v-icon>
                  </td>
                </tr>
              </tbody>
            </template>
          </v-simple-table>
        </div>
      </v-card-text>
      <v-card-actions>
        <v-spacer />
        <v-btn
          color="green darken-1"
          text
          @click="closeDialog"
        >
          Close
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
import { convertTimestampToLocal, getRequestHeader, getHost } from "../../helper/common"
import { RouteError } from "../../helper/enums"
import axios from "axios"

export default {
  name: "DialogSkycarWarningDetail",
  props: {
    currentZone: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      convertTimestampToLocal,
      showDialog: false,
      errorDetail: null,
      errorCodeData: null,
      errorCodeLoading: false,
    }
  },
  computed: {
    parsedWrnDetails() {
      if (!this.errorDetail) {
        return null;
      }
      
      // Get parsed details from the WRN message for display in error detail dialog
      const wrnMessage = this.errorDetail.child_messages.find(msg => msg.message.startsWith('WRN'))
      if (wrnMessage) {
        return this.parseWarningMessage(wrnMessage.message)
      }
      return null
    },
    motorIds() {
      // Get motor IDs for the current warning detail, using same logic as Skycar.vue
      if (!this.parsedWrnDetails || !this.parsedWrnDetails.motor || this.parsedWrnDetails.motor === 'N/A') {
        return [];
      }
      
      // Split motor string by character (3 -> ["3"], 13 -> ["1","3"], 135 -> ["1","3","5"])
      return this.parsedWrnDetails.motor.toString().split('');
    },
  },
  methods: {
    async openDialog(item) {
      this.showDialog = true
      this.errorDetail = item
      this.errorCodeData = null
      this.errorCodeLoading = false

      // Get parsed WRN details to extract app_module and app_code
      const parsedDetails = this.parsedWrnDetails
      if (parsedDetails && parsedDetails.app_module && parsedDetails.app_code) {
        this.errorCodeLoading = true
        try {
          // Call TC API to get error code details
          let url = getHost(this.currentZone) + RouteError.ERROR_CODE
          let params = new URLSearchParams({
            "module": parsedDetails.app_module,
            "error_code": parsedDetails.app_code
          })
          
          const res = await axios.get(`${url}?${params}`, {
            headers: getRequestHeader()
          })
          
          if (res.data && res.data.data && res.data.data.length > 0) {
            this.errorCodeData = res.data.data[0]
          }
        } catch (error) {
          console.error("Failed to fetch error code details:", error)
          // Still show dialog even if API call fails
        } finally {
          this.errorCodeLoading = false
        }
      }
    },
    closeDialog() {
      this.showDialog = false
      this.errorDetail = null
      this.errorCodeData = null
      this.errorCodeLoading = false
    },
    parseWarningMessage(message) {
      // Parse WRN message format based on the provided structure:
      // WRN,SC,1,E,S1-197826d4fef0,10,ERR3,xy,34,5,0,,CB,BATTERY_M,15,E_BATT_SEQ_LOW_BATTERY,3,NONE,0,,0,934
      const parts = message.split(',')
      
      if (parts.length < 22) {
        return {
          skycar_id: 'Invalid',
          status: 'Invalid',
          job_id: 'Invalid',
          error_code: 'Invalid',
          coordinate: 'Invalid',
          app_module: 'Invalid',
          app_code: 'Invalid',
          mgr_module: 'Invalid',
          mgr_code: 'Invalid',
          component_module: 'Invalid',
          component_code: 'Invalid',
          motor: 'Invalid',
          bsp: 'Invalid',
          qr: 'Invalid'
        }
      }
      
      return {
        skycar_id: parts[2] || 'N/A',           // Position 2: Dev ID (Skycar ID)
        status: parts[3] || 'N/A',              // Position 3: Cmd (Status)
        job_id: parts[4] || 'N/A',              // Position 4: JOBID
        qty: parts[5] || 'N/A',                 // Position 5: QTY
        error_code: parts[6] || 'N/A',          // Position 6: Action (Error code)
        axis: parts[7] || 'N/A',                // Position 7: Axis
        coordinate: parts[8] && parts[9] ? `${parts[8]},${parts[9]}` : 'N/A',  // Position 8,9: X,Y coordinates
        z_coordinate: parts[10] || 'N/A',       // Position 10: Z
        bin: parts[11] || 'N/A',                // Position 11: BIN
        dest_type: parts[12] || 'N/A',          // Position 12: DEST_TYPE
        app_module: parts[13] || 'N/A',         // Position 13: APPMODULE
        app_code: parts[14] || 'N/A',           // Position 14: APPCODE
        mgr_module: parts[15] || 'N/A',         // Position 15: MGRMODULE
        mgr_code: parts[16] || 'N/A',           // Position 16: MGRCODE
        component_module: parts[17] || 'N/A',   // Position 17: COMPONENTMODULE
        component_code: parts[18] || 'N/A',     // Position 18: COMPONENTCODE
        motor: parts[19] || 'N/A',              // Position 19: MOTOR
        bsp: parts[20] || 'N/A',                // Position 20: BSP
        qr: parts[21] || 'N/A'                  // Position 21: QR
      }
    },
  }
}
</script> 