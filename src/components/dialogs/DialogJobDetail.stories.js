import DialogJobDetail from './DialogJobDetail.vue';

export default {
  title: 'Job/DialogJob',
  component: DialogJobDetail,
  parameters: {
    docs: {
      description: {
        component: 'A comprehensive job details dialog with SM Order integration'
      }
    }
  },
  argTypes: {
    currentZone: {
      control: { type: 'select' },
      options: ['CUBE1', 'CUBE2', 'CUBE3'],
      description: 'Current zone for API calls'
    },
    showNotification: {
      action: 'notification',
      description: 'Notification callback function'
    },
    storybookMode: {
      control: { type: 'boolean' },
      description: 'Enable Storybook mode to bypass API calls'
    }
  }
};

// Story with sample data
export const TcJobDetail = (args, { argTypes }) => ({
  props: Object.keys(argTypes),
  components: { DialogJobDetail },
  template: `
    <div>
     
      <DialogJobDetail 
        :current-zone="currentZone"
        :show-notification="showNotification"
        :storybook-mode="storybookMode"
        :mock-job-data="mockJobData"
        ref="dialogJobDetail"
      />
    </div>
  `,
  mounted() {
    this.$refs.dialogJobDetail.openDialog(11);
},

});


TcJobDetail.args = {
  currentZone: 'C',
  storybookMode: true,
  showNotification: (success, message) => console.log('Notification:', { success, message }),
  mockJobData: {
    "skycar_id": 1,
    "zone": "C",
    "job_id": 11,
    "type": "RETRIEVING",
    "station_desc": "To station 1",
    "from_station_code": 0,
    "to_station_code": 1,
    "from_coordinate": "10,6,2",
    "to_coordinate": "16,2,4",
    "from_node": 1723,
    "to_node": 627,
    "storage_no": "SBG-001723",
    "with_storage": true,
    "status": "PROCESSING",
    "error_name": "",
    "error_msg": "",
    "begin_at": "2025-06-26T03:39:20.477128+00:00",
    "created_at": "2025-06-26T03:39:20.283016+00:00",
    "updated_at": "2025-06-26T08:29:27.869137+00:00",
    "completed_at": null,
    "completed_by_skycar": null,
    "prefer_skycar_id": 1,
    "sm_order_id": 247972,
    "storage_code": 1723,
    "eta": 0,
    "eta_actions_data": null,
    "failed_rule": "Skycar is impeded.",
    "is_halt": false,
    "is_deleted": false,
    "position": "BOTH",
    "tc_rank": null,
    "for_station": [],
    "start_job_time": "2025-06-26T03:39:20.272785+00:00",
    "halt_reason": ["Halt due to cancellation" , "Waiting SM Compensate"]
  }
};

// Story with sample data
export const TcJobDetailWithForStation = (args, { argTypes }) => ({
    props: Object.keys(argTypes),
    components: { DialogJobDetail },
    template: `
      <div>
     
        <DialogJobDetail 
          :current-zone="currentZone"
          :show-notification="showNotification"
          :storybook-mode="storybookMode"
          :mock-job-data="mockJobData"
          ref="dialogJobDetail"
        />
      </div>
    `,
    mounted() {
        this.$refs.dialogJobDetail.openDialog(11);
    },
   
  });

TcJobDetailWithForStation.args = {
    currentZone: 'C',
    storybookMode: true,
    showNotification: (success, message) => console.log('Notification:', { success, message }),
    mockJobData: {
      "skycar_id": 2,
      "zone": "C",
      "job_id": 12,
      "type": "INTERNAL",
      "station_desc": "To Cube ",
      "from_station_code": 0,
      "to_station_code": 0,
      "from_coordinate": "10,6,2",
      "to_coordinate": "16,2,4",
      "from_node": 1723,
      "to_node": 627,
      "storage_no": "SBG-001723",
      "with_storage": true,
      "status": "COMPLETED",
      "error_name": "Skycar WEX got error bla bla bla",
      "error_msg": "",
      "begin_at": "2025-06-26T03:39:20.477128+00:00",
      "created_at": "2025-06-26T03:39:20.283016+00:00",
      "updated_at": "2025-06-26T08:29:27.869137+00:00",
      "completed_at": "2025-06-27T08:29:27.869137+00:00",
      "completed_by_skycar": 3,
      "prefer_skycar_id": 1,
      "sm_order_id": 247972,
      "storage_code": 1723,
      "eta": 0,
      "eta_actions_data": null,
      "failed_rule": "",
      "is_halt": true,
      "is_deleted": false,
      "position": "BOTH",
      "tc_rank": 1,
      "for_station": [3],
      "start_job_time": "2025-06-26T03:39:20.272785+00:00",
      "halt_reason": "Halt due to cancellation"
    }
  };
  

export const SmOrderDetail = (args, { argTypes }) => ({
  props: Object.keys(argTypes),
  components: { DialogJobDetail },
  template: `
    <div>
      <DialogJobDetail 
        :current-zone="currentZone"
        :show-notification="showNotification"
        :storybook-mode="storybookMode"
        :mock-job-data="mockJobData"
        :mock-sm-order-data="mockSmOrderData"
        ref="dialogJobDetail"
      />
    </div>
  `,
  mounted() {
    this.$refs.dialogJobDetail.openSmOrderDialog(247972);
},

});

SmOrderDetail.args = {
  currentZone: 'C',
  storybookMode: true,
  showNotification: (success, message) => console.log('Notification:', { success, message }),
  mockJobData: {}, // not used in this story
  mockSmOrderData: {
    "createdAt": "2025-06-12T10:36:22.505Z",
    "updatedAt": "2025-06-12T10:36:22.505Z",
    "deletedAt": null,
    "id": 247972,
    "type": "INTERNAL",
    "priorityTier": 4,
    "binStationRank": 0,
    "status": "AVAILABLE",
    "orderNo": "01JXHVCMJZ157NP9XNS69NQ0TA",
    "chainNo": null,
    "completedAt": null,
    "targetedZoneGroup": null,
    "targetedStack": null,
    "isTransfer": false,
    "requestedAt": null,
    "requestedBy": "storage-optimizer-3-flatten-1",
    "dispatchedAt": null,
    "startPickingAt": null,
    "pickedAt": null,
    "startJobAt": null,
    "lastErroredAt": null,
    "creationReason": "OPTIMIZATION",
    "errorReason": null,
    "recoveryType": null,
    "skycar": null,
    "preferredSkycar": null,
    "storage": {
      "createdAt": "2025-01-24T18:02:40.395Z",
      "updatedAt": "2025-06-11T03:16:11.849Z",
      "deletedAt": null,
      "code": 2278,
      "status": "AVAILABLE",
      "lastMovement": "IN_CUBE",
      "lastMovedAt": "2025-06-11T03:16:11.844Z",
      "station": null,
      "skycar": null,
      "disenrolledAt": null,
      "node": {
        "createdAt": "2024-10-25T03:52:19.398Z",
        "updatedAt": "2024-10-25T03:52:19.398Z",
        "deletedAt": null,
        "id": 2071,
        "type": "STORAGE",
        "x": 22,
        "y": 21,
        "z": 2,
        "station": null,
        "storageCap": 1,
        "hardwareIndex": null,
        "zoneGroup": "C"
      },
      "tags": []
    },
    "pickupNode": null,
    "dropoffNode": null,
    "errorParent": null
  }
}; 