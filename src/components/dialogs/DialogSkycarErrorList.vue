<template>
    <div>
      <v-data-table
        :headers="errorHeaders"
        :items="errorData"
        :items-per-page="15"
        group-by="zone"
        class="elevation-1"
        dark
        item-class="yellow--text text--accent-4"
      >
        <!-- Local Time -->
        <template v-slot:[`item.time`]="{ item }">
          {{ item.dt_local }}
        </template>
  
        <template v-slot:[`item.recovered_time`]="{ item }">
          {{ item.rt_local }}
        </template>
  
        <!-- Error Message -->
        <template v-slot:[`item.error_msg`]="{ item }">
          <v-tooltip bottom>
            <template v-slot:activator="{ on, attrs }">
              <span
                v-bind="attrs"
                v-on="on"
              >{{ item.error_msg }}</span>
            </template>
            <v-card
              dark
              min-width="500"
            >
              <v-card-title>Error Message</v-card-title>
              <v-simple-table 
                fixed-header
              >
                <template v-slot:default>
                  <thead>
                    <tr>
                      <th class="text-left">
                        Title
                      </th>
                      <th class="text-left">
                        Data
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      v-for="detail in item.msg_detail"
                      :key="detail.title"
                    >
                      <td>{{ detail.title }}</td>
                      <td>{{ detail.data }}</td>
                    </tr>
                  </tbody>
                </template>
              </v-simple-table>
            </v-card>
          </v-tooltip>
        </template>
  
        <!-- Error Name -->
        <template v-slot:[`item.error_name`]="{ item }">
          <div class="d-flex align-center">
            <v-tooltip bottom>
              <template v-slot:activator="{ on, attrs }">
                <v-icon
                  v-bind="attrs"
                  v-on="on"
                  @click="$emit('show-error-detail', item)"
                  class="mr-2"
                >
                  mdi-information
                </v-icon>
              </template>
              <span>Show Error Detail</span>
            </v-tooltip>
            <span class="mr-2">{{ item.id }}</span>
            <v-chip 
              v-if="getPrimaryErrorCode(item.error_detail)"
              :color="getErrorChipColor(getPrimaryErrorCode(item.error_detail))"
              dark 
              small
            >
              {{ getPrimaryErrorCode(item.error_detail) }}
            </v-chip>
          </div>
        </template>
      
        <!-- Action Button -->
        <template v-slot:[`item.error_action_remark`]="{ item }">
          <v-btn
            small
            class="mr-2"
            light
            @click="$emit('error-action', item, 'remark')"
          >
            Action
          </v-btn>
        </template>
  
        <!-- Downtime -->
        <template v-slot:[`item.downtime`]="{ item }">
          <span v-if="item.recovered_time" :class="getDowntimeTextColor(item.time, item.recovered_time)">
            {{ getDowntime(item.time, item.recovered_time) }}
          </span>
          <span v-else class="grey--text">
            Ongoing
          </span>
        </template>
  
        <!-- Warning Log Button -->
        <template v-slot:[`item.warning_log`]="{ item }">
          <v-btn
            small
            class="mr-2"
            light
            @click="$emit('error-action', item, 'warning')"
          >
            Warning Log
          </v-btn>
        </template>
  
        <!-- Optional Column Templates -->
        <!-- Maintenance Dock ID -->
        <template v-slot:[`item.md_id`]="{ item }">
          {{ item.md_id || 'N/A' }}
        </template>
  
        <!-- Recovered By -->
        <template v-slot:[`item.recovered_by`]="{ item }">
          {{ item.recovered_by || 'N/A' }}
        </template>
  
        <!-- Recovery Mode -->
        <template v-slot:[`item.recover_mode`]="{ item }">
          <v-chip
            v-if="item.recover_mode"
            :color="getRecoveryModeColor(item.recover_mode)"
            dark
            small
          >
            {{ item.recover_mode }}
          </v-chip>
          <span v-else class="grey--text">N/A</span>
        </template>
      </v-data-table>
    </div>
  </template>
  
  <script>
  export default {
    name: "DialogSkycarErrorList",
    props: {
      errorHeaders: {
        type: Array,
        required: true
      },
      errorData: {
        type: Array,
        required: true
      }
    },
    methods: {
      getPrimaryErrorCode(errorDetails) {
        // Get the most relevant error code to display in the list
        if (!errorDetails || !Array.isArray(errorDetails)) {
          return null;
        }
        
        // Priority 1: Manual triggers (special case)
        const manualTrigger = errorDetails.find(detail => 
          detail.error === "MANUAL_TRIGGER" && detail.layer === "MANUAL"
        );
        if (manualTrigger) {
          return "M";
        }
        
        // Priority 2: Actual numeric errors (non-zero)
        const numericError = errorDetails.find(detail => 
          detail.error && detail.error !== "0" && detail.layer && 
          detail.error !== "MANUAL_TRIGGER" && !isNaN(detail.error)
        );
        if (numericError) {
          return numericError.error;
        }
        
        // Priority 3: Any non-zero string errors
        const stringError = errorDetails.find(detail => 
          detail.error && detail.error !== "0" && detail.layer && 
          detail.error !== "MANUAL_TRIGGER"
        );
        if (stringError) {
          return stringError.error;
        }
        
        // Priority 4: Return first available error code (including "0")
        const firstError = errorDetails.find(detail => detail.error && detail.layer);
        return firstError ? firstError.error : null;
      },
      getErrorChipColor(errorCode) {
        // Determine chip color based on error code
        if (!errorCode) {
          return 'grey';
        }
        
        if (errorCode === 'M') {
          return 'orange'; // Orange for manual triggers
        }
        
        if (errorCode === '0') {
          return 'green'; // Green for no error
        }
        
        return 'red'; // Red for actual errors
      },
      getRecoveryModeColor(mode) {
        // Color code recovery modes
        switch (mode) {
          case 'REVIVE':
            return 'green';
          case 'MANUAL':
            return 'orange';
          case 'AUTO':
            return 'blue';
          default:
            return 'grey';
        }
      },
      getDowntime(startTime, endTime) {
        // Calculate downtime between start and end time
        if (!startTime || !endTime) {
          return 'N/A';
        }
        
        const start = new Date(startTime);
        const end = new Date(endTime);
        const diffMs = end - start;
        
        if (diffMs < 0) {
          return 'Invalid';
        }
        
        const diffMinutes = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMinutes / 60);
        const diffDays = Math.floor(diffHours / 24);
        
        if (diffDays > 0) {
          return `${diffDays}d ${diffHours % 24}h ${diffMinutes % 60}m`;
        } else if (diffHours > 0) {
          return `${diffHours}h ${diffMinutes % 60}m`;
        } else if (diffMinutes > 0) {
          return `${diffMinutes}m`;
        } else {
          const diffSeconds = Math.floor(diffMs / 1000);
          return `${diffSeconds}s`;
        }
      },
      getDowntimeTextColor(startTime, endTime) {
        // Color code downtime based on duration
        if (!startTime || !endTime) {
          return 'grey--text';
        }
        
        const start = new Date(startTime);
        const end = new Date(endTime);
        const diffMs = end - start;
        const diffMinutes = Math.floor(diffMs / 60000);
        
        if (diffMinutes < 5) {
          return 'green--text'; // Quick recovery (< 5 minutes)
        } else if (diffMinutes < 15) {
          return 'blue--text'; // Normal recovery (5-15 minutes)
        } else if (diffMinutes < 60) {
          return 'orange--text'; // Long recovery (15-60 minutes)
        } else {
          return 'red--text'; // Very long recovery (> 1 hour)
        }
      }
    }
  }
  </script>