<template>
  <v-container>
    <v-row>
      <v-col>
        <v-card>
          <v-card-title>
            Find Storages Factors
          </v-card-title>
          <v-card-subtitle>
            Weightage of each factors for calculating ranking of bins to select
            by tags
          </v-card-subtitle>
          <v-divider></v-divider>

          <v-card-text>
            <v-row>
              <v-col>
                <v-text-field
                  type="number"
                  class="centered-input"
                  label="Layer"
                  v-model.number="factors['Layer']"
                  step=".05"
                  outlined
                  :min="0"
                  :prepend-inner-icon="null"
                >
                  <template v-slot:prepend-inner>
                    <v-tooltip right>
                      <template v-slot:activator="{ on, attrs }">
                        <v-icon v-on="on" v-bind="attrs" dense>
                          mdi-information-outline
                        </v-icon>
                      </template>
                      <span>Number of digging required for the bin</span>
                    </v-tooltip>
                  </template>
                </v-text-field>
              </v-col>
              <v-col>
                <v-text-field
                  type="number"
                  class="centered-input"
                  label="Ongoing Orders Of Stack"
                  v-model.number="factors['OnGoingOrdersOfStack']"
                  step=".05"
                  outlined
                  :min="0"
                  :prepend-inner-icon="null"
                >
                  <template v-slot:prepend-inner>
                    <v-tooltip right>
                      <template v-slot:activator="{ on, attrs }">
                        <v-icon v-on="on" v-bind="attrs" dense>
                          mdi-information-outline
                        </v-icon>
                      </template>
                      <span
                        >Number of orders involving (pick from and drop to) the
                        stack of the bin</span
                      >
                    </v-tooltip>
                  </template>
                </v-text-field>
              </v-col>
            </v-row>
            <v-row>
              <v-col>
                <v-text-field
                  type="number"
                  class="centered-input"
                  label="Distance To Gateway"
                  v-model.number="factors['DistanceToGateway']"
                  step=".05"
                  outlined
                  :min="0"
                  :prepend-inner-icon="null"
                >
                  <template v-slot:prepend-inner>
                    <v-tooltip right>
                      <template v-slot:activator="{ on, attrs }">
                        <v-icon v-on="on" v-bind="attrs" dense>
                          mdi-information-outline
                        </v-icon>
                      </template>
                      <span>Distance of bin to station</span>
                    </v-tooltip>
                  </template>
                </v-text-field>
              </v-col>
              <v-col>
                <v-text-field
                  type="number"
                  class="centered-input"
                  label="Z-Depth"
                  v-model.number="factors['ZDepth']"
                  step=".05"
                  outlined
                  :min="0"
                  :prepend-inner-icon="null"
                >
                  <template v-slot:prepend-inner>
                    <v-tooltip right>
                      <template v-slot:activator="{ on, attrs }">
                        <v-icon v-on="on" v-bind="attrs" dense>
                          mdi-information-outline
                        </v-icon>
                      </template>
                      <span
                        >Z-Coordinate of the bin (Depth of bin from the
                        top)</span
                      >
                    </v-tooltip>
                  </template>
                </v-text-field>
              </v-col>
            </v-row>

            <v-input
              prepend-icon="mdi-sigma"
              messages="Recommended: Sum weightage of all factors = 1"
              :error-messages="
                totalWeightage > 1
                  ? 'Warning! Total weightage greater than 1. You may still proceed.'
                  : totalWeightage < 0
                  ? 'Warning! Total weightage greater than 0. You may still proceed.'
                  : ''
              "
            >
              Total : {{ totalWeightage }}
            </v-input>
          </v-card-text>

          <v-divider></v-divider>

          <v-card-actions class="justify-center">
            <v-btn
              block
              text
              color="primary"
              :loading="isBtnLoading.saveBtn"
              @click="saveWeightage()"
              >Save</v-btn
            >
          </v-card-actions>
          <v-expand-transition>
            <v-card-text v-show="stringifiedResponse">
              Response:
              <CodeBlock>{{ stringifiedResponse }}</CodeBlock>
            </v-card-text>
          </v-expand-transition>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import CodeBlock from "@/dashboard/model/CodeBlock.vue";
import { SettingAPI } from "../../api/settings";
import { SmSettingsKey } from "../../helper/enums";

export default {
  name: "FindStoragesFactorsComponent",
  components: {
    CodeBlock,
  },
  data() {
    return {
      isLoading: false,
      isBtnLoading: {
        saveBtn: false,
      },
      stringifiedResponse: "",
      factors: {
        Layer: null,
        OnGoingOrdersOfStack: null,
        DistanceToGateway: null,
        ZDepth: null,
      },
    };
  },
  methods: {
    async init() {
      const settings = await SettingAPI.getByKey(
        SmSettingsKey.FindStoragesFactors
      );
      for (const dbFactor of settings) {
        if (dbFactor.factorName in this.factors) {
          this.factors[dbFactor.factorName] = dbFactor.weightage;
        }
      }
      this.isLoading = false;
    },

    async saveWeightage() {
      this.isBtnLoading.saveBtn = true;

      try {
        const factors = Object.entries(this.factors).map((x) => {
          return {
            factorName: x[0],
            weightage: x[1],
          };
        });
        const response = await SettingAPI.updateFindStoragesFactors(factors);

        this.stringifiedResponse = JSON.stringify(response, undefined, 2);
      } catch (axiosError) {
        this.stringifiedResponse = JSON.stringify(
          { errObj: axiosError, response: axiosError.response },
          undefined,
          2
        );
      }

      this.isBtnLoading.saveBtn = false;
    },
  },
  computed: {
    totalWeightage() {
      // Round to avoid floating point precision error during display
      return (
        Math.round(
          Object.values(this.factors).reduce(
            (sum, weightage) => sum + weightage,
            0
          ) * 100
        ) / 100
      );
    },
  },

  beforeMount() {
    this.init();
  },
};
</script>

<style scoped>
.centered-input >>> input {
  text-align: center;
}
</style>
