<template>
  <v-container>
    <v-row>
      <v-col>
        <v-card>
          <v-card-title>
            Order Prioritisation
          </v-card-title>
          <v-card-subtitle>
            Adjust certain factors for ranking orders
          </v-card-subtitle>
          <v-divider></v-divider>

          <v-card-text>
            <v-row>
              <v-col>
                <v-radio-group
                  v-model="factors.diggingStrategy"
                  label="Priority for retrieving order to the same station"
                >
                  <v-radio
                    v-for="item in diggingStrategies"
                    :key="item.id"
                    :value="item.value"
                    :label="item.label"
                  ></v-radio>
                </v-radio-group>
              </v-col>
            </v-row>
            <!-- <v-row>
              <v-col>
                <v-switch v-model="factors.shouldPrioritiseDiggingStarted">
                  <template v-slot:label>
                    <span>
                      Started digging
                      <v-tooltip bottom>
                        <template v-slot:activator="{ on, attrs }">
                          <v-icon v-bind="attrs" v-on="on" color="primary">
                            mdi-information
                          </v-icon>
                        </template>
                        <span
                          >Between retrieving order to the same station,
                          prioritise retrieving order that has previously
                          started doing</span
                        >
                      </v-tooltip>
                    </span>
                  </template></v-switch
                >
              </v-col>
              <v-col>
                <v-switch
                  v-model="factors.isStationLastRetrievedPriorEnabled"
                  label="Station Last Retrieved"
                >
                  <template v-slot:label>
                    <span>
                      Station Last Retrieved
                      <v-tooltip bottom>
                        <template v-slot:activator="{ on, attrs }">
                          <v-icon v-bind="attrs" v-on="on" color="primary">
                            mdi-information
                          </v-icon>
                        </template>
                        <span
                          >Between stations that have the same priority,
                          prioritise station that earliest last retrieve
                          time</span
                        >
                      </v-tooltip>
                    </span>
                  </template>
                </v-switch>
              </v-col>
            </v-row> -->
            <v-row>
              <v-col>
                <v-switch
                  v-model="factors.allowPrioritiseTransfer"
                  :label="null"
                >
                  <template v-slot:label>
                    <span>
                      Prioritise Transfer
                      <v-tooltip bottom>
                        <template v-slot:activator="{ on, attrs }">
                          <v-icon v-bind="attrs" v-on="on" color="primary">
                            mdi-information
                          </v-icon>
                        </template>
                        <span
                          >Prioritise transfer tier orders (priority tier 4)
                          when: <br />1. All stations reach max concurrent
                          job<br />2.
                          <i
                            >(1 - Minimum Transfer Percentage) * Order
                            Dispatcher Threshold</i
                          >
                          number of orders is already dispatched</span
                        >
                      </v-tooltip>
                    </span>
                  </template>
                </v-switch>
              </v-col>
              <v-col>
                <v-text-field
                  type="number"
                  class="centered-input"
                  label="Min Transfer Percentage"
                  v-model.number="factors.minTransferPerc"
                  step=".05"
                  outlined
                  :min="0"
                  :max="1"
                  :prepend-inner-icon="null"
                >
                  <template v-slot:prepend-inner>
                    <v-tooltip right>
                      <template v-slot:activator="{ on, attrs }">
                        <v-icon v-on="on" v-bind="attrs" dense>
                          mdi-information-outline
                        </v-icon>
                      </template>
                      <span
                        >Percentage of orders dispatched that must fall under
                        transfer tier order</span
                      >
                    </v-tooltip>
                  </template>
                </v-text-field>
              </v-col>
            </v-row>
          </v-card-text>

          <v-divider></v-divider>

          <v-card-actions class="justify-center">
            <v-btn
              block
              text
              color="primary"
              :loading="isBtnLoading.saveBtn"
              @click="save()"
              >Save</v-btn
            >
          </v-card-actions>
          <v-expand-transition>
            <v-card-text v-show="stringifiedResponse">
              Response:
              <CodeBlock>{{ stringifiedResponse }}</CodeBlock>
            </v-card-text>
          </v-expand-transition>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import CodeBlock from "@/dashboard/model/CodeBlock.vue";
import { SettingAPI } from "../../api/settings";
import { SmSettingsKey } from "../../helper/enums";

export default {
  name: "OrderPrioritisationComponent",
  components: {
    CodeBlock,
  },
  data() {
    return {
      isLoading: false,
      isBtnLoading: {
        saveBtn: false,
      },
      stringifiedResponse: "",
      factors: {
        diggingStrategy: null,
        shouldPrioritiseDiggingStarted: null,
        isStationLastRetrievedPriorEnabled: null,
        allowPrioritiseTransfer: null,
        minTransferPerc: null,
      },
      diggingStrategies: [
        { id: 1, value: "LEAST-FIRST", label: "Least number of digging" },
        { id: 2, value: "MOST-FIRST", label: "Most number of digging" },
        { id: 3, value: "CREATED-TIME", label: "Created time" },
      ],
    };
  },
  methods: {
    async init() {
      const settings = await SettingAPI.getByKey(
        SmSettingsKey.OrderPrioritisation
      );
      for (const [factorName, weightage] of Object.entries(settings)) {
        if (factorName in this.factors) {
          this.factors[factorName] = weightage;
        }
      }
      if (this.factors.diggingStrategy === null) {
        this.factors.diggingStrategy = "CREATED-TIME";
      }
      this.isLoading = false;
    },

    async save() {
      this.isBtnLoading.saveBtn = true;

      try {
        const factors = { ...this.factors };
        if (factors.diggingStrategy === "CREATED-TIME") {
          factors.diggingStrategy = null;
        }
        const response = await SettingAPI.updateOrderPrioritisation(factors);

        this.stringifiedResponse = JSON.stringify(response, undefined, 2);
      } catch (axiosError) {
        this.stringifiedResponse = JSON.stringify(
          { errObj: axiosError, response: axiosError.response },
          undefined,
          2
        );
      }

      this.isBtnLoading.saveBtn = false;
    },
  },

  beforeMount() {
    this.init();
  },
};
</script>

<style scoped>
.centered-input >>> input {
  text-align: center;
}
</style>
