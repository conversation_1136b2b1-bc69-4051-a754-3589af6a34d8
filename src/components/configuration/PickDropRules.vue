<template>
  <v-container>
    <v-row>
      <v-col>
        <v-card>
          <v-card-title>
            Pick Drop Rules
          </v-card-title>
          <v-divider></v-divider>

          <v-card-subtitle>For Stack</v-card-subtitle>
          <v-card-text>
            <v-row>
              <v-col>
                <v-switch
                  hide-details
                  class="mt-0"
                  v-model="setting.stack.allowMultiPick"
                  :disabled="setting.stack.allowMultiPick === null"
                >
                  <template slot="label">
                    <span>Multi Pick from Same Stack</span>
                    <v-tooltip right open-delay="300">
                      <template v-slot:activator="{ on, attrs }">
                        <div class="ml-4" v-on="on" v-bind="attrs">
                          <v-icon dense>mdi-information-outline</v-icon>
                        </div>
                      </template>
                      <span
                        >Can send more than 1 (Internal/Retrieving) order (to
                        pick) from same stack</span
                      >
                    </v-tooltip>
                  </template>
                </v-switch>
              </v-col>
              <v-col>
                <v-text-field
                  v-model.number="setting.stack.multiPickThreshold"
                  type="number"
                  :min="0"
                  label="Multi Pick Threshold"
                  :append-icon="null"
                >
                  <template v-slot:append>
                    <v-tooltip right>
                      <template v-slot:activator="{ on, attrs }">
                        <v-icon v-on="on" v-bind="attrs" dense>
                          mdi-information-outline
                        </v-icon>
                      </template>
                      <span
                        >Limit number of sent order from same stack (0 for no
                        limit)</span
                      >
                    </v-tooltip>
                  </template>
                </v-text-field>
              </v-col>
            </v-row>

            <v-switch
              hide-details
              class="mt-0"
              v-model="setting.stack.allowMultiDrop"
              :disabled="setting.stack.allowMultiDrop === null"
            >
              <template slot="label">
                <span>Multi Drop to Same Stack</span>
                <v-tooltip right open-delay="300">
                  <template v-slot:activator="{ on, attrs }">
                    <div class="ml-4" v-on="on" v-bind="attrs">
                      <v-icon dense>mdi-information-outline</v-icon>
                    </div>
                  </template>
                  <span
                    >Can send more than 1 (Internal/Putaway) order (to drop) to
                    same stack</span
                  >
                </v-tooltip>
              </template>
            </v-switch>

            <v-switch
              hide-details
              class="mt-0"
              v-model="setting.stack.allowMultiDropFromSameGateway"
              :disabled="setting.stack.allowMultiDropFromSameGateway === null"
            >
              <template slot="label">
                <span>Multi Drop from Same Gateway</span>
                <v-tooltip bottom open-delay="300">
                  <template v-slot:activator="{ on, attrs }">
                    <div class="ml-4" v-on="on" v-bind="attrs">
                      <v-icon dense>mdi-information-outline</v-icon>
                    </div>
                  </template>
                  <span
                    >Can resolve drop destinations to same stack for
                    Putaway-order from same Gateway</span
                  >
                </v-tooltip>
              </template>
            </v-switch>

            <v-switch
              hide-details
              class="mt-0"
              v-model="setting.stack.allowMultiDropFromMultiGateway"
              :disabled="setting.stack.allowMultiDropFromMultiGateway === null"
            >
              <template slot="label">
                <span>Multi Drop from Multi Gateway</span>
                <v-tooltip bottom open-delay="300">
                  <template v-slot:activator="{ on, attrs }">
                    <div class="ml-4" v-on="on" v-bind="attrs">
                      <v-icon dense>mdi-information-outline</v-icon>
                    </div>
                  </template>
                  <span
                    >Can resolve drop destinations to same stack for
                    Putaway-order from different Gateway</span
                  >
                </v-tooltip>
              </template>
            </v-switch>
          </v-card-text>

          <v-card-subtitle>For Gateway</v-card-subtitle>
          <v-card-text>
            <v-switch
              hide-details
              class="mt-0"
              v-model="setting.gateway.allowMultiPick"
              :disabled="setting.gateway.allowMultiPick === null"
            >
              <template slot="label">
                <span>Multi Pick from Gateway</span>
                <v-tooltip bottom open-delay="300">
                  <template v-slot:activator="{ on, attrs }">
                    <div class="ml-4" v-on="on" v-bind="attrs">
                      <v-icon dense>mdi-information-outline</v-icon>
                    </div>
                  </template>
                  <span
                    >Can send another Putaway order before the bin of it's
                    previous order is picked</span
                  >
                </v-tooltip>
              </template>
            </v-switch>

            <v-switch
              hide-details
              class="mt-0"
              v-model="setting.gateway.enableDropThenPickAtGateway"
              :disabled="setting.gateway.enableDropThenPickAtGateway === null"
            >
              <template slot="label">
                <span>Enable Drop Then Pick At Gateway</span>
                <v-tooltip bottom open-delay="300">
                  <template v-slot:activator="{ on, attrs }">
                    <div class="ml-4" v-on="on" v-bind="attrs">
                      <v-icon dense>mdi-information-outline</v-icon>
                    </div>
                  </template>
                  <span
                    >Allowing order to be picked at Pick Gateway right after
                    order is dropped at Drop Gateway</span
                  >
                </v-tooltip>
              </template>
            </v-switch>
          </v-card-text>

          <v-card-actions class="justify-center">
            <v-btn
              block
              text
              color="primary"
              :loading="isBtnLoading.saveBtn"
              @click="saveRules()"
              >Save</v-btn
            >
          </v-card-actions>
          <v-expand-transition>
            <v-card-text v-show="stringifiedResponse">
              Response:
              <CodeBlock>{{ stringifiedResponse }}</CodeBlock>
            </v-card-text>
          </v-expand-transition>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import CodeBlock from "@/dashboard/model/CodeBlock.vue";
import { SettingAPI } from "../../api/settings";
import { SmSettingsKey } from "../../helper/enums";

export default {
  name: "PickDropRulesComponent",
  components: {
    CodeBlock,
  },
  data() {
    return {
      isLoading: false,
      isBtnLoading: {
        saveBtn: false,
      },
      stringifiedResponse: "",
      initialSetting: null,
      setting: {
        stack: {
          allowMultiPick: null,
          allowMultiDrop: null,
          allowMultiDropFromSameGateway: null,
          allowMultiDropFromMultiGateway: null,
        },
        gateway: {
          allowMultiPick: null,
          enableDropThenPickAtGateway: null,
        },
      },
    };
  },
  methods: {
    async init() {
      this.initialSetting = await this.getSetting();

      const value = this.initialSetting;
      this.setting = value;

      this.isLoading = false;
    },

    async getSetting() {
      return SettingAPI.getByKey(SmSettingsKey.PickDropRules);
    },

    async saveRules() {
      this.isBtnLoading.saveBtn = true;

      try {
        const response = await SettingAPI.updatePickDropRules(this.setting);

        this.stringifiedResponse = JSON.stringify(response, undefined, 2);
      } catch (axiosError) {
        this.stringifiedResponse = JSON.stringify(
          { errObj: axiosError, response: axiosError.response },
          undefined,
          2
        );
      }

      this.isBtnLoading.saveBtn = false;
    },
  },

  beforeMount() {
    this.init();
  },
};
</script>

<style scoped>
.centered-input >>> input {
  text-align: center;
}
</style>
