<template>
  <v-container>
    <v-row>
      <v-col>
        <v-card>
          <v-card-title>
            Order Dispatcher
            <v-tooltip right open-delay="300">
              <template v-slot:activator="{ on, attrs }">
                <div class="ml-4" v-on="on" v-bind="attrs">
                  <v-icon dense>mdi-information-outline</v-icon>
                </div>
              </template>
              <span
                >isActive: Switch on/off for each zone group to execute
                order</span
              >
              <br />
              <span
                >Threshold: Total no. of orders that will be sent to TC per
                ZoneGroup</span
              >
            </v-tooltip>
          </v-card-title>

          <v-divider></v-divider>

          <v-row class="pl-2">
            <v-col cols="11" sm="4" lg="4" xl="4">
              <v-card-subtitle class="p-1">
                <b>isActive</b>
              </v-card-subtitle>
              <v-card-text>
                <v-switch
                  class="mt-0 pb-4"
                  v-for="(zoneGroup, key) in this.setting"
                  :key="key"
                  v-model="zoneGroup.isActive"
                >
                  <template slot="label">
                    <span>{{ key }}</span>
                  </template>
                </v-switch>
              </v-card-text>
            </v-col>

            <v-col cols="11" sm="6" lg="7" xl="8">
              <v-card-subtitle>
                <b>Threshold</b>
              </v-card-subtitle>
              <v-card-text>
                <v-text-field
                  type="number"
                  class="my-0 centered-input"
                  v-for="(group, key) in this.setting"
                  :key="key"
                  :label="`${key}:`"
                  v-model.number="group.threshold"
                  :messages="group.messages"
                >
                </v-text-field>
              </v-card-text>
            </v-col>
          </v-row>

          <v-card-actions class="justify-center">
            <v-btn
              block
              text
              color="primary"
              :loading="isBtnLoading"
              @click="saveOrderExecutor()"
              >Save</v-btn
            >
          </v-card-actions>

          <v-expand-transition>
            <v-card-text v-show="stringifiedResponse">
              Response:
              <CodeBlock>{{ stringifiedResponse }}</CodeBlock>
            </v-card-text>
          </v-expand-transition>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import CodeBlock from "@/dashboard/model/CodeBlock.vue";
import { SettingAPI } from "../../api/settings";
import { ZoneGroupAPI } from "../../api/zone-group";
import { SmSettingsKey } from "../../helper/enums";

export default {
  name: "OrderDispatcherComponent",
  components: {
    CodeBlock,
  },
  props: {
    zoneGroups: {
      type: Array,
    },
  },
  data() {
    return {
      isLoading: true,
      stringifiedResponse: "",
      setting: null,
      zoneGroupIdMap: null,
      thresholdByZoneGroups: [],
      isBtnLoading: false,
      initialZoneGroups: null,
    };
  },
  methods: {
    async init() {
      this.setting = await this.getSetting();

      if (!this.zoneGroups) {
        this.initialZoneGroups = await ZoneGroupAPI.getAll();
      } else {
        this.initialZoneGroups = this.zoneGroups;
      }

      this.isLoading = false;
    },

    async getSetting() {
      return SettingAPI.getByKey(SmSettingsKey.OrderDispatcher);
    },

    async saveOrderExecutor() {
      this.isBtnLoading = true;

      let settingZoneGroups = [];

      for (const key in this.setting) {
        const data = {
          name: key,
          isActive: this.setting[key].isActive,
          threshold: this.setting[key].threshold,
        };
        settingZoneGroups.push(data);
      }
      try {
        const response = await SettingAPI.updateOrderDispatcher({
          zoneGroups: settingZoneGroups,
        });
        console.log(response);
        this.stringifiedResponse = JSON.stringify(response, undefined, 2);
      } catch (ex) {
        this.stringifiedResponse = JSON.stringify(
          { errObj: ex, response: ex.response },
          undefined,
          2
        );
      } finally {
        this.isBtnLoading = false;
      }
    },
  },
  beforeMount() {
    this.init();
  },
};
</script>

<style scoped>
.centered-input >>> input {
  text-align: center;
}
</style>
