<template>
  <v-container>
    <v-row>
      <v-col>
        <v-card>
          <v-card-title>
            Storage Optimizer
            <v-tooltip right open-delay="300">
              <template v-slot:activator="{ on, attrs }">
                <div class="ml-4" v-on="on" v-bind="attrs">
                  <v-icon dense>mdi-information-outline</v-icon>
                </div>
              </template>
              <span>isActive: Switch on/off for each zone group</span>
              <br />
              <span
                >isDedicatedStack: Switch on/off for dedicated stack mode</span
              >
              <br />
              <span>threshold: maximum number of bins to optimize at once</span>
              <br />
              <span
                >noOfBinsOnTop: no. of bins can stack on top of the target
                bin</span
              >
              <br />
              <span>acceptableZLevel: if bin placed below z, relocate it</span>
            </v-tooltip>
          </v-card-title>

          <v-divider></v-divider>

          <v-row class="pl-2">
            <!-- isActive -->
            <v-col cols="12" sm="2" lg="2" xl="2">
              <v-card-subtitle class="p-1">
                <b>isActive</b>
              </v-card-subtitle>
              <v-card-text>
                <v-switch
                  class="mt-0 pb-4"
                  v-for="(zoneGroup, key) in this.setting"
                  :key="key"
                  v-model="zoneGroup.isActive"
                >
                  <template slot="label">
                    <span>{{ key }}</span>
                  </template>
                </v-switch>
              </v-card-text>
            </v-col>

            <!-- isDedicatedStack -->
            <v-col cols="12" sm="2" lg="2" xl="2">
              <v-card-subtitle class="p-1">
                <b>isDedicatedStack</b>
              </v-card-subtitle>
              <v-card-text>
                <v-switch
                  class="mt-0 pb-4"
                  v-for="(zoneGroup, key) in this.setting"
                  :key="key"
                  v-model="zoneGroup.isDedicatedStack"
                >
                  <template slot="label">
                    <span>{{ key }}</span>
                  </template>
                </v-switch>
              </v-card-text>
            </v-col>

            <!-- natural slotting -->
            <v-col cols="12" sm="2" lg="2" xl="2">
              <v-card-subtitle class="p-1">
                <b>naturalSlottingMode</b>
              </v-card-subtitle>
              <v-card-text>
                <v-switch
                  class="mt-0 pb-4"
                  v-for="(zoneGroup, key) in this.setting"
                  :key="key"
                  v-model="zoneGroup.naturalSlottingMode"
                >
                  <template slot="label">
                    <span>{{ key }}</span>
                  </template>
                </v-switch>
              </v-card-text>
            </v-col>

            <!-- noOfBinsOnTop -->
            <v-col cols="12" sm="2" lg="2" xl="2">
              <v-card-subtitle class="p-1">
                <b>noOfBinsOnTop</b>
              </v-card-subtitle>
              <v-card-text>
                <v-text-field
                  type="number"
                  class="my-0 centered-input"
                  v-for="(group, key) in this.setting"
                  :key="key"
                  :label="`${key}:`"
                  v-model.number="group.noOfBinOnTop"
                  :messages="group.messages"
                >
                </v-text-field>
              </v-card-text>
            </v-col>

            <!-- acceptableZLevel -->
            <v-col cols="12" sm="2" lg="2" xl="2">
              <v-card-subtitle>
                <b>acceptableZLevel</b>
              </v-card-subtitle>
              <v-card-text>
                <v-text-field
                  type="number"
                  class="my-0 centered-input"
                  v-for="(group, key) in this.setting"
                  :key="key"
                  :label="`${key}:`"
                  v-model.number="group.acceptableZLevel"
                  :messages="group.messages"
                >
                </v-text-field>
              </v-card-text>
            </v-col>

            <!-- actions -->
            <v-col cols="12" sm="1" lg="1" xl="1">
              <v-card-subtitle>
                <b>action</b>
              </v-card-subtitle>
              <v-card-text>
                <v-btn
                  v-for="(group, key) in this.setting"
                  :key="key"
                  class="mb-4"
                  style="display: block;"
                  @click="optimize(key)"
                  color="primary"
                  >Optimize</v-btn
                >
              </v-card-text>
            </v-col>
          </v-row>

          <v-card-actions class="justify-center">
            <v-btn
              block
              text
              color="primary"
              :loading="isBtnLoading"
              @click="saveStorageOptimizer()"
              >Save</v-btn
            >
          </v-card-actions>

          <v-expand-transition>
            <v-card-text v-show="stringifiedResponse">
              Response:
              <CodeBlock>{{ stringifiedResponse }}</CodeBlock>
            </v-card-text>
          </v-expand-transition>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import CodeBlock from "@/dashboard/model/CodeBlock.vue";
import { SettingAPI } from "../../api/settings";
import { SmOperationAPI } from "../../api/sm-operation";
import { ZoneGroupAPI } from "../../api/zone-group";
import { SmSettingsKey } from "../../helper/enums";

export default {
  name: "StorageOptimizerComponent",
  components: {
    CodeBlock,
  },
  props: {
    zoneGroups: {
      type: Array,
    },
  },
  data() {
    return {
      isLoading: true,
      stringifiedResponse: "",
      setting: [],
      zoneGroupIdMap: null,
      thresholdByZoneGroups: [],
      isBtnLoading: false,
      initialZoneGroups: null,
      headers: [
        { text: "Zone Group", value: "zg" },
        { text: "isActive", value: "isActive" },
        { text: "isDedicatedStack", value: "isDedicatedStack" },
        { text: "naturalSlottingMode", value: "naturalSlottingMode" },
        { text: "noOfBinsOnTop", value: "noOfBinsOnTop" },
        { text: "acceptableZLevel", value: "acceptableZLevel" },
      ],
    };
  },
  methods: {
    async init() {
      this.setting = await this.getSetting();

      if (!this.zoneGroups) {
        this.initialZoneGroups = await ZoneGroupAPI.getAll();
      } else {
        this.initialZoneGroups = this.zoneGroups;
      }

      this.isLoading = false;
    },

    async optimize(zg) {
      try {
        await SmOperationAPI.triggerStorageOptimizer(zg);
      } catch (ex) {
        console.error(ex);
        alert("api failed with message: " + ex.messages);
      }
    },

    async getSetting() {
      return await SettingAPI.getByKey(SmSettingsKey.StorageOptimizer);
    },

    async saveStorageOptimizer() {
      this.isBtnLoading = true;

      let settingZoneGroups = [];

      for (const key in this.setting) {
        const data = {
          name: key,
          isActive: this.setting[key].isActive,
          isDedicatedStack: this.setting[key].isDedicatedStack,
          naturalSlottingMode: this.setting[key].naturalSlottingMode,
          noOfBinOnTop: this.setting[key].noOfBinOnTop,
          acceptableZLevel: this.setting[key].acceptableZLevel,
        };
        settingZoneGroups.push(data);
      }
      try {
        const response = await SettingAPI.updateStorageOptimizer({
          zoneGroups: settingZoneGroups,
        });
        this.stringifiedResponse = JSON.stringify(response, undefined, 2);
      } catch (ex) {
        this.stringifiedResponse = JSON.stringify(
          { errObj: ex, response: ex.response },
          undefined,
          2
        );
      } finally {
        this.isBtnLoading = false;
      }
    },
  },
  beforeMount() {
    this.init();
  },
};
</script>

<style scoped>
.centered-input >>> input {
  text-align: center;
}
</style>
