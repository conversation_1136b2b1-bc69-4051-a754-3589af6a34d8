<template>
  <v-container>
    <v-row>
      <v-col>
        <v-card>
          <v-card-title>
            Find Destination Factors
          </v-card-title>
          <v-card-subtitle>
            Weightage of each factors for calculating scoring of valid
            destinations
          </v-card-subtitle>
          <v-divider></v-divider>

          <v-card-text>
            <v-row>
              <v-col>
                <span>Merit : Added to score</span>
                <v-row>
                  <v-col class="pt-8" cols="6">
                    <v-text-field
                      type="number"
                      class="centered-input"
                      label="Stack Capacity"
                      v-model.number="factors.merit.stackCapacity"
                      outlined
                      :prepend-inner-icon="null"
                    >
                      <template v-slot:prepend-inner>
                        <v-tooltip right>
                          <template v-slot:activator="{ on, attrs }">
                            <v-icon v-on="on" v-bind="attrs" dense>
                              mdi-information-outline
                            </v-icon>
                          </template>
                          <span
                            >Number of remaining slot for placing bin in the
                            stack (more slot equal higher score)</span
                          >
                        </v-tooltip>
                      </template>
                    </v-text-field>
                  </v-col>
                  <v-col class="pt-8" cols="6">
                    <v-text-field
                      type="number"
                      class="centered-input"
                      label="Advanced Bin"
                      v-model.number="factors.merit.advancedBinStack"
                      outlined
                      :prepend-inner-icon="null"
                    >
                      <template v-slot:prepend-inner>
                        <v-tooltip right>
                          <template v-slot:activator="{ on, attrs }">
                            <v-icon v-on="on" v-bind="attrs" dense>
                              mdi-information-outline
                            </v-icon>
                          </template>
                          <span
                            >Have advanced bin in stack which are lower
                            priority</span
                          >
                        </v-tooltip>
                      </template>
                    </v-text-field>
                  </v-col>
                  <v-col cols="6">
                    <v-text-field
                      type="number"
                      class="centered-input"
                      label="Distance from Pick (Retrieving)"
                      v-model.number="factors.merit.fastDiggingforRetrieving"
                      outlined
                      :prepend-inner-icon="null"
                    >
                      <template v-slot:prepend-inner>
                        <v-tooltip right>
                          <template v-slot:activator="{ on, attrs }">
                            <v-icon v-on="on" v-bind="attrs" dense>
                              mdi-information-outline
                            </v-icon>
                          </template>
                          <span
                            >Distance from bin pick point (applicable to
                            internal order created due to retrieving order
                            only)</span
                          >
                        </v-tooltip>
                      </template>
                    </v-text-field>
                  </v-col>
                  <v-col cols="6">
                    <v-text-field
                      type="number"
                      class="centered-input"
                      label="Distance from pick (Adv)"
                      v-model.number="factors.merit.fastDiggingforAdvOrder"
                      outlined
                      :prepend-inner-icon="null"
                    >
                      <template v-slot:prepend-inner>
                        <v-tooltip right>
                          <template v-slot:activator="{ on, attrs }">
                            <v-icon v-on="on" v-bind="attrs" dense>
                              mdi-information-outline
                            </v-icon>
                          </template>
                          <span
                            >Distance from bin pick point (applicable to
                            internal order created due to advanced order
                            only)</span
                          >
                        </v-tooltip>
                      </template>
                    </v-text-field>
                  </v-col>
                  <v-col cols="6">
                    <v-text-field
                      type="number"
                      class="centered-input"
                      label="Targeted stack"
                      v-model.number="factors.merit.targetedStack"
                      outlined
                      :prepend-inner-icon="null"
                    >
                      <template v-slot:prepend-inner>
                        <v-tooltip right>
                          <template v-slot:activator="{ on, attrs }">
                            <v-icon v-on="on" v-bind="attrs" dense>
                              mdi-information-outline
                            </v-icon>
                          </template>
                          <span
                            >Destination is targeted stack of order (applicable
                            only if order has targeted stack)</span
                          >
                        </v-tooltip>
                      </template>
                    </v-text-field>
                  </v-col>
                </v-row>
              </v-col>

              <v-divider vertical></v-divider>

              <v-col>
                <span class="pb-4">Penalty : Deduct from score</span>
                <v-row>
                  <v-col class="pt-8" cols="6">
                    <v-text-field
                      type="number"
                      class="centered-input"
                      label="Order Picking From Stack"
                      v-model.number="factors.penalty.orderPickingFromStack"
                      outlined
                      :prepend-inner-icon="null"
                    >
                      <template v-slot:prepend-inner>
                        <v-tooltip right>
                          <template v-slot:activator="{ on, attrs }">
                            <v-icon v-on="on" v-bind="attrs" dense>
                              mdi-information-outline
                            </v-icon>
                          </template>
                          <span>Have order pending picking from the stack</span>
                        </v-tooltip>
                      </template>
                    </v-text-field>
                  </v-col>
                  <v-col class="pt-8" cols="6">
                    <v-text-field
                      type="number"
                      class="centered-input"
                      label="Order Dropping To Stack"
                      v-model.number="factors.penalty.orderDroppingToStack"
                      outlined
                      :prepend-inner-icon="null"
                    >
                      <template v-slot:prepend-inner>
                        <v-tooltip right>
                          <template v-slot:activator="{ on, attrs }">
                            <v-icon v-on="on" v-bind="attrs" dense>
                              mdi-information-outline
                            </v-icon>
                          </template>
                          <span>Have order pending dropping to the stack</span>
                        </v-tooltip>
                      </template>
                    </v-text-field>
                  </v-col>
                  <v-col cols="6">
                    <v-text-field
                      type="number"
                      class="centered-input"
                      label="Advanced Bin"
                      v-model.number="factors.penalty.advancedOrderInStack"
                      outlined
                      :prepend-inner-icon="null"
                    >
                      <template v-slot:prepend-inner>
                        <v-tooltip right>
                          <template v-slot:activator="{ on, attrs }">
                            <v-icon v-on="on" v-bind="attrs" dense>
                              mdi-information-outline
                            </v-icon>
                          </template>
                          <span
                            >Have advanced bin in stack which are higher
                            priority</span
                          >
                        </v-tooltip>
                      </template>
                    </v-text-field>
                  </v-col>
                  <v-col cols="6">
                    <v-text-field
                      type="number"
                      class="centered-input"
                      label="Cross Cube"
                      v-model.number="factors.penalty.crossZoneGroup"
                      outlined
                      :prepend-inner-icon="null"
                    >
                      <template v-slot:prepend-inner>
                        <v-tooltip right>
                          <template v-slot:activator="{ on, attrs }">
                            <v-icon v-on="on" v-bind="attrs" dense>
                              mdi-information-outline
                            </v-icon>
                          </template>
                          <span>Destination require to cross cube</span>
                        </v-tooltip>
                      </template>
                    </v-text-field>
                  </v-col>
                  <v-col cols="6">
                    <v-text-field
                      type="number"
                      class="centered-input"
                      label="Non-AcceptableZLevel"
                      v-model.number="factors.penalty.nonAcceptableZLevel"
                      outlined
                      :prepend-inner-icon="null"
                    >
                      <template v-slot:prepend-inner>
                        <v-tooltip right>
                          <template v-slot:activator="{ on, attrs }">
                            <v-icon v-on="on" v-bind="attrs" dense>
                              mdi-information-outline
                            </v-icon>
                          </template>
                          <span
                            >Destination z-level higher than acceptableZLevel
                            (only applicable when finding destination for
                            advanced bin)</span
                          >
                        </v-tooltip>
                      </template>
                    </v-text-field>
                  </v-col>
                  <v-col cols="6">
                    <v-text-field
                      type="number"
                      class="centered-input"
                      label="Targeted Stack"
                      v-model.number="factors.penalty.targetedStackOfOtherOrder"
                      outlined
                      :prepend-inner-icon="null"
                    >
                      <template v-slot:prepend-inner>
                        <v-tooltip right>
                          <template v-slot:activator="{ on, attrs }">
                            <v-icon v-on="on" v-bind="attrs" dense>
                              mdi-information-outline
                            </v-icon>
                          </template>
                          <span
                            >Destination is targeted stack of another order
                            (will not penalize if the order also has the same
                            targeted stack)</span
                          >
                        </v-tooltip>
                      </template>
                    </v-text-field>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </v-card-text>

          <v-divider></v-divider>

          <v-card-actions class="justify-center">
            <v-btn
              block
              text
              color="primary"
              :loading="isBtnLoading.saveBtn"
              @click="saveWeightage()"
              >Save</v-btn
            >
          </v-card-actions>
          <v-expand-transition>
            <v-card-text v-show="stringifiedResponse">
              Response:
              <CodeBlock>{{ stringifiedResponse }}</CodeBlock>
            </v-card-text>
          </v-expand-transition>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import CodeBlock from "@/dashboard/model/CodeBlock.vue";
import { SettingAPI } from "../../api/settings";
import { SmSettingsKey } from "../../helper/enums";

export default {
  name: "FindDestinationFactorsComponent",
  components: {
    CodeBlock,
  },
  data() {
    return {
      isLoading: false,
      isBtnLoading: {
        saveBtn: false,
      },
      stringifiedResponse: "",
      factors: {
        penalty: {
          orderPickingFromStack: null,
          orderDroppingToStack: null,
          advancedOrderInStack: null,
          crossZoneGroup: null,
          nonAcceptableZLevel: null,
          targetedStackOfOtherOrder: null,
        },
        merit: {
          stackCapacity: null,
          fastDiggingforAdvOrder: null,
          fastDiggingforRetrieving: null,
          targetedStack: null,
          advancedBinStack: null,
        },
      },
    };
  },
  methods: {
    async init() {
      const settings = await SettingAPI.getByKey(
        SmSettingsKey.FindDestinationFactors
      );
      for (const [factorName, weightage] of Object.entries(settings.penalty)) {
        if (factorName in this.factors.penalty) {
          this.factors.penalty[factorName] = weightage;
        }
      }
      for (const [factorName, weightage] of Object.entries(settings.merit)) {
        if (factorName in this.factors.merit) {
          this.factors.merit[factorName] = weightage;
        }
      }
      this.isLoading = false;
    },

    async saveWeightage() {
      this.isBtnLoading.saveBtn = true;

      try {
        const response = await SettingAPI.updateFindDestinationFactors(
          this.factors
        );

        this.stringifiedResponse = JSON.stringify(response, undefined, 2);
      } catch (axiosError) {
        this.stringifiedResponse = JSON.stringify(
          { errObj: axiosError, response: axiosError.response },
          undefined,
          2
        );
      }

      this.isBtnLoading.saveBtn = false;
    },
  },

  beforeMount() {
    this.init();
  },
};
</script>

<style scoped>
.centered-input >>> input {
  text-align: center;
}
</style>
