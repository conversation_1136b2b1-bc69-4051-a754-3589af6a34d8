<template>
  <v-container>
    <v-row>
      <v-col>
        <v-card>
          <v-card-title>
            <span>Station Configuration</span>
            <v-spacer></v-spacer>
            <!-- <v-icon @click="gotoMore()" class="icon">
              mdi-dots-horizontal
            </v-icon> -->

            <v-icon @click="init(true)" class="icon">mdi-refresh</v-icon>
          </v-card-title>
          <v-divider></v-divider>
          <v-card-subtitle>
            <p>
              Auto Store - Bin will be stored after N-seconds of delay upon
              arrive at work point if enabled.
            </p>
          </v-card-subtitle>

          <v-card-text>
            <div v-if="isLoading">
              <v-skeleton-loader
                type="table-thead, table-tbody"
              ></v-skeleton-loader>
            </div>
            <v-data-table
              v-else
              show-select
              fixed-header
              height="30vh"
              v-model="selected"
              :headers="headers"
              :items="setting.stations"
              item-key="id"
              :items-per-page="-1"
              hide-default-footer
            >
              <template v-slot:[`item.isActive`]="{ item }">
                <v-icon
                  @click="toggleIsActive(item)"
                  :color="item.isActive ? 'green' : 'red'"
                  >mdi-circle</v-icon
                >
              </template>
              <template v-slot:[`item.allowBinPassThrough`]="{ item }">
                <v-switch
                  v-model="item.allowBinPassThrough"
                  :label="item.allowBinPassThrough ? 'Allow' : 'Disallow'"
                  @change="toggleAllowBinPassThrough(item)"
                ></v-switch>
              </template>
              <template v-slot:[`item.maxConcurrentJob`]="{ item }">
                <v-text-field
                  v-model.number="item.maxConcurrentJob"
                  type="number"
                  :min="0"
                  @input="updateText(item)"
                ></v-text-field>
              </template>
              <template v-slot:[`item.extraRetrieveQty`]="{ item }">
                <v-text-field
                  v-model.number="item.extraRetrieveQty"
                  type="number"
                  :min="0"
                  @input="updateText(item)"
                ></v-text-field>
              </template>
              <template v-slot:[`item.allowStrictMode`]="{ item }">
                <v-switch
                  v-model="item.allowStrictMode"
                  :label="item.allowStrictMode ? 'Allow' : 'Disallow'"
                  @change="toggleAllowStrictMode(item)"
                ></v-switch>
              </template>
              <template v-slot:[`item.orderNo`]="{ item }">
                <template v-if="!item.orderNoEditable">
                  <div @click="enableEditing(item)">
                    {{
                      item.orderNo != null && item.orderNo != ""
                        ? item.orderNo
                        : "NA"
                    }}
                  </div>
                </template>
                <template v-else>
                  <v-text-field
                    v-model="item.orderNo"
                    @input="updateText(item)"
                  ></v-text-field>
                </template>
              </template>
              <template v-slot:[`item.autoStore`]="{ item }">
                <v-icon
                  :color="item.autoStore ? 'success' : ''"
                  :style="{ opacity: item.autoStore ? 1 : 0.4 }"
                  >{{ autoStoreIcon }}</v-icon
                >
              </template>
              <template v-slot:[`item.actions`]="{ item }">
                <v-btn
                  color="primary"
                  :disabled="!item.isChanged"
                  @click="applyChanges(item)"
                  >Apply</v-btn
                >
              </template>
            </v-data-table>

            <v-row align="baseline">
              <v-col class="my-4">
                {{ selected.length }} station(s) selected
              </v-col>
              <v-col cols="auto">
                <v-row align="baseline" justify="end">
                  <v-col class="py-0 pr-0" cols="auto"
                    >Auto Store delay for</v-col
                  >
                  <v-col class="py-0 px-1" cols="3">
                    <v-text-field
                      dense
                      hide-details
                      class="centered-input"
                      type="number"
                      v-model="setting.delay"
                    ></v-text-field>
                  </v-col>
                  <v-col class="py-0 pl-0" cols="auto">second(s)</v-col>
                </v-row>
              </v-col>
            </v-row>

            <v-row align="center" justify="center">
              <v-col cols="auto">
                <v-btn
                  text
                  large
                  plain
                  rounded
                  elevation="1"
                  color="success"
                  :disabled="isBtnLoading.disableBtn"
                  :loading="isBtnLoading.enableBtn"
                  @click="enableAutoStore($event)"
                >
                  <v-icon>mdi-plus</v-icon> Enable
                  <v-icon>{{ autoStoreIcon }}</v-icon>
                </v-btn>
              </v-col>
              <v-col cols="auto">
                <v-btn
                  text
                  large
                  plain
                  rounded
                  elevation="1"
                  color="error"
                  :disabled="isBtnLoading.enableBtn"
                  :loading="isBtnLoading.disableBtn"
                  @click="disableAutoStore($event)"
                >
                  <v-icon>mdi-minus</v-icon> Disable
                  <v-icon>{{ autoStoreIcon }}</v-icon>
                </v-btn>
              </v-col>
            </v-row>

            <v-expand-transition>
              <div v-show="stringifiedResponse">
                Response:
                <CodeBlock>{{ stringifiedResponse }}</CodeBlock>
              </div>
            </v-expand-transition>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <SnackbarNotification ref="snackbarNotification" />
  </v-container>
</template>

<script>
import CodeBlock from "@/dashboard/model/CodeBlock.vue";
import { SettingAPI } from "../../api/settings";
import { StationAPI } from "../../api/station";
import SnackbarNotification from "../../components/shared/SnackbarNotification.vue";
import {
  AutoStoreUpdateActions,
  Route,
  SmSettingsKey,
} from "../../helper/enums";

export default {
  name: "StationConfigComponent",
  components: {
    CodeBlock,
    SnackbarNotification,
  },
  props: {
    stations: {
      type: Array,
    },
  },
  data() {
    return {
      isLoading: true,
      isBtnLoading: {
        enableBtn: false,
        disableBtn: false,
      },
      stringifiedResponse: "",
      selected: [],
      headers: [
        { text: "Code", value: "id" },
        { text: "Status", value: "isActive" },
        { text: "BinPassThrough", value: "allowBinPassThrough" },
        { text: "Max Concurrent Job", value: "maxConcurrentJob" },
        { text: "Extra Retrieve Qty", value: "extraRetrieveQty" },
        // { text: "Strict Mode", value: "allowStrictMode" },
        // { text: "Order No.", value: "orderNo" },
        { text: "Auto Store", value: "autoStore" },
        { text: "Actions", value: "actions", sortable: false },
      ],
      initialSetting: null,
      initialStations: null,
      setting: {
        stations: null,
        delay: 0,
      },
      autoStoreIcon: "mdi-refresh-auto",
      binPrimmingIcon: "mdi-pause-octagon",
    };
  },
  methods: {
    showNotification(success, message) {
      this.$refs.snackbarNotification.showNotification(success, message);
    },
    updateText(item) {
      item.isChanged = true;
    },
    enableEditing(item) {
      item.orderNoEditable = true;
    },
    async init(force = false) {
      this.initialSetting = await this.getSetting();

      const autoStoreValue = this.initialSetting.autoStore;

      const autoStoreStations = new Set(autoStoreValue.stations);

      if (!this.stations || force) {
        this.initialStations = await StationAPI.getAll();
      } else {
        this.initialStations = this.stations;
      }

      this.setting.stations = this.initialStations.map((station) => {
        return {
          ...station,
          autoStore: autoStoreStations.has(station.id),
          isChanged: false,
          orderNoEditable: false,
        };
      });
      this.setting.delay = autoStoreValue.delay;

      this.isLoading = false;
    },
    toggleIsActive(station) {
      station.isActive = !station.isActive;
      station.isChanged = true;
    },
    toggleAllowBinPassThrough(station) {
      station.isChanged = true;
    },
    toggleAllowStrictMode(station) {
      station.isChanged = true;
    },
    async applyChanges(station) {
      // Make API call to update the value in the backend server
      try {
        await StationAPI.updateStation(station.id, {
          isActive: station.isActive,
          allowBinPassThrough: station.allowBinPassThrough,
          maxConcurrentJob: station.maxConcurrentJob,
          extraRetrieveQty: station.extraRetrieveQty,
          // allowStrictMode: station.allowStrictMode,
          // orderNo:
          //   station.orderNo != null ? station.orderNo.trim() : station.orderNo,
        });

        await this.init(true);
        this.showNotification(
          true,
          `station ${station.id} updated successfully.`
        );
      } catch (error) {
        this.showNotification(false, `Failed: ${error.message}`);
      }
    },
    async enableAutoStore() {
      this.isBtnLoading.enableBtn = true;

      try {
        const response = await SettingAPI.updateAutoStore(
          AutoStoreUpdateActions.Enable,
          this.selected.map((item) => item.id),
          this.setting.delay
        );

        this.selected.forEach((item) => (item.autoStore = true));
        this.stringifiedResponse = JSON.stringify(response, undefined, 2);
      } catch (axiosError) {
        this.stringifiedResponse = JSON.stringify(
          { errObj: axiosError, response: axiosError.response },
          undefined,
          2
        );
      }

      this.isBtnLoading.enableBtn = false;
      this.selected = [];
    },

    async disableAutoStore() {
      this.isBtnLoading.disableBtn = true;

      try {
        const response = await SettingAPI.updateAutoStore(
          AutoStoreUpdateActions.Disable,
          this.selected.map((item) => item.id),
          this.setting.delay
        );

        this.selected.forEach((item) => (item.autoStore = false));
        this.stringifiedResponse = JSON.stringify(response, undefined, 2);
      } catch (e) {
        this.stringifiedResponse = JSON.stringify(e, undefined, 2);
      }

      this.isBtnLoading.disableBtn = false;
      this.selected = [];
    },

    async getSetting() {
      return {
        autoStore: await SettingAPI.getByKey(SmSettingsKey.AutoStore),
      };
    },

    gotoMore() {
      this.$router.push(Route.StationNode);
    },
  },

  beforeMount() {
    this.init();
  },
};
</script>

<style scoped>
.centered-input >>> input {
  text-align: center;
}
</style>
