<template>
  <v-container>
    <h2>Operation</h2>

    <v-tabs class="mt-5">
      <v-tab href="#call">Call Bin</v-tab>
      <v-tab href="#store">Store Bin</v-tab>
      <v-tab href="#transfer">Transfer Bin</v-tab>

      <!-- Call Bin START -->
      <v-tab-item class="mt-5" value="call">
        <v-card elevation="10" class="pa-4">
          <v-card-title> Bin Call </v-card-title>
          <v-spacer></v-spacer>
          <v-form ref="bincall">
            <v-card-text>
              <v-row>
                <v-col cols="3"> Storage Code </v-col>
                <v-col cols="5">
                  <v-combobox
                    v-model="binToCall.storageCodeToCall"
                    label="Storage Code"
                    multiple
                    small-chips
                    deletable-chips
                    clearable
                    outlined
                    append-icon=""
                  ></v-combobox>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="3">To Station</v-col>
                <v-col cols="5">
                  <v-text-field
                    v-model.number="binToCall.toStationCode"
                    dense
                    outlined
                    filled
                    rounded
                    class="shrink"
                    :rules="[(value) => !!value || 'Invalid station #']"
                  ></v-text-field>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="3">Tags</v-col>
                <v-col cols="5">
                  <v-combobox
                    v-model="binToCall.tags"
                    multiple
                    small-chips
                    deletable-chips
                    clearable
                    outlined
                    append-icon=""
                  ></v-combobox>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="3">Qty</v-col>
                <v-col cols="5">
                  <v-text-field
                    v-model.number="binToCall.qty"
                    type="number"
                    dense
                    outlined
                    filled
                    rounded
                    class="shrink"
                    :rules="[
                      (value) =>
                        value != null ? value >= 0 || 'Invalid qty' : true,
                    ]"
                  ></v-text-field>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="3">Reservation No</v-col>
                <v-col cols="5">
                  <v-text-field
                    v-model.trim="binToCall.reservationNo"
                    dense
                    outlined
                    filled
                    rounded
                    class="shrink"
                  ></v-text-field>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="3">Advanced Order No</v-col>
                <v-col cols="5">
                  <v-text-field
                    v-model.trim="binToCall.advancedOrderNo"
                    dense
                    outlined
                    filled
                    rounded
                    class="shrink"
                  ></v-text-field>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="3"> Exclude storegs at station </v-col>
                <v-col cols="5">
                  <v-radio-group v-model="binToCall.excludeStoragesAtStation">
                    <v-radio :key="1" label="Yes" :value="1"></v-radio>
                    <v-radio :key="2" label="No" :value="2"></v-radio>
                  </v-radio-group>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="3"> Exclude tags </v-col>
                <v-col cols="5">
                  <v-combobox
                    v-model="binToCall.excludeTags"
                    multiple
                    small-chips
                    deletable-chips
                    clearable
                    outlined
                    append-icon=""
                  ></v-combobox>
                </v-col>
              </v-row>
            </v-card-text>
            <v-card-actions class="justify-center">
              <v-btn @click="binCall()">Call</v-btn>
            </v-card-actions>
          </v-form>
          <v-spacer></v-spacer>
          <v-expand-transition>
            <v-card-text v-show="stringifiedCallBinResponse">
              <div class="mb-2" v-if="savedCallBinResponse">
                <span>Response:</span>
                <span
                  :class="[
                    `mx-2  font-weight-bold`,
                    `${
                      savedCallBinResponse.data.status >= 400 ? 'red' : 'green'
                    }--text`,
                  ]"
                  >{{ savedCallBinResponse.status }}
                  {{ savedCallBinResponse.statusText }}
                </span>
                <i> (received on {{ new Date().toLocaleString() }})</i>
              </div>
              <CodeBlock name="callBin">{{
                stringifiedCallBinResponse
              }}</CodeBlock>
            </v-card-text>
          </v-expand-transition>

          <!-- <v-card-text ref="bincall_response"></v-card-text> -->
        </v-card>
      </v-tab-item>
      <!-- Call Bin END -->

      <!-- Store Bin START-->
      <v-tab-item class="mt-5" value="store">
        <v-card elevation="10" class="pa-4">
          <v-card-title>Bin Store</v-card-title>
          <v-spacer></v-spacer>
          <v-form ref="binstore">
            <v-card-text>
              <v-row>
                <v-col cols="3"> Storage Code </v-col>
                <v-col cols="5">
                  <v-text-field
                    v-model.number="binToStore.storageCodeToStore"
                    dense
                    outlined
                    filled
                    rounded
                    class="shrink"
                    :rules="[(value) => !!value || 'Required.']"
                  >
                  </v-text-field>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="3"> From Station </v-col>
                <v-col cols="5">
                  <v-text-field
                    v-model.number="binToStore.fromStationCode"
                    dense
                    outlined
                    filled
                    rounded
                    class="shrink"
                    :rules="[(value) => !!value || 'Required.']"
                  ></v-text-field>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="3">Preferred Zone</v-col>
                <v-col cols="5">
                  <v-text-field
                    v-model.number="binToStore.preferredZone"
                    dense
                    outlined
                    filled
                    rounded
                    class="shrink"
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-card-text>
            <v-card-actions class="justify-center">
              <v-btn @click="binStore()">Store</v-btn>
            </v-card-actions>
          </v-form>
          <v-spacer></v-spacer>
          <v-expand-transition>
            <v-card-text v-show="stringifiedStoreBinResponse">
              <div class="mb-2" v-if="savedStoreBinResponse">
                <span>Response:</span>
                <span
                  :class="[
                    `mx-2  font-weight-bold`,
                    `${
                      savedStoreBinResponse.data.status >= 400 ? 'red' : 'green'
                    }--text`,
                  ]"
                  >{{ savedStoreBinResponse.status }}
                  {{ savedStoreBinResponse.statusText }}
                </span>
                <i> (received on {{ new Date().toLocaleString() }})</i>
              </div>
              <CodeBlock name="storeBin">{{
                stringifiedStoreBinResponse
              }}</CodeBlock>
            </v-card-text>
          </v-expand-transition>
        </v-card>
      </v-tab-item>
      <!-- Store Bin END -->

      <!-- Transfer Bin START-->
      <v-tab-item class="mt-5" value="transfer">
        <v-card elevation="10" class="pa-4">
          <v-card-title>Transfer Bin</v-card-title>
          <v-spacer></v-spacer>
          <v-form ref="bintransfer">
            <v-card-text>
              <v-row>
                <v-col cols="3"> Storage Code </v-col>
                <v-col cols="5">
                  <v-combobox
                    v-model="binToTransfer.storages"
                    label="Storage Code"
                    multiple
                    small-chips
                    deletable-chips
                    clearable
                    outlined
                    append-icon=""
                    :rules="[(value) => !!value || 'Required.']"
                  ></v-combobox>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="3"> Destination </v-col>
                <v-col cols="2" align-self="start">
                  <v-text-field
                    v-model.number="binToTransfer.destination.x"
                    type="number"
                    dense
                    outlined
                    filled
                    rounded
                    class="shrink"
                    :rules="[
                      (value) =>
                        value != null ? value >= 0 || 'Invalid X' : true,
                    ]"
                  ></v-text-field>
                </v-col>
                <v-col cols="2">
                  <v-text-field
                    v-model.number="binToTransfer.destination.y"
                    type="number"
                    dense
                    outlined
                    filled
                    rounded
                    class="shrink"
                    :rules="[
                      (value) =>
                        value != null ? value >= 0 || 'Invalid Y' : true,
                    ]"
                  ></v-text-field>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="3">Zone Group</v-col>
                <v-col cols="5">
                  <v-text-field
                    v-model.number="binToTransfer.zoneGroup"
                    dense
                    outlined
                    filled
                    rounded
                    placeholder="A"
                    class="shrink"
                    :rules="[(value) => !!value || 'Required.']"
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-card-text>
            <v-card-actions class="justify-center">
              <v-btn @click="binTransfer()">Transfer</v-btn>
            </v-card-actions>
          </v-form>
          <v-spacer></v-spacer>
          <v-expand-transition>
            <v-card-text v-show="stringifiedTransferBinResponse">
              <div class="mb-2" v-if="savedTransferBinResponse">
                <span>Response:</span>
                <span
                  :class="[
                    `mx-2  font-weight-bold`,
                    `${
                      savedTransferBinResponse.data.status >= 400
                        ? 'red'
                        : 'green'
                    }--text`,
                  ]"
                  >{{ savedTransferBinResponse.status }}
                  {{ savedTransferBinResponse.statusText }}
                </span>
                <i> (received on {{ new Date().toLocaleString() }})</i>
              </div>
              <CodeBlock name="transferBin">{{
                stringifiedTransferBinResponse
              }}</CodeBlock>
            </v-card-text>
          </v-expand-transition>
        </v-card>
      </v-tab-item>
      <!-- Transfer Bin END -->
    </v-tabs>
  </v-container>
</template>

<script>
import CodeBlock from "@/dashboard/model/CodeBlock.vue";
import { SettingAPI } from "../api/settings";
import { SmOperationAPI } from "../api/sm-operation";
import { SmSettingsKey } from "../helper/enums";

export default {
  name: "CallStoreBins",
  components: {
    CodeBlock,
  },
  data() {
    return {
      isDualStation: false,

      stringifiedCallBinResponse: undefined,
      savedCallBinResponse: undefined,

      stringifiedStoreBinResponse: undefined,
      savedStoreBinResponse: undefined,

      stringifiedTransferBinResponse: undefined,
      savedTransferBinResponse: undefined,

      binToTransfer: {
        storages: [],
        destination: {
          x: undefined,
          y: undefined,
        },
        zoneGroup: undefined,
      },

      binToStore: {
        fromStationCode: undefined,
        storageCodeToStore: undefined,
        preferredZone: undefined,
      },

      binToCall: {
        storageCodeToCall: [],
        toStationCode: undefined,
        tags: [],
        qty: undefined,
        reservationNo: undefined,
        advancedOrderNo: undefined,
        excludeStoragesAtStation: undefined,
        excludeTags: [], //undefined, // string
      },
    };
  },

  mounted() {
    this.checkDualStation();
  },

  methods: {
    async checkDualStation() {
      const getDualStationMap = await SettingAPI.getByKey("DualStationMap");
      this.isDualStation =
        getDualStationMap != null
          ? Object.keys(getDualStationMap)?.length > 0
            ? true
            : false
          : false;
    },

    async autostoreWarning() {
      const autoStoreValue = await SettingAPI.getByKey(SmSettingsKey.AutoStore);
      const autoStoreStationIds = autoStoreValue?.stations;

      if (autoStoreStationIds.includes(this.binToCall.toStationCode)) {
        const confimationBox = new Promise((res, rej) => {
          this.$awn.confirm(
            `Auto store is ENABLED for Station ${this.binToCall.toStationCode}. Are you sure to call bin?`,
            res,
            rej,
            {
              labels: {
                confirm: "Auto store is Enabled",
                confirmOk: "Call Bin",
                confirmCancel: "Maybe later",
              },
            }
          );
        });

        try {
          await confimationBox;
        } catch (e) {
          return false;
        }
      }

      return true;
    },

    async binCall() {
      // # Prompt confirmation when auto-store is enabled
      if (!(await this.autostoreWarning())) return;

      if (this.$refs.bincall.validate()) {
        const tags = this.binToCall.tags?.length > 0 ? this.binToCall.tags : [];

        const excludeTags =
          this.binToCall.excludeTags?.length > 0
            ? this.binToCall.excludeTags
            : undefined;

        const data = {
          station: parseInt(this.binToCall.toStationCode),
          storages:
            this.binToCall.storageCodeToCall?.length > 0
              ? this.binToCall.storageCodeToCall.map((x) => parseInt(x))
              : undefined,
          tags,
          qty:
            this.binToCall.qty > 0 ? parseInt(this.binToCall.qty) : undefined,
          reservationNo: this.binToCall.reservationNo,
          advancedOrderNo: this.binToCall.advancedOrderNo,
          excludeStoragesAtStation: this.binToCall.excludeStoragesAtStation,
          excludeTags,
        };

        SmOperationAPI.callBin(data, {
          "bypass-autostore-validation": true,
        })
          .then((response) => {
            this.savedCallBinResponse = response;
            this.stringifiedCallBinResponse = JSON.stringify(
              response,
              undefined,
              2
            );
          })
          .catch((error) => {
            this.savedCallBinResponse = error.response;
            this.stringifiedCallBinResponse = JSON.stringify(
              { errObj: error, response: error.response },
              undefined,
              2
            );
          });
      }
    },

    async binStore() {
      let zoneGroupInput = this.binToStore.preferredZone?.toUpperCase();
      if (this.$refs.binstore.validate()) {
        if (this.isDualStation) {
          zoneGroupInput =
            zoneGroupInput === "A"
              ? "LEFT"
              : zoneGroupInput === "B"
              ? "RIGHT"
              : zoneGroupInput;
        }
        const data = {
          station: parseInt(this.binToStore.fromStationCode),
          storage: parseInt(this.binToStore.storageCodeToStore),
          preferredZone: zoneGroupInput,
        };

        SmOperationAPI.storeBin(data)
          .then((response) => {
            this.savedStoreBinResponse = response;
            this.stringifiedStoreBinResponse = JSON.stringify(
              response,
              undefined,
              2
            );
          })
          .catch((error) => {
            this.savedStoreBinResponse = error.response;
            this.stringifiedStoreBinResponse = JSON.stringify(
              { errObj: error, response: error.response },
              undefined,
              2
            );
          });
      }
    },

    async binTransfer() {
      if (this.$refs.bintransfer.validate()) {
        const data = {
          storages:
            this.binToTransfer.storages?.length > 0
              ? this.binToTransfer.storages.map((x) => parseInt(x))
              : undefined,
          destination:
            this.binToTransfer.destination.x && this.binToTransfer.destination.y
              ? {
                  x: parseInt(this.binToTransfer.destination.x),
                  y: parseInt(this.binToTransfer.destination.y),
                }
              : undefined,
          zoneGroup:
            this.binToTransfer.zoneGroup.toUpperCase() === "A"
              ? "LEFT"
              : this.binToTransfer.zoneGroup.toUpperCase() === "B"
              ? "RIGHT"
              : this.binToTransfer.zoneGroup,
        };

        SmOperationAPI.transferBin(data)
          .then((response) => {
            this.savedTransferBinResponse = response;
            this.stringifiedTransferBinResponse = JSON.stringify(
              response,
              undefined,
              2
            );
          })
          .catch((error) => {
            this.savedTransferBinResponse = error.response;
            this.stringifiedTransferBinResponse = JSON.stringify(
              { errObj: error, response: error.response },
              undefined,
              2
            );
          });
      }
    },
  },
};
</script>
