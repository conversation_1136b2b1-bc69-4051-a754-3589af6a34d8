<template>
  <v-app app>
    <v-container>
      <v-card flat outlined>
        <div>
          <v-card dark>
            <v-row align="center" class="ml-4 mr-4">
              <v-col cols="auto">
                <v-select
                  class="text-h5 ml-4"
                  v-model="currentZone"
                  :items="cubes"
                  :rules="[(v) => !!v || 'Cube selection is required']"
                  label="Cube"
                  required
                  @change="onCubeChanged"
                />
              </v-col>
              <v-col>Last Updated: {{ lastUpdated }}</v-col>
            </v-row>
          </v-card>
        </div>

  
      </v-card>

      <v-card flat outlined>
        <div class="fixed-container">
          <v-card dark>
            <v-row align="center" class="ml-4 mr-4">
        
              <v-col cols="auto">
                <v-btn
                  :ripple="{ center: true }"
                  dark
                  class="text-h6 ml-4"
                  height="60px"
                  @click="initialize()"
                >
                  <v-icon left> mdi-reload</v-icon>Refresh
                </v-btn>
              </v-col>

              <v-col cols="auto">
                <v-btn
                  :ripple="{ center: true }"
                  color="success"
                  class="text-h6 ml-4 "
                  height="60px"
                  @click="onClickUpdateObstacle('POST')"
                  :disabled="!btnValidation"
                >
                  <v-icon dark left>mdi-plus-box</v-icon>
                  Add
                </v-btn>

                <v-btn
                  :ripple="{ center: true }"
                  class="text-h6 ml-4"
                  color="error"
                  height="60px"
                  @click="onClickUpdateObstacle('DELETE')"
                  :disabled="!btnValidation"
                >
                  <v-icon dark left>mdi-minus-box</v-icon>
                  Remove
                </v-btn>
              </v-col>

           
            </v-row>
          </v-card>
        </div>

        <v-row justify="center">
          <v-col cols="12">
            <Grid
              ref="gridComponent"
              :cubeDescName="currentZone"
              :gridObject="gridObject"
              :obstacleObject="gridObstacle"
              :skycarObject="gridSkycar"
              @grid-selected="handleGridSelect"
              @grid-selected-reset="handleGridReset"
              @cell-right-press="handleGridRightPress"
              @cell-long-press="handleGridLongPress"
            ></Grid>
          </v-col>
        </v-row>
      </v-card>
    </v-container>
    <DialogObstacleResponse ref="dialogObstacleResponse" />
  </v-app>
</template>

<script>
import Grid from "../components/cube/Grid.vue";
import DialogObstacleResponse from "../components/dialogs/DialogObstacleResponse.vue";
import { getHost, getCube, useRefreshToken } from "../helper/common";
import { SkycarError, routeMatrix, RouteSkycar } from "../helper/enums";
import { initializeGrid } from "../api/grid.js"; // Import the initializeGrid function from the grid.js file

const httpRequest = require("../helper/http_request.js");

export default {
  components: {
    Grid,
    DialogObstacleResponse,
  },

  data() {
    return {
      // Zone
      cubes: getCube(),
      currentZone: getCube()[0],
      selectionValidation: false,
      lastUpdated: null,

      // Child object
      gridObject: {
        gridName: "C",
        gridMinX: 0,
        gridMaxX: 0,
        gridMinY: 0,
        gridMaxY: 0,
      },
      gridObstacle: {},
      gridSkycar: {},

      // Context Menu
      showingContextMenu: false,
      contextMenuX: 0,
      contextMenuY: 0,

      // Child output
      gridData: {
        selectedCells: [],
      },
    };
  },
  async created() {
    // console.log("initialize");
    this.initialize();
  },
  computed: {
    btnValidation() {
      return this.gridData.selectedCells.length > 0 ? true : false;
    },
  },

  methods: {
    async initialize() {
      const gridData = await initializeGrid(this.currentZone);
      if (gridData) {
        this.$refs.gridComponent.updateAll(
          gridData.gridObstacle,
          gridData.gridSkycar,
          gridData.gridObject,
          gridData.gridQr
        );
        this.lastUpdated = gridData.lastUpdated;
      }
    },
    async getGrid() {
      const url = getHost(this.currentZone);
      const response = await httpRequest.axiosRequest(
        "GET",
        url,
        routeMatrix.grid
      );
      if (response != null) {
        this.gridObject = response.data.data;
      }

      this.$refs.gridComponent.updateGrid(this.gridObject);
    },
    async getSkycar() {
      const url = getHost(this.currentZone);
      const response = await httpRequest.axiosRequest(
        "GET",
        url,
        RouteSkycar.GET_SKYCAR
      );
      if (response != null) {
        this.gridSkycar = this.transformSkycarData(response.data.data);
      }

      this.$refs.gridComponent.updateSkycar(this.gridSkycar);
    },
    async getObstacle() {
      const url = getHost(this.currentZone);
      const response = await httpRequest.axiosRequest(
        "GET",
        url,
        routeMatrix.gridObstacle
      );
      if (response != null) {
        this.gridObstacle = response.data.data;
      }

      this.$refs.gridComponent.updateObstacle(this.gridObstacle);
    },

    handleGridSelect(selectedCells) {
      this.gridData.selectedCells = selectedCells;
      // this.selectionValidation = true;
      console.log(
        "Obstacle Parent handleGridSelect",
        this.gridData.selectedCells
      );
    },
    handleGridReset() {
      this.gridData.selectedCells = [];
      // this.selectionValidation = false;
      console.log("Obstacle Parent handleGridReset");
    },
    handleGridLongPress() {
      console.log("Obstacle Parent handle grid long press");
    },
    handleGridRightPress() {
      console.log("Obstacle Parent handle grid right press");
      // this.$awn.info("Right clicked");
    },

    transformSelectionRange(selectedCells) {
      const output = {
        two_d: selectedCells.map((e) => e.twod),
      };
      return output;
    },

    async onClickUpdateObstacle(method) {
      const _selectedCells = this.gridData.selectedCells;
      if (_selectedCells) {
        const twoDArray = this.transformSelectionRange(_selectedCells);
        var res = await this.updateObstacleHttp(
          twoDArray,
          method,
          this.currentZone,
          this.$store.state.user.accessToken
        );
      }

      if (res) {
        this.initialize();
        let data = res.data;
        if (!data.status) {
          this.$awn.alert(data.message);
        } else {
          this.$refs.dialogObstacleResponse.openDialog(data.data);
        }
      }

      //Reset selection
      this.$refs.gridComponent.resetAll();
    },
    async onCubeChanged() {
      console.log("onCubeChanged");
      this.initialize();
    },

    async updateObstacleHttp(data, method, cube) {
      const url = getHost(cube);
      const response = await httpRequest.axiosRequest(
        method,
        url,
        SkycarError.addRemoveObs,
        JSON.stringify(data)
      );

      if (response == null) {
        return;
      }

      if (response.status == 401) {
        return useRefreshToken(
          this,
          this.updateObstacleHttp,
          data,
          method,
          cube
        );
      }

      return response;
    },
  },
};
</script>

<style scoped>
.fixed-container {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
}

</style>
