<template>
  <v-app app>
    <v-container fluid>
      <v-col>
        
        <!-- Troubleshooting Info -->
        <v-card light elevation="4" color="grey lighten-4">
          <v-card-actions>
            <v-btn
              text
              color="black"
              @click="showTroubleshootingInfo = !showTroubleshootingInfo"
            >
            Troubleshooting Info
              <v-icon>{{ showTroubleshootingInfo ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon>
            </v-btn>
            <v-spacer></v-spacer>
          </v-card-actions>
          <v-expand-transition>
            <div v-show="showTroubleshootingInfo">
              <v-divider></v-divider>
              <v-col>
                <p>• Please note currently data in table is not real-time, 
                  'refresh' frequently if needed to track a job.</p>
                <p>• If charging station emit error, charging station will 
                  automatically be un-paired, status set to 'unavailable', and its related job set to 'errored'.</p>
                <p>• Before restarting a job, please ensure charging station is paired. 
                  Can check pairing by 'refresh'-ing the charging stations table.</p>
                <p>• If error occurred while Skycar is docked, obstacle will 
                  need to be removed manually (use Error Handling tab).</p>
                <p>• If canceling a job, can create a new request for the same Skycar using the manual entry below.</p>
              </v-col>
            </div>
          </v-expand-transition>
        </v-card>
        
        <v-divider class="py-4"></v-divider>

        <!-- Health Check / Operational Help -->
        <v-card light elevation="4">
          <v-col>
            <v-row class="d-flex px-2">
              <v-card-title>Status: </v-card-title>
                <v-chip
                  class="white--text mt-4 mr-auto"
                  :color="getOperationalStatusColor(operation_status)"
                >
                  {{ operation_status }}
                </v-chip>
                <v-btn
                    @click="refreshStatus()"
                    color="green"
                    light
                    class="mx-4 mt-3 white--text"
                    :disabled="!doneSyncStatus"
                  >
                    Check
                  </v-btn>
            </v-row>

            <v-card color="red" v-if="issue_NoAvailableStations"
            >
              <v-card-title class="white--text">No available charging stations, please verify and pair.</v-card-title>
            </v-card>

            <v-card color="red" v-if="issue_ExistingErrors"
            >
              <v-card-title class="white--text">Existing Errors, please check and resolve through dashboard error logs below.</v-card-title>
            </v-card>

            <v-divider v-if="issue_BMS" class="py-1"></v-divider>

            <v-card color="orange" v-if="issue_BMS"              
            > 
              <v-card-title class="white--text">Possible BMS Issue</v-card-title>
              <v-col>
                <v-row class="py-1" v-for="skycar in issuedata_BMS" :key="skycar.device_id">
                  <v-chip class="px-2 ml-4 white--text font-weight-bold text-h6" color="yellow darken-4">
                    Skycar {{ skycar.device_id }} - {{ skycar.battery_comment }}
                  </v-chip>
                </v-row>
              </v-col>
            </v-card>
 
            <v-divider v-if="issue_BlockedStation" class="py-1"></v-divider>

            <v-card color="orange" v-if="issue_BlockedStation">
              <v-col>
                <v-row class="py-1" v-for="message in issuedata_BlockedStations" :key="message">
                  <v-chip class="px-2 ml-4 white--text font-weight-bold text-h6" color="yellow darken-4">
                    {{ message }}
                  </v-chip>
                </v-row>
              </v-col>
            </v-card>

            <v-divider v-if="issue_StaleJobs" class="py-1"></v-divider>

            <v-card color="orange" v-if="issue_StaleJobs"
            >
              <v-card-title class="white--text">Possible Stuck Charging Job</v-card-title>
            </v-card>

            <v-divider v-if="issue_SkycarNotTraveling" class="py-1"></v-divider>

            <v-card color="orange" v-if="issue_SkycarNotTraveling"
            >
              <v-card-title class="white--text">Skycar haven't traveled for charging job yet.</v-card-title>
            </v-card>

          </v-col>
        </v-card>

        <v-divider class="py-4"></v-divider>

        <!-- Statistics Info -->
        <v-card light elevation="4">
          <v-col>
            <v-row class="justify-space-between">
              <v-card-title>Data & Metrics</v-card-title>
              <v-btn
                @click="refreshStatistics()"
                color="green"
                light
                class="mx-4 mt-3 white--text"
                :disabled="!doneSyncStatistics"
              >
                Refresh
              </v-btn>
            </v-row>
            <v-progress-linear
              v-if="!doneSyncStatistics"
              color="green"
              indeterminate
            ></v-progress-linear>
            <v-row class="justify-space-between">
              <v-col class="justify-end">
                <p class="pl-2" v-bind:show="data_metrics" :style="paragraphStyle">
                  # of Skycars Connected: {{ data_metrics.connected_skycars }}
                </p>
                <p class="pl-2" v-bind:show="data_metrics" :style="paragraphStyle">
                  # of Total Charging Requests: {{ data_metrics.total_requests }}
                </p>
                <p class="pl-2" v-bind:show="data_metrics" :style="paragraphStyle">
                  Total CM uptime: {{ data_metrics.cm_uptime }}
                </p>
              </v-col>
              <v-col class="justify-end">
                <p class="pl-2" v-bind:show="data_metrics" :style="paragraphStyle">
                  # of Charging Requests Today: {{ data_metrics.requests_today }}
                </p>
                <p class="pl-2" v-bind:show="data_metrics" :style="paragraphStyle">
                  # of Charging Requests per Day: {{ data_metrics.requests_per_day }}
                </p>
                <p class="pl-2" v-bind:show="data_metrics" :style="paragraphStyle">
                  # of Charging Requests per Day per Skycar: {{ data_metrics.requests_per_day_per_skycar }}
                </p>
              </v-col>
            </v-row>
          </v-col>
        </v-card>

        <v-divider class="py-4"></v-divider>

        <!-- Charging Jobs Table -->
        <v-card light elevation="4">
          <v-row class="d-flex px-2">
            <v-card-title class="mr-auto">Charging Jobs</v-card-title>
            <v-row class="d-flex mr-1 justify-end">
              <v-switch class="mx-2" label="Canceled" true-value="false" 
                false-value="true" v-model="filter_JobCanceled">
              </v-switch>
              <v-btn
                @click="downloadExcel()"
                color="orange"
                light
                class="mx-4 mt-3 white--text"
              >
                Download Excel
              </v-btn> 
              <v-btn
                @click="viewJobs()"
                color="green"
                light
                class="mx-4 mt-3 white--text"
                :disabled="!doneSyncJobs"
              >
                Refresh
              </v-btn>
            </v-row>
          </v-row>
          <v-progress-linear
            v-if="!doneSyncJobs"
            color="green"
            indeterminate
          ></v-progress-linear>
          <v-data-table
            :headers="headers_jobs"
            :items="filteredJobs"
            class="elevation-1"
            light
            multi-sort
            :sort-by="['id']"
            :sort-desc="[true]"
            :single-expand="true"
            :expanded.sync="expandedJobs"
            show-expand
          >
            <template v-slot:header.status="{ header }">
              {{ header.text }}
              <v-menu offset-y :close-on-content-click="false">
                <template v-slot:activator="{ on, attrs }">
                  <v-btn icon v-bind="attrs" v-on="on">
                    <v-icon small>
                      mdi-filter
                    </v-icon>
                  </v-btn>
                </template>
                <div class="px-4" style="background-color: white; width:340px">
                  <v-row class="justify-space-around">
                    <v-text-field
                      v-model="filter_JobStatus"
                      class="ml-4 mr-2"
                      type="string"
                      label="Clear"
                    ></v-text-field>
                    <v-btn
                      @click="filter_JobStatus = ''"
                      color="green"
                      class="mt-4 mr-4 ml-2 white--text"
                    >Filter</v-btn>
                  </v-row>
                </div>
              </v-menu>
            </template>
            <template v-slot:header.station="{ header }">
              {{ header.text }}
              <v-menu offset-y :close-on-content-click="false">
                <template v-slot:activator="{ on, attrs }">
                  <v-btn icon v-bind="attrs" v-on="on">
                    <v-icon small>
                      mdi-filter
                    </v-icon>
                  </v-btn>
                </template>
                <div class="px-4" style="background-color: white; width:340px">
                  <v-row class="justify-space-around">
                    <v-text-field
                      v-model="filter_JobStation"
                      class="ml-4 mr-2"
                      type="number"
                      label="Clear"
                    ></v-text-field>
                    <v-btn
                      @click="filter_JobStation = ''"
                      color="green"
                      class="mt-4 mr-4 ml-2 white--text"
                    >Filter</v-btn>
                  </v-row>
                </div>
              </v-menu>
            </template>
            <template v-slot:header.skycar="{ header }">
              {{ header.text }}
              <v-menu offset-y :close-on-content-click="false">
                <template v-slot:activator="{ on, attrs }">
                  <v-btn icon v-bind="attrs" v-on="on">
                    <v-icon small>
                      mdi-filter
                    </v-icon>
                  </v-btn>
                </template>
                <div class="px-4" style="background-color: white; width:340px">
                  <v-row class="justify-space-around">
                    <v-text-field
                      v-model="filter_JobSkycar"
                      class="ml-4 mr-2"
                      type="number"
                      label="Clear"
                    ></v-text-field>
                    <v-btn
                      @click="filter_JobSkycar = ''"
                      color="green"
                      class="mt-4 mr-4 ml-2 white--text"
                    >Filter</v-btn>
                  </v-row>
                </div>
              </v-menu>
            </template>

            <template v-slot:expanded-item="{ headers, item }">
              <td :colspan="headers.length">
                <v-row class="d-flex">
                  <v-btn
                    color="yellow darken-4"
                    class="white--text"
                    @click="resetChargingJob(item.id)"
                    :disabled="!checkJobErrored(item.status)"
                  >
                    Trigger Error for Job {{item.id}}
                  </v-btn>
                  <v-btn
                    color="green"
                    class="mx-4 white--text"
                    @click="restartChargingJob(item.id, item.tc_order_id, item.station, item.skycar)"
                    :disabled="checkJobErrored(item.status)"
                  >
                    Restart Job {{item.id}}
                  </v-btn>
                  <v-btn
                    color="red"
                    class="white--text"
                    @click="cancelChargingJob(item.id)"
                    :disabled="checkJobErrored(item.status)"
                  >
                    Cancel Job {{item.id}}
                  </v-btn>
                  <v-dialog
                    v-model="dialog_clearDockedObstacle"
                    with="400"
                  >
                    <template v-slot:activator="{ dialog_clearDockedObstacle, attrs }">
                      <v-btn
                        color="yellow darken-4"
                        class="mx-4 white--text"
                        @click="clearDockedObstacle(item.skycar)"
                        v-bind="attrs"
                        v-on="dialog_clearDockedObstacle"
                        :disabled="checkJobErrored(item.status)"
                      >
                        Clear Docked Obstacle
                      </v-btn>
                    </template>
                    <v-card light>
                      <v-card-title>Clear Docked Obstacle Response:</v-card-title>
                      <v-card-text v-bind="dialog_clearDockedObstacle_response">
                        {{ dialog_clearDockedObstacle_response }}
                      </v-card-text>
                      <v-card-text>Please note that this function does not automatically disenroll 
                        Skycar and update Skycar status. Also please ensure that this node stays clear.
                      </v-card-text>
                      <v-card-actions>
                        <v-btn
                          color="green"
                          text
                          @click="dialog_clearDockedObstacle = false"
                        >
                        Ok
                        </v-btn>
                      </v-card-actions>
                    </v-card>
                  </v-dialog>
                </v-row>
              </td>
            </template>
            <template v-slot:item.datetime="{ item }">
              {{ convertStringToLocal(item.datetime, true) }}
            </template>
            <template v-slot:item.status="{ item }">
              <v-chip
              class = "white--text"
                :color="getStatusColor(item.status)"
              >
              {{ item.status }}
              </v-chip>
            </template>
            <template v-slot:item.actions="{ item } ">
              <v-tooltip bottom>
                <template v-slot:activator="{ on: tooltip }">
                  <v-icon
                    class="mr-2"
                    v-on="{ ...tooltip }"
                    @click="dialogUndockJob(item.skycar, item.station, item.id)"
                    :disabled="!checkJobErrored(item.status)"
                  >
                    mdi-connection
                  </v-icon>
                </template>
                <span>Undock Skycar {{ item.skycar }} from Station {{ item.station }}</span>
              </v-tooltip>
              <v-tooltip bottom>
                <template v-slot:activator="{ on: tooltip }">
                  <v-icon
                    class="mr-2"
                    v-on="{ ...tooltip }"
                    @click="dialogTriggerJobError(item.skycar, item.station, item.id, item)"
                    :disabled="!checkJobErrored(item.status)"
                  >
                    mdi-alert
                  </v-icon>
                </template>
                <span>Trigger Error for Job {{ item.id }}</span>
              </v-tooltip>
              <v-tooltip bottom>
                <template v-slot:activator="{ on: tooltip }">
                  <v-icon
                    class="mr-2"
                    v-on="{ ...tooltip }"
                    @click="dialogDeleteJob(item.skycar, item.station, item.id)"
                  >
                    mdi-delete
                  </v-icon>
                </template>
                <span>Cancel & Delete Job {{ item.id }}</span>
              </v-tooltip>
            </template>
          </v-data-table>
        </v-card> 

        <v-dialog
          v-model="dialog_ChargingJobUndock"
          width="800"
        >
          <v-card light>
            <v-card-title v-bind:value="[dialog_currentJob_id]">
              Undock Skycar {{ dialog_currentJob_skycar }} from Station {{ dialog_currentJob_station }}?
            </v-card-title>
            <v-card-subtitle v-bind:value="[dialog_currentJob_id]">
              Please note that Charging Job {{  dialog_currentJob_id }} will automatically be cancelled once successfully undocked. 
            </v-card-subtitle>
            <v-card-text v-bind:value="[dialog_currentJob_skycar, dialog_currentJob_station]">
              CM will send a 'STOPCHARGE' command to charging station {{  dialog_currentJob_station }} to turn off charging
              connector and will send an 'UNDOCK' command to Skycar {{ dialog_currentJob_skycar }} to attempt to undock.
            </v-card-text>
            <v-card-actions>
              <v-btn
                color="grey"
                text
                class="px-6"
                @click="dialog_ChargingJobUndock = false"
              >
                Cancel
              </v-btn>
              <v-spacer></v-spacer>
              <v-btn
                color="orange"
                class="white--text px-6 mb-2 mr-2"
                v-bind:value="dialog_CurrentJob_id"
                @click="undockJob(dialog_currentJob_id)"
              >
                Undock Skycar {{ dialog_currentJob_skycar }}
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>

        <v-dialog
          v-model="dialog_ChargingJobError"
          width="800"
        >
          <v-card light>
            <v-card-title v-bind:value="[dialog_currentJob_id]">
              Reset & Trigger Error for Job {{ dialog_currentJob_id }}?
            </v-card-title>
            <v-card-subtitle v-bind:value="[dialog_currentJob_id, dialog_currentJob_skycar, dialog_currentJob_station]">
              Charging Job {{ dialog_currentJob_id }} for Skycar {{ dialog_currentJob_skycar}} 
              at Charging Station {{ dialog_currentJob_station }}.
            </v-card-subtitle>
            <v-card-text>This will set job status to 'ERRORED' and stop any further charging progress. 
              You have two options now - either charge out Skycar, fix charging station and cancel charging job, 
              OR fix in-place and restart charging job from the beginning of the charging process.
              Also, the table has expanded to show you the action buttons 'Restart Job', 'Cancel Job' or 
              'Clear Docked Obstacle'. Note that cancelling job will also automatically remove obstacle.
            </v-card-text>
            <v-card-actions>
              <v-btn
                color="grey"
                text
                class="px-6"
                @click="dialog_ChargingJobError = false"
              >
                Cancel
              </v-btn>
              <v-spacer></v-spacer>
              <v-btn
                v-bind="dialog_currentJob_id"
                color="orange"
                class="white--text px-6 mb-2 mr-2"
                @click="resetChargingJob(dialog_currentJob_id)"
              >
                Confirm Reset & Trigger Error Job {{ dialog_currentJob_id }}
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>

        <v-dialog
          v-model="dialog_ChargingJobDelete"
          width="700"
        >
          <v-card light>
            <v-card-title v-bind:value="[dialog_currentJob_id, dialog_currentJob_skycar, dialog_currentJob_station]">
              Delete & Cancel Charging Job {{ dialog_currentJob_id }}?
            </v-card-title>
            <v-card-subtitle v-bind:value="[dialog_currentJob_id, dialog_currentJob_skycar, dialog_currentJob_station]">
              Charging Job {{ dialog_currentJob_id }} for Skycar {{ dialog_currentJob_skycar }} 
              at Charging Station {{ dialog_currentJob_station }}.
            </v-card-subtitle>
            <v-card-text>Please note that deleting the charging job will also cancel it. This means 
              it will also automatically delete the charging request.
            </v-card-text>
            <v-card-actions>
              <v-btn
                color="grey"
                text
                class="px-6"
                @click="dialog_ChargingJobDelete = false"
              >
                Cancel
              </v-btn>
              <v-spacer></v-spacer>
              <v-btn
                v-bind="dialog_currentJob_id"
                color="red"
                class="white--text px-6 mb-2 mr-2"
                @click="cancelChargingJob(dialog_currentJob_id)"
              >
                Delete & Cancel Job {{ dialog_currentJob_id }}
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>

        <v-divider class="py-4"></v-divider>

        <!-- Charging Requests Table -->
        <v-card light elevation="4">
          <v-row class="d-flex px-2 justify-space-between">
            <v-card-title>Charging Requests</v-card-title>
            <v-row class="d-flex mr-1 justify-end">
              <v-switch class="mx-2" label="Canceled" true-value="false" 
                false-value="true" v-model="filter_RequestCanceled">
              </v-switch>
              <v-switch class="mx-2" label="Invalid" true-value="false" 
                false-value="true" v-model="filter_RequestInvalid">
              </v-switch>
              <v-btn
                @click="viewRequests()"
                color="green"
                light
                class="mx-4 mt-3 white--text"
                :disabled="!doneSyncRequests"
              >
                <span>Refresh</span>
              </v-btn>
            </v-row>
          </v-row>
          <v-progress-linear
            v-if="!doneSyncRequests"
            color="green"
            indeterminate
          ></v-progress-linear>
          <v-data-table
            :headers="headers_requests"
            :items="filteredRequests"
            class="elevation-1"
            light
            multi-sort
            :sort-by="['id']"
            :sort-desc="[true]"
            :single-expand="true"
            :expanded.sync="expanded_requests"
            @item-expanded="onRequestExpanded"
          >
            <template v-slot:header.status="{ header }">
              {{ header.text }}
              <v-menu offset-y :close-on-content-click="false">
                <template v-slot:activator="{ on, attrs }">
                  <v-btn icon v-bind="attrs" v-on="on">
                    <v-icon small>
                      mdi-filter
                    </v-icon>
                  </v-btn>
                </template>
                <div class="px-4" style="background-color: white; width:340px">
                  <v-row class="justify-space-around">
                    <v-text-field
                      v-model="filter_RequestStatus"
                      class="ml-4 mr-2"
                      type="string"
                      label="Clear"
                    ></v-text-field>
                    <v-btn
                      @click="filter_RequestStatus = ''"
                      color="green"
                      class="mt-4 mr-4 ml-2 white--text"
                    >Filter</v-btn>
                  </v-row>
                </div>
              </v-menu>
            </template>
            <template v-slot:header.job_id="{ header }">
              {{ header.text }}
              <v-menu offset-y :close-on-content-click="false">
                <template v-slot:activator="{ on, attrs }">
                  <v-btn icon v-bind="attrs" v-on="on">
                    <v-icon small>
                      mdi-filter
                    </v-icon>
                  </v-btn>
                </template>
                <div class="px-4" style="background-color: white; width:340px">
                  <v-row class="justify-space-around">
                    <v-text-field
                      v-model="filter_RequestJobId"
                      class="ml-4 mr-2"
                      type="string"
                      label="Clear"
                    ></v-text-field>
                    <v-btn
                      @click="filter_RequestJobId = ''"
                      color="green"
                      class="mt-4 mr-4 ml-2 white--text"
                    >Filter</v-btn>
                  </v-row>
                </div>
              </v-menu>
            </template>
            <template v-slot:header.skycar="{ header }">
              {{ header.text }}
              <v-menu offset-y :close-on-content-click="false">
                <template v-slot:activator="{ on, attrs }">
                  <v-btn icon v-bind="attrs" v-on="on">
                    <v-icon small>
                      mdi-filter
                    </v-icon>
                  </v-btn>
                </template>
                <div class="px-4" style="background-color: white; width:340px">
                  <v-row class="justify-space-around">
                    <v-text-field
                      v-model="filter_RequestSkycar"
                      class="ml-4 mr-2"
                      type="string"
                      label="Clear"
                    ></v-text-field>
                    <v-btn
                      @click="filter_RequestSkycar = ''"
                      color="green"
                      class="mt-4 mr-4 ml-2 white--text"
                    >Filter</v-btn>
                  </v-row>
                </div>
              </v-menu>
            </template>
            <template v-slot:header.station="{ header }">
              {{ header.text }}
              <v-menu offset-y :close-on-content-click="false">
                <template v-slot:activator="{ on, attrs }">
                  <v-btn icon v-bind="attrs" v-on="on">
                    <v-icon small>
                      mdi-filter
                    </v-icon>
                  </v-btn>
                </template>
                <div class="px-4" style="background-color: white; width:340px">
                  <v-row class="justify-space-around">
                    <v-text-field
                      v-model="filter_RequestStation"
                      class="ml-4 mr-2"
                      type="string"
                      label="Clear"
                    ></v-text-field>
                    <v-btn
                      @click="filter_RequestStation = ''"
                      color="green"
                      class="mt-4 mr-4 ml-2 white--text"
                    >Filter</v-btn>
                  </v-row>
                </div>
              </v-menu>
            </template>
            <template v-slot:expanded-item="{ headers, item }">
              <td :colspan="headers.length">
                <v-stepper v-bind="job_current">
                  <v-stepper-header
                  class="justify-start">
                    <template v-for="st in job_process">
                      <v-divider
                        v-if="st.id < (job_process.length-1)"
                        :key="`${st.id}${item.id}div`">
                      </v-divider>
                    </template>
                  </v-stepper-header>
                </v-stepper>
              </td>
            </template>
            <template v-slot:item.datetime="{ item }">
              {{ convertStringToLocal(item.datetime, true) }}
            </template>
            <template v-slot:item.status="{ item }">
              <v-chip
                class="white--text"
                :color="getStatusColor(item.status)"
              >
              {{ item.status }}
              </v-chip>
            </template>
            <template v-slot:item.actions="{ item }">
              <v-tooltip bottom>
                <template v-slot:activator="{ on: tooltip }">
                  <v-icon
                    class="mr-2"
                    v-on="{ ...tooltip }"
                    @click="dialogDeleteRequest(item.skycar, item.station, item.id)"
                  >
                    mdi-delete
                  </v-icon>
                </template>
                <span>Delete Request {{item.id }}</span>
              </v-tooltip>
            </template>
          </v-data-table>
        </v-card>

        <v-dialog
          v-model="dialog_ChargingRequestDelete"
          width="700"
        >
          <v-card light v-bind:value="[dialog_currentRequest_id, 
            dialog_currentRequest_skycar, dialog_currentRequest_station]">
            <v-card-title>Delete Charging Request {{ dialog_currentRequest_id }}?</v-card-title>
            <v-card-subtitle v-if="dialog_currentRequest_station != undefined">
              Charging Request {{ dialog_currentRequest_id }} for Skycar {{ dialog_currentRequest_skycar }} 
              and Station {{ dialog_currentRequest_station }}.
            </v-card-subtitle>
            <v-card-subtitle v-if="dialog_currentRequest_station == undefined">
              Charging Request {{ dialog_currentRequest_id }} for Skycar {{ dialog_currentRequest_skycar }}.
            </v-card-subtitle>
            <v-card-text>Please note that this won't automatically delete the charging job (if already created). 
              Also, note that if a station is already assigned and reserved, please remember to manually set 
              its status as available using the 'Set Available' button in the 'Charging Stations' table below.
            </v-card-text>
            <v-card-actions>
              <v-btn 
                color="grey"
                text
                class="px-6"
                @click="dialog_ChargingRequestDelete = false"
              >
                Cancel
              </v-btn>
              <v-spacer></v-spacer>
              <v-btn
                v-bind="{ dialog_currentRequest_id }"
                color="red"
                class="white--text px-6 mb-2 mr-2" 
                @click="deleteRequest(dialog_currentRequest_id)"
              >
                Delete Request {{ dialog_currentRequest_id }}
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>

        <v-divider class="py-4"></v-divider>

        <!-- Charging Stations Table -->
        <v-card light elevation="4">
          <v-row class="d-flex px-2 justify-space-between">
            <v-card-title>Charging Stations</v-card-title>
            <v-btn
              @click="viewStations()"
              color="green"
              light
              class="mx-4 mt-3 white--text"
              :disabled="!doneSyncStations"
            >
              <span>Refresh</span>
            </v-btn>
          </v-row>
          <v-progress-linear
            v-if="!doneSyncStations"
            color="green"
            indeterminate
          ></v-progress-linear>
          <v-data-table
            :headers="headers_stations"
            :items="items_stations"
            class="elevation-1"
            sort-by="is_paired"
            :sort-desc="true"
          >
            <template v-slot:item.status="{ item }">
              <v-chip
                class="white--text"
                :color="getStationStatusColor(item.status)"
              >
              {{ item.status }}
              </v-chip>
            </template>
            <template v-slot:item.hardware_status="{ item }">
              <v-chip
                class="white--text"
                :color="getStationHardwareStatusColor(item.hardware_status)"
              >
              {{ item.hardware_status }}
              </v-chip>
            </template>
            <template v-slot:item.is_paired="{ item }">
              <v-chip
                class="white--text"
                :color="getPairedColor(item.is_paired)"
              >
              {{ item.is_paired }}
              </v-chip>
            </template>
            <template v-slot:item.is_available="{ item }">
              <v-chip
                class="white--text"
                :color="getTrueFalseColor(item.is_available)"
              >
              {{ item.is_available }}
              </v-chip>
            </template>
            <template v-slot:item.is_operational="{ item }">
              <v-chip
                class="white--text"
                :color="getTrueFalseColor(item.is_operational)"
              >
              {{ item.is_operational }}
              </v-chip>
            </template>
            <template v-slot:item.is_blocked="{ item }">
              <v-chip
                class="white--text"
                :color="getTrueFalseColor(!item.is_blocked)"
              >
              {{ item.is_blocked }}
              </v-chip>
            </template>
            <template v-slot:item.actions="{ item }">
              <v-tooltip bottom>
                <template v-slot:activator="{ on: tooltip }">
                  <v-icon
                    class="mr-2"
                    v-on="{ ...tooltip }"
                    @click="dialogSetStationAvailable(item.device_id)"
                  >
                    mdi-check-circle
                  </v-icon>
                </template>
                <span>Set 'Available' CS{{ item.device_id }}</span>
              </v-tooltip>
              <v-tooltip bottom>
                <template v-slot:activator="{ on: tooltip }">
                  <v-icon
                    class="mr-2"
                    v-on="{ ...tooltip }"
                    @click="requestStationStatus(item.device_id)"
                  >
                    mdi-refresh-circle
                  </v-icon>
                </template>
                <span>Request Status CS{{ item.device_id }}</span>
              </v-tooltip>
              <v-tooltip bottom>
                <template v-slot:activator="{ on: tooltip }">
                  <v-icon
                    class="mr-2"
                    v-on="{ ...tooltip }"
                    @click="dialogStartStationChargingTimer(item.device_id)"
                  >
                    mdi-timer
                  </v-icon>
                </template>
                <span>Start Charging Timer CS{{ item.device_id }}</span>
              </v-tooltip>
              <v-tooltip bottom>
                <template v-slot:activator="{ on: tooltip }">
                  <v-icon
                    class="mr-2"
                    v-on="{ ...tooltip }"
                    @click="dialogSetStationMaintenance(item.device_id, item.under_maintenance)"
                  >
                    mdi-cog-box
                  </v-icon>
                </template>
                <span>Toggle Maintenance CS{{ item.device_id }}</span>
              </v-tooltip>
              <v-tooltip bottom>
                <template v-slot:activator="{ on: tooltip }">
                  <v-icon
                    class="mr-2"
                    v-on="{ ...tooltip }"
                    color="red"
                    @click="dialogResetChargingStation(item.device_id)"
                  >
                    mdi-cog-refresh
                  </v-icon>
                </template>
                <span>Reset CS{{ item.device_id }}</span>
              </v-tooltip>
            </template>
          </v-data-table>
        </v-card>

        <v-dialog v-model="dialog_setStationAvailable" width="700">
          <v-card light v-bind:value="[dialog_setStationAvailable_id]">
            <v-card-title>Manually set station {{ dialog_setStationAvailable_id }} as 'Available'?</v-card-title>
            <v-card-text>This will immediately make this station available to 
              be reserved for current charging requests or newly-created ones.
            </v-card-text>
            <v-card-actions>
              <v-btn
                color="grey"
                text
                class="px-6"
                @click="dialog_setStationAvailable = false"
              >
                Cancel
              </v-btn>
              <v-spacer></v-spacer>
              <v-btn
                v-bind="{ dialog_setStationAvailable_id }"
                color="green"
                class="white--text px-6 mb-2 mr-2"
                @click="setStationAvailable(dialog_setStationAvailable_id)"
              >
                Confirm Set Available Station {{ dialog_setStationAvailable_id }}
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>

        <v-dialog v-model="dialog_resetStation" width="700">
          <v-card light color="yellow" v-bind:value="[dialog_resetStation_id]">
            <v-card-title>Send RESET command to Charging Station {{ dialog_resetStation_id }}?</v-card-title>
            <v-card-text>This will send "SB,{{ dialog_resetStation_id }},RESET;" 
              and force hardware to auto-recover after Skycar release solenoid error. Reminder that 
              this reset command can only be used for Skycar Solenoid Release Error only.
            </v-card-text>
            <v-card-actions>
              <v-btn
                color="grey"
                text
                class="px-6"
                @click="dialog_resetStation = false"
              >
                Cancel
              </v-btn>
              <v-spacer></v-spacer>
              <v-btn
                v-bind="{ dialog_resetStation_id }"
                color="orange"
                class="white--text px-6 mb-2 mr-2"
                @click="resetChargingStation(dialog_resetStation_id)"
              >
                Confirm RESET for CS{{ dialog_resetStation_id }}
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>

        <v-dialog v-model="dialog_startStationChargingTimer" width="700">
          <v-card light v-bind:value="[dialog_startStationChargingTimer_id, config_current_charging_timer]">
            <v-card-title>Start charging timer for station {{ dialog_startStationChargingTimer_id }}?</v-card-title>
            <v-card-text>This will set station {{ dialog_startStationChargingTimer_id }} to 
              'Charging' status for {{ config_current_charging_timer }} minutes and make it 
              unavailable to be used for any incoming charging requests.
            </v-card-text>
            <v-card-actions>
              <v-btn
                color="grey"
                text
                class="px-6"
                @click="dialog_startStationChargingTimer = false"
              >
                Cancel
              </v-btn>
              <v-spacer></v-spacer>
              <v-btn
                v-bind="{ dialog_startStationChargingTimer_id }"
                color="green"
                class="white--text px-6 mb-2 mr-2"
                @click="startStationChargingTimer(dialog_startStationChargingTimer_id)"
              >
                Confirm Set Charging Timer Station {{ dialog_startStationChargingTimer_id }}
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>

        <v-dialog v-model="dialog_setStationMaintenance" width="700">
          <v-card light v-bind:value="{ dialog_setStationMaintenance_id, 
            dialog_setStationMaintenance_enabled, dialog_setStationMaintenance_enabledtarget }">
            <v-card-title v-if="dialog_setStationMaintenance_enabled == 'true'">
              Set Charging Station {{ dialog_setStationMaintenance_id }} back to Operational?
            </v-card-title>
            <v-card-title v-if="dialog_setStationMaintenance_enabled == 'false'">
              Set Charging Station {{ dialog_setStationMaintenance_id }} under Maintenance?
            </v-card-title>
            <v-card-actions>
              <v-btn
                color="grey"
                text
                class="px-6"
                @click="dialog_setStationMaintenance = false"
              >
                Cancel
              </v-btn>
              <v-spacer></v-spacer>
              <v-btn
                v-bind="{ dialog_setStationMaintenance_id, dialog_setStationMaintenance_enabledtarget }"
                color="green"
                class="white--text px-6 mb-2 mr-2"
                @click="setStationMaintenance(dialog_setStationMaintenance_id, 
                  dialog_setStationMaintenance_enabledtarget)"
              >
                Confirm
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>
          
        <v-divider class="py-4"></v-divider>

        <!-- Skycars Table -->
        <v-card light elevation="4">
          <v-row class="d-flex px-2 justify-space-between">
            <v-card-title>Skycars</v-card-title>
            <v-btn
              @click="viewSkycars()"
              color="green"
              light
              class="mx-4 mt-3 white--text"
              :disabled="!doneSyncSkycars"
            >
              <span>Refresh</span>
            </v-btn>
          </v-row>
          <v-progress-linear
            v-if="!doneSyncSkycars"
            color="green"
            indeterminate
          ></v-progress-linear>
          <v-data-table
            :headers="headers_skycars"
            :items="items_skycars"
            :items-per-page="25"
            class="elevation-1"
            sort-by="is_paired"
            :sort-desc="true"
          >
            <template v-slot:item.cm_status="{ item }">
              <v-chip
                class="white--text"
                :color="getCMStatusColor(item.cm_status)"
              >
              {{ item.cm_status }}
              </v-chip>
            </template>
            <template v-slot:item.charging_enabled="{ item }">
              <v-chip
                class="white--text"
                :color="getChargingEnabledColor(item.charging_enabled)"
              >
              {{ item.charging_enabled }}
              </v-chip>
            </template>
            <template v-slot:item.battery_history="{ item }">
                <v-sparkline
                  :value="nullHandleArray(item.battery_history)"
                  :show-labels="true"
                  label-size="14"
                  color="green"
                  line-width="3"
                  padding="8"
                ></v-sparkline>
            </template>
            <template v-slot:item.actions="{ item }">
              <v-tooltip bottom>
                <template v-slot:activator="{ on: tooltip }">
                  <v-icon
                    class="mr-2"
                    v-on="{ ...tooltip }"
                    @click="dialogChargeSkycar(item.device_id, item.cube, item.orientation, item.charging_mode)"
                  >
                    mdi-battery-charging
                  </v-icon>
                </template>
                <span>Charge SC{{ item.device_id }}</span>
              </v-tooltip>
              <!-- <v-tooltip bottom>
                <template v-slot:[`activator`]="{ on: tooltip }">
                  <v-icon
                    class="mr-2"
                    v-on="{ ...tooltip }"
                    @click="requestSkycarStatus(item.device_id)"
                  >
                    mdi-refresh-circle
                  </v-icon>
                </template>
                <span>Request Status SC{{ item.device_id }}</span>
              </v-tooltip> -->
              <v-tooltip bottom>
                <template v-slot:activator="{ on: tooltip }">
                  <v-icon
                    class="mr-2"
                    v-on="{ ...tooltip }"
                    @click="dialogChargingEnabledSkycar(item.device_id, item.cube, item.charging_enabled)"
                  >
                    mdi-toggle-switch
                  </v-icon>
                </template>
                <span>Toggle Charging Enabled SC{{ item.device_id }}</span>
              </v-tooltip>
            </template>
          </v-data-table>
        </v-card>

        <v-dialog v-model="dialog_chargeSkycar" width="700">
          <v-card light v-bind:value="[ dialog_chargeSkycar_id, 
            dialog_chargeSkycar_cube, dialog_chargeSkycar_orientation, dialog_chargeSkycar_mode ]">
            <v-card-title>Create a charging request for Skycar {{ dialog_chargeSkycar_id }}?</v-card-title>
            <v-card-subtitle>Charging request details: Skycar {{ dialog_chargeSkycar_id }}, 
              Cube '{{ dialog_chargeSkycar_cube }}', Orientation '{{ dialog_chargeSkycar_orientation }}', 
              Mode '{{ dialog_chargeSkycar_mode }}'
            </v-card-subtitle>
            <v-card-text>This will create a charging request for the Skycar, 
              but will still check for charging station availability. 
              If want to immediately charge, then can manually set a charging station as 
              'Available' after creating this request.
            </v-card-text>
            <v-card-actions>
              <v-btn
                color="grey"
                text
                class="px-6"
                @click="dialog_chargeSkycar = false"
              >
                Cancel
              </v-btn>
              <v-spacer></v-spacer>
              <v-btn
                v-bind="{ dialog_chargeSkycar_id, dialog_chargeSkycar_cube, 
                  dialog_chargeSkycar_orientation, dialog_chargeSkycar_mode }"
                color="green"
                class="white--text px-6 mb-2 mr-2"
                @click="chargeSkycar(dialog_chargeSkycar_id, dialog_chargeSkycar_cube, 
                  dialog_chargeSkycar_orientation, dialog_chargeSkycar_mode)"
              >
                Confirm Charge SC{{ dialog_chargeSkycar_id }}
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>

        <v-dialog v-model="dialog_chargingEnabledSkycar" max-width="700">
          <v-card light v-bind:value="{ dialog_chargingEnabledSkycar_id, 
            dialog_chargingEnabledSkycar_cube, dialog_chargingEnabledSkycar_current,
            dialog_chargingEnabledSkycar_target }">
            <v-card-title v-if="dialog_chargingEnabledSkycar_current == 'Enabled'">
              Disable Charging Feature for Skycar {{ dialog_chargingEnabledSkycar_id }}?
            </v-card-title>
            <v-card-title v-if="dialog_chargingEnabledSkycar_current == 'Disabled'">
              Enable Charging Feature for Skycar {{ dialog_chargingEnabledSkycar_id }}?
            </v-card-title>
            <v-card-subtitle>Currently Skycar {{ dialog_chargingEnabledSkycar_id }} is set 
              to {{ dialog_chargingEnabledSkycar_current }}, will set to {{ dialog_chargingEnabledSkycar_target }}.
            </v-card-subtitle>
            <v-card-text>All Skycars will be charging-enabled on default and will be set 
              upon first pairing. However, if Skycar {{ dialog_chargingEnabledSkycar_id }} is for some 
              reason unable to use charging infrastructure, then can manually disable.
            </v-card-text>
            <v-card-actions>
              <v-btn
                color="grey"
                text
                class="px-6"
                @click="dialog_chargingEnabledSkycar = false"
              >
                Cancel
              </v-btn>
              <v-spacer></v-spacer>
              <v-btn
                v-bind="{ dialog_chargingEnabledSkycar_id, 
                  dialog_chargingEnabledSkycar_cube, dialog_chargingEnabledSkycar_target }"
                color="green"
                class="white--text px-6 mb-2 mr-2"
                @click="toggleChargingEnabledSkycar(dialog_chargingEnabledSkycar_id, 
                  dialog_chargingEnabledSkycar_cube, dialog_chargingEnabledSkycar_target)"
              >
                Confirm Set SC{{dialog_chargingEnabledSkycar_id}} to {{ dialog_chargingEnabledSkycar_target }}
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>

        <v-divider class="py-4"></v-divider>

        <!-- Debugging Card -->
        <v-card light elevation="4">
          <v-card-title>Debugging</v-card-title>
          <v-card-subtitle>Manually send TCP messages, and look at last 15 lines of log output.</v-card-subtitle>
          <v-card-actions>
            <v-btn
              text
              color="yellow darken-4"
              @click="showManualTCP = !showManualTCP"
            >
              Send TCP Message
              <v-icon>{{ showManualTCP ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon>
            </v-btn>
            <v-spacer></v-spacer>
            <v-btn
              text
              color="yellow darken-4"
              @click="showTCPLog = !showTCPLog"
            >
              Show TCP Logs
              <v-icon>{{ showTCPLog ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon>
            </v-btn>
            <v-btn
              text
              color="yellow darken-4"
              @click="showEventLog = !showEventLog"
            >
              Show Event Logs
              <v-icon>{{ showEventLog ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon>
            </v-btn>
          </v-card-actions>

          <v-expand-transition>
            <div v-show="showManualTCP">
              <v-divider></v-divider> 
              <v-col class="pr-2 pl-10 mt-5 justify-space-between">
                <v-row class="pb-4 justify-space-between">
                  <v-row class="justify-start">
                    <h2 :style="subtitleStyle">Connected Clients:</h2>
                    <span class="px-2"></span>
                    <v-chip-group
                      class="mt-n1"
                      active-class="primary--text"
                      v-model="current_client"
                      mandatory
                      column
                    >
                      <v-chip
                        class="px-5"
                        v-for="client in items_clients"
                        :key="client.socket_id"
                        :value="client"
                      >
                        <v-icon v-if="client.type === 'Skycar'" left>
                          mdi-taxi
                        </v-icon>
                        <v-icon v-if="client.type === 'Station'" left>
                          mdi-ev-station
                        </v-icon> 
                        <v-icon v-if="client.type === 'Gateway'" left>
                          mdi-radio-tower
                        </v-icon>
                        {{ client.tag }}
                      </v-chip>
                    </v-chip-group>
                  </v-row>
                  <v-spacer></v-spacer>
                  <v-btn
                    @click="viewClients()"
                    color="green"
                    light
                    class="mx-5 mt-1 white--text"
                    :disabled="!doneSyncClients"
                  >
                    Refresh
                  </v-btn>
                </v-row>
                <span class="ml-n5" ></span>
                <v-row class="ml-n8 mr-1 mt-n2 mb-n4 justify-space-between">
                  <v-col cols="auto">
                    <h2 class="pb-3" :style="subtitleStyle">Client Details:</h2>
                    <p class="pl-2" v-bind:show="current_client" v-if="current_client !== null" 
                      :style="paragraphStyle">Type: {{ current_client.type }}
                    </p>
                      <p class="pl-2" v-bind:show="current_client" v-if="current_client !== null" 
                      :style="paragraphStyle">{{ current_client.desc }}
                    </p>
                      <p class="pl-2" v-bind:show="current_client" v-if="current_client !== null" 
                      :style="paragraphStyle">TCP/IP Address at - {{ current_client.host }}:{{ current_client.port }}
                    </p>
                  </v-col>
                  <v-col class="justify-end" cols="7">
                    <v-text-field
                      v-model="current_tcp_message"
                      label="TCP Message"
                      placeholder="SC,1,S;"
                      :ref="current_tcp_message"
                      :rules="[() => !!current_tcp_message || `Message shouldn't be empty`]"
                    ></v-text-field> 
                    <v-row class="justfy-end">
                      <v-spacer></v-spacer>
                      <v-dialog
                        v-model="dialog_TCP"
                        width="800"
                      >
                        <template v-slot:activator="{ dialog_TCP, attrs }">
                          <v-btn
                            @click="sendTCPMessage()"
                            color="blue"
                            light
                            class="white--text mr-2"
                            v-bind="attrs"
                            v-on="dialog_TCP"
                          >
                          Send
                          </v-btn>
                        </template>
                        <v-card light>
                          <v-card-title>Response</v-card-title>
                          <v-card-text v-bind="dialog_TCP_response">{{ dialog_TCP_response }}</v-card-text>
                          <v-card-actions>
                            <v-spacer></v-spacer>
                            <v-btn
                              color="green"
                              text
                              @click="dialog_TCP = false"
                            >
                            Ok
                            </v-btn>
                          </v-card-actions>
                        </v-card>
                      </v-dialog>
                    </v-row>
                  </v-col>
                </v-row>
              </v-col>
            </div>
          </v-expand-transition>

          <v-expand-transition>
            <div v-show="showTCPLog">
              <v-divider></v-divider>
              <v-row class="d-flex px-2 py-5 justify-space-between">
                <v-col>
                  <v-row class="d-flex px-2 mb-1 justify-space-between">
                    <p
                      class="pl-2"
                      :style="subtitleStyle"
                    >
                      TCP Logs
                    </p>
                    <v-btn
                      @click="refreshTCPLogs()"
                      color="green"
                      light
                      class="mx-3 mt-2 white--text"
                      :disabled="!doneSyncTCPLogs"
                    >
                      <span>Refresh</span>  
                    </v-btn>
                  </v-row>
                <p 
                  class="pl-2"
                  :style="tcpLogStyle"
                  v-for="log in tcp_logs"
                  :key="log.timestamp+log.message"
                >
                  {{ convertStringToLocal(log.timestamp) }} - {{log.sender}}>{{log.receiver}} : {{log.message}}
                </p>
                </v-col>
              </v-row>
            </div>
          </v-expand-transition>

          <v-expand-transition>
            <div v-show="showEventLog">
              <v-divider></v-divider>
              <v-row class="d-flex px-2 py-5 justify-space-between">
                <v-col>
                  <v-row class="d-flex px-2 mb-1 justify-space-between">
                    <p
                      class="pl-2"
                      :style="subtitleStyle"
                    >
                      Event Logs
                    </p>
                    <v-btn
                      @click="refreshEventLogs()"
                      color="green"
                      light
                      class="mx-3 mt-2 white--text"
                      :disabled="!doneSyncEventLogs"
                    >
                      <span>Refresh</span>  
                    </v-btn>
                  </v-row>
                  <p
                    class="pl-2"
                    :style="tcpLogStyle"
                    v-for="log in event_logs"
                    :key="log.timestamp+log.message"
                  >
                    {{ convertStringToLocal(log.timestamp) }} - {{ log.message }}  
                  </p>
                </v-col>
              </v-row>
            </div>
          </v-expand-transition>
        </v-card>

        <v-divider class="py-4"></v-divider>

        <!-- Manual Entry / Debugging Forms -->
        <v-card light elevation="4" color="grey lighten-3">
          <v-card-title>Manual Mode</v-card-title>
          <v-card-subtitle>
            For manually creating a charging request, or clearing all current requests & jobs.
          </v-card-subtitle>
          <v-card-actions>
            <v-btn
              text
              color="yellow darken-4"
              @click="showCreateRequest = !showCreateRequest"
            >
              Create New Charging Request
              <v-icon>{{ showCreateRequest ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon>
            </v-btn>
            <v-spacer></v-spacer>
            <v-btn
              text
              color="red"
              @click="showStationAvailable = !showStationAvailable"
            >
              Set Stations to Available
              <v-icon>{{ showStationAvailable ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon>
            </v-btn>
            <v-btn
              text
              color="red"
              @click="showDeleteRequests = !showDeleteRequests"
            >
              Delete Requests
              <v-icon>{{ showDeleteRequests ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon>
            </v-btn>
            <v-btn
              text
              color="red"
              @click="showDeleteJobs = !showDeleteJobs"
            >
              Delete Jobs
              <v-icon>{{ showDeleteJobs ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon>
            </v-btn>
          </v-card-actions>

          <v-expand-transition>
            <div v-show="showStationAvailable">
              <v-divider></v-divider>
              <v-row class="d-flex px-2 justify-space-between">
                <v-card-title>Confirm set all connected stations to AVAILABLE?</v-card-title>
                <v-btn
                  color="red"
                  class="mx-4 mt-3 white--text"
                  @click="setStationsAvailable()"
                >
                  CONFIRM
                </v-btn>
              </v-row>
              <v-alert
                dismissible
                :value="deleteRequestResponseShow"
                type="warning"
                v-bind:show="deleteRequestResponse"
              >{{deleteRequestResponse}}</v-alert>
            </div>
          </v-expand-transition>

          <v-expand-transition>
            <div v-show="showDeleteRequests">
              <v-divider></v-divider>
              <v-row class="d-flex px-2 justify-space-between">
                <v-card-title>Confirm delete ALL charging requests?</v-card-title>
                <v-btn
                  color="red"
                  class="mx-4 mt-3 white--text"
                  @click="deleteAllRequests()"
                >
                  CONFIRM DELETE ALL REQUESTS
                </v-btn>
              </v-row>
              <v-alert
                dismissible
                :value="deleteRequestResponseShow"
                type="warning"
                v-bind:show="deleteRequestResponse"
              >{{deleteRequestResponse}}</v-alert>
            </div>
          </v-expand-transition>

          <v-expand-transition>
            <div v-show="showDeleteJobs">
              <v-divider></v-divider>
              <v-row class="d-flex px-2 justify-space-between">
                <v-card-title>Confirm delete ALL charging jobs?</v-card-title>
                <v-btn
                  color="red"
                  class="mx-4 mt-3 white--text"
                  @click="deleteAllJobs()"
                >
                  CONFIRM DELETE ALL JOBS
                </v-btn>
              </v-row>
              <v-alert
                dismissible
                :value="deleteJobResponseShow"
                type="warning"
                v-bind:show="deleteJobResponse"
              >{{deleteJobResponse}}</v-alert>
            </div>
          </v-expand-transition>

          <v-expand-transition>
            <div v-show="showCreateRequest">
              <v-divider></v-divider>
              <v-card-title>Creating Charging Request...</v-card-title>
                <div class="mx-4">
                  <v-row class="justify-space-around">
                    <v-col>
                      <p :style="paragraphStyle" v-bind:show="selectedCube">
                        Selected Cube: {{ selectedCube }}
                      </p>
                      <p :style="paragraphStyle" v-bind:show="selectedOrientation">
                        Selected Orientation: {{ selectedOrientation }}</p>
                      <p :style="paragraphStyle" v-bind:show="selectedMode">
                        Selected Mode (of Skycar): {{ selectedMode }}
                      </p>
                      <p :style="paragraphStyle" v-bind:show="inputStationId">
                        Assigned Station ID: {{ inputStationId }}
                      </p>
                      <p :style="paragraphStyle" v-bind:show="inputSkycarId">
                        For Skycar ID: {{ inputSkycarId }}
                      </p>
                      <v-row class="justify-center">
                        <v-btn
                          color="green"
                          class="justify-end white--text"
                          @click="createChargingRequest()"
                        >
                          Create Request
                        </v-btn>
                      </v-row>
                      <p class="mt-4" v-bind:show="createRequestResponse">{{createRequestResponse}}</p>
                      <v-divider class="my-4"></v-divider>
                      <p class="text--disabled">
                        Note: Assigned Station is optional - if empty, 
                        will be automatically assigned later by service.
                      </p>
                    </v-col>
                    <v-col>
                      <v-select
                        v-model="selectedCube"
                        :items="cubes"
                        item-text="cubes"
                        label="Cube"
                      ></v-select>
                      <v-select
                        v-model="selectedOrientation"
                        :items="batteryOrientations"
                        item-text="batteryOrientations"
                        label="Battery Orientation"
                      ></v-select>
                      <v-select
                        v-model="selectedMode"
                        :items="requestCharingModes"
                        item-text="batteryOrientations"
                        label="Skycar Charging Mode"
                      ></v-select>
                      <v-row class="px-2 justify-space-between">
                        <v-text-field
                          type="number"
                          v-model.number="inputStationId"
                          label="Assigned Station ID (Optional)"
                        ></v-text-field>
                        <v-btn
                          color="amber darken-3"
                          class="mx-2 white--text"
                          @click="clearStationId"
                        >
                          Clear
                        </v-btn>
                      </v-row>
                      <v-text-field
                        type="number"
                        v-model.number="inputSkycarId"
                        label="Skycar ID"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </div>
            </div>
          </v-expand-transition>
        </v-card>

        <v-divider class="py-4"></v-divider>

        <!-- Configurations Card -->

        <v-card light elevation="4">
          <v-card-title>Configuration</v-card-title>
          <v-card-subtitle>Change the settings and parameters for charging service.</v-card-subtitle>
          <v-card-actions>
            <v-btn
              text
              color="yellow darken-4"
              @click="expandConfigCard()"
            >
              <span v-if="showConfigurationsCard == true">Collapse</span>
              <span v-if="showConfigurationsCard == false">Expand</span>
              <v-icon>{{ showConfigurationsCard ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon>
            </v-btn>
            <v-spacer></v-spacer>
          </v-card-actions>
          <v-expand-transition>
            <div v-show="showConfigurationsCard">
              <div v-if="configs_loaded">
                <v-divider></v-divider>
                <v-col class="px-2">

                  <!-- Skycar Status Update Time Threshold -->
                  <v-row class="px-4 mt-1 mb-2 justify-space-around">
                    <v-col class="mr-4" cols="5">
                      <h2 class="mb-2" :style="subtitleStyle">Skycar Status Update Time Threshold</h2>
                      <p :style="paragraphMultilineStyle">
                        This will affect how long until charging service will automatically request a 
                        status update from a Skycar. Note that it is still possible to manually request 
                        status by clicking the 'Request Status' button on 'Skycars' table. Default value is 5 minutes.
                      </p>
                    </v-col>
                    <v-col class="mt-4">
                      <p :style="paragraphStyle" v-bind:show="config_current_skycar_status_update_time">
                        Current status time threshold is {{ config_current_skycar_status_update_time }} minutes.
                      </p>
                      <v-text-field
                        type="number"
                        v-model.number="config_target_skycar_status_update_time"
                        label="Number of minutes"
                      ></v-text-field>
                      <v-btn
                        color="green"
                        class="justify-end white--text"
                        @click="updateConfig_SkycarStatusTime()"
                      >
                        Update Time Threshold
                      </v-btn>
                    </v-col>
                  </v-row>

                  <v-divider></v-divider>

                  <!-- Charging Station Status Update Time Threshold -->
                  <v-row class="px-4 mt-1 mb-2 justify-space-around">
                    <v-col class="mr-4" cols="5">
                      <h2 class="mb-2" :style="subtitleStyle">Station Status Update Time Threshold</h2>
                      <p :style="paragraphMultilineStyle">
                        This will affect how long until charging service will automatically request 
                        a status update from a charging station. In addition to this periodic automatic 
                        status request, charging station status will also be requested when a charging 
                        request is available. Note that it is still possible to manually request status 
                        by clicking the 'Request Status' button on 'Charging Stations' table. Default value is 5 minutes.
                      </p>
                    </v-col>
                    <v-col class="mt-4">
                      <p :style="paragraphStyle" v-bind:show="config_current_station_status_update_time">
                        Current status time threshold is {{ config_current_station_status_update_time }} minutes.
                      </p>
                      <v-text-field
                        type="number"
                        v-model.number="config_target_station_status_update_time"
                        label="Number of minutes"
                      ></v-text-field>
                      <v-btn
                        color="green"
                        class="justify-end white--text"
                        @click="updateConfig_StationStatusTime()"
                      >
                        Update Time Threshold
                      </v-btn>
                    </v-col>
                  </v-row>

                  <v-divider></v-divider>

                  <!-- Skycar Pre-Emptive Auto-Request -->
                  <v-row class="px-4 mt-1 mb-2 justify-space-around">
                    <v-col class="mr-4" cols="5">
                      <h2 v-if="config_preemptive_skycar_enabled" class="mb-2" :style="subtitleStyle">
                        Skycar Preemptive-Charging:
                        <v-chip color="green" class="white--text" :style="subtitleStyle">
                          Enabled
                        </v-chip>
                      </h2>
                      <h2 v-if="!config_preemptive_skycar_enabled" class="mb-2" :style="subtitleStyle">
                        Skycar Preemptive-Charging:
                        <v-chip color="red" class="white--text" :style="subtitleStyle">
                          Disabled
                        </v-chip>
                      </h2>
                      <p :style="paragraphMultilineStyle">
                        Charging Service will try to monitor Skycar battery percentages and will 
                        automatically create a charging request for that particular Skycar if the 
                        battery percentage falls below the threshold. Default value is 20%.
                      </p>
                    </v-col>
                    <v-col class="mt-4">
                      <p :style="paragraphStyle" v-bind:show="config_current_skycar_preemptive_charge_percent">
                        Current battery percent threshold is {{ config_current_skycar_preemptive_charge_percent }}%.
                      </p>
                      <v-text-field
                        type="number"
                        v-model.number="config_target_skycar_preemptive_charge_percent"
                        label="Battery percent"
                      ></v-text-field>
                      <v-row class="px-2">
                        <v-btn
                          color="green"
                          class="justify-end white--text"
                          :disabled="!config_preemptive_skycar_enabled"
                          @click="updateConfig_SkycarPreemptivePercentage()"
                        >
                          Update Battery % Threshold
                        </v-btn>
                        <v-spacer></v-spacer>
                        <v-btn
                          v-if="config_preemptive_skycar_enabled"
                          color="red"
                          class="justify-end white--text"
                          @click="updateConfig_SkycarPreemptiveEnable()"
                        >
                          Disable Skycar Preemptive-Charging
                        </v-btn>
                        <v-btn
                          v-if="!config_preemptive_skycar_enabled"
                          color="green"
                          class="justify-end white--text"
                          @click="updateConfig_SkycarPreemptiveEnable()"
                        >
                          Enable Skycar Preemptive-Charging
                        </v-btn>
                      </v-row>
                    </v-col>
                  </v-row>

                  <v-divider></v-divider>

                  <!-- Direct Charge Settings -->
                  <v-row class="px-4 mt-1 mb-2 justify-space-around">
                    <v-col class="mr-4" cols="5">
                      <h2 class="mb2" :style="subtitleStyle">Direct Charge Settings</h2>
                      <p :style="paragraphMultilineStyle" v-bind:show="[config_directcharge_randomizer, config_directcharge_lower_percent, config_directcharge_upper_percent, config_directcharge_default_percent]">
                        Currently Skycars are set to charge up to {{ config_directcharge_default_percent }}% through TCP command but will also be monitored by CM to reach at least {{ config_directcharge_lower_percent }}%.
                        When the Skycar reaches above one of these values, the Skycar will automatically undock and complete the charge.
                      </p>
                    </v-col>
                    <v-col class="mt-4">
                      <v-row class="px-2">
                        <v-btn
                          v-if="!config_directcharge_randomizer"
                          color="green"
                          class="justify-end white--text mb-4"
                          @click="updateConfig_DirectCharge_Randomizer()"
                        >
                          Enable Direct Charge Randomizer 
                        </v-btn>
                        <v-btn
                          v-if="config_directcharge_randomizer"
                          color="red"
                          class="justify-end white--text mb-4"
                          @click="updateConfig_DirectCharge_Randomizer()"
                        >
                          Disable Direct Charge Randomizer 
                        </v-btn>
                      </v-row>
                      <v-row>
                        <v-text-field
                          type="number"
                          class="mt-2 mx-2"
                          :disabled="!config_directcharge_randomizer"
                          v-model.number="temp_config_directcharge_lower_percent"
                          label="Lower % Threshold (Random Lower Bound)"
                          hint="When randomizer is on, minimum value that can be generated."
                        ></v-text-field>
                        <v-text-field
                          type="number"
                          class="mt-2 mx-2"
                          :disabled="!config_directcharge_randomizer"
                          v-model.number="temp_config_directcharge_upper_percent"
                          label="Upper % Threshold (Random Upper Bound)"
                          hint="When randomizer is on, maximum value that can be generated."
                        ></v-text-field>
                      </v-row>
                      <v-row>
                        <v-text-field
                          type="number"
                          class="mt-2 mx-2"
                          :disabled="config_directcharge_randomizer"
                          v-model.number="temp_config_directcharge_default_percent"
                          label="Default Charging % Threshold (Skycar Charge Up to %)"
                          hint="When randomizer is off, will use this value."
                        ></v-text-field>
                      </v-row>
                    </v-col>
                    <v-col class="mt-4">
                      <v-row class="px-2">
                        <v-btn
                          color="blue"
                          class="justify-end white--text mb-4"
                          @click="updateConfig_DirectCharge_Values()"
                        >
                          Update Direct Charge Configs
                        </v-btn>
                        <v-spacer></v-spacer>
                      </v-row>
                      <v-row class="px-2">
                        <v-text-field
                          type="number"
                          class="mt-2 mx-2"
                          v-model.number="temp_config_directcharge_skycar_ping_rate"
                          label="Skycar Ping Status Interval Rate"
                          hint="(interval, default 60 seconds)"
                        ></v-text-field>
                        <v-text-field
                          type="number"
                          class="mt-2 mx-2"
                          v-model.number="temp_config_directcharge_startstop_max_attempts"
                          label="Skycar Total Ping Status Duration"
                          hint="(total duration, default 3600 seconds 1hr)"
                        ></v-text-field>
                      </v-row>
                      <v-row class="px-2">
                        <v-text-field
                          type="number"
                          class="mt-2 mx-2"
                          v-model.number="temp_config_directcharge_startstop_attempt_interval"
                          label="Station StartCharge/StopCharge Attempt Intervals (seconds)"
                          hint="(default 5 seconds)"
                        ></v-text-field>
                        <v-text-field
                          type="number"
                          class="mt-2 mx-2"
                          v-model.number="temp_config_directcharge_startstop_max_attempts"
                          label="Station StartCharge/StopCharge Max Attempts (#)"
                          hint="(default 10 attempts)"
                        ></v-text-field>
                      </v-row>
                    </v-col>
                  </v-row>

                  <v-divider></v-divider>

                  <!-- Swap Charging Timer Config -->
                  <v-row class="px-4 mb-3 justify-space-around">
                    <v-col class="mr-4" cols="5">
                      <h2 class="mb-2" :style="subtitleStyle">Charging Timer Duration</h2>
                      <p :style="paragraphMultilineStyle">
                        This will affect how long a charging station will be under 'charging' 
                        status to allow the battery to be charged back up. During this time, 
                        charging station will be considered unavailable to be used by new incoming 
                        charging requests. Default value is 30 minutes.
                      </p>
                    </v-col>
                    <v-col class="mt-4">
                      <p :style="paragraphStyle" v-bind:show="config_current_charging_timer">
                        Current charging timer duration is {{ config_current_charging_timer }} minutes.
                      </p>
                      <v-text-field
                        type="number"
                        v-model.number="config_target_charging_timer"
                        label="Number of minutes"
                      ></v-text-field>
                      <v-btn
                        color="green"
                        class="justify-end white--text"
                        @click="updateConfig_ChargingTimer()"
                      >
                        Update Charging Timer
                      </v-btn>
                    </v-col>
                  </v-row>

                  <v-divider></v-divider>

                  <!-- Charging Station Pre-Emptive Auto-Request -->
                  <v-row class="px-4 mt-1 mb-2 justify-space-around">
                    <v-col class="mr-4" cols="5">
                      <h2 v-if="config_preemptive_station_enabled" class="mb-2" :style="subtitleStyle">
                        Station Preemptive-Charging: Currently Enabled
                      </h2>
                      <h2 v-if="!config_preemptive_station_enabled" class="mb2" :style="subtitleStyle">
                        Station Preemptive-Charging: Currently Disabled
                      </h2>
                      <p :style="paragraphMultilineStyle">
                        Charging Service will try to maximize charging station utility by always 
                        automatically charging the lowest-battery Skycar when charging stations reach 
                        this battery percentage threshold. This will keep charging stations busy and may 
                        lead to a lot of charging requests. Default value is 95%.
                      </p>
                    </v-col>
                    <v-col class="mt-4">
                      <p :style="paragraphStyle" v-bind:show="config_current_station_preemptive_charge_percent">
                        Current battery percent threshold is {{ config_current_station_preemptive_charge_percent }}%.
                      </p>
                      <v-text-field
                        type="number"
                        v-model.number="config_target_station_preemptive_charge_percent"
                        label="Battery percent"
                      ></v-text-field>
                      <v-row class="px-2">
                        <v-btn
                          color="green"
                          class="justify-end white--text"
                          :disabled="!config_preemptive_station_enabled"
                          @click="updateConfig_StationPreemptivePercentage()"
                        >
                          Update Battery % Threshold
                        </v-btn>
                        <v-spacer></v-spacer>
                        <v-btn
                          v-if="config_preemptive_station_enabled"
                          color="red"
                          class="justify-end white--text"
                          @click="updateConfig_StationPreemptiveEnable()"
                        >
                          Disable Station Preemptive-Charging
                        </v-btn>
                        <v-btn
                          v-if="!config_preemptive_station_enabled"
                          color="green"
                          class="justify-end white--text"
                          @click="updateConfig_StationPreemptiveEnable()"
                        >
                          Enable Station Preemptive-Charging
                        </v-btn>
                      </v-row>
                    </v-col>
                  </v-row>

                  <v-divider></v-divider>

                  <!-- Skycar BMS Auto-Swap -->
                  <v-row class="px-4 mt-1 mb-2 justify-space-around">
                    <v-col class="mr-4" cols="5">
                      <h2 class="mb-2" :style="subtitleStyle">Skycar BMS Tracking & Battery History</h2>
                      <p :style="paragraphMultilineStyle">
                        Charging service will try its best to monitor for potential BMS issues. 
                        "Recent Discharge" will try to find any problem from current Skycar status 
                        to previous status message. "Long Discharge" will try to find any problem from 
                        most recent Skycar status to the oldest Skycar status, according to battery history count.
                      </p>
                      <h2 v-if="config_bms_autoswap" class="mb-2" :style="subtitleStyle">
                        BMS Issue Auto-Swap: Enabled
                      </h2>
                      <h2 v-if="!config_bms_autoswap" class="mb-2" :style="subtitleStyle">
                        BMS Issue Auto-Swap: Disabled
                      </h2>
                    </v-col>
                    <v-col class="mt-4">
                      <v-text-field
                        type="number"
                        v-model.number="config_recent_discharge_min"
                        label="Recent Discharge Min (Default 1%)"
                      ></v-text-field>
                      <v-text-field
                        type="number"
                        v-model.number="config_long_discharge_min"
                        label="Long Discharge Min (Default 5%)"
                      ></v-text-field>
                      <v-divider class="py-2"></v-divider>
                      <v-text-field
                        type="number"
                        v-model.number="config_battery_history_count"
                        label="Long-term battery history (Visualization only) (Default 40)"
                      ></v-text-field>
                      <v-btn
                        color="green"
                        class="justify-end white--text"
                        @click="updateConfig_BMSTracking()"
                      >
                        Update BMS & History Configs
                      </v-btn>
                    </v-col>
                    <v-col class="mt-4">
                      <v-text-field
                        type="number"
                        v-model.number="config_recent_discharge_max"
                        label="Recent Discharge Max (Default 50%)"
                      ></v-text-field>
                      <v-text-field
                        type="number"
                        v-model.number="config_long_discharge_max"
                        label="Long Discharge Max (Default 90%)"
                      ></v-text-field>
                      <v-divider class="py-2"></v-divider>
                      <v-text-field
                        type="number"
                        v-model.number="config_battery_recent_history_count"
                        label="Recent battery history (for BMS tracking, cleared on swap) (Default 10)"
                      ></v-text-field>
                      <v-btn
                        v-if="!config_bms_autoswap"
                        color="green"
                        class="justify-end white--text"
                        @click="updateConfig_BMSAutoSwap()"
                      >
                        Enable Auto-Swap
                      </v-btn>
                      <v-btn
                        v-if="config_bms_autoswap"
                        color="red"
                        class="justify-end white--text"
                        @click="updateConfig_BMSAutoSwap()"
                      >
                        Disable Auto-Swap
                      </v-btn>
                    </v-col>
                  </v-row>

                </v-col>
              </div>
            </div>
          </v-expand-transition>
        </v-card>

        <v-dialog v-model="config_dialog" max-width="600">
          <v-card light v-bind:value="{ config_dialog_text }">
            <v-card-title>Failed to update configuration:</v-card-title>
            <v-card-subtitle>{{ config_dialog_text }}</v-card-subtitle>
            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn
                color="grey"
                text
                class="px-6"
                @click="config_dialog = false"
              >
                OK
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>

        <v-divider class="py-4"></v-divider>

        <!-- Error Logs Card -->
        <v-card light elevation="4">
          <v-card-title>Error Logs</v-card-title>
          <v-card-subtitle>Errors that Charging Module have encountered and logged.</v-card-subtitle>
          <v-card-actions>
            <v-btn
              text
              color="yellow darken-4"
              @click="expandErrorLogCard()"
            >
              <span v-if="showErrorLogsCard == true">Collapse</span>
              <span v-if="showErrorLogsCard == false">Expand</span>
              <v-icon>{{ showErrorLogsCard ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon>
            </v-btn>
            <v-spacer></v-spacer>

            <!-- Export Excel Button -->
            <v-btn   class="mx-4 mt-3 mb-2 white--text" color="blue">  <download-csv 
              :data="items_errorlogs"
              :fields="['job_id','skycar_id','station_id','error_code',
                        'error_name','error_desc','error_note','datetime']"
              :name="getErrorLogExportFileName()"
              >
              <v-icon>mdi-download</v-icon> DOWNLOAD CSV      
            </download-csv></v-btn>

            <!-- Refresh Button -->
            <v-btn
                @click="viewErrorLogs()"
                color="green"
                light
                class="mx-4 mt-3 mb-2 white--text"
                :disabled="!doneSyncErrorLogs"
              >
                Refresh
              </v-btn>
          </v-card-actions>
          <v-expand-transition>
            <div v-show="showErrorLogsCard">
              <v-divider></v-divider>
              <v-progress-linear
                v-if="!doneSyncErrorLogs"
                color="green"
                indeterminate
              ></v-progress-linear>
              <v-data-table
                :headers="headers_errorlogs"
                :items="items_errorlogs"
                class="elevation-1"
                light
                multi-sort
                :sort-by="id"
                :sort-desc="[true]"
              >
                <template v-slot:item.actions="{ item }">
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on: tooltip }">
                      <v-icon
                        class="mr-2"
                        v-on="{ ...tooltip }"
                        @click="dialogResolveError(item.id, item.station_id, item.skycar_id, 
                                                  item.error_name, item.datetime)"
                      >
                        mdi-check-circle
                      </v-icon>
                    </template>
                    <span>Resolve Error {{ item.id }}</span>
                  </v-tooltip>
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on: tooltip }">
                      <v-icon
                        class="mr-2"
                        v-on="{ ...tooltip }"
                        @click="dialogEditError(item.id, item.station_id, item.skycar_id, 
                                                item.error_name, item.datetime)"
                      >
                        mdi-pencil-circle
                      </v-icon>
                    </template>
                    <span>Edit Error {{ item.id }}</span>
                  </v-tooltip>
                </template>
                <template v-slot:item.resolved="{ item }">
                  <v-chip
                    class="white--text"
                    :color="getTrueFalseColor(item.resolved)"
                  >
                  {{ item.resolved }}
                  </v-chip>
                </template>
                <template v-slot:item.datetime="{ item }">
                  {{ convertStringToLocal(item.datetime, true) }}
                </template>
                <template v-slot:item.resolved_at="{ item }">
                  {{ convertStringToLocal(item.resolved_at, true) }}
                </template>
              </v-data-table>
            </div>
          </v-expand-transition>
        </v-card>

        <v-dialog v-model="dialog_resolveError" max-width="600">
          <v-card light v-bind:value="{ dialog_error_id, dialog_error_stationid, dialog_error_skycarid, 
                                        dialog_error_errortype, dialog_error_date }">
            <v-card-title>
              This will mark Error ID {{ dialog_error_id }} ({{ dialog_error_errortype }}) as resolved.
            </v-card-title>
            <v-card-subtitle>
              Error is type {{ dialog_error_errortype }} for Skycar {{ dialog_error_skycarid }} 
              at Charging Station {{ dialog_error_stationid }} at {{ convertStringToLocal(dialog_error_date, true) }}
            </v-card-subtitle>
            <v-row class="px-8 justify-space-between">
              <v-text-field
                v-model="dialog_error_edit_resolvedremarks"
                label="Resolved Remarks"
              ></v-text-field>
            </v-row>
            <v-card-actions>
              <v-btn
                color="grey"
                text
                class="px-6"
                @click="dialog_resolveError = false"
              >
                Cancel
              </v-btn>
              <v-spacer></v-spacer>
              <v-btn
                color="green"
                text
                class="px-6"
                @click="resolveError(dialog_error_id, dialog_error_edit_resolvedremarks)"
              >
                Mark Resolved
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>

        <v-dialog v-model="dialog_editError" max-width="600">
          <v-card light v-bind:value="{ dialog_error_id, dialog_error_stationid, 
                                        dialog_error_skycarid, dialog_error_errortype, dialog_error_date }">
            <v-card-title>Editting Error {{ dialog_error_id }} ({{dialog_error_errortype }}).</v-card-title>
            <v-card-subtitle>
              Error is type {{ dialog_error_errortype }} for Skycar {{ dialog_error_skycarid }} 
              at Charging Station {{ dialog_error_stationid }} at {{ convertStringToLocal(dialog_error_date, true) }}
            </v-card-subtitle>
            <v-col class="px-8 justify-space-between">
              <v-text-field
                v-model="dialog_error_edit_remarks"
                label="Error Cause Remarks"
              ></v-text-field>
              <v-row class="justify-space-between">
                <v-checkbox class="mr-4" v-model="dialog_error_resolved" label="Resolved?"></v-checkbox>
                <v-text-field
                v-model="dialog_error_edit_resolvedremarks"
                label="Resolved Remarks"
              ></v-text-field>
              </v-row>
            </v-col>
            <v-card-actions>
              <v-btn
                color="grey"
                text
                class="px-6"
                @click="dialog_editError = false"
              >
                Cancel
              </v-btn>
              <v-spacer></v-spacer>
              <v-btn
                color="green"
                text
                class="px-6"
                @click="editError(dialog_error_id, dialog_error_edit_remarks, dialog_error_edit_resolvedremarks, 
                                  dialog_error_resolved)"
              >
                Confirm Edits
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>

        <v-dialog v-model="common_response_card" max-width="600">
          <v-card light v-bind:value="{ common_response_title, common_response_desc }">
            <v-card-title>{{ common_response_title }}</v-card-title>
            <v-card-text>{{ common_response_desc }}</v-card-text>
            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn
                color="grey"
                text
                class="px-6"
                @click="common_response_card = false"
              >
                OK
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>

      </v-col>
    </v-container>
  </v-app>
</template>


<script>
import { getCMHost, convertStringToLocal, getCube, getRequestHeader } from "../helper/common";
import { routeCM } from "../helper/enums";
// import { socket_cm } from "../App.vue";
// import { CMWebSocketEvents } from "../helper/enums";
// import AWN from "awesome-notifications";
// import * as JobStub from "../mock/cm/cm"

// =========== API Routes =========== //
const routeRefreshStatistics = "/check/stats"                                            // GET
const routeDownloadExcel = "/export-excel/charging-jobs"                                 // POST

// Operational Health Check //
const routeCheckStatus = "/health-check"

// Charging Jobs //
const routeCheckChargingJobs = "/check/charging-jobs"                                    // GET
const routeDeleteChargingJob = "/cancel/charging-job"                                    // POST
const routeResetChargingJob = "/reset/charging-job"                                      // POST
const routeRestartChargingJob = "/restart/charging-job"                                  // POST
const routeCancelChargingJob = "/cancel/charging-job"                                    // POST
const routeUndockSkycar = "/undock/skycar"

// Charging Requests //
const routeCheckChargingRequests = "/check/charging-requests"                            // GET
const routeDeleteChargingRequests = "/clear/request"                                      // POST

// Charging Stations //
const routeCheckChargingStations = routeCM.CHECK_CHARGING_STATION                        // GET
const routeSetAvailableChargingStation = "/setavailable/station"                         // POST
const routeSetMaintenanceChargingStation = "/setmaintenance/station"                     // POST
const routeRequestStatusChargingStation = "/requeststatus/station"                       // POST
const routeSetTimerChargingStation = "/setcharging/station"                              // POST
const routeResetChargingStation = "/reset/charging-station"                              // POST

// Skycars //
const routeCheckSkycars = "/check/skycars"                                               // GET
const routeChargeSkycar = routeCM.CHARGE_SKYCAR                                          // POST
const routeRequestStatusSkycar = "/requeststatus/skycar"                                 // POST
const routeToggleChargingEnabledSkycar = "/config/skycar-charging-enabled"               // POST

// Error Handling //
const routeResolveError = "/error-logs"
const routeClearDockedObstacle = "/charging-obstacle"                                    // POST
const routeClearAllRequests = "/clear/all/requests"                                      // POST
const routeClearAllJobs = "/clear/all/jobs"                                              // POST
const routeSetAllStationsAvailable = "/clear/connected/stations"                         // POST

// Manual Mode //
const routeCreateChargingRequest = routeCM.CREATE_REQUEST                                // POST

// Debugging //
const routeRefreshTCPLogs = "/check/tcp-logs"                                            // GET
const routeRefreshEventLogs = "/check/event-logs"                                        // GET
const routeViewClients = "/check/tcp-clients"                                            // GET
const routeSendTCPMessage = "/send/tcp-client"                                           // POST
const routeCheckErrorLogs = "/error-logs"                                                // GET

// Configurations //
const routeGetConfigs = "/config/get/all"                                                       // GET
// const routeConfigRequestValidation = "/config/request-validation"                               // GET & POST
// const routeConfigAutoRequest = "/config/auto-request"                                           // GET & POST
const routeConfigDirectCharge = "/config/direct-charge"                                         // GET & POST
const routeUpdateStationChargeTimer = "/config/station-charge-timer"                            // POST
const routeUpdateStationStatusTime = "/config/station-status-update-threshold"                  // POST
const routeUpdateStationPreemptivePercentage = "/config/preemptive-station-swap-percent"        // POST
const routeUpdateStationPreemptiveEnabled = "/config/preemptive-station-swap-enabled"           // POST
const routeUpdateSkycarStatusTime = "/config/skycar-status-update-threshold"                    // POST
const routeUpdateSkycarPreemptivePercentage = "/config/preemptive-skycar-charge-percent"        // POST
const routeUpdateSkycarPreemptiveEnabled = "/config/preemptive-skycar-charge-enabled"           // POST
const routeUpdateBMSTracker = "/config/bms-tracker"

//  config json input to internal variable mapping
const mapping = {
      "station_status_update_threshold":                      "config_current_station_status_update_time",
      "skycar_status_update_threshold":                       "config_current_skycar_status_update_time",

      "preemptive_charge_skycar_percentage":                  "config_current_skycar_preemptive_charge_percent",
      "preemptive_charge_skycar_enabled":                     "config_preemptive_skycar_enabled",

      "skycar_high_battery_threshold":                        "config_pipeline_skycar_high_battery_threshold",
      "skycar_high_battery_threshold_enabled":                "config_pipeline_skycar_high_battery_threshold_enabled",
      "skycar_last_charged_threshold":                        "config_pipeline_last_charged_threshold",
      "skycar_last_charged_threshold_enabled":                "config_pipeline_last_charged_threshold_enabled",
      "station_skycar_check_difference_threshold_enabled":    "config_pipeline_station_skycar_check_difference_threshold_enabled",
      "station_skycar_battery_difference_threshold":          "config_pipeline_station_skycar_check_difference_threshold",

      "preemptive_swap_station_percentage":                   "config_current_station_preemptive_charge_percent",
      "preemptive_swap_station_enabled":                      "config_preemptive_station_enabled",
      "preemptive_swap_station_check_skycar_high_threshold":  'config_preemptive_station_check_skycar_high_threhsold',

      "direct_charge_randomizer":                             "config_directcharge_randomizer",
      "direct_charge_lower_percentage_default":               "config_directcharge_lower_percent",
      "direct_charge_upper_percentage_default":               "config_directcharge_upper_percent",
      "direct_charge_skycar_default_threshold":               "config_directcharge_default_percent",
      "direct_charge_startstop_attempt_interval":             "config_directcharge_startstop_attempt_interval",
      "direct_charge_startstop_max_attempts":                 "config_directcharge_startstop_max_attempts",
      "direct_charge_skycar_ping_rate":                       "config_directcharge_skycar_ping_rate",
      "direct_charge_skycar_ping_duration":                   "config_directcharge_skycar_ping_duration",


      "hibernate_enabled":                                    "config_hibernate_enabled",
      "skycar_hibernate_threshold":                           "config_hibernate_threshold",
      "hibernate_times":                                      "config_hibernate_times_stringified",
      "hibernate_monday_note":                                "config_hibernate_monday_note",
      "hibernate_tuesday_note":                               "config_hibernate_tuesday_note",
      "hibernate_wednesday_note":                             "config_hibernate_wednesday_note",
      "hibernate_thursday_note":                              "config_hibernate_thursday_note",
      "hibernate_friday_note":                                "config_hibernate_friday_note",
      "hibernate_saturday_note":                              "config_hibernate_saturday_note",
      "hibernate_sunday_note":                                "config_hibernate_sunday_note",

      "swap_station_charge_timer":                            "config_current_charging_timer",
      "skycar_battery_history_count":                         "config_battery_history_count",
      "skycar_battery_recent_history_count":                  "config_battery_recent_history_count",
      "skycar_recentdischarge_error_min_threshold":           "config_recent_discharge_min",
      "skycar_recentdischarge_error_max_threshold":           "config_recent_discharge_max",
      "skycar_enddischarge_error_min_threshold":              "config_long_discharge_min",
      "skycar_enddischarge_error_max_threhsold":              "config_long_discharge_max",
      "skycar_bms_issue_autoswap":                            "config_bms_autoswap",
    }

const temp_mapping = {
  "temp_config_pipeline_skycar_high_battery_threshold":               "config_pipeline_skycar_high_battery_threshold",
  "temp_config_pipeline_last_charged_threshold":                      "config_pipeline_last_charged_threshold",
  "temp_config_pipeline_station_skycar_check_difference_threshold":   "config_pipeline_station_skycar_check_difference_threshold",

  "temp_config_directcharge_lower_percent":                           "config_directcharge_lower_percent",
  "temp_config_directcharge_upper_percent":                           "config_directcharge_upper_percent",
  "temp_config_directcharge_default_percent":                         "config_directcharge_default_percent",
  "temp_config_directcharge_startstop_attempt_interval":              "config_directcharge_startstop_attempt_interval",
  "temp_config_directcharge_startstop_max_attempts":                  "config_directcharge_startstop_max_attempts",
  "temp_config_directcharge_skycar_ping_rate":                        "config_directcharge_skycar_ping_rate",
  "temp_config_directcharge_skycar_ping_duration":                    "config_directcharge_skycar_ping_duration"
}



export default {
  name: "App",
  components: {},

  created() {
    // let notifier = new AWN()

    this.refreshStatus()
    this.viewJobs()
    this.viewRequests()
    this.viewStations()
    this.viewSkycars()
    this.viewClients()
    this.refreshStatistics()
    this.refreshTCPLogs()
    this.refreshEventLogs()
    this.getConfigs()
    this.viewErrorLogs()
    // this.getMessage(socket_cm)

    // socket_cm.on(CMWebSocketEvents.HealthCheckUpdate, () => {
    //   this.refreshStatus()
    // })
    // socket_cm.on(CMWebSocketEvents.JobUpdate, () => {
    //   this.viewJobs()
    // })
    // socket_cm.on(CMWebSocketEvents.RequestUpdate, () => {
    //   this.viewRequests()
    // })
    // socket_cm.on(CMWebSocketEvents.SkycarsUpdate, () => {
    //   this.viewSkycars()
    // })
    // socket_cm.on(CMWebSocketEvents.LogTCP, () => {
    //   this.refreshTCPLogs()
    // })
    // socket_cm.on(CMWebSocketEvents.LogEvent, () => {
    //   this.refreshEventLogs()
    // })

    // socket_cm.on('connect', (data) => {
    //   this.notifyConnect(data, notifier)
    // })
    // socket_cm.on(CMWebSocketEvents.Pairing, (data) => {
    //   this.notifyPairing(data, notifier)
    // })
    // socket_cm.on(CMWebSocketEvents.LogTCP, (data) => {
    //   this.updateTCPLogs(data, notifier)
    // })

    // socket_cm.on(CMWebSocketEvents.keepalive, () => {
    //   socket_cm.emit(CMWebSocketEvents.keepalive, "pong")
    // })
  },

  methods: {
    // getMessage: (socket) => {
      // let notifier = new AWN()

      // socket.on('connect', (data) => {
      //   this.notifyConnect(data)
      // })
      // socket.on(CMWebSocketEvents.Pairing, (data) => {
      //   this.notifyPairing(data)
      // })

      // socket.on('connect', function () {
      //   notifier.info("Connected to CM socket!")
      //   console.info("Connected to CM Websocket!")
      // })
      // socket.on(CMWebSocketEvents.Pairing, function(json) {
      //   notifier.info("New client paired! " + json)
      //   console.info(json)
      // })
      // socket.on(CMWebSocketEvents.JobUpdate, function(json) {
      //   console.info(json)
      //   notifier.info("New Charging Job Update")
      // })
      // socket.on(CMWebSocketEvents.LogTCP) = (json_list) => {
      //   console.info(json_list)
      //   if (this.pause_tcp_logs == false) {
      //     console.log("updating tcp logs to: " + json_list)
      //     this.tcp_logs = json_list
      //   }
      // }
      // socket.on(CMWebSocketEvents.LogTCP, function(json_list) {
      //   console.info(json_list)
      //   if (this.pause_tcp_logs == false) {
      //     console.log("updating tcp logs to: " + json_list)
      //     this.tcp_logs = json_list
      //   }
      //   // notifier.info("New TCP Message")
      // })
      // socket.on(CMWebSocketEvents.LogEvent, function(json) {
      //   console.info(json)
      //   notifier.info("New Event Log")
      // })

    getStatusColor(status) {
      if (status == "AVAILABLE") {
        return "green";
      } else if (status == "COMPLETE" || status == "CANCELED" || status == "INVALID") {
        return "grey";
      } else if (status == "ERRORED") {
        return "red";
      } else {
        return "orange";
      }
    },
    getStationStatusColor(status) {
      if (status == "AVAILABLE") {
        return "green";
      } else if (status == "RESERVED") {
        return "blue";
      } else if (status == "OCCUPIED") {
        return "red";
      } else if (status == "RESERVED") {
        return "grey"
      } else if (status == "CHARGING") {
        return "orange"
      }
    },
    getStationHardwareStatusColor(hardwareStatus) {
      if (hardwareStatus == "OPERATIONAL") {
        return "green";
      } else if (hardwareStatus == "ERRORED") {
        return "red";
      } else if (hardwareStatus == "ESTOP") {
        return "red";
      } else if (hardwareStatus == "MAINTENANCE") {
        return "orange"
      } else {
        return "grey"
      }
    },
    getPairedColor(isPaired) {
      if (isPaired == "True") {
        return "green";
      } else if (isPaired == "False") {
        return "grey";
      }
    },
    getTrueFalseColor(bool) {
      if (bool == "true" || bool == "True" || bool == true) {
        return "green";
      } else if (bool == "false" || bool == "False" || bool == false) {
        return "red";
      }
    },
    getCMStatusColor(cmStatus) {
      if (cmStatus == "NORMAL") {
        return "green";
      } else if (cmStatus == "ALERT") {
        return "orange"
      } else if (cmStatus == "UNPAIRED") {
        return "grey"
      }
    },
    onRequestExpanded({ item }) {
      if (item.charging_mode == "Swap") {
        this.job_process = this.jobProcesses.swap
        this.job_current = this.getProcessStatus(item.charging_mode, item.status)
      } else if (item.charging_mode == "Charge") {
        this.job_process = this.jobProcesses.charge
        this.job_current = this.getProcessStatus(item.charging_mode, item.status)
      }
      console.log(`onRequestExpanded - job_current: ${this.job_current}`)
    },
    getProcessStatus(mode, status) {
      let currentStep;
      if (mode == "Swap") {
        switch(status) {
          case "AVAILABLE": currentStep=1; break;
          case "RELEASING": currentStep=2; break;
          case "PULLING": currentStep=3; break;
          case "PUSHING": currentStep=4; break;
          case "SWAPPING": currentStep=5; break;
          case "COMPLETE": currentStep=6; break;
        }
      } else if (mode == "Charge" || mode == "Both") {
        switch(status) {
          case "AVAILABLE": currentStep=1; break;
          case "EXTENDING": currentStep=2; break;
          case "CHARGING": currentStep=3; break;
          case "RETRACTING": currentStep=4; break;
          case "COMPLETE": currentStep=5; break;
        }
      }
      return currentStep
    },
    checkProcessComplete(process_id, currentStep) {
      if (process_id > (currentStep-1)) {
        return false
      } else {
        return true
      }
    },
    clearStationId() {
      this.inputStationId = null
    },
    checkJobErrored(status) {
      if (status == "ERRORED") {
        return false
      } else {
        return true
      }
    },
    getChargingEnabledColor(chargingEnabled) {
      if (chargingEnabled == "Enabled") {
        return "green";
      } else if (chargingEnabled == "Disabled") {
        return "red";
      }
    },
    getOperationalStatusColor(status) {
      if (status == "Operating Normally") {
        return "green";
      } else if (status == "Please check") {
        return "orange"
      }
    },
    getErrorLogExportFileName() {
      const today = new Date().toISOString().slice(0, 10);
      return `cm_error_log_${today}.csv` 
    },
    nullHandleArray(inputArray) {
      if (inputArray && inputArray.length > 0) {
        console.log("nullHandle() Input Valid: " + inputArray + 
        " -- Processed Value is: " + JSON.parse(inputArray).map(Number))

        return JSON.parse(inputArray).map(Number);
      } else {
        console.log("nullHandle() Empty Input, return []")
        return [];
      }
    },

    filterJobStatus(item) {
      return item.status.toLowerCase().includes(this.filter_JobStatus.toLowerCase());
    }





  },

  data: () => ({
    convertStringToLocal,

    // subtitleStyle: ['1rem', '400', '.009375em', 4],
    subtitleStyle: { 
      "font-size": "1.1rem", "font-weight": "500", "font-spacing": ".009375em", "margin": "4"
    },
    subtitleBoldedStyle: {
      "font-size": "1.0rem", "font-weight": "600", "font-spacing": ".009375em", "margin": "4"
    },
    paragraphStyle: {
      "font-size": "0.95rem", "font-weight": "450", "margin": "4", "line-height": "0.6", "opacity": "0.6" 
    },
    paragraphMultilineStyle: {
      "font-size": "0.95rem", "font-weight": "450", "margin": "4", "line-height": "1.2", "opacity": "0.6"
    },
    tcpLogStyle: {
      "font-size": "0.9rem", "font-weight": "400", "margin": "4", "line-height": "0.5", "opacity": "0.9"
    },

    // Common Configurations //
    cubes: getCube(),
    batteryOrientations: ["North", "South"],
    requestCharingModes: ["Both", "Charge", "Swap"],

    // --------- Troubleshooting Info --------- //
    common_response_card: false,
    common_response_title: "",
    common_response_desc: "",

    // --------- Troubleshooting Info --------- //
    showTroubleshootingInfo: false,

    // --------- Health Check and Operational Help --------- //
    doneSyncStatus: true,
    operation_status: "Normal",
    issue_ExistingErrors: false,
    issue_NoAvailableStations: false,
    issue_StaleJobs: false,
    issue_BMS: true,
    issue_SkycarNotTraveling: false,
    issue_BlockedStation: false,
    issuedata_BMS: [],
    issuedata_BlockedStations: [],

    refreshStatus: async function() {
      this.doneSyncStatus = false
      let api_json = await onClickGet(routeCheckStatus)
      this.issuedata_BMS = api_json.bms
      this.issuedata_BlockedStations = api_json.stations_blocked
      this.issue_BMS = (api_json.issue_bms === "true")
      this.issue_ExistingErrors = (api_json.issue_existing_errors === "true")
      this.issue_StaleJobs = (api_json.issue_stale_jobs === "true")
      this.issue_SkycarNotTraveling = (api_json.issue_skycar_not_traveling === "true")
      this.issue_BlockedStation = (api_json.issue_station_blocked === "true")
      this.issue_NoAvailableStations = (api_json.issue_no_available_stations === "true")
      this.operation_status = "Operating Normally"
      if (
        this.issue_BMS == true || 
        this.issue_ExistingErrors == true || 
        this.issue_StaleJobs == true || 
        this.issue_SkycarNotTraveling == true || 
        this.issue_BlockedStation == true ||
        this.issue_NoAvailableStations == true
        ) {
        this.operation_status = "Please check"
      }
      this.doneSyncStatus = true
    },

    // --------- Data and Metrics --------- //
    data_metrics: {
      "connected_skycars": "4",
      "total_requests": "2",
      "cm_uptime": "1",
      "requests_today": "1",
      "requests_per_day": "0.5",
      "requests_per_day_per_skycar": "0.2"
    },
    doneSyncStatistics: false,

    refreshStatistics: async function() {
      this.doneSyncStatistics = false
      let api_json = await onClickGet(routeRefreshStatistics)
      this.data_metrics = api_json.data
      this.doneSyncStatistics = true
    },

    // --------- Charging Jobs --------- //
    expandedJobs: [],
    doneSyncJobs: false,
    dialog_ChargingJobUndock: false,
    dialog_ChargingJobError: false,
    dialog_ChargingJobDelete: false,
    dialog_currentJob_id: undefined,
    dialog_currentJob_skycar: "",
    dialog_currentJob_station: "",
    headers_jobs: [
      {
        text: "Job ID",
        align: "end",
        sortable: true,
        value: "id"
      },
      { text: "Time", value: "datetime", sortable: true },
      // { text: "TC Order ID", value: "tc_order_id"},
      { text: "Station", value: "station" },
      { text: "Skycar", value: "skycar" },
      { text: "Job Status", value: "status" },
      { text: "Actions", value: "actions", sortable: false }
    ],
    filter_JobStatus: "",
    filter_JobStation: "",
    filter_JobSkycar: "",
    filter_JobCanceled: false,
    job_conditions: [],
    search_jobs: "",
    items_jobs: [],

    dialog_clearDockedObstacle: false,
    dialog_clearDockedObstacle_response: "",

    viewJobs: async function () {
      this.doneSyncJobs = false
      console.log("Current switch JobCanceled value is: " + this.filter_JobCanceled)
      let params = new URLSearchParams({ "filter_canceled": this.filter_JobCanceled }).toString()
      let api_json = await onClickGet(routeCheckChargingJobs+"?"+params);
      this.doneSyncJobs = true
      this.items_jobs = api_json.data
      this.refreshStatus();
    },

    dialogTriggerJobError: function(skycarId, stationId, jobId, item) {
      this.dialog_currentJob_id = jobId
      this.dialog_currentJob_skycar = skycarId
      this.dialog_currentJob_station = stationId
      this.expandedJobs.push(item)
      this.dialog_ChargingJobError = true
    },

    dialogDeleteJob: function(skycarId, stationId, jobId) {
      this.dialog_currentJob_id = jobId
      this.dialog_currentJob_skycar = skycarId
      this.dialog_currentJob_station = stationId
      this.dialog_ChargingJobDelete = true
      console.log("dialog CurrentJobID: " + this.dialog_currentJob_id + " SkycarID: " + 
      this.dialog_currentJob_skycar + " StationID: " + this.dialog_currentJob_station)
    },

    dialogUndockJob: function(skycarId, stationId, jobId) {
      this.dialog_currentJob_id = jobId
      this.dialog_currentJob_skycar = skycarId
      this.dialog_currentJob_station = stationId
      this.dialog_ChargingJobUndock = true
      console.log("dialogUndockJob! CurrentJobID: " + this.dialog_currentJob_id + " SkycarID: " + this.dialog_currentJob_skycar + " StationID: " + this.dialog_currentJob_station)
    },

    deleteJob: async function(jobId) {
      var route = new URL(getCMHost() + routeDeleteChargingJob)

      let outbound_json = {
        "job_id": jobId
      }
      await fetch(route, {
        method: "POST",
        headers: getRequestHeader() ,
        body: JSON.stringify(outbound_json)
      }).then(async response => {
        const data = await response.json();
        if (!response.ok) {
          const error = (data) || response.status
          return Promise.reject(error)
        }
        this.dialog_ChargingJobDelete = false
        this.viewJobs()
      })
      .catch(error => {
        console.log(error)
      })
    },

    undockJob: async function(jobId) {
      var route = new URL(getCMHost() + routeUndockSkycar)
      let outbound_json = {
        'job_id': Number(jobId)
      }
      console.log('undockJob! for job_id: ' + Number(jobId))
      await fetch(route, {
        method: "POST",
        headers: getRequestHeader() ,
        body: JSON.stringify(outbound_json)
      }).then(async response => {
        const data = await response.json();
        if (!response.ok) {
          const error = (data) || response.status
          return Promise.reject(error)
        }
        this.dialog_ChargingJobUndock = false
        this.viewJobs()
        this.viewSkycars()
        this.viewStations()
      })
      .catch(error => {
        console.log(error)
      })
    },

    downloadExcel: async function() {
      var route = new URL(getCMHost() + routeDownloadExcel)

      let outbound_json = {
        "start_date": "sample",
        "end_date": "sample"
      }
      await fetch(route, {
        method: "POST",
        headers: getRequestHeader(),
        body: JSON.stringify(outbound_json)
      }).then(async response => {
        const data = await response.json();
        if (!response.ok) {
          const error = (data) || response.status
          return Promise.reject(error)
        }
      })
      .catch(error => {
        console.log(error)
      })
    },

    // --------- Charging Requests --------- //
    doneSyncRequests: false,
    dialog_ChargingRequestDelete: false,
    dialog_currentRequest_id: undefined,
    dialog_currentRequest_skycar: "",
    dialog_currentRequest_station: "",
    headers_requests: [
      {
        text: "Request ID",
        align: "start",
        sortable: true,
        value: "id"
      },
      { text: "Time", value: "datetime", sortable: true },
      { text: "Job ID", value: "job_id" },
      // { text: "TC Order ID", value: "tc_order_id"},
      { text: "Cube", value: "cube" },
      { text: "Station", value: "station" },
      { text: "Skycar", value: "skycar" },
      { text: "Status", value: "status" },
      { text: "Orientation", value: "orientation" },
      { text: "Actions", value: "actions", sortable: false }
    ],
    filter_RequestStatus: "",
    filter_RequestStation: "",
    filter_RequestSkycar: "",
    filter_RequestJobId: "",
    filter_RequestInvalid: false,
    filter_RequestCanceled: false,
    sort_requests: [{ key: "id", order: "desc" }],
    search_requests: "",
    items_requests: [],
    expanded_requests: [],

    viewRequests: async function () {
      this.doneSyncRequests = false
      // http://localhost:6200/check/charging-requests?filter_canceled=true&filter_invalid=true'
      let params = new URLSearchParams(
        { 
        "filter_canceled": this.filter_RequestCanceled, 
        "filter_invalid": this.filter_RequestInvalid 
      }).toString()
      let api_json = await onClickGet(routeCheckChargingRequests+"?"+params);
      this.doneSyncRequests = true
      this.items_requests = api_json.data
      this.refreshStatus();
    },

    dialogDeleteRequest: function(skycarId, stationId, requestId) {
      this.dialog_currentRequest_id = requestId
      this.dialog_currentRequest_skycar = skycarId
      this.dialog_currentRequest_station = stationId
      this.dialog_ChargingRequestDelete = true
      console.log("dialog CurrentRequestID: " + 
        this.dialog_currentRequest_id + 
        " SkycarID: " + 
        this.dialog_currentRequest_skycar + 
        " StationID: " + 
        this.dialog_currentRequest_station)
    },

    deleteRequest: async function(requestId) {
      var route = new URL(getCMHost() + routeDeleteChargingRequests)

      let outbound_json = {
        "id": requestId
      }
      await fetch(route, {
        method: "POST",
        headers: getRequestHeader(),
        body: JSON.stringify(outbound_json)
      }).then(async response => {
        const data = await response.json();
        if (!response.ok) {
          const error = (data) || response.status
          return Promise.reject(error)
        }
        this.dialog_ChargingRequestDelete = false
        this.viewRequests()
      })
      .catch(error => {
        console.log(error)
      })
    },

    // --------- Charging Stations --------- //
    doneSyncStations: false,
    dialog_setStationAvailable: false,
    dialog_setStationAvailable_id: undefined,
    dialog_startStationChargingTimer: false,
    dialog_startStationChargingTimer_id: undefined,
    dialog_setStationMaintenance: false,
    dialog_setStationMaintenance_id: undefined,
    dialog_setStationMaintenance_enabled: undefined,
    dialog_setStationMaintenance_enabledtarget: undefined,
    dialog_resetStation: false,
    dialog_resetStation_id: undefined,
    headers_stations: [
      {
        text: "Charging Station ID ",
        align: "start",
        sortable: true,
        value: "device_id",
        width: "9%"
      },
      { text: "Cube", value: "cube", width: "5%" },
      { text: "X Pos", value: "x_pos", width: "5%" },
      { text: "Y Pos", value: "y_pos", width: "5%" },
      { text: "Paired?", value: "is_paired", width: "7%" },
      { text: "Available?", value: "is_available", width: "4%" },
      { text: "Operational?", value: "is_operational", width: "4%" },
      { text: "Blocked?", value: "is_blocked", width:"4%" },
      { text: "Battery Percent", value: "battery_level", width: "6%" },
      { text: "CM Status", value: "status", width: "10%" },
      { text: "Hardware status", value: "hardware_status", width: "10%" },
      { text: "Actions", value: "actions", sortable: false, width: "15%" }
    ],
    items_stations: [],

    viewStations: async function () {
      this.doneSyncStations = false
      let api_json = await onClickGet(routeCheckChargingStations);
      this.doneSyncStations = true
      this.items_stations = api_json.data
      this.refreshStatus();
    },

    dialogSetStationAvailable: function(stationId) {
      this.dialog_setStationAvailable_id = stationId
      this.dialog_setStationAvailable = true
    },

    dialogSetStationMaintenance: function(stationId, enabled) {
      this.dialog_setStationMaintenance_id = stationId
      if (enabled == "true" || enabled == "True" || enabled == true) {
        this.dialog_setStationMaintenance_enabled = "true"
        this.dialog_setStationMaintenance_enabledtarget = "False"
      } else {
        this.dialog_setStationMaintenance_enabled = "false"
        this.dialog_setStationMaintenance_enabledtarget = "True"
      }
      this.dialog_setStationMaintenance = true
    },

    dialogStartStationChargingTimer: function(stationId) {
      this.dialog_startStationChargingTimer_id = stationId
      this.dialog_startStationChargingTimer = true
    },

    dialogResetChargingStation: function(stationId) {
      this.dialog_resetStation_id = stationId
      this.dialog_resetStation = true
    },

    setStationAvailable: async function(stationId) {
      var route = new URL(getCMHost() + routeSetAvailableChargingStation)

      let outbound_json = {
        "station_id": stationId
      }
      await fetch(route, {
        method: "POST",
        headers: getRequestHeader(),
        body: JSON.stringify(outbound_json)
      }).then(async response => {
        const data = await response.json();
        if (!response.ok) {
          const error = (data) || response.status
          return Promise.reject(error)
        }
        this.dialog_setStationAvailable = false
        this.viewStations()
      })
      .catch(error => {
        console.log(error)
      })
    },

    setStationMaintenance: async function(stationId, enabled) {
      var route = new URL(getCMHost() + routeSetMaintenanceChargingStation)

      let outbound_json = {
        "station_id": stationId,
        "enabled": enabled
      }
      await fetch(route, {
        method: "POST",
        headers: getRequestHeader(),
        body: JSON.stringify(outbound_json)
      }).then(async response => {
        const data = await response.json();
        if (!response.ok) {
          const error = (data) || response.status
          return Promise.reject(error)
        }
        this.dialog_setStationMaintenance = false
        this.viewStations()
        this.common_response_desc = data.response
        this.common_response_card = true
      }).catch(error => {
        console.log(error)
        this.common_response_desc = error
        this.common_response_card = true
      })
    },
    
    requestStationStatus: async function(stationId) {
      var route = new URL(getCMHost() + routeRequestStatusChargingStation)

      let outbound_json = {
        "station_id": stationId
      }
      await fetch(route, {
        method: "POST",
        headers: getRequestHeader(),
        body: JSON.stringify(outbound_json)
      }).then(async response => {
        const data = await response.json();
        if (!response.ok) {
          const error = (data) || response.status
          return Promise.reject(error)
        }
        this.viewStations()
      })
      .catch(error => {
        console.log(error)
      })
    },

    startStationChargingTimer: async function(stationId) {
      var route = new URL(getCMHost() + routeSetTimerChargingStation)

      let outbound_json = {
        "station_id": stationId
      }
      await fetch(route, {
        method: "POST",
        headers: getRequestHeader(),
        body: JSON.stringify(outbound_json)
      }).then(async response => {
        const data = await response.json();
        if (!response.ok) {
          const error = (data) || response.status
          return Promise.reject(error)
        }
        this.dialog_startStationChargingTimer = false
        this.viewStations()
      })
      .catch(error => {
        console.log(error)
      })
    },

    resetChargingStation: async function(stationId) {
      var route = new URL(getCMHost() + routeResetChargingStation)

      let outbound_json = {
        "station_id": stationId
      }
      await fetch(route, {
        method: "POST",
        headers: getRequestHeader(),
        body: JSON.stringify(outbound_json)
      }).then(async response => {
        const data = await response.json();
        if (!response.ok) {
          const error = (data) || response.status
          return Promise.reject(error)
        }
        this.dialog_startStationChargingTimer = false
        this.viewStations()
      })
      .catch(error => {
        console.log(error)
      })
    },

    // --------- Skycars --------- //
    doneSyncSkycars: false,
    dialog_chargeSkycar: false,
    dialog_chargeSkycar_id: undefined,
    dialog_chargeSkycar_cube: undefined,
    dialog_chargeSkycar_orientation: undefined, 
    dialog_chargeSkycar_mode: undefined,
    dialog_chargingEnabledSkycar: false,
    dialog_chargingEnabledSkycar_id: undefined,
    dialog_chargingEnabledSkycar_cube: undefined,
    dialog_chargingEnabledSkycar_current: undefined,
    dialog_chargingEnabledSkycar_target: undefined,
    headers_skycars: [
      {
        text: "Skycar ID ",
        align: "start",
        sortable: true,
        value: "device_id",
        width: "9%"
      },
      { text: "Cube", value: "cube", width: "6%" },
      { text: "Charging Enabled?", value: "charging_enabled", width: "7%" },
      { text: "Orientation", value: "orientation", width: "6%" },
      { text: "Battery Percent", value: "battery_percentage", width: "6%" },
      { text: "Battery History", value: "battery_history", sortable: false, width: "8%" },
      { text: "CM Status", value: "cm_status", width: "9%" },
      { text: "Actions", value: "actions", sortable: false, width: "12%" }
    ],
    items_skycars: [],

    viewSkycars: async function () {
      this.doneSyncSkycars = false
      let api_json = await onClickGet(routeCheckSkycars);
      this.doneSyncSkycars = true
      this.items_skycars = api_json.data
      this.refreshStatus();
    },

    dialogChargeSkycar: function(skycarId, cube, orientation, mode) {
      this.dialog_chargeSkycar_id = skycarId
      this.dialog_chargeSkycar_cube = cube
      this.dialog_chargeSkycar_orientation = orientation
      this.dialog_chargeSkycar_mode = mode
      this.dialog_chargeSkycar = true
    },

    dialogChargingEnabledSkycar: function(skycarId, cube, charging_enabled) {
      this.dialog_chargingEnabledSkycar_id = skycarId
      this.dialog_chargingEnabledSkycar_cube = cube
      if (charging_enabled == "Enabled") {
        this.dialog_chargingEnabledSkycar_current = "Enabled"
        this.dialog_chargingEnabledSkycar_target = "Disabled"
      } else if(charging_enabled == "Disabled") {
        this.dialog_chargingEnabledSkycar_current = "Disabled"
        this.dialog_chargingEnabledSkycar_target = "Enabled"
      }
      this.dialog_chargingEnabledSkycar = true
    },

    chargeSkycar: async function(skycarId, cube, orientation, mode) {
      var route = new URL(getCMHost() + routeChargeSkycar)

      let outbound_json = {
        "skycar_id": skycarId,
        "cube": cube,
        "battery_orientation": orientation,
        "charging_mode": mode
      }
      await fetch(route, {
        method: "POST",
        headers: getRequestHeader(),
        body: JSON.stringify(outbound_json)
      }).then(async response => {
        const data = await response.json();
        if (!response.ok) {
          const error = (data) || response.status
          return Promise.reject(error)
        }
        this.dialog_chargeSkycar = false
        this.viewRequests()
      })
    },

    requestSkycarStatus: async function(skycarId) {
      var route = new URL(getCMHost() + routeRequestStatusSkycar)

      let outbound_json = {
        "skycar_id": skycarId
      }
      await fetch(route, {
        method: "POST",
        headers: getRequestHeader(),
        body: JSON.stringify(outbound_json)
      }).then(async response => {
        const data = await response.json();
        if (!response.ok) {
          const error = (data) || response.status
          return Promise.reject(error)
        }
        this.viewSkycars()
      })
      .catch(error => {
        console.log(error)
      })
    },

    toggleChargingEnabledSkycar: async function(skycarId, cube, targetEnable) {
      var route = new URL(getCMHost() + routeToggleChargingEnabledSkycar)

      let outbound_json = {
        "skycar_id": skycarId,
        "cube": cube,
        "enabled": targetEnable
      }
      await fetch(route, {
        method: "POST",
        headers: getRequestHeader(),
        body: JSON.stringify(outbound_json)
      }).then(async response => {
        const data = await response.json();
        if (!response.ok) {
          const error = (data) || response.status
          return Promise.reject(error)
        }
        this.dialog_chargingEnabledSkycar = false
        this.viewSkycars()
      })
    },

    // Misc Functions //
    jobProcesses: {
      swap: [
        { id: 1, status: "AVAILABLE", desc: "Waiting..." },
        { id: 2, status: "RELEASING", desc: "Releasing solenoid..." },
        { id: 3, status: "PULLING", desc: "Pulling battery..." },
        { id: 4, status: "PUSHING", desc: "Pushing battery..." },
        { id: 5, status: "SWAPPING", desc: "Finalizing..." },
        { id: 6, status: "COMPLETE", desc: "Complete!" }
      ],
      charge: [
        { id: 1, status: "AVAILABLE", desc: "Waiting..." },
        { id: 2, status: "EXTENDING", desc: "Extending station arm..." },
        { id: 3, status: "CHARGING", desc: "Charging Skycar..." },
        { id: 4, status: "RETRACTING", desc: "Retracting station arm..." },
        { id: 5, status: "COMPLETE", desc: "Complete!" }
      ]
    },
    job_process: [{ id: 0, status:"First", desc: "Hello" }],
    job_current: 0,

    resetChargingJob: async function (jobId) {
      var route = new URL(getCMHost() + routeResetChargingJob)

      let outbound_json = {
        "job_id": jobId
      }
      await fetch(route, {
        method: "POST",
        headers: getRequestHeader(),
        body: JSON.stringify(outbound_json)
      }).then(async response => {
        const data = await response.json();
        if (!response.ok) {
          const error = (data) || response.status
          return Promise.reject(error)
        }
        this.viewJobs()
        this.dialog_ChargingJobError = false
      })
      .catch(error => {
        console.log(error)
      })
    },

    restartChargingJob: async function (jobId, tcOrderId, stationId, skycarId) {
      var route = new URL(getCMHost() + routeRestartChargingJob)

      let outbound_json = {
        "job_id": jobId,
        "tc_order_id": tcOrderId,
        "station_id": stationId,
        "skycar_id": skycarId
      }
      await fetch(route, {
        method: "POST",
        headers: getRequestHeader(),
        body: JSON.stringify(outbound_json)
      }).then(async response => {
        const data = await response.json();
        if (!response.ok) {
          const error = (data) || response.status
          return Promise.reject(error)
        }
        this.viewJobs()
      })
      .catch(error => {
        console.log(error)
      })
    },

    cancelChargingJob: async function(jobId) {
      var route = new URL(getCMHost() + routeCancelChargingJob)

      let outbound_json = {
        "job_id": jobId
      }
      await fetch(route, {
        method: "POST",
        headers: getRequestHeader(),
        body: JSON.stringify(outbound_json)
      }).then(async response => {
        const data = await response.json();
        if (!response.ok) {
          const error = (data) || response.status
          return Promise.reject(error)
        }
        this.viewJobs()
        this.viewRequests()
        this.dialog_ChargingJobDelete = false
      })
      .catch(error => {
        console.log(error)
      })
    },

    clearDockedObstacle: async function(skycarId) {
      var route = new URL(getCMHost() + routeClearDockedObstacle)

      let outbound_json = {
        "skycar_id": skycarId
      }
      await fetch(route, {
        method: "POST",
        headers: getRequestHeader(),
        body: JSON.stringify(outbound_json)
      }).then(async response => {
        const data = await response.json();
        if (!response.ok) {
          const error = (data) || response.status
          return Promise.reject(error)
        }
        this.dialog_clearDockedObstacle_response = data.response
        this.dialog_clearDockedObstacle = true
      })
      .catch(error => {
        console.log(error)
        this.dialog_clearDockedObstacle_response = error
        this.dialog_clearDockedObstacle = true
      })
    },

    // --------- Manual Mode --------- //
    showCreateRequest: false,
    showDeleteRequests: false,
    showDeleteJobs: false,
    showStationAvailable: false,

    // Delete Requests & Jobs //
    setStationAvailableShow: false,
    setStationAvailableResponse: "",
    deleteRequestResponseShow: false,
    deleteRequestResponse: "",
    deleteJobResponseShow: false,
    deleteJobResponse: "",

    deleteAllRequests: async function () {
      var route = new URL(getCMHost() + routeClearAllRequests)

      await fetch(route, {
        method: "POST", headers:getRequestHeader()
      }).then(async response => {
          const data = await response.json();
          if (!response.ok) {
            const error = (data) || response.status
            return Promise.reject(error)
          }
          this.deleteRequestResponse = data.response
          this.deleteRequestResponseShow = true
          this.viewRequests()
        })
        .catch(error => {
          this.deleteRequestResponse = error;
          console.log(error)
      })
    },

    deleteAllJobs: async function () {
      var route = new URL(getCMHost() + routeClearAllJobs)

      await fetch(route, {
        method: "POST", headers:getRequestHeader()
      }).then(async response => {
          const data = await response.json();
          if (!response.ok) {
            const error = (data) || response.status
            return Promise.reject(error)
          }
          this.deleteJobResponse = data.response
          this.deleteJobResponseShow = true
          this.viewJobs()
        })
        .catch(error => {
          this.deleteJobResponse = error;
          console.log(error)
      })
    },

    setStationsAvailable: async function () {
      var route = new URL(getCMHost() + routeSetAllStationsAvailable)

      await fetch(route, {
        method: "POST", headers: getRequestHeader()
      }).then(async response => {
          const data = await response.json();
          if (!response.ok) {
            const error = (data) || response.status
            return Promise.reject(error)
          }
          this.setStationAvailableResponse = data.response
          this.setStationAvailableShow = true
          this.viewStations()
        })
        .catch(error => {
          this.setStationAvailableResponse = error;
          console.log(error)
      })
    },

    // Creating Charging Request //
    selectedCube: getCube()[0],
    selectedOrientation: "North",
    selectedMode: "Both",
    inputStationId: null,   // station_assigned
    inputSkycarId: null,
    inputNodeId: null,
    inputSkycarHost: null,
    inputSkycarPort: null, 
    createRequestResponse: "",

    createChargingRequest: async function () {
      var route = new URL(getCMHost() + routeCreateChargingRequest)

      if (this.inputSkycarId == null) {
        this.createRequestResponse = "Failed: Skycar ID cannot be left blank."
      } else {
        if (this.inputStationId === "") {
          this.inputStationId = null
        }
        let outbound_json = {
          "cube": this.selectedCube,
          "skycar_id": this.inputSkycarId,
          "host": "127.0.0.1",        // even though unused, still cannot be NULL
          "port": "55500",                 // even though unused, still cannot be NULL
          "battery_orientation": this.selectedOrientation,
          "node_id": "1",              // even though unused, still cannot be NULL
          "charging_mode": this.selectedMode,
          "station_id": this.inputStationId
        }
        await fetch(route, {
          method: "POST",
          headers: getRequestHeader(),
          body: JSON.stringify(outbound_json)
        })
        .then(async response => {
          const data = await response.json();
          if (!response.ok) {
            const error = (data) || response.status
            return Promise.reject(error)
          }
          this.createRequestResponse = data.response
          this.viewRequests()
        })
        .catch(error => {
          this.createRequestResponse = error;
          console.log(error)
        })
      }
    },

    // --------- Debugging Card --------- //
    showManualTCP: false,
    showEventLog: false,
    showTCPLog: false,
    dialog_TCP: false,

    doneSyncClients: false,
    items_clients: [],

    current_client: { "type": "Gateway", "desc": "NA", "device_id": "0", "host":"0.0.0.0", "port": "0000" },
    current_tcp_message: "",
    dialog_TCP_response: "",

    pause_tcp_logs: false,
    tcp_logs: [{
      "timestamp": "na",
      "message": "message",
      "sender": "sender",
      "receiver": "receiver"
    }],
    doneSyncTCPLogs: false,

    pause_event_logs: false,
    event_logs: [{
      "timestamp": "na",
      "message": "message"
    }],
    doneSyncEventLogs: false,
    
    refreshTCPLogs: async function() {
      this.doneSyncTCPLogs = false
      let api_json = await onClickGet(routeRefreshTCPLogs);
      this.tcp_logs = api_json.data
      this.doneSyncTCPLogs = true
    },

    refreshEventLogs: async function() {
      this.doneSyncEventLogs = false
      let api_json = await onClickGet(routeRefreshEventLogs);
      this.event_logs = api_json.data
      this.doneSyncEventLogs = true
    },

    viewClients: async function () {
      this.doneSyncClients = false
      let api_json = await onClickGet(routeViewClients);
      this.doneSyncClients = true
      this.items_clients = api_json.data
    },

    sendTCPMessage: async function () {
      var route = new URL(getCMHost() + routeSendTCPMessage)

      let outbound_json = {
        "type": this.current_client["type"],
        "device_id": this.current_client["device_id"],
        "message": this.current_tcp_message
      }
      this.dialog_TCP = true
      await fetch(route, {
        method: "POST",
        headers: getRequestHeader(),
        body: JSON.stringify(outbound_json)
      })
      .then(async response => {
        const data = await response.json();
        if (!response.ok) {
          const error = (data) || response.status
          return Promise.reject(error)
        }
        this.dialog_TCP_response = data.response
        this.refreshTCPLogs()
      })
      .catch(error => {
        this.dialog_TCP_response = error
        console.log(error)
      })

      // console.log(outbound_json)
    },
    
    // notifyConnect(json, notifier) {
    //   console.info("Connected! " + json)
    //   notifier.info("Connected to CM websocket server!")
    // },
    // notifyPairing(json, notifier) {
    //   console.info("Paired: " + json)
    //   notifier.info("New client paired! Client is " + json)
    // },
    // updateTCPLogs(json_list, notifier) {
    //   if (this.pause_tcp_logs == false) {
    //     console.info("Updating TCP logs to: " + json_list)
    //     notifier.info("New TCP message received!")
    //     this.tcp_logs = json_list
    //   }
    // },

    // --------- Configuration --------- //
    showConfigurationsCard: false,
    configs_loaded: false,
    config_dialog: false,
    config_dialog_text: false,

    config_current_station_status_update_time: 0,
    config_target_station_status_update_time: 0,
    config_current_skycar_status_update_time: 0,
    config_target_skycar_status_update_time: 0,
    
    config_current_skycar_preemptive_charge_percent: 0,
    config_target_skycar_preemptive_charge_percent: 0,
    config_preemptive_skycar_enabled: false,

    config_pipeline_skycar_high_battery_threshold: 0,
    temp_config_pipeline_skycar_high_battery_threshold: 0,
    config_pipeline_skycar_high_battery_threshold_enabled: false,
    config_pipeline_last_charged_threshold: 0,
    temp_config_pipeline_last_charged_threshold: 0,
    config_pipeline_last_charged_threshold_enabled: false,
    config_pipeline_station_skycar_check_difference_threshold_enabled: false,
    config_pipeline_station_skycar_check_difference_threshold: 0,
    temp_config_pipeline_station_skycar_check_difference_threshold: 0,

    config_current_station_preemptive_charge_percent: 0,
    config_target_station_preemptive_charge_percent: 0,
    config_preemptive_station_enabled: false,
    config_preemptive_station_check_skycar_high_threhsold: false,

    config_directcharge_randomizer: false,
    config_directcharge_lower_percent: 0,
    temp_config_directcharge_lower_percent: 0,
    config_directcharge_upper_percent: 0,
    temp_config_directcharge_upper_percent: 0,
    config_directcharge_default_percent: 0,
    temp_config_directcharge_default_percent: 0,
    config_directcharge_startstop_attempt_interval: 0,
    temp_config_directcharge_startstop_attempt_interval: 0,
    config_directcharge_startstop_max_attempts: 0,
    temp_config_directcharge_startstop_max_attempts: 0,
    config_directcharge_skycar_ping_rate: 0,
    temp_config_directcharge_skycar_ping_rate: 0,
    config_directcharge_skycar_ping_duration: 0,
    temp_config_directcharge_skycar_ping_duration: 0,

    config_hibernate_enabled: false,
    config_hibernate_threshold: 0,
    config_hibernate_times_stringified: "",
    config_hibernate_monday_note: "",
    config_hibernate_tuesday_note: "",
    config_hibernate_wednesday_note: "",
    config_hibernate_thursday_note: "",
    config_hibernate_friday_note: "",
    config_hibernate_saturday_note: "",
    config_hibernate_sunday_note: "",

    config_current_charging_timer: 0,
    config_target_charging_timer: 0,

    config_battery_history_count: 0,
    config_battery_recent_history_count: 0,
    config_recent_discharge_min: 0,
    config_recent_discharge_max: 0,
    config_long_discharge_min: 0,
    config_long_discharge_max: 0,
    config_bms_autoswap: false,

    updateConfig(api_json, mapping) {
      if (!api_json?.data) return;

      Object.keys(mapping).forEach(key => {
        const value = api_json.data[key];
        const variableName = mapping[key];

        console.log('checking api_json value ' + value + ' to variable: ' + variableName)

        // Handle boolean or string-to-boolean conversions
        if (typeof value === 'boolean' || typeof value === 'string') {
          if (value === "true" || value === "false") {
            this[variableName] = value === "true";
          } else {
            this[variableName] = value;
          }
        }
        // Handle cases where value exists and isn't undefined
        else if (value !== undefined) {
          this[variableName] = value;
        }
      });
    },

    updateTempConfigs() {
      Object.keys(temp_mapping).forEach(tempKey => {
        const internalKey = temp_mapping[tempKey];
        this[tempKey] = this[internalKey]; // Copy internal value to temporary variable
      });
    },

    getConfigs: async function() {
      try {
        console.log('GETCONFIG():' + this.configs_loaded)
        let api_json = await onClickGet(routeGetConfigs)

        this.updateConfig(api_json, mapping);
        this.updateTempConfigs()
        
        this.configs_loaded = true;
        console.log('GETCONFIG():' + this.configs_loaded)
      } catch (error) {
        console.error("Error fetching configs:", error);
      }
    },

    expandConfigCard: async function() {
      if (this.showConfigurationsCard == false) {
        this.getConfigs()
        this.showConfigurationsCard = true
      } else {
        this.showConfigurationsCard = false
      }
    },

    updateConfig_ChargingTimer: async function() {
      var route = new URL(getCMHost() + routeUpdateStationChargeTimer)

      let outbound_json = {
        "charge_timer": this.config_target_charging_timer
      }
      await fetch(route, {
        method: "POST",
        headers: getRequestHeader(),
        body: JSON.stringify(outbound_json)
      }).then(async response => {
        const data = await response.json();
        if (!response.ok) {
          const error = (data) || response.status
          return Promise.reject(error)
        }
        this.config_current_charging_timer = this.config_target_charging_timer
      }).catch(error => {
        console.log(error)
        this.config_dialog_text = error
        this.config_dialog = true
      })
    },

    updateConfig_StationStatusTime: async function() {
      var route = new URL(getCMHost() + routeUpdateStationStatusTime)

      let outbound_json = {
        "threshold_time": this.config_target_station_status_update_time
      }
      await fetch(route, {
        method: "POST",
        headers: getRequestHeader(),
        body: JSON.stringify(outbound_json)
      }).then(async response => {
        const data = await response.json();
        if (!response.ok) {
          const error = (data) || response.status
          return Promise.reject(error)
        }
        this.config_current_station_status_update_time = this.config_target_station_status_update_time
      }).catch(error => {
        console.log(error)
        this.config_dialog_text = error
        this.config_dialog = true
      })
    },

    updateConfig_StationPreemptivePercentage: async function() {
      var route = new URL(getCMHost() + routeUpdateStationPreemptivePercentage)

      let outbound_json = {
        "battery_percent": this.config_target_station_preemptive_charge_percent
      }
      await fetch(route, {
        method: "POST",
        headers: getRequestHeader(),
        body: JSON.stringify(outbound_json)
      }).then(async response => {
        const data = await response.json();
        if (!response.ok) {
          const error = (data) || response.status
          return Promise.reject(error)
        }
        this.config_current_station_preemptive_charge_percent = this.config_target_station_preemptive_charge_percent
      }).catch(error => {
        console.log(error)
        this.config_dialog_text = error
        this.config_dialog = true
      })
    },

    updateConfig_StationPreemptiveEnable: async function() {
      var route = new URL(getCMHost() + routeUpdateStationPreemptiveEnabled)

      var new_bool = false
      if (this.config_preemptive_station_enabled == true) {
        new_bool = false
      } else {
        new_bool = true
      }
      let outbound_json = {
        "enabled": new_bool
      }
      await fetch(route, {
        method: "POST",
        headers: getRequestHeader(),
        body: JSON.stringify(outbound_json)
      }).then(async response => {
        const data = await response.json();
        if (!response.ok) {
          const error = (data) || response.status
          return Promise.reject(error)
        }
        this.config_preemptive_station_enabled = new_bool
      }).catch(error => {
        console.log(error)
        this.config_dialog_text = error
        this.config_dialog = true
      })
    },

    updateConfig_SkycarStatusTime: async function() {
      var route = new URL(getCMHost() + routeUpdateSkycarStatusTime)

      let outbound_json = {
        "threshold_time": this.config_target_skycar_status_update_time
      }
      await fetch(route, {
        method: "POST",
        headers: getRequestHeader(),
        body: JSON.stringify(outbound_json)
      }).then(async response => {
        const data = await response.json();
        if (!response.ok) {
          const error = (data) || response.status
          return Promise.reject(error)
        }
        this.config_current_skycar_status_update_time = this.config_target_skycar_status_update_time
      }).catch(error => {
        console.log(error)
        this.config_dialog_text = error
        this.config_dialog = true
      })
    },

    updateConfig_SkycarPreemptivePercentage: async function() {
      var route = new URL(getCMHost() + routeUpdateSkycarPreemptivePercentage)

      let outbound_json = {
        "battery_percent": this.config_target_skycar_preemptive_charge_percent
      }
      await fetch(route, {
        method: "POST",
        headers: getRequestHeader(),
        body: JSON.stringify(outbound_json)
      }).then(async response => {
        const data = await response.json();
        if (!response.ok) {
          const error = (data) || response.status
          return Promise.reject(error)
        }
        this.config_current_skycar_preemptive_charge_percent = this.config_target_skycar_preemptive_charge_percent
      }).catch(error => {
        console.log(error)
        this.config_dialog_text = error
        this.config_dialog = true
      })
    },

    updateConfig_SkycarPreemptiveEnable: async function() {
      var route = new URL(getCMHost() + routeUpdateSkycarPreemptiveEnabled)
      var new_bool = false

      if (this.config_preemptive_skycar_enabled == true) {
        new_bool = false
      } else {
        new_bool = true
      }
      let outbound_json = {
        "enabled": new_bool
      }
      await fetch(route, {
        method: "POST",
        headers: getRequestHeader(),
        body: JSON.stringify(outbound_json)
      }).then(async response => {
        const data = await response.json();
        if (!response.ok) {
          const error = (data) || response.status
          return Promise.reject(error)
        }
        this.config_preemptive_skycar_enabled = new_bool
      }).catch(error => {
        console.log(error)
        this.config_dialog_text = error
        this.config_dialog = true
      })
    },

    updateConfig_DirectCharge_Randomizer: async function() {
      var route = new URL(getCMHost() + routeConfigDirectCharge)
      var new_bool = false

      // Related to how the UI shows the opposite of what is set
      if (this.config_directcharge_randomizer == true) {
        new_bool = false
      } else {
        new_bool = true
      }
      let outbound_json = {
        "direct_charge_randomizer": new_bool,
      }
      await fetch(route, {
        method: "POST",
        headers: getRequestHeader(),
        body: JSON.stringify(outbound_json)
      }).then(async response => {
        const data = await response.json();
        if (!response.ok) {
          const error = (data) || response.status
          return Promise.reject(error)
        }
        this.config_directcharge_randomizer = new_bool
      }).catch(error => {
        console.log(error)
        this.config_dialog_text = error
        this.config_dialog = true
      })
    },

    updateConfig_DirectCharge_Values: async function() {
      console.log("updateConfig_DirectCharge_Values!!!")
      var route = new URL(getCMHost() + routeConfigDirectCharge)
      let outbound_json = {
        "direct_charge_lower_percentage_default": this.temp_config_directcharge_lower_percent,
        "direct_charge_upper_percentage_default": this.temp_config_directcharge_upper_percent,
        "direct_charge_skycar_default_threshold": this.temp_config_directcharge_default_percent,
        "direct_charge_startstop_attempt_interval": this.temp_config_directcharge_startstop_attempt_interval,
        "direct_charge_startstop_max_attempts": this.temp_config_directcharge_startstop_max_attempts,
        "direct_charge_skycar_ping_rate": this.temp_config_directcharge_skycar_ping_rate,
        "direct_charge_skycar_ping_duration": this.temp_config_directcharge_skycar_ping_duration
      }
      await fetch(route, {
        method: "POST",
        headers: getRequestHeader(),
        body: JSON.stringify(outbound_json)
      }).then(async response => {
        const data = await response.json();
        if (!response.ok) {
          const error = (data) || response.status
          return Promise.reject(error)
        }
      }).catch(error => {
        console.log(error)
        this.config_dialog_text = error
        this.config_dialog = true
      })
    },

    updateConfig_BMSTracking: async function() {
      var route = new URL(getCMHost() + routeUpdateBMSTracker)

      let outbound_json = {
        "battery_history_count": this.config_battery_history_count,
        "battery_recent_history_count": this.configbattery_recent_history_count,
        "recent_discharge_min": this.config_recent_discharge_min,
        "recent_discharge_max": this.config_recent_discharge_max,
        "long_discharge_min": this.config_long_discharge_min,
        "long_discharge_max": this.config_long_discharge_max
      }
      await fetch(route, {
        method: "POST",
        headers: getRequestHeader(),
        body: JSON.stringify(outbound_json)
      }).then(async response => {
        const data = await response.json();
        if (!response.ok) {
          const error = (data) || response.status
          return Promise.reject(error)
        }
      })
    },

    updateConfig_BMSAutoSwap: async function() {
      var route = new URL(getCMHost() + routeUpdateBMSTracker)

      var new_autoswap_bool = false
      if (this.config_bms_autoswap == true) {
        new_autoswap_bool = false
      } else {
        new_autoswap_bool = true
      }
      let outbound_json = {
        "bms_autoswap": new_autoswap_bool
      }
      await fetch(route, {
        method: "POST",
        headers: getRequestHeader(),
        body: JSON.stringify(outbound_json)
      }).then(async response => {
        const data = await response.json();
        if (!response.ok) {
          const error = (data) || response.status
          return Promise.reject(error)
        }
        this.config_bms_autoswap = new_autoswap_bool
      })
    },

    // --------- Error Logs Card --------- //
    headers_errorlogs: [
      {
        text: "Error ID",
        align: "end",
        sortable: true,
        value: "id",
        width: "4%"
      },
      { text: "Job ID", value: "job_id", width:"3%" },
      { text: "Skycar ID", value: "skycar_id", width: "2%", sortable: true },
      { text: "Station ID", value: "station_id", width: "2%", sortable: true },
      { text: "Error Code", value: "error_code", width: "2%", sortable: true },
      { text: "Error Name", value: "error_name", width: "4%", sortable: true },
      { text: "Error Description", value: "error_desc", width: "9%" },
      { text: "Cause Remarks", value: "remarks", width: "9%" },
      { text: "Error Time", value: "datetime", width: "2%", sortable: true },
      { text: "Resolved?", value: "resolved", width: "1%", sortable: true },
      { text: "Resolved Remarks", value: "resolved_remarks", width: "9%" },
      { text: "Resolved At", value: "resolved_at", width: "2%", sortable: true },
      { text: "Actions", value: "actions", width: "6%", sortable: false }
    ],
    items_errorlogs: [],

    showErrorLogsCard: true,
    doneSyncErrorLogs: false,
    dialog_resolveError: false,
    dialog_editError: false,
    dialog_error_stationid: null,
    dialog_error_skycarid: null,
    dialog_error_errortype: null,
    dialog_error_date: null,
    dialog_error_id: null,
    dialog_error_resolved: false,
    dialog_error_edit_remarks: null,
    dialog_error_edit_resolvedremarks: null,

    viewErrorLogs: async function() {
      this.doneSyncErrorLogs = false
      let api_json = await onClickGet(routeCheckErrorLogs)
      this.doneSyncErrorLogs = true
      this.items_errorlogs = api_json.data
      // this.items_errorlogs = JobStub.mock_error_log()
    },
    
    expandErrorLogCard: async function() {
      if (this.showErrorLogsCard == false) {
        this.viewErrorLogs()
        this.showErrorLogsCard = true
      } else {
        this.showErrorLogsCard = false
      }
    },

    dialogResolveError: function(errorId, stationId, skycarId, errorName, errorDate) {
      this.dialog_error_id = errorId
      this.dialog_error_stationid = stationId
      this.dialog_error_skycarid = skycarId
      this.dialog_error_errortype = errorName
      this.dialog_error_date = errorDate
      this.dialog_resolveError = true
    },

    dialogEditError: function(errorId, stationId, skycarId, errorName, errorDate) {
      this.dialog_error_id = errorId
      this.dialog_error_stationid = stationId
      this.dialog_error_skycarid = skycarId
      this.dialog_error_errortype = errorName
      this.dialog_error_date = errorDate
      this.dialog_editError = true
    },

    resolveError: async function(errorId, resolvedRemarks) {
      var route = new URL(getCMHost() + routeResolveError)

      let outbound_json = {
        "error_id": errorId,
        "resolved_remarks": resolvedRemarks,
        "resolved": true
      }
      await fetch(route, {
        method: "POST",
        headers: getRequestHeader(),
        body: JSON.stringify(outbound_json)
      }).then(async response => {
        const data = await response.json();
        if (!response.ok) {
          const error = (data) || response.status
          return Promise.reject(error)
        }
        this.viewErrorLogs()
        this.common_response_desc = data.response
        this.common_response_card = true
      })
      .catch(error => {
        console.log(error)
        this.common_response_desc = error
        this.common_response_card = true
      })
    },

    editError: async function(errorId, causeRemarks, resolvedRemarks, resolved) {
      var route = new URL(getCMHost() + routeResolveError)

      let outbound_json = {
        "error_id": errorId,
        "remarks": causeRemarks,
        "resolved_remarks": resolvedRemarks,
        "resolved": resolved
      }
      await fetch(route, {
        method: "POST",
        headers: getRequestHeader(),
        body: JSON.stringify(outbound_json)
      }).then(async response => {
        const data = await response.json();
        if (!response.ok) {
          const error = (data) || response.status
          return Promise.reject(error)
        }
        this.viewErrorLogs()
        this.common_response_desc = data.response
        this.common_response_card = true
      })
      .catch(error => {
        console.log(error)
        this.common_response_desc = error
        this.common_response_card = true
      })
    }

  }),

  computed: {
    filteredJobs() {

      var filterParams = []

      if (this.filter_JobStatus !== null && this.filter_JobStatus !== "") {
        filterParams["status"] = this.filter_JobStatus.toUpperCase()
      }
      if (this.filter_JobStation !== null && this.filter_JobStation !== "") {
        filterParams["station"] = this.filter_JobStation
      }
      if (this.filter_JobSkycar !== null && this.filter_JobSkycar !== "") {
        filterParams["skycar"] = this.filter_JobSkycar
      }

      var filteredJobs = this.items_jobs.filter((job) => {
        for (var key in filterParams) {
          var filterValue = filterParams[key];

          if (typeof job[key] === "string" && typeof filterValue === "string") {
            if (!job[key].toUpperCase().includes(filterValue)) {
              return false;
            }
          } else {
            if (filterValue !== null && job[key] !== filterValue) {
              return false
            }
          }
        }
        return true;
      })

      return filteredJobs

    },

    filteredRequests() {
      var filterParams = []

      if (this.filter_RequestStatus !== null && this.filter_RequestStatus !== "") {
        filterParams["status"] = this.filter_RequestStatus.toUpperCase()
      }
      if (this.filter_RequestStation !== null && this.filter_RequestStation !== "") {
        filterParams["station"] = this.filter_RequestStation
      }
      if (this.filter_RequestSkycar !== null && this.filter_RequestSkycar !== "") {
        filterParams["skycar"] = this.filter_RequestSkycar
      }
      if (this.filter_RequestJobId !== null && this.filter_RequestJobId !== "") {
        filterParams["job_id"] = this.filter_RequestJobId
      }

      var filteredRequests = this.items_requests.filter((request) => {
        for (var key in filterParams) {
          var filterValue = filterParams[key];

          if (typeof request[key] === "string" && typeof filterValue === "string") {
            if (!request[key].toUpperCase().includes(filterValue)) {
              return false;
            }
          } else {
            if (filterValue !== null && request[key] !== filterValue) {
              return false
            }
          }
        }
        return true;
      })

      return filteredRequests

    }

  }
};

async function onClickGet(route) {
  var cmHost = new URL(getCMHost() + route);
    const response = await fetch(cmHost, { method: "GET", headers:getRequestHeader() });
    const myJson = await response.json()
    return myJson
}


// async function apiPost(route, json) {
//     var api_route = new URL(getCMHost() + route) 
//     await fetch(api_route, {
//       method: "POST",
//       headers: {"Content-Type": "application/json"},
//       body: JSON.stringify(json)
//     }).then(async response => {
//       const data = await response.json();
//       if (!response.ok) {
//         const error = (data) || response.status
//         return Promise.reject(error)
//       }
//       return data
//     }).catch(error => {
//       console.log(error)
//       return error
//     })
// }
</script>