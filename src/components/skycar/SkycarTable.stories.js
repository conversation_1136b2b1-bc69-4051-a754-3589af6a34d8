import Vue from 'vue';
import Vuetify from 'vuetify/lib';
import 'vuetify/dist/vuetify.min.css';
import SkycarTable from './SkycarTable.vue';

Vue.use(Vuetify);

export default {
  title: 'Skycar/SkycarTable',
  component: SkycarTable,
  parameters: {
    docs: {
      description: {
        component: 'SkycarTable component showcasing different job states: active jobs, pre-enqueue jobs (OTW), tentative jobs, and various skycar statuses with elapsed time functionality.'
      }
    }
  },
  argTypes: {
    items: { control: 'object', description: 'Array of skycar objects with various job states' },
    headers: { control: 'object', description: 'Table headers configuration' }
  }
};

const vuetify = new Vuetify();

export const SkycarDataTable = (args, { argTypes }) => ({
  vuetify,
  components: { SkycarTable },
  props: Object.keys(argTypes),
  template: `
    <v-app dark>
      <v-container fluid>
        <v-card dark class="pa-4">
          <v-card-title class="headline">SkycarTable - Job States Demo</v-card-title>
          <v-card-subtitle>Showcasing active jobs, pre-enqueue (OTW), tentative jobs, and elapsed time</v-card-subtitle>
          <SkycarTable 
            :headers="headers" 
            :items="items" 
            @skycar-detail="onSkycarDetail"
            @job-detail="onJobDetail"
            @action-dialog="onActionDialog"
            @event-log="onEventLog"
          />
        </v-card>
      </v-container>
    </v-app>
  `,
  methods: {
    onSkycarDetail(item) {
      console.log('Skycar Detail:', item);
    },
    onJobDetail(jobId) {
      console.log('Job Detail:', jobId);
    },
    onActionDialog(item) {
      console.log('Action Dialog:', item);
    },
    onEventLog(item) {
      console.log('Event Log:', item);
    }
  }
});

SkycarDataTable.args = {
  headers: [
    {
      text: "Skycar ID",
      align: "left",
      sortable: true,
      value: "skycar_id",
    },
    { text: "Status", value: "status" },
    { text: "Connect", value: "connect" },
    { text: "Pair", value: "pair" },
    { text: "Coordinate", value: "coordinate" },
    { text: "Job", value: "job_id" },
    { text: "Job Elapsed", value: "job_elapsed" },
    { text: "Winch", value: "winch" },
    { text: "Battery %", value: "battery" },
    { text: "Mode", value: "maintenance_mode" },
    { text: "Action", value: "action" },
  ],
  items: [
         // Skycar with Active Job (Priority 1) - Left Winch Only
     {
       skycar_id: 1,
       status: 'AVAILABLE',
       connect: true,
       pair: true,
       direction: 'X',
       coordinate: '16,1,0',
       storage_no: null,
       battery: 85,
       mode: 'Normal',
       orientation: 'North',
       maintenance_mode: 'TRAVEL',
       is_docked: false,
       winch: {
         BOTH: {
           storage_no: 'SBG-001234',
           is_active: true,
           bin_on_hold: false,
           assign_storage_code: 1234,
           platform: false
         }
       },
       tentative_job_id: [45], // Still has upcoming jobs
       job_id: 42, // Currently working on this job
       preenqueue_job_id: null, // No pre-enqueue since actively working
       job_elapsed_second: 180, // 3 minutes elapsed
       job_begin_at: "2024-01-15T10:30:00Z"
     },
    
         // Skycar with Pre-enqueue Job (Priority 2) - OTW to job
     {
       skycar_id: 2,
       status: 'AVAILABLE',
       connect: true,
       pair: true,
       direction: 'Y',
       coordinate: '8,15,0',
       storage_no: null,
       battery: 92,
       mode: 'Normal',
       orientation: 'South',
       maintenance_mode: 'TRAVEL',
       is_docked: false,
             winch: {
         BOTH: {
           storage_no: 'SBG-005678',
           is_active: true,
           bin_on_hold: false,
           assign_storage_code: 5678,
           platform: false
         }
       },
      tentative_job_id: [48 ], // Multiple upcoming jobs
      job_id: null, // No active job yet
      preenqueue_job_id: 47, // OTW to this job location
      job_elapsed_second: 65, // 1 minute 5 seconds since started moving
      job_begin_at: null
    },

         // Skycar with only Tentative Jobs (Priority 3) - Right Winch Only
     {
       skycar_id: 3,
       status: 'AVAILABLE',
       connect: true,
       pair: true,
       direction: 'X',
       coordinate: '12,8,0',
       storage_no: null,
       battery: 100,
       mode: 'Normal',
       orientation: 'East',
       maintenance_mode: 'DOCK',
       is_docked: true,
       winch: {
         BOTH: {
           storage_no: 'SBG-003456',
           is_active: true,
           bin_on_hold: false,
           assign_storage_code: 3456,
           platform: false
         }
       },
       tentative_job_id: [51], // Only upcoming jobs planned
       job_id: null, // No active job
       preenqueue_job_id: null, // Not moving to any job yet
       job_elapsed_second: null, // No elapsed time
       job_begin_at: null
     },

         // Skycar with Bin on Hold
     {
       skycar_id: 4,
       status: 'AVAILABLE',
       connect: true,
       pair: false,
       direction: 'Y',
       coordinate: '20,5,0',
       storage_no: 'SBG-009876 (A)',
       battery: 78,
       mode: 'Normal',
       orientation: 'West',
       maintenance_mode: 'PARK',
       is_docked: false,
             winch: {
         BOTH: {
           storage_no: 'SBG-009876',
           is_active: true,
           bin_on_hold: true, // Bin is on hold
           assign_storage_code: 9876,
           platform: false
         }
       },
      tentative_job_id: null,
      job_id: null, // bin on hold = dont have job
      preenqueue_job_id: null,
      job_elapsed_second: null , 
      job_begin_at: null
    },

         // Skycar in Manual Mode - Both Winches with Different States
     {
       skycar_id: 5,
       status: 'AVAILABLE',
       connect: true,
       pair: true,
       direction: 'X',
       coordinate: '5,12,0',
       storage_no: null,
       battery: 88,
       mode: 'Manual', // Manual mode
       orientation: 'North',
       maintenance_mode: 'DRY_RUN',
       is_docked: false,
       winch: {
         BOTH: {
           storage_no: null,
           is_active: false, // Inactive winch in manual mode
           bin_on_hold: false,
           assign_storage_code: 9999, // Has assignment but no storage
           platform: false
         }
       },
       tentative_job_id: null,
       job_id: null,
       preenqueue_job_id: null,
       job_elapsed_second: null,
       job_begin_at: null
     },

         // Skycar with Connection Issues - Mixed Winch States
     {
       skycar_id: 6,
       status: 'ERROR',
       connect: false, // Disconnected
       pair: false,
       direction: 'Unknown',
       coordinate: '0,0,0',
       storage_no: null,
       battery: 45, // Low battery
       mode: 'Normal',
       orientation: 'Unknown',
       maintenance_mode: 'INSPECT',
       is_docked: false,
       winch: {
         BOTH: {
           storage_no: 'SBG-006789',
           is_active: false, // Inactive due to error
           bin_on_hold: true, // Stuck bin on hold
           assign_storage_code: 6789,
           platform: false
         }
       },
       tentative_job_id: null,
       job_id: null,
       preenqueue_job_id: null,
       job_elapsed_second: null,
       job_begin_at: null
     },

         // Skycar with Long Running Job (demonstrating different elapsed time colors)
     {
       skycar_id: 7,
       status: 'AVAILABLE',
       connect: true,
       pair: true,
       direction: 'Y',
       coordinate: '25,18,0',
       storage_no: 'SBG-111222 (B)',
       battery: 65,
       mode: 'Normal',
       orientation: 'South',
       maintenance_mode: 'QR_ENROLL',
       is_docked: false,
             winch: {
         BOTH: {
           storage_no: 'SBG-111222',
           is_active: true,
           bin_on_hold: false,
           assign_storage_code: 1111,
           platform: false
         }
       },
      tentative_job_id: [60],
      job_id: 58, // Long running job
      preenqueue_job_id: null,
      job_elapsed_second: 2700, // 45 minutes (very long running - red color)
      job_begin_at: "2024-01-15T09:45:00Z"
    },

         // Skycar in Maintenance Mode - On Platform
     {
       skycar_id: 8,
       status: 'MAINTENANCE',
       connect: true,
       pair: true,
       direction: 'X',
       coordinate: '30,25,0',
       storage_no: null,
       battery: 100,
       mode: 'Normal',
       orientation: 'North',
       maintenance_mode: 'CHARGE_IN',
       is_docked: true,
       winch: {
         BOTH: {
           storage_no: null,
           is_active: false,
           bin_on_hold: false,
           assign_storage_code: null,
           platform: true // On platform
         }
       },
       tentative_job_id: null,
       job_id: null,
       preenqueue_job_id: null,
       job_elapsed_second: null,
       job_begin_at: null
     }
  ]
};

// Additional story showing different elapsed time scenarios
export const ElapsedTimeStates = (args, { argTypes }) => ({
  vuetify,
  components: { SkycarTable },
  props: Object.keys(argTypes),
  template: `
    <v-app dark>
      <v-container fluid>
        <v-card dark class="pa-4">
          <v-card-title class="headline">Elapsed Time Color Coding</v-card-title>
          <v-card-subtitle>Green (&lt;5min) → Blue (5-15min) → Orange (15-30min) → Red (&gt;30min)</v-card-subtitle>
          <SkycarTable :headers="headers" :items="items" />
        </v-card>
      </v-container>
    </v-app>
  `
});

 ElapsedTimeStates.args = {
   headers: SkycarDataTable.args.headers,
   items: [
     {
       skycar_id: 101,
       status: 'AVAILABLE',
       connect: true,
       pair: true,
       coordinate: '10,10,0',
       battery: 90,
       mode: 'Normal',
       maintenance_mode: 'TRAVEL',
       winch: { 
         BOTH: { storage_no: 'SBG-101111', is_active: true, bin_on_hold: false, assign_storage_code: 1111, platform: false }
       },
       job_id: 201,
       job_elapsed_second: 120, // 2 minutes - GREEN
     },
     {
       skycar_id: 102,
       status: 'AVAILABLE',
       connect: true,
       pair: true,
       coordinate: '11,11,0',
       battery: 85,
       mode: 'Normal',
       maintenance_mode: 'DOCK',
       winch: { 
         BOTH: { storage_no: 'SBG-102222', is_active: true, bin_on_hold: false, assign_storage_code: 2222, platform: false }
       },
       preenqueue_job_id: 202,
       job_elapsed_second: 600, // 10 minutes - BLUE
     },
     {
       skycar_id: 103,
       status: 'AVAILABLE',
       connect: true,
       pair: true,
       coordinate: '12,12,0',
       battery: 80,
       mode: 'Normal',
       maintenance_mode: 'PARK',
       winch: { 
         BOTH: { storage_no: 'SBG-103333', is_active: true, bin_on_hold: false, assign_storage_code: 3333, platform: false }
       },
       job_id: 203,
       job_elapsed_second: 1200, // 20 minutes - ORANGE
     },
     {
       skycar_id: 104,
       status: 'AVAILABLE',
       connect: true,
       pair: true,
       coordinate: '13,13,0',
       battery: 75,
       mode: 'Normal',
       maintenance_mode: 'CHARGE_OUT',
       winch: { 
         BOTH: { storage_no: 'SBG-104555', is_active: true, bin_on_hold: false, assign_storage_code: 4555, platform: false }
       },
       job_id: 204,
       job_elapsed_second: 2400, // 40 minutes - RED
     }
   ]
 };

// Story showing job state progression
export const JobStateProgression = (args, { argTypes }) => ({
  vuetify,
  components: { SkycarTable },
  props: Object.keys(argTypes),
  template: `
    <v-app dark>
      <v-container fluid>
        <v-card dark class="pa-4">
          <v-card-title class="headline">Job State Progression</v-card-title>
          <v-card-subtitle>Tentative → Pre-enqueue (OTW) → Active Job</v-card-subtitle>
          <SkycarTable :headers="headers" :items="items" />
        </v-card>
      </v-container>
    </v-app>
  `
});

 JobStateProgression.args = {
   headers: SkycarDataTable.args.headers,
   items: [
     {
       skycar_id: 301,
       status: 'AVAILABLE',
       connect: true,
       pair: true,
       coordinate: '5,5,0',
       battery: 100,
       mode: 'Normal',
       maintenance_mode: 'PARK',
       winch: { 
         BOTH: { storage_no: null, is_active: true, bin_on_hold: false, assign_storage_code: null, platform: false }
       },
       tentative_job_id: [401, 402, 403], // Only tentative jobs
       job_id: null,
       preenqueue_job_id: null,
       job_elapsed_second: null,
     },
     {
       skycar_id: 302,
       status: 'AVAILABLE',
       connect: true,
       pair: true,
       coordinate: '15,8,0',
       battery: 95,
       mode: 'Normal',
       maintenance_mode: 'TRAVEL',
       winch: { 
         BOTH: { storage_no: 'SBG-302111', is_active: true, bin_on_hold: false, assign_storage_code: 3021, platform: false }
       },
       tentative_job_id: [405, 406], // Still has upcoming jobs
       job_id: null,
       preenqueue_job_id: 404, // OTW to this job
       job_elapsed_second: 45, // Started moving 45 seconds ago
     },
     {
       skycar_id: 303,
       status: 'AVAILABLE',
       connect: true,
       pair: true,
       coordinate: '25,12,0',
       battery: 90,
       mode: 'Normal',
       maintenance_mode: 'CHARGE_IN_FROM_DOCK',
       winch: { 
         BOTH: { storage_no: 'SBG-007890', is_active: true, bin_on_hold: false, assign_storage_code: 7890, platform: false }
       },
       tentative_job_id: [408], // Still has more jobs coming
       job_id: 407, // Now actively working
       preenqueue_job_id: null,
       job_elapsed_second: 300, // 5 minutes into the job
     }
   ]
 }; 