import GWButton from './GWButton.vue';

export default {
  title: 'Skycar/GWButton',
  component: GWButton,
  argTypes: {
    gwData: {
      control: 'object',
      description: 'Gateway data object containing connection status and client info'
    },
    loading: {
      control: 'boolean',
      description: 'Show loading state on button'
    },
    showNotification: {
      action: 'showNotification',
      description: 'Function to show notifications'
    },
    'sync-gw': {
      action: 'sync-gw',
      description: 'Emitted when sync button is clicked'
    }
  },
  parameters: {
    docs: {
      description: {
        component: 'A reusable Gateway Button component that displays connection status and allows copying gateway IP address.'
      }
    }
  }
};

const Template = (args, { argTypes }) => ({
  components: { GWButton },
  props: Object.keys(argTypes),
  template: '<GWButton v-bind="$props" @sync-gw="syncGw" />',
  methods: {
    syncGw() {
      console.log('Gateway sync requested');
    }
  }
});

export const Disconnected = Template.bind({});
Disconnected.args = {
  gwData: {
    clients: null,
    allClients: null,
    btnColor: 'red',
    btnText: 'GW Disconnected'
  },
  loading: false,
  showNotification: (success, message) => {
    console.log(`${success ? 'Success' : 'Error'}: ${message}`);
  }
};

export const Connected = Template.bind({});
Connected.args = {
  gwData: {
    clients: {
      ip: '*************',
      port: '9080',
      is_gateway: true,
      socket_id: 'gateway_123',
      type: 'Gateway',
      tag: 'Main Gateway'
    },
    allClients: [],
    btnColor: 'green',
    btnText: 'GW Connected'
  },
  loading: false,
  showNotification: (success, message) => {
    alert(`${success ? 'Success' : 'Error'}: ${message}`);
  }
};

export const Loading = Template.bind({});
Loading.args = {
  gwData: {
    clients: null,
    allClients: null,
    btnColor: 'orange',
    btnText: 'GW Syncing...'
  },
  loading: true,
  showNotification: (success, message) => {
    console.log(`${success ? 'Success' : 'Error'}: ${message}`);
  }
};

Loading.parameters = {
  docs: {
    description: {
      story: 'Shows the orange loading state when the syncGW API call is in progress. Features a sync icon and loading spinner.'
    }
  }
};

export const ServerUnreachable = Template.bind({});
ServerUnreachable.args = {
  gwData: {
    clients: null,
    allClients: null,
    btnColor: 'red',
    btnText: 'Server Unreachable'
  },
  loading: false,
  showNotification: (success, message) => {
    console.log(`${success ? 'Success' : 'Error'}: ${message}`);
  }
};

ServerUnreachable.parameters = {
  docs: {
    description: {
      story: 'Shows the red server unreachable state when the syncGW API call fails due to server being unreachable or network issues.'
    }
  }
}; 