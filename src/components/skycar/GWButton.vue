<template>
  <v-btn
    @click="handleSync"
    dark
    class="ma-2"
    :color="gwData.btnColor"
    :loading="loading"
  >
    <v-icon left>
      {{ gwData.btnColor === 'green' ? 'mdi-lan-connect' : 
         gwData.btnColor === 'orange' ? 'mdi-sync' : 'mdi-lan-disconnect' }}
    </v-icon>
    <span>
      {{ gwData.btnText }}
      <template v-if="gwData.clients && gwData.clients.ip">
        <v-tooltip bottom>
          <template v-slot:activator="{ on, attrs }">
            <span
              v-bind="attrs"
              v-on="on"
              class="ml-2 gateway-ip clickable"
              @click.stop="copyGatewayAddress"
            >
              ({{ gwData.clients.ip }})
              <v-icon small class="ml-1">mdi-content-copy</v-icon>
            </span>
          </template>
          <span>Click to copy IP</span>
        </v-tooltip>
      </template>
    </span>
  </v-btn>
</template>

<script>
export default {
  name: 'G<PERSON><PERSON><PERSON><PERSON>',
  props: {
    gwData: {
      type: Object,
      required: true,
      default: () => ({
        clients: null,
        allClients: null,
        btnColor: 'red',
        btnText: 'GW Disconnected'
      })
    },
    loading: {
      type: Boolean,
      default: false
    },
    showNotification: {
      type: Function,
      default: () => {}
    }
  },
  emits: ['sync-gw'],
  methods: {
    handleSync() {
      this.$emit('sync-gw');
    },
    
    async copyGatewayAddress() {
      if (this.gwData.clients && this.gwData.clients.ip) {
        const text = `${this.gwData.clients.ip}`;
        
        try {
          // Check if clipboard API is available and secure context
          if (navigator.clipboard && window.isSecureContext) {
            await navigator.clipboard.writeText(text);
            this.showNotification(true, `Copied: ${text}`);
          } else {
            // Fallback for insecure contexts or unsupported browsers
            this.fallbackCopyTextToClipboard(text);
          }
        } catch (err) {
          console.error('Failed to copy to clipboard:', err);
          // Try fallback method
          this.fallbackCopyTextToClipboard(text);
        }
      }
    },
    
    fallbackCopyTextToClipboard(text) {
      // Create a temporary textarea element
      const textArea = document.createElement('textarea');
      textArea.value = text;
      
      // Avoid scrolling to bottom
      textArea.style.top = '0';
      textArea.style.left = '0';
      textArea.style.position = 'fixed';
      textArea.style.opacity = '0';
      
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      
      try {
        // Use the older execCommand method as fallback
        const successful = document.execCommand('copy');
        if (successful) {
          this.showNotification(true, `Copied: ${text}`);
        } else {
          this.showNotification(false, 'Failed to copy to clipboard');
        }
      } catch (err) {
        console.error('Fallback copy failed:', err);
        this.showNotification(false, 'Copy to clipboard not supported');
      }
      
      document.body.removeChild(textArea);
    }
  }
};
</script>

<style scoped>
.gateway-ip {
  cursor: pointer;
  color: #90caf9;
  text-decoration: underline;
  transition: all 0.2s ease;
}

.gateway-ip:hover {
  color: #64b5f6;
  text-decoration-color: #64b5f6;
  transform: translateY(-1px);
}

.clickable {
  cursor: pointer;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.clickable:active {
  transform: translateY(0px);
}
</style> 