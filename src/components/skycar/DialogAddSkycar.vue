<template>
  <v-dialog 
    v-model="dialogBool" 
    width="1200"
  >
    <v-card dark>
      <v-toolbar 
        color="primary"
        dark
      >
        <v-icon class="mx-2">
          mdi-taxi
        </v-icon>
        <v-toolbar-title>Enroll Skycar</v-toolbar-title>
      </v-toolbar>
      <v-card-text>
        <v-form v-model="form">
          <v-row>
            <v-col 
              cols="12" 
              sm="6" 
              md="3"
            >
              <v-text-field
                label="Skycar ID"
                placeholder="int only"
                filled
                v-model="skycar"
                type="number"
                :rules="[(v) => !!v || 'Required']"
              />
            </v-col>
            <v-col 
              cols="12"   
              sm="6" 
              md="3"
            >
              <v-text-field
                label="Coordinate X"
                placeholder="int only"
                filled
                v-model="coordX"
                type="number"
                :rules="[(v) => v !== null && v !== undefined && v !== '' || 'Required']"
              >
                <template #prepend-inner>
                  <v-icon 
                    @click="openGrid()" 
                    style="margin-right: 5px;"
                  >
                    mdi-grid
                  </v-icon>
                </template>
              </v-text-field>
            </v-col>

            <v-col 
              cols="12" 
              sm="6" 
              md="3"
            >
              <v-text-field
                label="Coordinate Y"
                placeholder="int only"
                filled
                v-model="coordY"
                type="number"
                :rules="[(v) => v !== null && v !== undefined && v !== '' || 'Required']"
              >
                <template #prepend-inner>
                  <v-icon 
                    @click="openGrid()" 
                    style="margin-right: 5px;"
                  >
                    mdi-grid
                  </v-icon>
                </template>
              </v-text-field>
            </v-col>
          </v-row>
          <v-row>
            <v-col md="3">
              <v-select
                filled
                v-model="orientation"
                :items="orientationOptions"
                label="Orientation"
                dense
              />
            </v-col>
          </v-row>
          <v-row>
            <v-col>
              <v-btn
                @click="btnConfirm()"
                color="green"
                :disabled="!form || !doneSync"
              >
                Confirm Enroll
              </v-btn>
            </v-col>
          </v-row>
          <DialogCoordinateSelection 
            ref="dialogCoordinateSelection" 
            @update-coord="updateCoord"
          />
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
import {
  getHost,
  getMapping,
  getRequestHeader,
  useRefreshToken,
} from "../../helper/common";
import { BatteryOrientation, RouteSkycar } from "../../helper/enums";
import DialogCoordinateSelection from "../dialogs/DialogCoordinateSelection";

export default {
  components: {
    DialogCoordinateSelection
  },
  data: () => ({
    dialogBool: false,
    doneSync: true,
    skycar: null,
    coordX: null,
    coordY: null,
    cube: null,
    orientation: BatteryOrientation.NORTH,
    orientationOptions: Object.values(BatteryOrientation),
    form: null,
  }),
  props: {
    showNotification: {
      type: Function,
    },
    syncSkycar: {
      type: Function,
    },
  },
  methods: {
    openGrid(){
      this.$refs.dialogCoordinateSelection.openDialog(this.cube)
    },
    updateCoord(selectedCells){
      if (selectedCells.length > 0) {
        this.coordX = String(selectedCells[0].x);
        this.coordY = String(selectedCells[0].y);
      }
    },
    openDialog(cube) {
      this.cube = cube;
      this.skycar = null;
      this.coordX = null;
      this.coordY = null;
      this.dialogBool = true;
    },
    closeDialog() {
      this.dialogBool = false;
    },
    async btnConfirm() {
      try {
        this.doneSync = false;
        let url = getHost(this.cube) + RouteSkycar.SKYCAR;
        let req = await fetch(url, {
          method: "POST",
          body: JSON.stringify({
            id: this.skycar,
            x: this.coordX,
            y: this.coordY,
            zone: getMapping(this.cube),
            orientation: this.orientation,
            // eslint-disable-next-line camelcase
          }),
          headers: getRequestHeader(),
        });
        let res = JSON.parse(await req.text());
        if (res.code === 401) {
          // If access token is unauthorized
          // use refresh token to get new access token from auth server
          return useRefreshToken(this, this.btnConfirm);
        }
        if (res.status) {
          this.closeDialog();
          this.showNotification(
            true,
            `Skycar ${this.skycar} is enrolled successfully.`
          );
          this.syncSkycar();
        } else {
          this.showNotification(false, res.message);
        }
      } catch (error) {
        this.showNotification(false, error);
      } finally {
        setTimeout(() => {
          this.doneSync = true;
        }, 500);
      }
    },
  },
};
</script>
