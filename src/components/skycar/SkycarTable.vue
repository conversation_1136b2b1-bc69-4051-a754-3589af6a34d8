<template>
  <v-data-table
    v-model="selected"
    :headers="headers"
    item-key="skycar_id"
    :items="items"
    :items-per-page="25"
    group-by="status"
    class="elevation-1"
    dark
    sort-by="skycar_id"
    @input="$emit('update:selected', $event)"
  >
    <!-- cell filtering -->
    <template v-slot:[`item.skycar_id`]="{ item }">
      <v-btn
        text
        small
        :class="getSidColor(item.mode)"
        @click="$emit('skycar-detail', item)"
      >
        <v-icon left size="16">mdi-car</v-icon>
        {{ item.skycar_id }}
      </v-btn>
    </template>

    <template v-slot:[`item.connect`]="{ item }">
      <v-chip
        :color="getColor(item.connect)"
        dark
      >
        {{ getStatus(item.connect) }}
      </v-chip>
    </template>

    <template v-slot:[`item.pair`]="{ item }">
      <v-chip
        :color="getColor(item.pair)"
        dark
      >
        {{ getStatus(item.pair) }}
      </v-chip>
    </template>

    <template v-slot:[`item.winch`]="{ item }">
      <div class="d-flex align-center">
        <div class="position-relative">
          <v-chip
            v-for="[position, winch] in Object.entries(item.winch).sort()"
            :key="position"
            :color="getWinchColor(winch.is_active, winch.assign_storage_code , winch.storage_no)"
            class="ml-1 fixed-width-chip"
            dark
          >
            <v-tooltip bottom>
              <template v-slot:activator="{ on, attrs }">
                <span
                  v-bind="attrs"
                  v-on="on"
                >
                  {{ getStorage(position[0], winch.storage_no) }}
                </span>
              </template>
              <span> {{ winch.assign_storage_code }} </span>
            </v-tooltip>
          </v-chip>
        </div>
        
        <!-- Bin on Hold Indicator -->
        <v-tooltip bottom v-if="hasBinOnHold(item.winch)">
          <template v-slot:activator="{ on, attrs }">
            <v-chip
              v-bind="attrs"
              v-on="on"
              small
              color="orange"
              text-color="white"
              class="ml-2 pulse-animation"
            >
              <v-icon left size="16">mdi-pause-circle</v-icon>
             Bin on hold
            </v-chip>
          </template>
          <div class="pa-2">
            <div class="text-subtitle2 mb-1">🔒 Bin on Hold</div>
            <div class="text-caption">
              This skycar has a bin that is currently on hold waiting for SM dispatch job.
              <br/>
              <strong>Storage:</strong> {{ getBinOnHoldInfo(item.winch) }}
            </div>
          </div>
        </v-tooltip>
      </div>
    </template>

    <template v-slot:[`item.job_id`]="{ item }">
      <div>
        <!-- Priority 1: Active and Confirmed Job (Currently Working) -->
        <div v-if="item.job_id">
          <v-tooltip bottom>
            <template v-slot:activator="{ on, attrs }">
              <v-btn
                text
                small
                color="blue"
                v-bind="attrs"
                v-on="on"
                @click="$emit('job-detail', item.job_id)"
              >
                <v-icon left size="16">mdi-briefcase-outline</v-icon>
                {{ item.job_id }}
              </v-btn>
            </template>
            <span>Active and confirmed job (currently working)</span>
          </v-tooltip>
        </div>
        <!-- Priority 2: Pre-enqueue Job (Moving to confirmed job location) -->
        <div v-else-if="item.preenqueue_job_id">
          <v-tooltip bottom>
            <template v-slot:activator="{ on, attrs }">
              <v-btn
                text
                small
                color="orange"
                v-bind="attrs"
                v-on="on"
                @click="$emit('job-detail', item.preenqueue_job_id)"
              >
                <v-icon left size="16">mdi-taxi</v-icon>
                {{ item.preenqueue_job_id }}
              </v-btn>
            </template>
            <span>Heading to job pickup coordinate</span>
          </v-tooltip>
        </div>
        <!-- Priority 3: No Active or Pre-enqueue Job -->
        <div v-else>
          <span class="grey--text">No Active Job</span>
        </div>
        
        <!-- Tentative Jobs (Upcoming to-do jobs) -->
        <div v-if="item.tentative_job_id && item.tentative_job_id.length" class="mt-1">
          <v-tooltip bottom>
            <template v-slot:activator="{ on, attrs }">
              <span v-bind="attrs" v-on="on">
                <v-chip
                  v-for="tid in item.tentative_job_id"
                  :key="tid"
                  small
                  color="orange lighten-2"
                  class="ml-1"
                  text-color="black"
                  @click.stop="$emit('job-detail', tid)"
                  style="cursor:pointer;"
                >
                  <v-icon left small>mdi-timer-sand</v-icon>
                  {{ tid }}
                </v-chip>
              </span>
            </template>
            <span>Upcoming to-do job(s) (will become pre-enqueue → active)</span>
          </v-tooltip>
        </div>
      </div>
    </template>

    <template v-slot:[`item.job_elapsed`]="{ item }">
      <div v-if="getElapsedSeconds(item) !== null" class="d-flex align-center">
        <v-icon size="16" color="blue" class="mr-1">mdi-clock-outline</v-icon>
        <v-chip
          :color="getElapsedTimeColor(getElapsedSeconds(item))"
          dark
          small
          class="font-weight-medium"
        >
          {{ formatElapsedTime(getElapsedSeconds(item)) }}
        </v-chip>
      </div>
      <span v-else class="grey--text text--lighten-1">
        <v-icon size="16" color="grey" class="mr-1">mdi-clock-off</v-icon>
        No Job
      </span>
    </template>

    <template v-slot:[`item.action`]="{ item }">
      <v-btn
        small
        class="mr-2"
        light
        @click="$emit('action-dialog', item)"
      >
        Action
      </v-btn>
      <v-btn
        small
        class="mr-2"
        light
        @click="$emit('event-log', item)"
      >
        Event Log
      </v-btn>
    </template>

  </v-data-table>
</template>

<script>
import { getStatus } from "../../helper/common.js";

const PositionMapping = {
  L: "A",
  R: "B",
  B: ""
}

export default {
  name: 'SkycarTable',
  props: {
    headers: { type: Array, required: true },
    items: { type: Array, required: true },
    selected: { type: Array, default: () => [] }
  },
  data() {
    return {
      elapsedSeconds: {}, // Track elapsed seconds for each skycar
      updateTimer: null   // Timer for auto-increment
    }
  },
  mounted() {
    this.initializeElapsedTimes()
    this.startTimer()
  },
  beforeDestroy() {
    this.stopTimer()
  },
  watch: {
    items: {
      handler(newItems) {
        this.updateElapsedTimes(newItems)
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    getStorage(position, storageNo) {
      return `${PositionMapping[position]} ${ storageNo ? storageNo : "EMPTY" }`
    },
    hasBinOnHold(winchData) {
      // Check if any winch position has bin_on_hold set to true
      return Object.values(winchData).some(winch => winch.bin_on_hold === true);
    },
    getBinOnHoldInfo(winchData) {
      // Get storage info for bins that are on hold
      const holdBins = Object.entries(winchData)
        .filter(([, winch]) => winch.bin_on_hold === true)
        .map(([position, winch]) => {
          if (position === 'BOTH') {
            return winch.storage_no || 'Unknown';
          } else {
            return `${PositionMapping[position]} - ${winch.storage_no || 'Unknown'}`;
          }
        });
      return holdBins.join(', ') || 'Unknown';
    },
    getColor(boolean) {
      if (boolean === true) {
        return "green";
      } else {
        return "red";
      }
    },
    getWinchColor(isActive , assignStorageCode, storageCode){
    // assign storage code = Macaroni and Cheese #F2BB66
    // with storage = green
    // inactive = red
      return isActive === false ? "red" : (isActive && assignStorageCode && !storageCode) ? "#F2BB66" : "green";
    },
    getStatus(bool) {
      return getStatus(bool)[1];
    },
    getSidColor(mode) {
      switch (mode) {
        case "Normal":
          return "green--text";
        case "Manual":
          return "orange--text";  
        default:
          return "white--text";
      }
    },
    initializeElapsedTimes() {
      // Initialize elapsed seconds from server data
      this.items.forEach(item => {
        if (item.skycar_id) {
          // Use Vue.set to ensure reactivity
          if (item.job_elapsed_second !== undefined && item.job_elapsed_second !== null) {
            this.$set(this.elapsedSeconds, item.skycar_id, item.job_elapsed_second)
          } else {
            // Set to null if no job/preenqueue job or no elapsed time
            this.$set(this.elapsedSeconds, item.skycar_id, null)
          }
        }
      })
    },
    updateElapsedTimes(newItems) {
      // Update elapsed seconds when new data arrives from server/socket
      newItems.forEach(item => {
        if (item.skycar_id) {
          // Use Vue.set to ensure reactivity
          if (item.job_elapsed_second !== undefined && item.job_elapsed_second !== null) {
            this.$set(this.elapsedSeconds, item.skycar_id, item.job_elapsed_second)
          } else if (!item.job_id && !item.preenqueue_job_id) {
            // Clear elapsed time if no active job or pre-enqueue job
            this.$set(this.elapsedSeconds, item.skycar_id, null)
          }
        }
      })
    },
    startTimer() {
      // Start the auto-increment timer
      this.updateTimer = setInterval(() => {
        this.incrementElapsedTimes()
      }, 1000) // Update every second
    },
    stopTimer() {
      // Stop the auto-increment timer
      if (this.updateTimer) {
        clearInterval(this.updateTimer)
        this.updateTimer = null
      }
    },
    incrementElapsedTimes() {
      // Increment elapsed seconds for skycars that have active jobs or are in pre-enqueue phase
      this.items.forEach(item => {
        // Only increment if skycar has an active job or preenqueue job and elapsed time is valid
        if (item.skycar_id && 
            (item.job_id || item.preenqueue_job_id) && // Must have an active job or pre-enqueue job
            this.elapsedSeconds[item.skycar_id] !== undefined && 
            this.elapsedSeconds[item.skycar_id] !== null && 
            this.elapsedSeconds[item.skycar_id] >= 0) {
          // Use Vue.set to ensure reactivity
          this.$set(this.elapsedSeconds, item.skycar_id, this.elapsedSeconds[item.skycar_id] + 1)
        }
      })
    },
    getElapsedSeconds(item) {
      // Get current elapsed seconds for a skycar
      if (!item.skycar_id) return null
      
      const elapsed = this.elapsedSeconds[item.skycar_id]
      if (elapsed === undefined || elapsed < 0) return null
      
      return elapsed
    },
    formatElapsedTime(seconds) {
      // Format elapsed seconds into readable time
      if (seconds === null || seconds < 0) return 'N/A'
      
      // Ensure we're working with whole numbers only
      const totalSeconds = Math.floor(seconds)
      const hours = Math.floor(totalSeconds / 3600)
      const minutes = Math.floor((totalSeconds % 3600) / 60)
      const secs = totalSeconds % 60
      
      if (hours > 0) {
        return `${hours}h ${minutes}m`
      } else if (minutes > 0) {
        return `${minutes}m ${secs}s`
      } else {
        return `${secs}s`
      }
    },
    getElapsedTimeColor(seconds) {
      // Color coding based on elapsed time in seconds
      if (seconds === null || seconds < 0) return 'grey'
      
      const minutes = Math.floor(seconds / 60)
      
      if (minutes < 5) {
        return 'green' // Fresh job (< 5 minutes)
      } else if (minutes < 15) {
        return 'blue' // Normal job (5-15 minutes)
      } else if (minutes < 30) {
        return 'orange' // Long running job (15-30 minutes)
      } else {
        return 'red' // Very long running job (> 30 minutes)
      }
    }
  }
}
</script>

<style scoped>
.fixed-width-chip {
  width: 120px
}

.pulse-animation {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
</style> 