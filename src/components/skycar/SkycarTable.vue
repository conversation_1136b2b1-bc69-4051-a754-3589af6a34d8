<template>
  <v-data-table
    v-model="selected"
    :headers="headers"
    item-key="skycar_id"
    :items="items"
    :items-per-page="25"
    group-by="status"
    class="elevation-1"
    dark
    sort-by="skycar_id"
    @input="$emit('update:selected', $event)"
  >
    <!-- cell filtering -->
    <template v-slot:[`item.skycar_id`]="{ item }">
      <v-btn
        text
        small
        :class="getSidColor(item.mode)"
        @click="$emit('skycar-detail', item)"
      >
        <v-icon left size="16">mdi-car</v-icon>
        {{ item.skycar_id }}
      </v-btn>
    </template>

    <template v-slot:[`item.connect`]="{ item }">
      <v-chip
        :color="getColor(item.connect)"
        dark
      >
        {{ getStatus(item.connect) }}
      </v-chip>
    </template>

    <template v-slot:[`item.pair`]="{ item }">
      <v-chip
        :color="getColor(item.pair)"
        dark
      >
        {{ getStatus(item.pair) }}
      </v-chip>
    </template>

    <template v-slot:[`item.winch`]="{ item }">
      <div class="d-flex align-center">
        <div class="position-relative">
          <v-chip
            v-for="[position, winch] in Object.entries(item.winch).sort()"
            :key="position"
            :color="getWinchColor(winch.is_active, winch.assign_storage_code , winch.storage_no)"
            class="ml-1 fixed-width-chip"
            dark
          >
            <v-tooltip bottom>
              <template v-slot:activator="{ on, attrs }">
                <span
                  v-bind="attrs"
                  v-on="on"
                >
                  {{ getStorage(position[0], winch.storage_no) }}
                </span>
              </template>
              <span> {{ winch.assign_storage_code }} </span>
            </v-tooltip>
          </v-chip>
        </div>
        
        <!-- Bin on Hold Indicator -->
        <v-tooltip bottom v-if="hasBinOnHold(item.winch)">
          <template v-slot:activator="{ on, attrs }">
            <v-chip
              v-bind="attrs"
              v-on="on"
              small
              color="orange"
              text-color="white"
              class="ml-2 pulse-animation"
            >
              <v-icon left size="16">mdi-pause-circle</v-icon>
             Bin on hold
            </v-chip>
          </template>
          <div class="pa-2">
            <div class="text-subtitle2 mb-1">🔒 Bin on Hold</div>
            <div class="text-caption">
              This skycar has a bin that is currently on hold waiting for SM dispatch job.
              <br/>
              <strong>Storage:</strong> {{ getBinOnHoldInfo(item.winch) }}
            </div>
          </div>
        </v-tooltip>
      </div>
    </template>

    <template v-slot:[`item.job_id`]="{ item }">
      <div>
        <div v-if="item.job_id">
          <v-btn
            text
            small
            color="blue"
            @click="$emit('job-detail', item.job_id)"
          >
            <v-icon left size="16">mdi-briefcase-outline</v-icon>
            {{ item.job_id }}
          </v-btn>
        </div>
        <div v-else>
          <span class="grey--text">No Active Job</span>
        </div>
        <div v-if="item.tentative_job_id && item.tentative_job_id.length">
          <v-tooltip bottom>
            <template v-slot:activator="{ on, attrs }">
              <span v-bind="attrs" v-on="on">
                <v-chip
                  v-for="tid in item.tentative_job_id"
                  :key="tid"
                  small
                  color="orange lighten-2"
                  class="ml-1"
                  text-color="black"
                  @click.stop="$emit('job-detail', tid)"
                  style="cursor:pointer;"
                >
                  <v-icon left small>mdi-timer-sand</v-icon>
                  {{ tid }}
                </v-chip>
              </span>
            </template>
            <span>Upcoming job(s) assigned to this skycar</span>
          </v-tooltip>
        </div>
      </div>
    </template>

    <template v-slot:[`item.job_elapsed`]="{ item }">
      <div v-if="item.job_begin_at" class="d-flex align-center">
        <v-icon size="16" color="blue" class="mr-1">mdi-clock-outline</v-icon>
        <v-chip
          :color="getElapsedTimeColor(item.job_begin_at)"
          dark
          small
          class="font-weight-medium"
        >
          {{ getJobElapsedTime(item.job_begin_at) }}
        </v-chip>
      </div>
      <span v-else class="grey--text text--lighten-1">
        <v-icon size="16" color="grey" class="mr-1">mdi-clock-off</v-icon>
        No Job
      </span>
    </template>

    <template v-slot:[`item.action`]="{ item }">
      <v-btn
        small
        class="mr-2"
        light
        @click="$emit('action-dialog', item)"
      >
        Action
      </v-btn>
      <v-btn
        small
        class="mr-2"
        light
        @click="$emit('event-log', item)"
      >
        Event Log
      </v-btn>
    </template>

  </v-data-table>
</template>

<script>
import { getStatus } from "../../helper/common.js";

const PositionMapping = {
  L: "A",
  R: "B",
  B: ""
}

export default {
  name: 'SkycarTable',
  props: {
    headers: { type: Array, required: true },
    items: { type: Array, required: true },
    selected: { type: Array, default: () => [] }
  },
  methods: {
    getStorage(position, storageNo) {
      return `${PositionMapping[position]} ${ storageNo ? storageNo : "EMPTY" }`
    },
    hasBinOnHold(winchData) {
      // Check if any winch position has bin_on_hold set to true
      return Object.values(winchData).some(winch => winch.bin_on_hold === true);
    },
    getBinOnHoldInfo(winchData) {
      // Get storage info for bins that are on hold
      const holdBins = Object.entries(winchData)
        .filter(([, winch]) => winch.bin_on_hold === true)
        .map(([position, winch]) => {
          if (position === 'BOTH') {
            return winch.storage_no || 'Unknown';
          } else {
            return `${PositionMapping[position]} - ${winch.storage_no || 'Unknown'}`;
          }
        });
      return holdBins.join(', ') || 'Unknown';
    },
    getColor(boolean) {
      if (boolean === true) {
        return "green";
      } else {
        return "red";
      }
    },
    getWinchColor(isActive , assignStorageCode, storageCode){
    // assign storage code = Macaroni and Cheese #F2BB66
    // with storage = green
    // inactive = red
      return isActive === false ? "red" : (isActive && assignStorageCode && !storageCode) ? "#F2BB66" : "green";
    },
    getStatus(bool) {
      return getStatus(bool)[1];
    },
    getSidColor(mode) {
      switch (mode) {
        case "Normal":
          return "green--text";
        case "Maintenance":
          return "orange--text";
        case "Error":
          return "red--text";
        default:
          return "white--text";
      }
    },
    getJobElapsedTime(jobBeginAt) {
      if (!jobBeginAt) return 'N/A';
      
      const startTime = new Date(jobBeginAt);
      const currentTime = new Date();
      const elapsedMs = currentTime - startTime;
      
      const hours = Math.floor(elapsedMs / (1000 * 60 * 60));
      const minutes = Math.floor((elapsedMs % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((elapsedMs % (1000 * 60)) / 1000);
      
      if (hours > 0) {
        return `${hours}h ${minutes}m`;
      } else if (minutes > 0) {
        return `${minutes}m ${seconds}s`;
      } else {
        return `${seconds}s`;
      }
    },
    getElapsedTimeColor(jobBeginAt) {
      if (!jobBeginAt) return 'grey';
      
      const startTime = new Date(jobBeginAt);
      const currentTime = new Date();
      const elapsedMs = currentTime - startTime;
      const elapsedMinutes = Math.floor(elapsedMs / (1000 * 60));
      
      // Color coding based on elapsed time
      if (elapsedMinutes < 5) {
        return 'green'; // Fresh job (< 5 minutes)
      } else if (elapsedMinutes < 15) {
        return 'blue'; // Normal job (5-15 minutes)
      } else if (elapsedMinutes < 30) {
        return 'orange'; // Long running job (15-30 minutes)
      } else {
        return 'red'; // Very long running job (> 30 minutes)
      }
    }
  }
}
</script>

<style scoped>
.fixed-width-chip {
  width: 120px
}

.pulse-animation {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
</style> 