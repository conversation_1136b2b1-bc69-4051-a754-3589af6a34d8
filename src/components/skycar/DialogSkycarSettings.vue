<template>
  <v-dialog v-model="dialog" max-width="1200px">
    <v-card dark color="black">
      <v-card-title class="pa-0">
        <v-toolbar
          dark
          color="grey darken-4"
          flat
        >
          <v-icon class="mr-3">mdi-cog</v-icon>
          <span class="text-h5">Skycar Settings</span>
          <v-spacer />
          <v-btn
            icon
            @click="closeDialog"
            class="ml-2"
          >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
      </v-card-title>

      <v-card-text class="pa-0">
        <v-container fluid class="pa-4">
          <v-progress-linear
            v-if="loading"
            indeterminate
            color="white"
            class="mb-4"
          />
        
        <v-form ref="form" v-model="valid">
          <!-- Battery Percentage Settings Row -->
          <v-row class="mb-4">
            <!-- Low Battery Threshold -->
            <v-col cols="12" md="4">
              <v-card
                outlined
                color="grey darken-3"
                class="pa-4"
              >
                <div class="d-flex align-center mb-3">
                  <v-icon color="white" class="mr-3" size="24">mdi-battery-low</v-icon>
                  <div class="flex-grow-1">
                    <div class="d-flex align-center">
                      <span class="text-h6 mr-2 white--text">Low Battery Threshold</span>
                      <v-tooltip bottom max-width="400">
                        <template v-slot:activator="{ on, attrs }">
                          <v-icon
                            v-bind="attrs"
                            v-on="on"
                            color="white"
                            size="18"
                            class="cursor-pointer"
                          >
                            mdi-help-circle-outline
                          </v-icon>
                        </template>
                        <div class="pa-2">
                          <div class="text-subtitle1 mb-2">📋 Description:</div>
                          <div class="text-body-2">
                            TC will request CM to create a charging job when Skycar battery falls below this percentage. 
                            Skycar will inform TC via low battery protocol.
                            <br/><br/>
                            <strong>Note:</strong> If skycar inform battery percentage higher than this threshold, TC will not request CM to create a charging job. 
                            You can configure CM's premptive charging percentage if want CM internally create a charging job.
                          </div>
                        </div>
                      </v-tooltip>
                    </div>
                    <div class="text-caption grey--text text--lighten-2">
                      Battery level for charging request
                    </div>
                  </div>
                </div>
                
                <v-text-field
                  v-model.number="lowBatteryThreshold"
                  @input="settingsChanged = true"
                  label="Percentage"
                  type="number"
                  suffix="%"
                  :rules="batteryRules"
                  :disabled="loading"
                  outlined
                  dense
                  hide-details="auto"
                  class="mb-3"
                ></v-text-field>

              </v-card>
            </v-col>

    
            <!-- Charge With Bin Threshold -->
            <v-col cols="12" md="4">
              <v-card
                outlined
                color="grey darken-3"
                class="pa-4"
              >
                <div class="d-flex align-center mb-3">
                  <v-icon color="white" class="mr-3" size="24">mdi-battery-charging</v-icon>
                  <div class="flex-grow-1">
                    <div class="d-flex align-center">
                      <span class="text-h6 mr-2 white--text">Charge With Bin Threshold</span>
                      <v-tooltip bottom max-width="400">
                        <template v-slot:activator="{ on, attrs }">
                          <v-icon
                            v-bind="attrs"
                            v-on="on"
                            color="white"
                            size="18"
                            class="cursor-pointer"
                          >
                            mdi-help-circle-outline
                          </v-icon>
                        </template>
                        <div class="pa-2">
                          <div class="text-subtitle1 mb-2">🔋 Description:</div>
                          <div class="text-body-2">
                            TC allows Skycar to charge while holding bin when battery drops below this percentage. 
                            Especially when station operators not handling the bin and bin cannot be dropped due to station capacity or i-model sequence limitation (processed bin have to move to buffer first).
                          </div>
                        </div>
                      </v-tooltip>
                    </div>
                    <div class="text-caption grey--text text--lighten-2">
                      Allow charging with bin at charging dock
                    </div>
                  </div>
                </div>
                
                <v-text-field
                  v-model.number="chargeWithBinThreshold"
                  @input="settingsChanged = true"
                  label="Percentage"
                  type="number"
                  suffix="%"
                  :rules="batteryRules"
                  :disabled="loading"
                  outlined
                  dense
                  hide-details="auto"
                  class="mb-3"
                ></v-text-field>

              </v-card>
            </v-col>
         

          <!-- Feature Toggle Row -->
    
            <v-col cols="12" md="4">
              <v-card
                outlined
                color="grey darken-3"
                class="pa-4"
              >
                <div class="d-flex align-center mb-3">
                  <v-icon color="white" class="mr-3" size="24">mdi-cog-box</v-icon>
                  <div class="flex-grow-1">
                    <div class="d-flex align-center">
                      <span class="text-h6 mr-2 white--text">Low Battery Cancel Job</span>
                      <v-chip
                        x-small
                        color="orange"
                        text-color="white"
                        class="mr-1"
                      >
                        BETA
                      </v-chip>
                      <v-tooltip bottom max-width="400">
                        <template v-slot:activator="{ on, attrs }">
                          <v-icon
                            v-bind="attrs"
                            v-on="on"
                            color="white"
                            size="18"
                            class="cursor-pointer"
                          >
                            mdi-help-circle-outline
                          </v-icon>
                        </template>
                        <div class="pa-2">
                          <div class="text-subtitle1 mb-2">⚙️ Description:</div>
                          <div class="text-body-2">
                            Toggle on/off cancel retrieving job when skycar unable to drop to station and TC received skycar SC,1,B;. 
                            TC will delete the retrieving job, inform SM to assign an internal job to store the bin, so skycar won't go charge dock with bin.
                          </div>
                        </div>
                      </v-tooltip>
                    </div>
                    <div class="text-caption grey--text text--lighten-2">
                      Cancel jobs on low battery
                    </div>
                  </div>
                </div>
                
                <div class="text-center">
                  <v-switch
                    v-model="lowBatteryCancelJobEnabled"
                    @change="settingsChanged = true"
                    :disabled="loading"
                    color="success"
                    inset
                    class="ma-0 pa-0"
                    hide-details
                  >
                    <template v-slot:label>
                      <span class="text-subtitle2" :class="lowBatteryCancelJobEnabled ? 'success--text' : 'text--secondary'">
                        {{ lowBatteryCancelJobEnabled ? 'ENABLED' : 'DISABLED' }}
                      </span>
                    </template>
                  </v-switch>
                </div>

              </v-card>
            </v-col>
          </v-row>

          <!-- Additional Settings Row -->
          <v-row class="mb-4">
            <!-- Initiate Skycar Timeout -->
            <v-col cols="12" md="4">
              <v-card
                outlined
                color="grey darken-3"
                class="pa-4"
              >
                <div class="d-flex align-center mb-3">
                  <v-icon color="white" class="mr-3" size="24">mdi-timer-outline</v-icon>
                  <div class="flex-grow-1">
                    <div class="d-flex align-center">
                      <span class="text-h6 mr-2 white--text">Initiate Skycar Timeout</span>
                      <v-tooltip bottom max-width="400">
                        <template v-slot:activator="{ on, attrs }">
                          <v-icon
                            v-bind="attrs"
                            v-on="on"
                            color="white"
                            size="18"
                            class="cursor-pointer"
                          >
                            mdi-help-circle-outline
                          </v-icon>
                        </template>
                        <div class="pa-2">
                          <div class="text-subtitle1 mb-2">⏱️ Description:</div>
                          <div class="text-body-2">
                            INITIATE_SKYCAR_TIMEOUT is the timeout duration for waiting for skycar initialization completion during the homing/positioning process. 
                            Specifically, it controls how long the system waits for a skycar to respond with a "JOB_DONE" message after sending a SET_POSITION command.
                          </div>
                        </div>
                      </v-tooltip>
                    </div>
                    <div class="text-caption grey--text text--lighten-2">
                      Timeout for skycar initialization (seconds)
                    </div>
                  </div>
                </div>
                
                <v-text-field
                  v-model.number="initiateSkycarTimeout"
                  @input="settingsChanged = true"
                  label="Timeout (seconds)"
                  type="number"
                  suffix="s"
                  :rules="timeoutRules"
                  :disabled="loading"
                  outlined
                  dense
                  hide-details="auto"
                  class="mb-3"
                ></v-text-field>

              </v-card>
            </v-col>

            <!-- Home When Recover Skycar -->
            <v-col cols="12" md="4">
              <v-card
                outlined
                color="grey darken-3"
                class="pa-4"
              >
                <div class="d-flex align-center mb-3">
                  <v-icon color="white" class="mr-3" size="24">mdi-home-outline</v-icon>
                  <div class="flex-grow-1">
                    <div class="d-flex align-center">
                      <span class="text-h6 mr-2 white--text">Home When Recover Skycar</span>
                      <v-tooltip bottom max-width="400">
                        <template v-slot:activator="{ on, attrs }">
                          <v-icon
                            v-bind="attrs"
                            v-on="on"
                            color="white"
                            size="18"
                            class="cursor-pointer"
                          >
                            mdi-help-circle-outline
                          </v-icon>
                        </template>
                        <div class="pa-2">
                          <div class="text-subtitle1 mb-2">🏠 Description:</div>
                          <div class="text-body-2">
                            HOME_WHEN_RECOVER_SKYCAR is a configuration flag that controls whether the home protocol is enabled when recovering skycars from error states.
                          </div>
                        </div>
                      </v-tooltip>
                    </div>
                    <div class="text-caption grey--text text--lighten-2">
                      Enable home protocol during skycar recovery
                    </div>
                  </div>
                </div>
                
                <div class="text-center">
                  <v-switch
                    v-model="homeWhenRecoverSkycar"
                    @change="settingsChanged = true"
                    :disabled="loading"
                    color="success"
                    inset
                    class="ma-0 pa-0"
                    hide-details
                  >
                    <template v-slot:label>
                      <span class="text-subtitle2" :class="homeWhenRecoverSkycar ? 'success--text' : 'text--secondary'">
                        {{ homeWhenRecoverSkycar ? 'ENABLED' : 'DISABLED' }}
                      </span>
                    </template>
                  </v-switch>
                </div>

              </v-card>
            </v-col>
          </v-row>
        </v-form>
        </v-container>
      </v-card-text>

      <v-card-actions class="pa-4">
        <v-spacer />
        <v-btn
          color="grey darken-1"
          text
          @click="closeDialog"
          :disabled="loading"
        >
          Cancel
        </v-btn>
        <v-btn
          color="blue"
          dark
          @click="saveSetting"
          :disabled="!valid || loading"
          :loading="loading"
        >
          <v-icon left>mdi-content-save</v-icon>
          Save
        </v-btn>
      </v-card-actions>

      <v-alert
        v-if="settingsChanged"
        type="info"
        color="blue lighten-4"
        text
        dense
        class="mb-2"
      >
        You have unsaved changes. Please click <strong>Save</strong> to confirm.
      </v-alert>
    </v-card>
  </v-dialog>
</template>

<script>
import { getHost, getRequestHeader, useRefreshToken } from "../../helper/common.js";

export default {
  name: "DialogSkycarSettings",
  props: {
    currentZone: {
      type: String,
      required: true
    },
    showNotification: {
      type: Function,
      required: true
    }
  },
  data() {
    return {
      dialog: false,
      valid: false,
      loading: false,
      lowBatteryThreshold: 30,
      noBatteryThreshold: 10,
      chargeWithBinThreshold: 15,
      lowBatteryCancelJobEnabled: false,
      initiateSkycarTimeout: 60,
      homeWhenRecoverSkycar: false,
      batteryRules: [
        v => !!v || "Battery percentage is required",
        v => (v >= 1 && v <= 100) || "Battery percentage must be between 1 and 100",
        v => Number.isInteger(Number(v)) || "Battery percentage must be a whole number"
      ],
      timeoutRules: [
        v => !!v || "Timeout is required",
        v => (v >= 1 && v <= 3600) || "Timeout must be between 1 and 3600 seconds",
        v => Number.isInteger(Number(v)) || "Timeout must be a whole number"
      ],
      settingsChanged: false
    }
  },
  methods: {
    async openDialog() {
      this.dialog = true;
      await this.fetchCurrentSettings();
    },
    closeDialog() {
      this.dialog = false;
      this.resetForm();
    },
    resetForm() {
      this.lowBatteryThreshold = 30;
      this.noBatteryThreshold = 10;
      this.chargeWithBinThreshold = 15;
      this.lowBatteryCancelJobEnabled = false;
      this.initiateSkycarTimeout = 60;
      this.homeWhenRecoverSkycar = false;
      this.loading = false;
      this.settingsChanged = false;
      if (this.$refs.form) {
        this.$refs.form.resetValidation();
      }
    },
    async fetchCurrentSettings() {
      this.loading = true;
      
      try {
        const response = await fetch(getHost(this.currentZone) + "/runtime/setting", {
          method: "GET",
          headers: getRequestHeader()
        });
        
        const result = await response.json();
        
        if (response.ok && result.status && result.model) {
          // Update the values with API response
          this.lowBatteryThreshold = result.model.LOW_BATTERY_THRESHOLD || 30;
          this.noBatteryThreshold = result.model.NO_BATTERY_THRESHOLD || 10;
          this.chargeWithBinThreshold = result.model.CHARGE_WITH_BIN_THRESHOLD || 15;
          this.lowBatteryCancelJobEnabled = result.model.LOW_BATTERY_CANCEL_JOB_ENABLED || false;
          this.initiateSkycarTimeout = result.model.INITIATE_SKYCAR_TIMEOUT || 10;
          this.homeWhenRecoverSkycar = result.model.HOME_WHEN_RECOVER_SKYCAR || false;
          this.settingsChanged = false;
        } else {
          // Use default values if API fails
          this.resetForm();
          this.showNotification(false, result.message || "Failed to fetch current settings");
        }
      } catch (error) {
        if (error.response && error.response.status === 401) {
          // If access token is unauthorized, use refresh token to get new access token
          return useRefreshToken(this, this.fetchCurrentSettings);
        }
        console.error("Error fetching settings:", error);
        // Use default values if network error occurs
        this.resetForm();
        this.showNotification(false, "Network error occurred while fetching settings");
      } finally {
        this.loading = false;
        if (this.$refs.form) {
          this.$refs.form.resetValidation();
        }
      }
    },
    async saveSetting() {
      if (!this.valid) return;
      
      this.loading = true;
      
      try {
        const payload = {
          LOW_BATTERY_THRESHOLD: parseInt(this.lowBatteryThreshold),
          NO_BATTERY_THRESHOLD: parseInt(this.noBatteryThreshold),
          CHARGE_WITH_BIN_THRESHOLD: parseInt(this.chargeWithBinThreshold),
          LOW_BATTERY_CANCEL_JOB_ENABLED: this.lowBatteryCancelJobEnabled,
          INITIATE_SKYCAR_TIMEOUT: parseInt(this.initiateSkycarTimeout),
          HOME_WHEN_RECOVER_SKYCAR: this.homeWhenRecoverSkycar
        };
        
        // Using PATCH method as specified
        const response = await fetch(getHost(this.currentZone) + "/runtime/setting", {
          method: "PATCH",
          headers: getRequestHeader(),
          body: JSON.stringify(payload)
        });
        
        const result = await response.json();
        
        if (response.ok) {
          this.showNotification(true, `Settings updated successfully (Low: ${this.lowBatteryThreshold}%, No Battery: ${this.noBatteryThreshold}%, Charge with Bin: ${this.chargeWithBinThreshold}%, Cancel Job: ${this.lowBatteryCancelJobEnabled ? 'ON' : 'OFF'}, Timeout: ${this.initiateSkycarTimeout}s, Home Recovery: ${this.homeWhenRecoverSkycar ? 'ON' : 'OFF'})`);
          this.settingsChanged = false;
          this.closeDialog();
        } else {
          this.showNotification(false, result.message || "Failed to update setting");
        }
      } catch (error) {
        if (error.response && error.response.status === 401) {
          // If access token is unauthorized, use refresh token to get new access token
          return useRefreshToken(this, this.saveSetting);
        }
        console.error("Error updating setting:", error);
        this.showNotification(false, "Network error occurred while updating setting");
      } finally {
        this.loading = false;
      }
    },
    resetSettingsChanged() {
      this.settingsChanged = false;
    }
  }
}
</script>

<style scoped>
.v-alert {
  border-radius: 8px;
}

.cursor-pointer {
  cursor: pointer;
}
</style> 