<template>
  <v-dialog v-model="dialog" max-width="1200px" persistent>
    <v-card>
      <v-toolbar dark color="blue">
        <v-toolbar-title>
          <v-icon left>mdi-cog</v-icon>
          Skycar Settings
        </v-toolbar-title>
        <v-spacer></v-spacer>
        <v-btn icon @click="closeDialog">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-toolbar>

      <v-card-text class="pt-6">
        <v-progress-linear
          v-if="loading"
          indeterminate
          color="blue"
          class="mb-4"
        ></v-progress-linear>
        
        <v-form ref="form" v-model="valid">
          <!-- Battery Percentage Settings Row -->
          <v-row class="mb-4">
            <!-- Low Battery Threshold -->
            <v-col cols="12" md="4">
              <v-card
                outlined
                class="pa-4"
                elevation="2"
              >
                <div class="d-flex align-center mb-3">
                  <v-icon color="info" class="mr-3" size="24">mdi-battery-low</v-icon>
                  <div class="flex-grow-1">
                    <div class="d-flex align-center">
                      <span class="text-h6 mr-2">Low Battery Threshold</span>
                      <v-tooltip bottom max-width="400">
                        <template v-slot:activator="{ on, attrs }">
                          <v-icon
                            v-bind="attrs"
                            v-on="on"
                            color="info"
                            size="18"
                            class="cursor-pointer"
                          >
                            mdi-help-circle-outline
                          </v-icon>
                        </template>
                        <div class="pa-2">
                          <div class="text-subtitle2 mb-2">📋 Description:</div>
                          <div class="text-caption">
                            TC will request CM to create a charging job when Skycar battery falls below this percentage. 
                            Skycar will inform TC via low battery protocol.
                            <br/><br/>
                            <strong>Note:</strong> If skycar inform battery percentage higher than this threshold, TC will not request CM to create a charging job. 
                            You can configure CM's premptive charging percentage if want CM internally create a charging job.
                          </div>
                        </div>
                      </v-tooltip>
                    </div>
                    <div class="text-caption text--secondary">
                      Battery level for charging request
                    </div>
                  </div>
                </div>
                
                <v-text-field
                  v-model.number="lowBatteryThreshold"
                  label="Percentage"
                  type="number"
                  suffix="%"
                  :rules="batteryRules"
                  :disabled="loading"
                  outlined
                  dense
                  hide-details="auto"
                  class="mb-3"
                ></v-text-field>

              </v-card>
            </v-col>

            <!-- No Battery Threshold -->
            <v-col cols="12" md="4">
              <v-card
                outlined
                class="pa-4"
                elevation="2"
              >
                <div class="d-flex align-center mb-3">
                  <v-icon color="warning" class="mr-3" size="24">mdi-battery-alert</v-icon>
                  <div class="flex-grow-1">
                    <div class="d-flex align-center">
                      <span class="text-h6 mr-2">No Battery Threshold</span>
                      <v-tooltip bottom max-width="400">
                        <template v-slot:activator="{ on, attrs }">
                          <v-icon
                            v-bind="attrs"
                            v-on="on"
                            color="warning"
                            size="18"
                            class="cursor-pointer"
                          >
                            mdi-help-circle-outline
                          </v-icon>
                        </template>
                        <div class="pa-2">
                          <div class="text-subtitle2 mb-2">⚠️ Description:</div>
                          <div class="text-caption">
                            TC will exclude assigning new jobs to Skycar below this percentage. Only travel jobs allowed. 
                            Prevents critical battery depletion.
                          </div>
                        </div>
                      </v-tooltip>
                    </div>
                    <div class="text-caption text--secondary">
                      Critical level for job exclusion
                    </div>
                  </div>
                </div>
                
                <v-text-field
                  v-model.number="noBatteryThreshold"
                  label="Percentage"
                  type="number"
                  suffix="%"
                  :rules="batteryRules"
                  :disabled="loading"
                  outlined
                  dense
                  hide-details="auto"
                  class="mb-3"
                ></v-text-field>

              </v-card>
            </v-col>

            <!-- Charge With Bin Threshold -->
            <v-col cols="12" md="4">
              <v-card
                outlined
                class="pa-4"
                elevation="2"
              >
                <div class="d-flex align-center mb-3">
                  <v-icon color="error" class="mr-3" size="24">mdi-battery-charging</v-icon>
                  <div class="flex-grow-1">
                    <div class="d-flex align-center">
                      <span class="text-h6 mr-2">Charge With Bin Threshold</span>
                      <v-tooltip bottom max-width="400">
                        <template v-slot:activator="{ on, attrs }">
                          <v-icon
                            v-bind="attrs"
                            v-on="on"
                            color="error"
                            size="18"
                            class="cursor-pointer"
                          >
                            mdi-help-circle-outline
                          </v-icon>
                        </template>
                        <div class="pa-2">
                          <div class="text-subtitle2 mb-2">🔋 Description:</div>
                          <div class="text-caption">
                            TC allows Skycar to charge while holding bin when battery drops below this percentage. 
                            Especially when station operators not handling the bin and bin cannot be dropped due to station capacity or i-model sequence limitation (processed bin have to move to buffer first).
                          </div>
                        </div>
                      </v-tooltip>
                    </div>
                    <div class="text-caption text--secondary">
                      Allow charging with bin at charging dock
                    </div>
                  </div>
                </div>
                
                <v-text-field
                  v-model.number="chargeWithBinThreshold"
                  label="Percentage"
                  type="number"
                  suffix="%"
                  :rules="batteryRules"
                  :disabled="loading"
                  outlined
                  dense
                  hide-details="auto"
                  class="mb-3"
                ></v-text-field>

              </v-card>
            </v-col>
          </v-row>

          <!-- Feature Toggle Row -->
          <v-row>
            <v-col cols="12">
              <v-card
                outlined
                class="pa-4"
                elevation="2"
              >
                <v-row align="center">
                  <v-col cols="12" md="6">
                    <div class="d-flex align-center">
                      <v-icon color="success" class="mr-3" size="24">mdi-cog-box</v-icon>
                      <div class="flex-grow-1">
                        <div class="d-flex align-center mb-1">
                          <span class="text-h6 mr-2">Cancel Job Feature</span>
                          <v-chip
                            small
                            color="orange"
                            text-color="white"
                            class="mr-2"
                          >
                            🚧 COMING SOON
                          </v-chip>
                          <v-tooltip bottom max-width="400">
                            <template v-slot:activator="{ on, attrs }">
                              <v-icon
                                v-bind="attrs"
                                v-on="on"
                                color="success"
                                size="18"
                                class="cursor-pointer"
                              >
                                mdi-help-circle-outline
                              </v-icon>
                            </template>
                            <div class="pa-2">
                              <div class="text-subtitle2 mb-2">⚙️ Description:</div>
                              <div class="text-caption">
                                Toggle on/off cancel retrieving job when skycar unable to drop to station and TC received skycar E_BATT_SEQ_LOW_BATTERY. 
                                TC will delete the retrieving job, inform SM to assign an internal job to store the bin, so skycar won't go charge dock with bin.
                              </div>
                            </div>
                          </v-tooltip>
                        </div>
                        <div class="text-caption text--secondary">
                          Advanced feature toggle for job cancellation management
                        </div>
                      </div>
                    </div>
                  </v-col>
                  <v-col cols="12" md="6" class="text-md-right">
                    <v-switch
                      v-model="cancelJobFeature"
                      :disabled="loading"
                      color="success"
                      inset
                      class="ma-0 pa-0"
                      hide-details
                    >
                      <template v-slot:label>
                        <span class="text-subtitle2" :class="cancelJobFeature ? 'success--text' : 'text--secondary'">
                          {{ cancelJobFeature ? 'ENABLED' : 'DISABLED' }}
                        </span>
                      </template>
                    </v-switch>
                  </v-col>
                </v-row>

              </v-card>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>

      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn
          color="grey"
          text
          @click="closeDialog"
          :disabled="loading"
        >
          Cancel
        </v-btn>
        <v-btn
          color="blue"
          dark
          @click="saveSetting"
          :disabled="!valid || loading"
          :loading="loading"
        >
          <v-icon left>mdi-content-save</v-icon>
          Save
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
import { getHost, getRequestHeader, useRefreshToken } from "../../helper/common.js";

export default {
  name: "DialogSkycarSettings",
  props: {
    currentZone: {
      type: String,
      required: true
    },
    showNotification: {
      type: Function,
      required: true
    }
  },
  data() {
    return {
      dialog: false,
      valid: false,
      loading: false,
      lowBatteryThreshold: 30,
      noBatteryThreshold: 10,
      chargeWithBinThreshold: 15,
      cancelJobFeature: false,
      batteryRules: [
        v => !!v || "Battery percentage is required",
        v => (v >= 1 && v <= 100) || "Battery percentage must be between 1 and 100",
        v => Number.isInteger(Number(v)) || "Battery percentage must be a whole number"
      ]
    }
  },
  methods: {
    async openDialog() {
      this.dialog = true;
      await this.fetchCurrentSettings();
    },
    closeDialog() {
      this.dialog = false;
      this.resetForm();
    },
    resetForm() {
      this.lowBatteryThreshold = 30;
      this.noBatteryThreshold = 10;
      this.chargeWithBinThreshold = 15;
      this.cancelJobFeature = false;
      this.loading = false;
      if (this.$refs.form) {
        this.$refs.form.resetValidation();
      }
    },
    async fetchCurrentSettings() {
      this.loading = true;
      
      try {
        const response = await fetch(getHost(this.currentZone) + "/runtime/setting", {
          method: "GET",
          headers: getRequestHeader()
        });
        
        const result = await response.json();
        
        if (response.ok && result.status && result.model) {
          // Update the values with API response
          this.lowBatteryThreshold = result.model.LOW_BATTERY_THRESHOLD || 30;
          this.noBatteryThreshold = result.model.NO_BATTERY_THRESHOLD || 10;
          this.chargeWithBinThreshold = result.model.CHARGE_WITH_BIN_THRESHOLD || 15;
          this.cancelJobFeature = result.model.CANCEL_JOB_FEATURE || false;
        } else {
          // Use default values if API fails
          this.resetForm();
          this.showNotification(false, result.message || "Failed to fetch current settings");
        }
      } catch (error) {
        if (error.response && error.response.status === 401) {
          // If access token is unauthorized, use refresh token to get new access token
          return useRefreshToken(this, this.fetchCurrentSettings);
        }
        console.error("Error fetching settings:", error);
        // Use default values if network error occurs
        this.resetForm();
        this.showNotification(false, "Network error occurred while fetching settings");
      } finally {
        this.loading = false;
        if (this.$refs.form) {
          this.$refs.form.resetValidation();
        }
      }
    },
    async saveSetting() {
      if (!this.valid) return;
      
      this.loading = true;
      
      try {
        const payload = {
          LOW_BATTERY_THRESHOLD: parseInt(this.lowBatteryThreshold),
          NO_BATTERY_THRESHOLD: parseInt(this.noBatteryThreshold),
          CHARGE_WITH_BIN_THRESHOLD: parseInt(this.chargeWithBinThreshold),
          CANCEL_JOB_FEATURE: this.cancelJobFeature
        };
        
        // Using PATCH method as specified
        const response = await fetch(getHost(this.currentZone) + "/runtime/setting", {
          method: "PATCH",
          headers: getRequestHeader(),
          body: JSON.stringify(payload)
        });
        
        const result = await response.json();
        
        if (response.ok) {
          this.showNotification(true, `Settings updated successfully (Low: ${this.lowBatteryThreshold}%, No Battery: ${this.noBatteryThreshold}%, Charge with Bin: ${this.chargeWithBinThreshold}%, Cancel Job: ${this.cancelJobFeature ? 'ON' : 'OFF'})`);
          this.closeDialog();
        } else {
          this.showNotification(false, result.message || "Failed to update setting");
        }
      } catch (error) {
        if (error.response && error.response.status === 401) {
          // If access token is unauthorized, use refresh token to get new access token
          return useRefreshToken(this, this.saveSetting);
        }
        console.error("Error updating setting:", error);
        this.showNotification(false, "Network error occurred while updating setting");
      } finally {
        this.loading = false;
      }
    }
  }
}
</script>

<style scoped>
.v-alert {
  border-radius: 8px;
}

.cursor-pointer {
  cursor: pointer;
}
</style> 