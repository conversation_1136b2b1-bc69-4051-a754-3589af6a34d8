<template>
    <v-container>
        

        <div class="dark-cloud"></div>
        <div class="stars"></div>
        <div class="stars2"></div>
        <div class="stars3"></div>

        <v-tabs dark :background-color="'transparent'" class="pa-4" v-model="tab">
            <v-tab href="#webhooks">Webhook</v-tab>
        </v-tabs>

        <v-divider></v-divider>
        <v-tabs-items class="transparent" v-model="tab">
            <v-tab-item value="webhooks">
                <v-card flat class="pa-4 transparent">
                    <v-btn
                        large
                        @click="getMessages()"
                        :loading="refreshLoading"
                        dark
                        style="position: fixed; top: 10%; right: 3%; z-index: 999"
                        >Refresh</v-btn
                    >
                    
                    <v-data-table
                        :headers="headers"
                        :items="messages"
                        :loading="refreshLoading"
                        :options.sync="options"
                        :server-items-length="maxPageSize"
                        hide-default-footer
                        dark
                        dense
                        class="transparent elevation-1"
                    >
                    <template v-slot:top>
                        <v-expansion-panels class="transparent">
                            <v-expansion-panel class="transparent">
                            <v-expansion-panel-header class="transparent">
                                Advanced Filter
                            </v-expansion-panel-header>
                            <v-expansion-panel-content class="transparent">
                                <v-row dense>
                                    <v-col>
                                        <v-combobox
                                        v-model="filters.ids"
                                        label="ids"
                                        multiple
                                        small-chips
                                        deletable-chips
                                        clearable
                                        outlined
                                        />
                                    </v-col>
                                    <v-col>
                                        <v-text-field
                                        label="Created At - Start Time"
                                        type="datetime-local"
                                        v-model="filters.createdAtStart"
                                        step="2"
                                        clearable
                                        outlined
                                        />
                                    </v-col>
                                    <v-col>
                                        <v-text-field
                                        label="Created At - End Time"
                                        type="datetime-local"
                                        v-model="filters.createdAtEnd"
                                        step="2"
                                        clearable
                                        outlined
                                        />
                                    </v-col>
                                </v-row>
                                <v-row dense>
                                    <v-col>
                                        <v-combobox
                                        v-model="filters.events"
                                        label="events"
                                        multiple
                                        small-chips
                                        deletable-chips
                                        clearable
                                        outlined
                                        />
                                    </v-col>
                                    <v-col>
                                        <v-text-field
                                        label="Created At - Start Time"
                                        type="datetime-local"
                                        v-model="filters.deliveredAtStart"
                                        step="2"
                                        clearable
                                        outlined
                                        />
                                    </v-col>
                                    <v-col>
                                        <v-text-field
                                        label="Created At - End Time"
                                        type="datetime-local"
                                        v-model="filters.deliveredAtEnd"
                                        step="2"
                                        clearable
                                        outlined
                                        />
                                    </v-col>
                                </v-row>
                            </v-expansion-panel-content>
                            </v-expansion-panel>
                        </v-expansion-panels>
                        <v-spacer></v-spacer>
                        <v-row>
                            <v-col cols="4" sm="3">
                            <v-select
                                v-model="filters.perPage"
                                :items="pageSizes"
                                label="Items per Page"
                                @change="handlePageSizeChange"
                            ></v-select>
                            </v-col>
                            <v-col cols="12" sm="9">
                            <v-pagination
                                v-model="filters.pageNo"
                                :length="totalPages"
                                total-visible="7"
                                next-icon="mdi-menu-right"
                                prev-icon="mdi-menu-left"
                                @input="handlePageChange"
                                dark
                            ></v-pagination>
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col class="text-left" cols="12">
                                <small>* all dates in MM/dd/yyyy HH:mm:ss format</small>
                            </v-col>
                        </v-row>
                    </template>

                    

                    <template v-slot:[`item.controls`]="props">
                        <v-tooltip bottom>
                            <template v-slot:activator="{ on }">
                                <v-icon 
                                    v-on="on"
                                    dark 
                                    @click="replay(props.item.id)"
                                    >mdi-replay
                                </v-icon>
                            </template>
                            <span>replay message</span>
                        </v-tooltip>
                    </template>
                    </v-data-table>
                </v-card>
            </v-tab-item>
        </v-tabs-items>
    </v-container>
</template>

<script>
import { WebhooksAPI } from "../api/webhooks";
import { getAccessToken } from "../helper/authorize";
import { convertStringToLocal, getAuthLogin } from "../helper/common";

export default {
    name: "Webhooks",

    data() {
        return {
            isAuthEnabled: false,

            tab: null,
            refreshLoading: false,
            totalPages: 10,
            currentPage: 1,
            perPage: 15,
            pageSizes: [10, 30, 50, 100],
            maxPageSize: 100,
            filters: {
                ids: [],
                events: [],
                createdAtStart: null,
                createdAtEnd: null,
                deliveredAtStart: null,
                deliveredAtEnd: null,
                perPage: 15,
                pageNo: 1,
                orderBy: "createdAt",
                ordering: "DESC"
            },
            options: {
                sortBy: [],
                sortDesc: [],
                page: 1,
                itemsPerPage: 10
            },
            headers: [
                { text: "#", value: "index", groupable: false, sortable: false },
                {
                    text: "ID",
                    value: "id",
                    groupable: false,
                },
                {
                    text: "EVENT",
                    value: "event",
                    groupable: false,
                },
                {
                    text: "CONTENT",
                    value: "content",
                    groupable: false,
                    sortable: false,
                    maxWidth: "500px",
                },
                {
                    text: "CREATED AT",
                    value: "createdAt",
                    groupable: false,
                },
                {
                    text: "DELIVERED AT",
                    value: "deliveredAt",
                    groupable: false,
                },
                {
                    text: "ATTEMPT",
                    value: "attempt",
                    groupable: false,
                },
                {
                    text: "LAST ATTEMPT",
                    value: "lastAttemptAt",
                    groupable: false,
                },
                {
                    text: "ACTION",
                    value: "controls",
                    groupable: false,
                    sortable: false,
                },
            ],
            messages: [],
        }
    },
    beforeMount(){
        this.isAuthEnabled = getAuthLogin()
        this.getMessages()
    },
    computed: {},
    watch: {
        options: {
            handler() {
                const { sortBy, sortDesc, page, itemsPerPage } = this.options
                this.filters.perPage = itemsPerPage
                this.filters.pageNo = page
                this.filters.orderBy = sortBy.length > 0 ? sortBy[0] : null
                this.filters.ordering = sortDesc.length > 0 ? sortDesc[0] ? "ASC" : "DESC" : "ASC"

                if(this.filters.orderBy == null) {
                    this.filters.orderBy = "createdAt"
                    this.filters.ordering = "DESC"
                }

                this.getMessages()
            },
            deep: true,
        }
    },
    methods: {
        handlePageChange(value) {
            this.filters.pageNo = value;
            this.getMessages();
        },

        handlePageSizeChange(value) {
            this.filters.perPage = value;
            this.getMessages();
        },
        async getMessages() {
            try {
                this.refreshLoading = true;

                const token = await getAccessToken()

                await WebhooksAPI.getMessages(this.filters, token).then(res => {
                    let result = res.data.data;

                    if (!result) return null;

                    this.totalPages = res.data.pagination.totalPages;

                    const messages = []
                    const page = this.filters.pageNo
                    const perPage = this.filters.perPage
                    const currentPageStart = page > 1 ?  perPage * (page - 1) : 0

                    result.forEach((message, index) => {
                        messages.push({
                            ...message,
                            index: currentPageStart + 1 + index,
                            createdAt: message.createdAt ? 
                                convertStringToLocal(message.createdAt, true) : null,
                            deliveredAt: message.deliveredAt ? 
                                convertStringToLocal(message.deliveredAt, true) : null,
                            lastAttemptAt: message.lastAttemptAt ? 
                                convertStringToLocal(message.lastAttemptAt, true) : null,
                            content: JSON.stringify(message.content)
                        })
                    })
                    
                    this.messages = messages
                })
            }catch(ex) {    
                console.error(ex)
                this.$awn.alert(`Failed to load data: ${ex.message}`);
            } finally{
                this.refreshLoading = false;
            }
        },
        async replay(messageId) {
            const isConfirmed = confirm(
                // eslint-disable-next-line max-len
                `Message #${messageId} will not be altered, instead, a new message will be created for replay, are you sure to proceed?`
            )

            if(isConfirmed) {
                try {
                    const token = await getAccessToken()
                    await WebhooksAPI.replaySpecificMessage(messageId, token).then(res => {
                        if(res.status === 200 || res.status === 202) {
                            this.$awn.success("Replay request has been accepted")
                        }
                    })
                }catch(ex) {
                    console.error(ex)
                    this.$awn.alert(`Failed to replay message: ${ex.message}`);
                }finally{
                    this.handlePageChange(1)
                    await this.getMessages()
                }
            }
        }
    }
}
</script>

<style>
/* @import '~@fortawesome/fontawesome-free/css/fontawesome.min.css';
          @import '~@fortawesome/fontawesome-free/css/solid.min.css'; */
@import "../assets/Stars.css";
@import "../assets/Background.css";
.content {
  margin: 50px;
  margin-top: 25px;
}
.v-data-table--dense > 
    .v-data-table__wrapper > 
        table > tbody > tr > td {
    max-width: 500px;
}
</style>