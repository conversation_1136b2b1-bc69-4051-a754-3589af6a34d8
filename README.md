# Traffic Control Dashboard

## Overview

[Vue.js: A progressive JavaScript framework for building user interfaces](https://vuejs.org)

> Version vue@2.6.12

### Framework or Library

[Vuetify: A material design component framework for Vue.js](https://vuetifyjs.com)

> Version vuetify@2.3.16

[Vuex: A state management pattern and library for Vue.js applications](https://vuex.vuejs.org)

> Version vuex@3.6.2

[Vue Router : A routing system for Vue.js applications](https://www.npmjs.com/package/vue-router?activeTab=versions)

> Version vue-router@3.4.9

[Json CSV : VueJS component to export Json Data into CSV file and download the resulting file.](https://www.npmjs.com/package/vue-json-csv)

> Version vue-json-csv@1.2.12

[Socket IO Client : Provides a client-side implementation of the Socket.IO protocol.](https://www.npmjs.com/package/socket.io-client)

> Version socket.io-client@2.4.0"

[Awesome Notifications is a lightweight, fully customizable JavaScript notifications library with enhanced async support.](https://www.npmjs.com/package/vue-awesome-notifications)

> Version awesome-notifications@3.1.1"

# File Structure

```
.
├── docs/                           # Documentation files
├── src/                            # Source files
        ├── assets/
        ├── components/             # Vue components and shared components
        ├── dashboard/              # obsolete ts files
        ├── helper/
            ├── common.js           # Reusable function and utility function
            ├── enum.js             # Enums and constant
            ├── http_request.js     # Reusable http function
        ├── mock/                   # Mock and stub for dev and test
        ├── plugins/                # Vuetify plugin
        ├── App.vue                 # Vue app with AppBar, NavBar and socketio initialize
        ├── main.js                 # Driver function and Routing
├── build_push_aws.sh               # Script to push to AWS ERC
├── dockerfile                      # Docker file
├── docker-compose.yml              # Docker compose file
└── README.md
└── .env                            # Create .env based on .env.sample
```

## Project setup

```
npm install
npm install -g @vue/cli
npm install -g @vue/cli-service-global
```

### Compiles and hot-reloads for development

```
npm run serve
```

### Compiles and minifies for production

```
npm run build

```

### Lints and fixes files

```
npm run lint
```

### Run lint on specific files

```
yarn lint src/components/MaintenanceDockv2.vue
```

### Customize configuration

```
See [Configuration Reference](https://cli.vuejs.org/config/).
```

### Lifecycle Diagram

```
https://vuejs.org/v2/guide/instance.html
```

# Development

1. Code Formatter [Prettier](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode)
2. Linting [ESLint](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint])

- Repo included tracked .eslintrc.json , if not exists , you can create one by > npx eslint --init



### Sample .env

[View Sample Environment File](/.env.example)

### For Hot Reload Configuration

```
To turn on hot reload feature
1. must npm run serve at terminal
2. f5 vscode
3. chrome incognito window pop up
```

Sample launch.json file

```
{
    "version": "0.2.0",
    "configurations": [
        {
            "type": "chrome",
            "request": "launch",
            "name": "vuejs: chrome",
            "url": "http://localhost:8080",
            "webRoot": "${workspaceFolder}/src",
            "breakOnLoad": true,
            "sourceMapPathOverrides": {
                "webpack:///src/*": "${webRoot}/*"
            }
        }
    ]
}
```

```
NODE_ENV set to production to enable hot reload
```

# Deployment

## Vitrox
1. Use branch `master` to build docker image that push image to AWS ECR with vitrox tag.
2. Pull 3 images to 3 server node
3. Kill or stop the tc-web services via portainer to spin up new container with newest image.

## Subang

1. Use branch `master` to build docker image, with Github Workflow, when merge or commit to master branch, github workflow will push image to AWS ECR, require ssh to Subang server and deploy with ihub-deployment user.

   - Require update Github Secret of tc-dashboard if got new .env key changes.
   - Refer `Github Workflow Docker Image Env Update Step`

## Penta Zone C (old tc+hcc+sm)

1. Deploy branch `master-penta` with command npm run deploy at /var/www/tc-dashboard, no docker container require.

   - env file at penta production /var/www/tc-dashboard/.env

## Penta Zone AB

1. Use branch `master` to build docker image that pushed to AWS ECR with unique tag.
   - env file use .env.penta_abc

## Penta Zone D

1. Deploy branch `master` via docker image that pushed to AWS ECR with unique tag.
   - env file use .env.penta_d

## Github Workflow Docker Image Env Update Step

1. Encode your local env and add env as secret to github repo
2. You need update github secret if repo env have any changes.
3. Here you go https://github.com/pingspace/tc-dashboard/settings/secrets/actions
4. Below are the step to encode and save as secret.

   a. Encoded with this terminal , output encoded_env.txt

   `openssl base64 -A -in .env -out encoded_env.txt`

   b. Copy the output content and update github secret
   c. ![img_github](/docs/env_secret.png)

# Docker

## Local docker build and run

1. docker build . -t cube-ihub-tc-web
2. docker rm -f cube-ihub-tc-web && docker run -d -p 8080:80 --name cube-ihub-tc-web cube-ihub-tc-web

## Local docker compose

1. docker-compose up -d

## Local build and push to AWS

1. Use build_push_aws.sh


# Auto Detect new version and prompt snackbar on client browser.
1. https://github.com/pingspace/tc-dashboard/pull/181


    