#!/bin/sh

# Check if version update had already been completed
if [ ! -f "/version_update_completed" ]; then
  if echo "$IMAGE_TAG" | grep -q "penta-d"; then # penta-d image tag
    URL='http://172.16.29.58:3030/dashboard/versioning'
  elif echo "$IMAGE_TAG" | grep -q "penta-abc"; then # deploy to penta-abc
    URL='http://172.16.29.64:3023/dashboard/versioning'
  elif echo "$IMAGE_TAG" | grep -q "vitrox"; then # deploy to vitrox
    URL='http://10.18.45.11:9050/dashboard/versioning'
  elif echo "$IMAGE_TAG" | grep -q "mockv"; then # deploy to vitrox mock
    URL='http://13.215.79.27:9020/dashboard/versioning'
  elif echo "$IMAGE_TAG" | grep -q "latest"; then # deploy to subang
    URL='http://172.16.3.101:9020/dashboard/versioning'
  elif echo "$IMAGE_TAG" | grep -q "wirago"; then # deploy to wirago
    URL='http://172.16.1.11:9050/dashboard/versioning'
  elif echo "$IMAGE_TAG" | grep -q "kha"; then # deploy to kha
    URL='http://172.16.1.11:9050/dashboard/versioning'
  else # others image tag
    if echo "$PLACES" | grep -q "penta-d"; then # deploy to penta-d
      URL='http://172.16.29.58:3030/dashboard/versioning'
    elif echo "$PLACES" | grep -q "penta-abc"; then # deploy to penta-abc
      URL='http://172.16.29.64:3023/dashboard/versioning'
    elif echo "$PLACES" | grep -q "vitrox"; then # deploy to vitrox
      URL='http://10.18.45.11:9050/dashboard/versioning'
    elif echo "$PLACES" | grep -q "mockv"; then # deploy to vitrox mock
      URL='http://13.215.79.27:9020/dashboard/versioning'
    elif echo "$PLACES" | grep -q "wirago"; then # deploy to wirago
      URL='http://172.16.1.11:9050/dashboard/versioning'
    elif echo "$PLACES" | grep -q "kha"; then # deploy to kha
      URL='http://172.16.1.11:9050/dashboard/versioning'
    else # deploy to subang
      URL='http://172.16.3.101:9020/dashboard/versioning'
    fi
  fi

  while true; do
    # Run the curl command to get the current version from DB
    response=$(curl -s -X GET "$URL" -H 'accept: application/json')

    # Check if the curl command was successful
    if [ $? -eq 0 ] && [ "$(echo "$response" | grep -o '"code": [0-9]*' | awk '{print $2}')" -eq 200 ]; then
      echo "Response received successfully"
      break
    else
      echo "Retry: Failed to connect or received 200 code response"
    fi
    
    # Wait for a few seconds before retrying
    sleep 5
  done

  current_version=$(echo "$response" | grep -o '"TC_DASHBOARD_VERSION": "[^"]*' | awk -F'"' '{print $4}')

  # Extract the version parts (major, minor, patch) from the current version
  major=$(echo "$current_version" | awk -F'.' '{print $1}')
  minor=$(echo "$current_version" | awk -F'.' '{print $2}')
  patch=$(echo "$current_version" | awk -F'.' '{print $3}')

  # Check if the IMAGE_TAG contains "latest"|"penta-d"|"penta-abc"|"vitrox"|"mockv"
  if echo "$IMAGE_TAG" | grep -Eq "latest|penta-d|penta-abc|vitrox|mockv"; then
    minor=$((minor + 1))
    patch=0
  else
    patch=$((patch + 1))
  fi

  # Construct the new version
  new_version="${major}.${minor}.${patch}"

  while true; do
    # Send the updated version back to the backend using curl
    update_response=$(curl -X PATCH "$URL" \
      -H 'accept: application/json' \
      -H 'Content-Type: application/json' \
      -d "{\"TC_DASHBOARD_VERSION\": \"$new_version\"}")

    # Check if the curl command was successful
    if [ $? -eq 0 ] && [ "$(echo "$update_response" | grep -o '"code": [0-9]*' | awk '{print $2}')" -eq 200 ]; then
      echo "Version updated successfully to $new_version"
      break
    else
      echo "Retry: Failed to connect or received 200 code response"
    fi
    
    # Wait for a few seconds before retrying
    sleep 5
  done

  # Create a flag file to indicate that the version update has been completed
  touch /version_update_completed
fi
