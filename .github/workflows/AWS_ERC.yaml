on:
  push:
    branches: [ master ]
  workflow_dispatch:
    inputs:
      tag:
        description: 'Image Tag to input'
        required: true
        default: 'latest'

name: BUILD_AND_PUSH_AWS_ECR

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest

    steps:
    - name: Checkout
      uses: actions/checkout@v2

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ap-southeast-1

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1

    - name : Create env from secrets
      id : env-creation
      env :
        PROD_ENV_FILE: ${{ secrets.SUBANG_PROD_ENV }}
      run: |
        echo $PROD_ENV_FILE | base64 --decode > .env

    - name: Build, tag, and push the image to Amazon ECR
      id: build-image
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        ECR_REPOSITORY: ${{ secrets.REPO_NAME }}
        IMAGE_TAG: ${{ github.event.inputs.tag }}
      run: |
        # Build a docker container and push it to ECR 
        echo $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG

        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .

        echo "Pushing image to ECR..."
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
        echo "::set-output name=image::$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG"