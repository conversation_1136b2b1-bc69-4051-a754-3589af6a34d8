# Storybook Setup for TC Dashboard

## Installation & Setup

### Dependencies Installed
```bash
npm install --save-dev @storybook/vue@^6.5.16 @storybook/addon-essentials@^6.5.16 @storybook/addon-actions@^6.5.16 @storybook/addon-links@^6.5.16 @storybook/addon-controls@^6.5.16 --legacy-peer-deps
```

### Configuration Files Created
- `.storybook/main.js` - Main Storybook configuration
- `.storybook/preview.js` - Preview configuration with Vuetify setup
- `src/components/dialogs/DialogJobDetail.stories.js` - Component stories

## Running Storybook

### Start Storybook Development Server
```bash
npm run storybook
```
This will start Storybook on `http://localhost:6006`

### Build Storybook for Production
```bash
npm run build-storybook
```

## DialogJobDetail Component Stories

### Available Stories

1. **Test** - Test and Sample
2. **Job** - **TC Job and SM Order Dialog**

## Benefits of Storybook Integration

### For Development
- **Isolated Testing**: Test components in isolation
- **Visual Testing**: See components in different states
- **Interactive Documentation**: Live component examples
- **Regression Testing**: Catch visual regressions

## Troubleshooting

### Common Issues
1. **Vuetify Not Loading**: Ensure Vuetify is properly configured in preview.js
2. **API Calls Failing**: Use mock data for testing
3. **Styling Issues**: Check that dark theme is applied correctly
4. **Component Not Rendering**: Verify component imports and dependencies

### Node Version Compatibility
- **Vue 2.x**: Compatible with Node 14-18
- **Storybook 6.x**: Best compatibility with Node 16
- **Legacy Peer Deps**: Required for Vue 2 compatibility

## Future Enhancements

### Potential Additions
- **More Stories**: Add stories for other dialog components
- **Interaction Testing**: Add user interaction tests
- **Accessibility Testing**: Add a11y testing addons
- **Visual Regression**: Add visual regression testing
- **Performance Testing**: Add performance monitoring

## Conclusion
Storybook provides an excellent platform for testing the DialogJobDetail component and its SM Dialog functionality. The setup allows for comprehensive testing of:

- ✅ **Component Isolation**: Test SM dialog independently
- ✅ **Visual Consistency**: Ensure dark theme compliance
- ✅ **Data Display**: Verify all SM order information
- ✅ **User Interactions**: Test dialog opening/closing
- ✅ **Error Handling**: Test various error scenarios
- ✅ **Responsive Design**: Test on different screen sizes

