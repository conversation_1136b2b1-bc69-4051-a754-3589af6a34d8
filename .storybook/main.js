const path = require('path');

module.exports = {
  stories: [
    "../src/**/*.stories.mdx",
    "../src/**/*.stories.@(js|jsx|ts|tsx)"
  ],
  addons: [
    "@storybook/addon-links",
    "@storybook/addon-essentials",
    "@storybook/addon-controls"
  ],
  framework: {
    name: "@storybook/vue",
    options: {}
  },
  docs: {
    autodocs: "tag"
  },
  webpackFinal: async (config) => {
    // Add Vuetify support
    config.module.rules.push({
      test: /\.s(c|a)ss$/,
      use: [
        'vue-style-loader',
        'css-loader',
        {
          loader: 'sass-loader',
          options: {
            implementation: require('sass'),
            sassOptions: {
              fiber: false,
            },
          },
        },
      ],
    });

    // Handle Node.js modules that don't work in browser
    config.node = {
      fs: 'empty',
      net: 'empty',
      tls: 'empty',
      crypto: 'empty',
      os: 'empty'
    };

    // Add path aliases
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': path.resolve(__dirname, '../src'),
      '@/dashboard': path.resolve(__dirname, '../src/dashboard'),
      '@/helper': path.resolve(__dirname, '../src/helper'),
      '@/api': path.resolve(__dirname, '../src/api'),
      '@/components': path.resolve(__dirname, '../src/components'),
      '@/assets': path.resolve(__dirname, '../src/assets'),
      '@/plugins': path.resolve(__dirname, '../src/plugins')
    };

    return config;
  },
}; 