{
  "env": {
    "browser": true,
    "node": true
  },
  "extends": [
    "plugin:vue/base", 
    "plugin:vue/essential" ,  
    "eslint:recommended"
  ],
  "globals": {
    "Atomics": "readonly",
    "SharedArrayBuffer": "readonly"
  },
  "parser": "vue-eslint-parser",
  "parserOptions": {
    "parser": "babel-eslint",
    "ecmaVersion": 2020, // nodejs 14 above
    "sourceType": "module"
  },
  "plugins": ["vue"],
  "rules": {
    "strict": "warn",
    "quotes": ["warn", "double"],
    "object-curly-spacing": ["warn", "always"],
    "max-len": ["warn", { "code": 120, "ignoreComments": true }],
    "linebreak-style": ["off"],
    "operator-linebreak": "off",
    "require-jsdoc": ["off"],
    "camelcase": "warn",
    "comma-dangle": ["off"]
  },
  "overrides": [
    {
      "files": ["**/*.js"], // Specify the file pattern or directory where you want to apply the override
      "rules": {
        "indent": "off" // Replace "indent" with the specific rule you want to disable ,
      }
    }
  ]
}
