#!/bin/bash
## Usage macOS
# cd scripts
# bash build_push_aws.sh your_tag      

## Usage Windows
## Open PowerShell
## cd scripts
## .\build_push_aws.sh your_tag

## For both image_tag and places
## places argument will only be used if image_tag is not from tag specified below 
## E.g If you want to deploy to penta d with other image_tag then your places argument should be "penta-d"
# "latest" for subang
# "penta-abc" for penta abc
# "penta-d" for penta d
# "vitrox" for vitrox
# "mockv" for mock vitrox
# "maintenance-staging" for maint dashbaord
# "vitrox-mock-sm-2" for sm-wms(vitrox dual-winch sc) integration staging server
# "vitrox-mock-sm-model-c" for sm-wms(vitrox model sc) integration staging server
# 'stable' for new vitrox ( vitrox2.0 )
# "wirago" for wirago


image_tag=${1:-latest}
places=${2:-latest}

echo "============Build on platform $OSTYPE ================"
if [[ "$OSTYPE" == "darwin"* ]]; then 
    docker build --platform linux/amd64 \
        --build-arg IMAGE_TAG="$image_tag" \
        --build-arg PLACES="$places" \
        -t 551979302293.dkr.ecr.ap-southeast-1.amazonaws.com/cube-ihub-tc-web:$image_tag .
else 
    docker build --build-arg IMAGE_TAG="$image_tag" \
        --build-arg PLACES="$places" \
        -t 551979302293.dkr.ecr.ap-southeast-1.amazonaws.com/cube-ihub-tc-web:$image_tag .
fi

echo "===========Push image to ECR============"
aws ecr get-login-password --region ap-southeast-1| docker login --username AWS --password-stdin 551979302293.dkr.ecr.ap-southeast-1.amazonaws.com
docker push 551979302293.dkr.ecr.ap-southeast-1.amazonaws.com/cube-ihub-tc-web:$image_tag
read -p "Press Enter to continue..."
